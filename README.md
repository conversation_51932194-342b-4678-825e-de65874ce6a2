# Mercately  
[![Maintainability](https://api.codeclimate.com/v1/badges/439ed5a23d7af6e1d4da/maintainability)](https://codeclimate.com/repos/5d94b45a3bf77409dc002e04/maintainability)
[![Test Coverage](https://api.codeclimate.com/v1/badges/439ed5a23d7af6e1d4da/test_coverage)](https://codeclimate.com/repos/5d94b45a3bf77409dc002e04/test_coverage)

## Setting up the environment

Our environment:
* Ruby 2.5.3
* Rails 5.2.2
* PostgreSQL 11.1

## Run the project locally

After installing all the required technologies pointed in the block above. You need to execute the
following commands, each one in a different terminal:

* `SCOUT_DEV_TRACE=true rails s` (execute Puma server along with scout gem to measure requests duration)
* `node server.js` (execute the server for nodejs)
* `./bin/webpack-dev-server` (execute webpacker)
* `bundle exec sidekiq` (execute sidekiq for background jobs)

## Branch naming convention
Please use one of these four prefixes for branch names: `feature/`, `test/`, `refactor/`, and `bug/`

## 🚀 Kafka Campaigns Architecture

Mercately now supports **asynchronous campaign processing** using Apache Kafka for improved scalability and reliability.

### Quick Start with Docker
```bash
# Start Kafka and Karafka services
./bin/kafka-docker up

# Run campaign tests
./bin/test-kafka-campaigns
```

### Key Features
- ✅ **Asynchronous processing** - No more timeouts on large campaigns
- ✅ **Horizontal scalability** - Handle 10+ concurrent campaigns
- ✅ **Automatic recovery** - Resilient to failures
- ✅ **Real-time tracking** - Monitor campaign progress
- ✅ **100% backward compatible** - Existing campaigns continue working

### Architecture Overview
- **K01 (Producer)**: Web interface, creates campaign events
- **K02 (Consumer)**: Background processing, sends WhatsApp messages
- **Kafka Topic**: `mercately_campaign_events` for event streaming
- **Redis**: Campaign counters and progress tracking

### Documentation
- 📚 [Complete Architecture Guide](README-KAFKA-ARCHITECTURE.md)
- 🔧 [Troubleshooting Guide](TROUBLESHOOTING-KAFKA.md)
- 📊 [Before/After Comparison](BEFORE-AFTER-COMPARISON.md)
- 🐳 [Docker Setup Guide](README-DOCKER-KAFKA.md)

## Setup

#### MercadoLibre (ML):
To run this project you should not need ML keys, but surely you will need to work with, so to get your own ML key follow these steps:
- Sign up and download [ngrok](https://ngrok.com/download)
- Exec `./ngrok http 3000` (You will need to do this ever you start your computer)
- Go to [ML developers portal](https://developers.mercadolibre.com.ec/), make an account, after that click in your profile icon and select `My applications`
- In `Auth and security` put `https://[your ngrok subdomain].ngrok.io/retailers/integrations/mercadolibre`
- Check all `Scopes` except `orders` and `created_orders`, because we are working with `orders_v2`
- In `Notification's settings` write `https://[your ngrok subdomain].ngrok.io/retailers/callbacks`
![Your configs should looks like this](https://i.imgur.com/gbFD0v9.png)
- Copy `.env.sample` file into `.env`
- Copy your ngrok domain and paste in `.env` file
- Copy your `App ID` and `Secret Key` and paste in `.env` file

#### Cloudinary:
For your local environment you will not need config this, but maybe you will need to work with it
- Sign up in [Cloudinary](https://cloudinary.com)
- Copy your `Cloud name`, `API Key` and `API Secret` into `.env` file
- Go to `config/environments/development.rb` file and change:
```ruby
config.active_storage.service = :local
```
to
```ruby
config.active_storage.service = :cloudinary
```

#### WhatsApp (Karix)
In order to use the WhatsApp integration in development mode with Karix, you need to follow the next steps:

- Sign up and download [ngrok](https://ngrok.com/download)
- Exec `./ngrok http 3000`
- Sign up on [Karix](https://www.karix.io/)
- Set in the retailer that you use for testing these two attributes in the database: `karix_account_uid={your Karix ACCOUNT ID}`, and `karix_account_token={your Karix ACCOUNT TOKEN}`. These two values can be found in your Karix dashboard after signing up|in.
- Edit your Retailer and update these two attributes: `whats_app_enabled` set to `true`, and `karix_whatsapp_phone` set to the sandbox phone number provided and specified in the Karix Dashboard, let's say `+***********`, for instance
- In your Karix dashboard, put the following URL in the text input besides the button `Edit Webhook URL` and click it later, `https://[your ngrok subdomain].ngrok.io/api/v1/karix_whatsapp?account_id={your Retailer ID}`
- Copy to your `.env` file the next variable: `KARIX_WEBHOOK={https://[your ngrok subdomain].ngrok.io/api/v1/karix_whatsapp}`
- In your cellphone, save this number `+***********`
- To join your Karix sandbox account, you have to send a whatsapp message, to the number saved in the past step, with the text indicated in your Karix dashboard, for example, something like this: `join generic-crayfish`. In general, that's the structure of the message, what changes between accounts is the last part of it, i.e: `join xxxxx-xxxxxx`. You should receive an answer with the text that you are already part of the sandbox.

#### Facebook Messenger
In order to use the Messenger integration in development mode, you need to follow the next steps:

- Sign up and download [ngrok](https://ngrok.com/download)
- Exec `./ngrok http 3000`
- Create a page on your facebook account, also known as fanpage
- Sign up on [facebook for developers](https://developers.facebook.com/)
- Click on `My Apps` and go to `Create App`
- Type a Display Name and Click `Create App ID`
- Go to `Settings` -> `Basic` in the left sidebar and copy to your `.env` file the next variables: `FACEBOOK_APP_ID={ID of your Facebook App}`, and `FACEBOOK_APP_SECRET={App Secret of your Facebook App}`.
- In the same view of the past step, put in the text input `App Domains` this: `[your ngrok subdomain].ngrok.io`
- In the same view, scroll down and click `Add Platform` and select in the modal `Website`. Put in the text input `Site URL` this `https://[your ngrok subdomain].ngrok.io/` and finally click `Save Changes`
- Go to `Dashboard` in the left sidebar and in the section `Add a Product`, click `Set Up` on the `Facebook Login` option.
- Go to `Facebook Login` -> `Settings` in the left sidebar, in the text input `Valid OAuth Redirect URIs` put `https://[your ngrok subdomain].ngrok.io/auth/facebook/callback` and click `Save Changes`
- Go back to `Dashboard`, go to the section `Add a Product`, and click `Set Up` on the `Webhooks` option.
- Select the option `Page` from the list, in the view shown after the past step, or clicking in the sidebar `Webhooks`, and click the button `Subscribe to this object`
- In the modal, put this `https://[your ngrok subdomain].ngrok.io/retailers/messenger_callbacks` in the text input `Callback URL` and put this `3388c56076ff02f0463f9b605958fa961e4457148586c791ccc0b04b3920c58e` in the text input `Verify Token` and click `Verify and Save`
- Copy to your `.env` file this variable: `FACEBOOK_VERIFY_TOKEN=3388c56076ff02f0463f9b605958fa961e4457148586c791ccc0b04b3920c58e`
- Go back to `Dashboard`, go to the section `Add a Product`, and click `Set Up` on the `Messenger` option.
- In the view shown after the past step, or clicking in the sidebar `Messenger` -> `Settings`, scroll down to the section `Access Tokens`, click on `Add or Remove Pages`, this will take you to select the page you want to link with your app (select only one page) and grant all permissions solicited in the view.
- Scroll down in the same view to the section `Webhooks`, and click `Add Subscriptions`, in the modal, select the next three options: `messages`, `message_deliveries` and `message_reads` and click `Save`
- Log into your Mercately account, go to the sidebar `Integraciones` option, and click `Conectar con Messenger`, this will take you to the facebook views to connect your app with Mercately.
- You have to grant all permissions solicited in the modal shown, so your Facebook App could be properly managed (select only one page in the modals shown in this step). Your config should look like this:
![Your config should look like this](https://i.imgur.com/KPp7Z21.png)

#### Instagram

- You must have a real FB account, connected with IG and the IG user name needs to start with `test_`, example: `test_dani777`
- Setup a IG business account with this IG account
- The same Facebook page must be selected (which one you integrated Messenger) to link the IG business account

- Go to 'Instagram settings' on Messenger config
- The callback URL must be `https://your-ngrok-id.ngrok.io/retailers/messenger_callbacks` and the verification token can be the same than Messenger at the `.env`
- Webhooks subscriptions:
![imagen](https://user-images.githubusercontent.com/********/*********-e122fc63-7071-4cca-843e-7ac5bbca7d22.png)
- All versions should be v5.0, since thats the version which Messenger is working
![imagen](https://user-images.githubusercontent.com/********/*********-691cc616-ebca-4b99-9672-97c94b6972c9.png)
- To confirm the webhook subscription go to `webhooks`, change to Instagram and verify
![imagen](https://user-images.githubusercontent.com/********/*********-66dbda32-652e-407b-86a3-9800cfb4a46f.png)

#### Create ML test users
A real ML account has some limitations like:
- Your products can be bought
- You have a buy number limit
- You will need to use multiple emails to create them

To create some test users follow these steps:
- Get your own [Access Token](https://developers.mercadolibre.com.ec/en_us/authentication-and-authorization#token)
- Open your term and write:
```sh
curl -X POST -H "Content-Type: application/json" -d
'{
  "site_id": "MEC"
}'
https://api.mercadolibre.com/users/test_user?access_token=[Your access_token]
```
Response should be something like:
```json
{
  "id": *********,
  "nickname": "TEST0548",
  "password": "qatest328",
  "site_status": "active"
}
```

##### Considerations
When working with test users, you need to take into account the following considerations (more info [here](https://developers.mercadolibre.com.ec/en_us/start-testing)):

    You can create up to 10 test users with your mercadolibre account.
    Test users won’t be active for too long, but once they expire, you can create new ones.
    List under the “Others” category as much as possible.
    Never list under “gold” or “gold_premium” so it doesn’t get to our home page.
    Test users can only operate with test items: Test users can only buy, sell, and make questions on test items.
    Test users showing no activity (buy, ask, publish, etc.) during 60 days are immediately removed.
    Test items are removed regularly.

## Git Hooks Installation Guide

For detailed instructions on how to install and configure Git Hooks in this project, see the [Git Hooks Installation Guide](./git-hooks-setup.md).
