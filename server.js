var express = require('express');
var http = require('http');
var { createAdapter } = require('@socket.io/redis-adapter');
var { Server } = require('socket.io');
var Redis = require('ioredis');
var cors = require('cors');

// Create an Express instance
var app = express();

// Enable CORS for all routes
app.use(cors()); 

// Create an HTTP server
var server = http.createServer(app);

// Initialize Socket.IO
var io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type'],
    credentials: true
  }
});

// Create Redis clients for the adapter
const pubClient = new Redis();
const subClient = pubClient.duplicate();  // Create a duplicate subscription client

// Use Redis adapter with Socket.IO
io.adapter(createAdapter(pubClient, subClient));

// Handle Redis connection ready state
pubClient.on("ready", function() {
  console.log("Redis client connected and ready to use.");
});

// Handle Redis errors
pubClient.on("error", function(error) {
  console.error("Redis error:", error);
});

var port = 8181;

// List of channels to subscribe to
const channels = [
  'new_message_counter',
  'customer_chat',
  'message_chat',
  'customer_facebook_chat',
  'message_facebook_chat',
  'customer_instagram_chat',
  'message_instagram_chat',
  'agent_assignment',
  'ml_orders',
  'ml_messages',
  'customer_fb_post_facebook',
  'customer_fb_post_instagram',
  'message_facebook_comment',
  'message_instagram_comment',
  'mobile_customer_event',
  'whatsapp_qr_code',
  'update_retailer_info',
  'update_retailer_user_info',
  'update_customer_chat_info'
];

/**
 * Subscribe to multiple Redis channels.
 * 
 * @param {string[]} channels - Array of Redis channels to subscribe to.
 */
subClient.subscribe(channels, (err, count) => {
  if (err) {
    console.error('Failed to subscribe:', err);
  } else {
    console.log(`Subscribed channels:`, channels);
  }
});

/**
 * Handle incoming messages from Redis channels.
 * 
 * @param {string} channel - The Redis channel where the message was published.
 * @param {string} data - The message data in JSON format.
 */
subClient.on('message', function(channel, data) {
  try {
    data = JSON.parse(data);
    io.in(data['room']).emit(channel, data);
  } catch (error) {
    console.error('Error parsing message data:', error);
  }
});

// Start the server on the specified port
server.listen(port, function () {
  console.log('Server listening at port %d', port);
});

/**
 * Handle new Socket.IO connections.
 * 
 * @param {Socket} socket - The connected socket instance.
 */
io.on('connection', function (socket) {
  console.log('A user connected');

  /**
   * Handle socket disconnection.
   */
  socket.on('disconnect', function() {
    console.log('User disconnected');
    socket.leave(socket.room);
  });

  /**
   * Handle room creation event.
   * 
   * @param {string} room - The name of the room to join.
   */
  socket.on('create_room', function(room) {
    socket.room = room;
    socket.join(room);
    console.log(`User joined room: ${room}`);
  });
});
