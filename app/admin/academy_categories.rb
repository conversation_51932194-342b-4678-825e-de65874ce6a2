ActiveAdmin.register AcademyCategory do
  permit_params :name, :module_name, :description, video_ids: []

  show do
    attributes_table do
      row :name
      row :module_name
      row :description
    end

    panel 'Videos de la Categoría' do
      table_for resource.academy_videos do
        column :title
        column :description
        column :position
        column 'Enlace' do |video|
          if video.external_url.present?
            link_to 'Ver Video', video.external_url, target: '_blank', rel: 'noopener'
          else
            'Sin enlace'
          end
        end
        column 'Acciones' do |video|
          links = link_to('Editar', edit_admin_academy_video_path(video), class: 'button')
          links += ' | '
          links += link_to('Eliminar', admin_academy_video_path(video), method: :delete,
                                                                        data: { confirm: '¿Estás seguro?' },
                                                                        class: 'button')
          links.html_safe # rubocop:disable Rails/OutputSafety
        end
      end

      div do
        link_to 'Agregar Video', new_admin_academy_video_path(academy_category_id: resource.id), class: 'button'
      end
    end
  end
end
