ActiveAdmin.register Retailer do
  includes :meli_retailer, :facebook_retailer, :retailer_user
  actions :all, except: :destroy

  action_item :gs_timezones, only: [:index] do
    link_to 'Update GS Timezones', action: 'upload_gs_timezone_excel'
  end

  permit_params :name,
                :id_type,
                :id_number,
                :address,
                :city,
                :state,
                :zip_code,
                :phone_number,
                :whats_app_enabled,
                :karix_whatsapp_phone,
                :karix_account_uid,
                :karix_account_token,
                :ws_balance,
                :ws_notification_cost,
                :ws_conversation_cost,
                :gupshup_phone_number,
                :gupshup_src_name,
                :is_capi,
                :unlimited_account,
                :ecu_charges,
                :int_charges,
                :gupshup_api_key,
                :ml_domain,
                :ml_site,
                :gupshup_app_id,
                :gupshup_app_token,
                :delete_assets,
                :gupshup_timezone,
                :allow_qr,
                :qr_phone_id,
                :campaign_chunk_size,
                :connect_qr_mercately,
                :allow_parallel_import,
                :keep_chat_assignment,
                :connect_bsp_mercately,
                :is_on_biz_app,
                :connect_feature_bsp,
                :kafka_enabled,
                retailer_bill_detail_attributes: [
                  :id,
                  :business_name,
                  :identification_type,
                  :identification_number,
                  :business_phone,
                  :business_email,
                  :business_address,
                  :iva_description,
                  :_destroy
                ],
                partner_config_attributes: [
                  :id,
                  :partner_id,
                  :retailer_id,
                  :marketing_cost,
                  :utility_cost,
                  :authentication_cost,
                  :service_cost,
                  :hide_credit_cards,
                  :hide_live_chat,
                  :hide_support_chat,
                  :hide_pricing_info,
                  :_destroy
                ]

  filter :id
  filter :name
  filter :slug
  filter :gupshup_phone_number
  filter :meli_retailer_meli_user_id_cont, label: 'Meli user id'
  filter :users_email_cont, label: 'User email'
  filter :created_at
  filter :retailer_onboarding_finish_eq, label: 'Onboarding Completo', as: :select,
                                         collection: [['Yes', true], ['No', false]]

  menu url: -> { "#{request.base_url}/admin/retailers?order=id_desc" }

  index do
    selectable_column
    id_column
    column :name
    column :slug
    column 'Meli User Id' do |retailer|
      retailer.meli_retailer&.meli_user_id
    end
    column 'Facebook User Id' do |retailer|
      retailer.facebook_retailer&.uid
    end
    column 'Karix Phone Number', &:karix_whatsapp_phone
    column :ws_balance
    column :retailer_user
    column :created_at
    actions
    column 'Login' do |resource|
      link_to 'Login as', login_as_admin_retailer_path(resource), class: 'member_link edit_link'
    end

    column 'Agregar Saldo' do |resource|
      link_to 'Agregar Saldo', new_admin_top_up_path(retailer_id: resource.id), class: 'member_link edit_link'
    end

    column 'Pago fallído' do |resource|
      link_to 'Enviar email', failed_charge_email_admin_retailer_path(resource.id), class:
        'member_link edit_link'
    end

    column 'Generar Factura' do |resource|
      link_to 'Generar Factura', new_admin_ecuador_invoice_path(retailer_id: resource.id), class:
        'member_link edit_link'
    end
  end

  csv do
    column :id
    column :name
    column(:email) { |retailer| retailer.retailer_user&.email }
    column(:first_name) { |retailer| retailer.retailer_user&.first_name }
    column(:last_name) { |retailer| retailer.retailer_user&.last_name }
    column :city
    column :phone_number
    column :retailer_number
    column :ws_balance
    column :karix_whatsapp_phone
    column :ws_notification_cost
    column :ws_conversation_cost
    column :gupshup_phone_number

    # Columnas de UTM parámetros
    column(:utm_source) { |retailer| retailer.retailer_onboarding&.utm_source }
    column(:utm_medium) { |retailer| retailer.retailer_onboarding&.utm_medium }
    column(:utm_campaign) { |retailer| retailer.retailer_onboarding&.utm_campaign }
    column(:utm_content) { |retailer| retailer.retailer_onboarding&.utm_content }
    column(:utm_term) { |retailer| retailer.retailer_onboarding&.utm_term }
    column(:utm_campaign_id) { |retailer| retailer.retailer_onboarding&.utm_campaign_id }

    column :created_at
  end

  show do
    attributes_table title: 'Detalles del Retailer' do
      row :id
      row :name
      row :first_name do |retailer|
        retailer.retailer_user&.first_name
      end
      row :last_name do |retailer|
        retailer.retailer_user&.last_name
      end
      row :retailer_number
      row :slug
      row :id_type
      row :id_number
      row :address
      row :city
      row :state
      row :zip_code
      row :phone_number
      row :phone_verified
      row :unlimited_account
      row :gupshup_timezone
      row :ecu_charges
      row :int_charges
      row :max_agents
      row :ml_domain
      row :ml_site
      row :delete_assets
      row :created_at
      row :updated_at
      row :allow_qr
      row :campaign_chunk_size
      row 'Workspaces', &:workspaces_count
      row :allow_parallel_import
      row :keep_chat_assignment
      row :kafka_enabled
    end

    panel 'Detalles de factura' do
      details = retailer.retailer_bill_detail
      attributes_table_for details do
        row :id
        row :business_name
        row :identification_type
        row :identification_number
        row :business_phone
        row :business_email
        row :business_address
        row :created_at
        row :updated_at
      end
    end

    panel 'Información de Mercado Libre' do
      meli_information = MeliRetailer.find_by(retailer_id: retailer.id)
      attributes_table_for meli_information do
        row :id
        row :nickname
        row :email
        row :access_token
        row :meli_user_id
        row :refresh_token
        row :points
        row :link
        row :seller_experience
        row :seller_reputation_level_id
        row :transactions_canceled
        row :transactions_completed
        row :ratings_negative
        row :ratings_positive
        row :ratings_neutral
        row :ratings_total
        row :phone
        row :has_meli_info
        row :retailer_id
        row :customer_id
        row :meli_token_updated_at
        row :meli_info_updated_at
        row :created_at
        row :updated_at
      end
    end

    panel 'Información de Facebook' do
      fb_info = retailer.facebook_retailer
      attributes_table_for fb_info do
        row :id
        row :access_token
        row :messenger_integrated
        row :uid
        row :instagram_integrated
        row :instagram_uid
        row :created_at
        row :updated_at
      end
    end

    panel 'Información de WhatsApp' do
      attributes_table_for retailer do
        row :whats_app_enabled
        row :qr_phone_id
        row :qr_phone_number
        row :karix_whatsapp_phone
        row :karix_account_uid
        row :karix_account_token
        row :gupshup_src_name
        row :gupshup_phone_number
        row :gupshup_api_key
        row :gupshup_app_id
        row :gupshup_app_token
        row :is_capi
        row :connect_bsp_mercately
        row 'Usa QR BSP Mercately', &:is_on_biz_app
        row 'Saldo', &:ws_balance
      end

      if retailer.connect_bsp_mercately
        begin
          phone_info = Whatsapp::Integration::BspClient.new(retailer).call
          if phone_info[:data].present?
            panel 'Información del Número de Teléfono (API)' do
              attributes_table_for phone_info[:data] do
                row 'id'
                row 'verified_name'
                row 'status'
                row 'quality_rating'
                row 'platform_type'
                row 'code_verification_status'
                row 'display_phone_number'
              end

              if @error_message.present?
                div class: 'alert alert-error' do
                  @error_message
                end
              end

              if phone_info[:data]['status'] != 'CONNECTED'
                div class: 'action-buttons' do
                  link_to 'Registrar Número', connect_api_admin_retailer_path(retailer.slug), method: :post,
                                                                                              class: 'button'
                end
              end
            end
          end
        rescue StandardError => e
          panel 'Información del Número de Teléfono (API)' do
            div class: 'alert alert-error' do
              "Error al consultar la API: #{e.message}"
            end
          end
        end
      end
    end

    panel 'Plan' do
      attributes_table_for retailer do
        row 'Hace pagos en Ecuador' do
          retailer.ecu_charges
        end
        row 'Hace pagos internacionales' do
          retailer.int_charges
        end
      end
      plan = retailer.payment_plan
      attributes_table_for plan do
        row :price
        row :month_interval
        row :start_date
        row :next_pay_date
        row :status
        row :plan
      end
    end

    panel 'Métodos de Pago' do
      if retailer.ecu_charges
        table_for retailer.paymentez_credit_cards do
          column 'card_type' do |pcc|
            PaymentezCardHelper.brand(pcc.card_type)
          end
          column :name
          column :number
          column :token
          column 'Expiration Date' do |pcc|
            "#{pcc['expiry_month']}/#{pcc['expiry_year']}"
          end
          column :main
        end
      elsif retailer.int_charges
        table_for retailer.payment_methods do
          column 'payment_type', &:payment_type
          column 'number' do |pm|
            card = JSON.parse(pm.payment_payload)['card']
            card['last4']
          end
          column 'token', &:stripe_pm_id
          column 'Expiration Date' do |pm|
            card = JSON.parse(pm.payment_payload)['card']
            "#{card['exp_month']}/#{card['exp_year']}"
          end
          column 'Card\'s holder name' do |pm|
            billing_details = JSON.parse(pm.payment_payload)['billing_details']
            billing_details['name']
          end
        end
      end
    end

    panel 'Transacciones' do
      if retailer.ecu_charges
        table_for retailer.paymentez_transactions.order(id: :desc) do
          column :status
          column :payment_date
          column :amount
          column :authorization_code
          column :installments
          column :dev_reference
          column :message
          column :carrier_code
          column 'REF #', &:pt_id
          column :status_detail
          column :transaction_reference
          column 'Payment Plan' do |pt|
            pt.retailer.payment_plan.plan
          end
          column :paymentez_credit_card_id
          column 'Credit Card' do |pt|
            card = PaymentezCreditCard.unscoped.find(pt.paymentez_credit_card_id)
            type = PaymentezCardHelper.brand(card.card_type)

            "ID: #{card.id}, #{type},
             #####{card.number},
             #{card.expiry_month}/#{card.expiry_year}"
          end
          column 'Refund Transaction' do |pt|
            if pt.status == 'refund'
              ''
            else
              link_to 'Refund Transaction',
                      refund_paymentez_transaction_admin_retailer_path(pt.retailer, pt_id: pt),
                      class: 'member_link edit_link'
            end
          end
        end
      end
    end

    panel 'Agentes del retailer' do
      table_for retailer.retailer_users do
        column :id

        column 'Nombres', &:full_name

        column :email

        column 'Estado' do |ret_u|
          if ret_u.removed_from_team
            'Inactivo'
          elsif ret_u.invitation_token.blank?
            'Activo'
          else
            'Invitado'
          end
        end

        column 'Role' do |ret_u|
          if ret_u.retailer_admin == true
            'Administrador'
          elsif ret_u.retailer_supervisor == true
            'Supervisor'
          else
            'Agente'
          end
        end

        column 'Mobile app', &:mobile_info

        column 'Iniciar sesión' do |ret_u|
          link_to 'Login as', login_as_admin_retailer_path(ret_u.retailer, retailer_user_email: ret_u.email), class:
            'member_link edit_link'
        end
      end
    end

    panel 'Información del Onboarding' do
      onboar_info = retailer.retailer_onboarding
      attributes_table_for onboar_info do
        row :id

        row 'Industria Empresa' do |package|
          if (business = package.business.presence)
            if I18n.exists?("retailer_onboarding.business_info.business.#{business}")
              translate(business, scope: 'retailer_onboarding.business_info.business').join(', ')
            else
              business
            end
          end
        end

        row 'Rol' do |package|
          if (user_role = package.user_role.presence)
            if I18n.exists?("retailer_onboarding.company_info.user_role.#{user_role}")
              translate(user_role, scope: 'retailer_onboarding.company_info.user_role')
            else
              user_role
            end
          end
        end

        row 'Qué vende?' do |package|
          if (market = package.market.presence)
            if I18n.exists?("retailer_onboarding.company_info.market.#{market}")
              translate(market, scope: 'retailer_onboarding.company_info.market')
            else
              market
            end
          end
        end

        row 'Colaboradores Actuales' do |package|
          if (workers = package.workers.presence)
            if I18n.exists?("retailer_onboarding.company_info.workers_options.#{workers}")
              translate(workers, scope: 'retailer_onboarding.company_info.workers_options')
            else
              workers
            end
          end
        end

        row 'Colaboradores Plataforma' do |package|
          if (user_workers = package.user_workers.presence)
            if I18n.exists?("retailer_onboarding.company_info.workers_options.#{user_workers}")
              translate(user_workers, scope: 'retailer_onboarding.company_info.workers_options')
            else
              user_workers
            end
          end
        end

        row 'Logros Plataforma' do |package|
          if (raw_expectations = package.expectations).present?
            expectations = Array.wrap(raw_expectations).compact_blank

            translated_expectations = expectations.map do |key|
              I18n.t(
                key,
                scope: 'retailer_onboarding.expectations_info.expectations',
                default: key.to_s
              )
            end

            translated_expectations.join(', ')
          end
        end

        row :ecommerce

        row 'Plataforma Ecommerce' do |package|
          if (current_ecommerce = package.current_ecommerce.presence)
            if I18n.exists?("retailer_onboarding.expectations_info.current_ecommerce.#{current_ecommerce}")
              translate(current_ecommerce, scope: 'retailer_onboarding.expectations_info.current_ecommerce')
            else
              current_ecommerce
            end
          end
        end

        row 'Otra Plataforma Ecommerce' do |package|
          if (other_ecommerce = package.other_ecommerce.presence)
            other_ecommerce
          end
        end

        row 'Experiencia CRMs' do |package|
          if (crm_experience = package.crm_experience.presence)
            if I18n.exists?("retailer_onboarding.experience_info.crm_experience.#{crm_experience}")
              translate(crm_experience, scope: 'retailer_onboarding.experience_info.crm_experience')
            else
              crm_experience
            end
          end
        end

        row 'Experiencia Chatbots' do |package|
          if (bot_experience = package.bot_experience.presence)
            if I18n.exists?("retailer_onboarding.experience_info.bot_experience.#{bot_experience}")
              translate(bot_experience, scope: 'retailer_onboarding.experience_info.bot_experience')
            else
              bot_experience
            end
          end
        end

        row 'Invierte?' do |package|
          if (ad_investment = package.ad_investment.presence)
            if I18n.exists?("retailer_onboarding.company_info.ad_investment.#{ad_investment}")
              translate(ad_investment, scope: 'retailer_onboarding.company_info.ad_investment')
            else
              ad_investment
            end
          end
        end

        row 'Área de Trabajo' do |package|
          if (work_area = package.work_area.presence)
            if I18n.exists?("retailer_onboarding.company_info.work_area.#{work_area}")
              translate(work_area, scope: 'retailer_onboarding.company_info.work_area')
            else
              work_area
            end
          end
        end

        row 'Atendido por Asesor?' do |package|
          if (id_attended = package.id_attended.presence)
            if I18n.exists?("retailer_onboarding.company_info.id_attended.#{id_attended}")
              translate(id_attended, scope: 'retailer_onboarding.company_info.id_attended')
            else
              id_attended
            end
          end
        end
      end
    end

    panel 'UTM parameters' do
      onboar_info = retailer.retailer_onboarding
      attributes_table_for onboar_info do
        row :utm_source
        row :utm_medium
        row :utm_campaign
        row :utm_content
        row :utm_term
        row :utm_campaign_id
      end
    end

    panel 'Attributer parameters' do
      onboar_info = retailer.retailer_onboarding
      attributes_table_for onboar_info do
        row :attributer_channel
        row :attributer_channeldrilldown1
        row :attributer_channeldrilldown2
        row :attributer_channeldrilldown3
        row :attributer_channeldrilldown4
        row :attributer_landingpage
        row :attributer_landingpagegroup
      end
    end

    panel 'Configuración del partner' do
      partner_config = retailer.partner_config
      attributes_table_for partner_config do
        row :partner
        row :marketing_cost
        row :utility_cost
        row :authentication_cost
        row :service_cost
        row :hide_credit_cards
        row :hide_live_chat
        row :hide_support_chat
        row :hide_pricing_info
      end
    end

    render partial: 'active_admin/retailer_data'
  end

  form do |f|
    f.inputs do
      f.input :name
      f.input :id_type
      f.input :id_number
      f.input :address
      f.input :city
      f.input :state
      f.input :zip_code
      f.input :phone_number
      f.input :unlimited_account
      f.input :whats_app_enabled
      f.input :ml_site, as: :select, collection: MlCountry.all.map { |mlc| [mlc.name, mlc.site] }
      f.input :ml_domain
      f.input :karix_whatsapp_phone
      f.input :karix_account_uid
      f.input :karix_account_token
      f.input :ws_balance
      f.input :ws_notification_cost
      f.input :ws_conversation_cost
      f.input :gupshup_phone_number
      f.input :gupshup_src_name
      f.input :gupshup_api_key
      f.input :gupshup_timezone, as: :searchable_select, collection: timezones_list
      f.input :gupshup_app_id
      f.input :gupshup_app_token
      f.input :qr_phone_id
      f.input :campaign_chunk_size, label: 'No. clientes por campaña'
      f.input :ecu_charges, label: 'Hace pagos en Ecuador'
      f.input :int_charges, label: 'Hace pagos Internacionales'
      f.input :delete_assets, label: 'Borrar archivos en Cloudinary'
      f.input :allow_qr, label: 'Permitir integrar mediante QR'
      f.input :connect_qr_mercately, label: 'Usar QR Mercately'
      f.input :allow_parallel_import, as: :boolean, label: 'Puede hacer importaciones en paralelo'
      f.input :keep_chat_assignment, as: :boolean, label: 'Mantener asignación de chat'
      f.input :is_capi, label: 'App de Retailer en Cloud API'
      f.input :connect_bsp_mercately, label: 'Usar BSP Mercately'
      f.input :connect_feature_bsp, label: 'Usar la función QR de BSP Mercately'
      f.input :kafka_enabled, label: 'Habilitar Kafka'

      f.inputs 'Detalles de factura', for: [
        :retailer_bill_detail, f.object.retailer_bill_detail || RetailerBillDetail.new
      ] do |rbd|
        rbd.input :business_name
        rbd.input :identification_type
        rbd.input :identification_number
        rbd.input :business_phone
        rbd.input :business_email
        rbd.input :business_address
        rbd.input :iva_description
        rbd.input :_destroy, as: :boolean, label: 'Eliminar detalles de factura' unless rbd.object.new_record?
      end

      if f.object.partner_id
        f.inputs 'Configuración del partner', for: [
          :partner_config, f.object.partner_config || PartnerConfig.new(partner_id: f.object.partner_id)
        ] do |pc|
          pc.input :partner_id, as: :hidden
          pc.input :marketing_cost
          pc.input :utility_cost
          pc.input :authentication_cost
          pc.input :service_cost
          pc.input :hide_credit_cards, label: 'Ocultar tarjetas de crédito'
          pc.input :hide_live_chat, label: 'Ocultar chat en vivo'
          pc.input :hide_support_chat, label: 'Ocultar chat de soporte'
          pc.input :hide_pricing_info, label: 'Ocultar precios y saldo'
          pc.input :_destroy, as: :boolean, label: 'Eliminar configuración' unless pc.object.new_record?
        end
      end
    end
    f.actions
  end

  controller do
    defaults finder: :find_by_slug
    include JwtCookie
  end

  # Custom actions
  member_action :login_as do
    retailer = Retailer.find_by(slug: params[:id])
    retailer_user = if params[:retailer_user_email].present?
                      retailer.users.find_by(email: params[:retailer_user_email])
                    else
                      retailer.users.first
                    end
    return redirect_to admin_retailers_path, alert: I18n.t('active_admin.retailers.user_not_found') unless retailer_user

    session[:old_retailer_id] = current_retailer_user.retailer.slug if current_retailer_user
    session[:current_retailer] = retailer
    session[:room_id] = retailer_user.id
    sign_in(:user, retailer_user)
    new_retailer_user = RetailerUser.find_by(user_id: retailer_user.id, retailer_id: retailer.id)
    new_retailer_user&.current!

    # Set new JWT token
    set_jwt_cookie(retailer_user, retailer.id)

    redirect_to root_path
  end

  member_action :go_back_as_admin do
    if session[:old_retailer_id]
      retailer = Retailer.find_by(slug: session[:old_retailer_id])
      retailer_user = retailer.retailer_users.first
      session[:current_retailer] = retailer
      session.delete(:old_retailer_id)
      sign_in(:user, retailer_user.user)
    end
    redirect_to admin_retailers_path
  end

  member_action :refund_paymentez_transaction do
    pt = PaymentezTransaction.find(params[:pt_id]).refund
    flash[:alert] = pt[:message]
    redirect_to admin_retailers_path
  end

  member_action :failed_charge_email do
    retailer = Retailer.find(params[:id])

    retailer.send_failed_charge_email
    flash[:alert] = I18n.t('active_admin.retailers.email_sent')
    redirect_to admin_retailers_path
  end

  collection_action :upload_gs_timezone_excel do
    render 'active_admin/upload_gs_timezone_excel'
  end

  collection_action :import_gs_timezone, method: :post do
    Retailers::UpdateGsTimezones.new.update(params[:retailer_timezone][:file])
    flash[:notice] = I18n.t('active_admin.retailers.tz_updated')
    redirect_to action: :index
  end

  collection_action :retailer_search, method: :get do
    retailers = Retailer.ransack(name_cont: params[:q]).result(distinct: true).limit(30)
    render json: retailers.map { |retailer| { id: retailer.id, text: retailer.name } }
  end

  member_action :connect_api, method: :post do
    @retailer = Retailer.find_by(slug: params[:id])

    begin
      Whatsapp::Integration::BspClient.new(@retailer).connect

      redirect_to admin_retailer_path(@retailer), notice: 'Conexión realizada con éxito.' # rubocop:disable Rails/I18nLocaleTexts
    rescue StandardError => e
      redirect_to admin_retailer_path(@retailer), alert: "Error al realizar la conexión: #{e.message}"
    end
  end
end
