ActiveAdmin.register AcademyVideo do
  permit_params :academy_category_id, :title, :description, :position, :external_url

  config.filters = false

  form do |f|
    f.inputs do
      f.input :academy_category, collection: AcademyCategory.all.map { |category|
        ["#{category.name} - #{category.module_name}", category.id]
      }
      f.input :title
      f.input :description
      f.input :position
      f.input :external_url, label: 'Enlace del video (YouTube, Vimeo, etc.)'
    end
    f.actions
  end

  index do
    selectable_column
    id_column
    column :title
    column :description
    column :position
    column 'Enlace' do |video|
      if video.external_url.present?
        link_to 'Ver Video', video.external_url, target: '_blank', rel: 'noopener'
      else
        'Sin enlace'
      end
    end
    actions
  end

  show do
    attributes_table do
      row :title
      row :description
      row :position
      row 'Enlace' do |video|
        if video.external_url.present?
          link_to 'Ver Video', video.external_url, target: '_blank', rel: 'noopener'
        else
          'Sin enlace'
        end
      end
    end
    div do
      link_to 'Volver a la categoría', admin_academy_category_path(resource.academy_category), class: 'button'
    end
  end
end
