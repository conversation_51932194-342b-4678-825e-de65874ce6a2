/* eslint-disable */
import AcademyMC_en from './en/AcademyMC.json';
import AutomationShopify_en from './en/AutomationShopify.json';
import CampaignsScreenMC_en from './en/CampaignsScreenMC.json';
import CampaignsStatusScreenMC_en from './en/CampaignsStatusScreenMC.json';
import Clients_en from './en/Clients.json';
import CustomerList_en from './en/CustomerList.json';
import CustomerRelatedFieldsMC_en from './en/CustomerRelatedFieldsMC.json';
import CustomersScreenMC_en from './en/CustomersScreenMC.json';
import ExportedCustomers_en from './en/ExportedCustomers.json';
import Funnels_en from './en/Funnels.json';
import MyTeam_en from './en/MyTeam.json';
import Orders_en from './en/Orders.json';
import RegistrationsMC_en from './en/RegistrationsMC.json';
import RemindersScreenMC_en from './en/RemindersScreenMC.json';
import StatusTagMC_en from './en/StatusTagMC.json';
import Tags_en from './en/Tags.json';
import campaigns_en from './en/campaigns.json';
import createCampaign_en from './en/createCampaign.json';
import translation_en from './en/translation.json';
import AcademyMC_es from './es/AcademyMC.json';
import AutomationShopify_es from './es/AutomationShopify.json';
import CampaignsScreenMC_es from './es/CampaignsScreenMC.json';
import CampaignsStatusScreenMC_es from './es/CampaignsStatusScreenMC.json';
import Clients_es from './es/Clients.json';
import CustomerList_es from './es/CustomerList.json';
import CustomerRelatedFieldsMC_es from './es/CustomerRelatedFieldsMC.json';
import CustomersScreenMC_es from './es/CustomersScreenMC.json';
import ExportedCustomers_es from './es/ExportedCustomers.json';
import Funnels_es from './es/Funnels.json';
import IntegrationsScreenMC_es from './es/IntegrationsScreenMC.json';
import MyTeam_es from './es/MyTeam.json';
import Orders_es from './es/Orders.json';
import RegistrationsMC_es from './es/RegistrationsMC.json';
import RemindersScreenMC_es from './es/RemindersScreenMC.json';
import StatusTagMC_es from './es/StatusTagMC.json';
import Tags_es from './es/Tags.json';
import campaigns_es from './es/campaigns.json';
import createCampaign_es from './es/createCampaign.json';
import integrations_es from './es/integrations.json';
import translation_es from './es/translation.json';
import Routes_es from './es/Routes.json';
import Routes_en from './es/Routes.json';
import whatsappIntegrations_en from './en/whatsappIntegrationsEN.json';
import whatsappIntegrations_es from './es/whatsappIntegrationsES.json';
import Inbox_es from './es/Inbox.json';
import Inbox_en from './en/Inbox.json';
import Widgets_es from './es/Widgets.json';
import Widgets_en from './en/Widgets.json';
import ConversationTopics_es from './es/ConversationTopicsES.json';
import ConversationTopics_en from './en/ConversationTopicsEN.json';
import ChatbotIA_es from './es/ChatbotIA.json';
import ChatbotIA_en from './en/ChatbotIA.json';

export const PATHS_LOCATIONS = {
    en: {
      Academy: AcademyMC_en,
      AutomationShopify: AutomationShopify_en,
      CampaignsScreenMC: CampaignsScreenMC_en,
      CampaignsStatusScreenMC: CampaignsStatusScreenMC_en,
      Clients: Clients_en,
      CustomerList: CustomerList_en,
      CustomerRelatedFieldsMC: CustomerRelatedFieldsMC_en,
      CustomersScreenMC: CustomersScreenMC_en,
      ExportedCustomers: ExportedCustomers_en,
      Funnels: Funnels_en,
      MyTeam: MyTeam_en,
      Orders: Orders_en,
      RegistrationsMC: RegistrationsMC_en,
      RemindersScreenMC: RemindersScreenMC_en,
      StatusTagMC: StatusTagMC_en,
      Tags: Tags_en,
      campaigns: campaigns_en,
      createCampaign: createCampaign_en,
      translation: translation_en,
      Routes: Routes_en,
      whatsappIntegrations: whatsappIntegrations_en,
      Inbox: Inbox_en,
      Widgets: Widgets_en,
      ConversationTopics: ConversationTopics_en,
      ChatbotIA: ChatbotIA_en
    },
    es: {
      Academy: AcademyMC_es,
      AutomationShopify: AutomationShopify_es,
      CampaignsScreenMC: CampaignsScreenMC_es,
      CampaignsStatusScreenMC: CampaignsStatusScreenMC_es,
      Clients: Clients_es,
      CustomerList: CustomerList_es,
      CustomerRelatedFieldsMC: CustomerRelatedFieldsMC_es,
      CustomersScreenMC: CustomersScreenMC_es,
      ExportedCustomers: ExportedCustomers_es,
      Funnels: Funnels_es,
      IntegrationsScreenMC: IntegrationsScreenMC_es,
      MyTeam: MyTeam_es,
      Orders: Orders_es,
      RegistrationsMC: RegistrationsMC_es,
      RemindersScreenMC: RemindersScreenMC_es,
      StatusTagMC: StatusTagMC_es,
      Tags: Tags_es,
      campaigns: campaigns_es,
      createCampaign: createCampaign_es,
      integrations: integrations_es,
      translation: translation_es,
      Routes: Routes_es,
      whatsappIntegrations: whatsappIntegrations_es,
      Inbox: Inbox_es,
      Widgets: Widgets_es,
      ConversationTopics: ConversationTopics_es,
      ChatbotIA: ChatbotIA_es
    }
  };
