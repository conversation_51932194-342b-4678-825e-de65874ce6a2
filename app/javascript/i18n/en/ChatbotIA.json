{"title": "Knowledge guides", "actions": {"edit": "Edit", "delete": "Delete"}, "buttonTexts": {"accept": "Accept", "cancel": "Cancel"}, "guides": {"search_placeholder": "Search guide by topic", "your_name_description": "Write the guide name", "name": "Name", "example_name": "E.g.: Shipping information", "instructions": "Instructions", "instruction": "Instruction", "instruction_placeholder": "Ex: If the customer asks for terms and conditions, respond with the following images.", "related_topics": "Related topics", "related_topics_description": "Tag topics that have relevance or complement this guide.", "setup_instructions_title": "📚 What are Knowledge Guides?", "setup_instructions_description": "Knowledge Guides are the foundation that feeds your chatbot with the information it needs to respond accurately. 🧠✨", "setup_instructions_sample": "💡 Example correct:", "setup_instructions_sample_description": "''Our store opens from 8am to 6pm.''", "setup_instructions_disclaimer": "🚫 Avoid writing instructions or prompts:", "setup_instructions_disclaimer_description": "❌ ''If the customer asks about the store hours, respond that it opens from 8am to 6pm.''", "setup_instructions_disclaimer_description_2": "This type of instructions can confuse MIA and affect its responses.", "setup_instructions_recomendation": "✅ Remember: ", "setup_instructions_recomendation_text": "here you should write only facts or specific information about your business.", "setup_instructions_recomendation_description": "Think you are building your assistant's memory, not telling it what to do step by step. 😉", "specific_instructions_title": "Sending documents", "specific_instructions_description": "Add detailed documentation or images so MIA can attach them when using this guide.", "specific_instruction_type_title": "Select the action you want to perform", "specific_instruction_image_title": "Reply with image", "specific_instruction_image_subtitle": "Reply with png or jpg files", "specific_instruction_doc_title": "Reply with document", "specific_instruction_doc_subtitle": "Reply with PDF files", "specific_instructions_step2_description": "Add key information that clearly and usefully details this action.", "specific_instructions_image_title2": "Image", "specific_instruction_image_description_2": "Attach an image that guides your customers to perform the required action.", "specific_instructions_doc_title2": "Document", "specific_instruction_doc_description_2": "Attach a document that guides your customers to perform the required action.", "specific_instruction_max_characters_error": "Maximum 300 characters", "attachment_click_image_label": "Drag your file or  ", "attachment_click_doc_label": "Drag your PDF file or ", "attachment_click_here_label": "click here", "add_instructions_button": "Add instruction +", "save_guide": "Guide saved successfully", "load_error": "An error occurred while loading the guide, please try again. If the error persists, contact the administrator.", "empty_error": "Required", "empty_question_error": "You must add at least one question and answer", "empty_file_error": "You must attach at least one file", "duplicate_files_error": "You cannot attach duplicate files", "title": "Knowledge guide", "guide": "Kind", "subtitle": "Write the knowledge topic", "input_placeholder": "Ex: Shipping information", "cancel": "Cancel", "type": "Select the format in which you will feed your chatbot", "empty_state": "Add your first guide", "empty_button": "Add guide", "right_button": "+ Create guide", "save": "Train and save", "table": {"list_title": "Knowledge guides", "guide": "Guide", "type": "Kind", "create_by": "Author", "last_update": "Last update", "actions": "Actions", "main_tooltip": "Customize MIA's knowledge to provide specialized and effective answers", "guide_tooltip": "Choose a descriptive name, e.g. 'Shipping'", "type_tooltip": "Select the content format: 'Web', 'Text', or 'Questions and Answers'."}, "text": {"title": "Business information", "description": "Provide a detailed description of your business. Who it is directed to, opening hours, shipping types, return policies, and any other relevant details.", "info": "Information"}, "questions_and_answers": {"title": "Questions and answers", "description": "Organize your business questions for quick and effective answers.", "add_question": "Add question +", "guide": "Guide", "question": "Question", "answer": "Answer", "question_placeholder": "Ex: What is the shipping cost?", "answer_placeholder": "Ex: The shipping cost will be free for orders over USD $10.00"}, "tutorial": {"title": "Train MIA", "subtitle": "Feed MIA with the information relevant to your business", "description": "Create knowledge guides, from opening hours to return policies, and customize MIA's responses for each interaction.", "ok": "Understood"}, "optimize_modal": {"title": "Improve your guide with MIA", "fetching_error": "An error occurred while loading the optimized guide, please try later.", "fetching_retry_error": "An error occurred while loading the optimized guide. We are trying again to get it", "waiting_subtitle": "MIA is improving your guide, this may take a few minutes...", "complete_subtitle": "MIA has improved your guide", "complete_message": "Review the new version of your guide. Select “Accept” to keep it or “Cancel” to keep the original version.", "warning_message": "The visual format may vary but the improvement made by MIA maintains the effectiveness of the Guide. You can adjust the style if you consider it necessary."}, "website": {"urls_scraped": "URLs obtained successfully", "trained": "Trained", "training": "Training", "failed": "Error", "url_input": "Web", "url_input_description": "Write the URL of your website", "url": "URL", "select_urls": "Please select the links you want to train MIA with", "not_found": "Url not found, check and try again", "select_all": "Select all", "selected": "selected"}, "type_options": {"questions_and_answers": {"title": "Questions and answers", "description": "Organize your business questions for quick and effective answers."}, "text": {"title": "Text - Information - Memories - Input", "description": "Organize your business questions for quick and effective answers."}, "website": {"title": "Web", "description": "Use the information from your website for quick answers."}}, "emptyScreen": {"title": "Train MIA with knowledge of your business", "description": "Train your AI agent to know all the details of your business like another member of your team.", "buttonCreateNewLabel": "Create guide +"}, "sidebar_create": {"type": "Select the format in which you will feed your chatbot", "name_placeholder": "Nombre", "name_label": "Información de la etiqueta", "color_label": "Color", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "name_field_required": "El nombre de la etiqueta es requerido", "update": "Guardar", "title": {"create": "Create knowledge guide"}}, "confirmDelete": "Are you sure you want to delete this guide?", "successDelete": "Guide successfully removed.", "errorDelete": "An error has occurred, please try again. If the error persists, contact the administrator."}, "config": {"coming_soon": "Coming Soon!", "tutorial": {"title": "Configure your Agent"}, "header_title": "Configuration", "general_instructions": "General Instructions", "clients_instructions": "Automatic follow-up with your client", "products_instructions": "Product and purchase instructions", "internal_message": "This is an internal message, your client can't see it.", "internal_message_turn": "It’s your turn to follow up with the client.", "save": "Save", "save_success": "Configuration saved successfully", "save_error": "An error has occurred, please try again. If the error persists, contact the administrator.", "error_loading": "An error occurred while loading the prompt, please try again.", "human_help_message": "MIA needs your help", "platforms": {"title": "Activate MIA", "description": "Configure the activation of your chatbot on different platforms.", "modal_title": "Activation", "section_1_title": "Activate AI chatbot", "section_1_description": "Your chatbot will only activate for new chats or chats with more than 24 hours of inactivity.", "section_2_title": "Connect MIA to your messaging tools", "section_2_description": "Select the available platforms.", "error_no_platform": "You must select at least one platform"}, "instructions": {"title": "Instructions (Prompt)", "description": "Adjust the guidelines that will guide your chatbot's responses.", "modal_title": "Instructions", "section_1_title": "Define your agent's instructions", "section_1_description": "Refine the instructions that will guide your agent's responses by detailing how the chatbot should reply."}, "goal": {"title": "Role and Goal", "description": "Define the chatbot’s main purpose within the conversation: what problem it solves and what actions it must perform (inform, qualify, sell, etc).", "modal_title": "Role and Goal", "section_1_title": "Define the role and goal", "section_1_description": "Define the chatbot’s main purpose within the conversation: what problem it solves and what actions it must perform (inform, qualify, sell, etc)."}, "general_parameters": {"title": "Tone, style, and response format", "description": "Set how MIA should express itself: friendly or professional tone, use of emojis, message length, and format (lists, buttons, links, etc.).", "modal_title": "Tone, style, and response format", "section_1_title": "Define tone, style, and response format", "section_1_description": "Set how MIA should express itself: friendly or professional tone, use of emojis, message length, and format (lists, buttons, links, etc.)."}, "step_by_step": {"title": "Conversational Flow (Step-by-step)", "description": "Describe the logical structure of the conversation: steps, conditions, and branches that guide the user toward a specific goal.", "modal_title": "Conversational Flow (Step-by-step)", "section_1_title": "Define the conversational flow", "section_1_description": "Describe the logical structure of the conversation: steps, conditions, and branches that guide the user toward a specific goal."}, "interaction_examples": {"title": "Interaction Examples", "description": "Include real or simulated examples of how MIA should interact with users in different scenarios.", "modal_title": "Interaction Examples", "section_1_title": "Define chatbot interactions", "section_1_description": "Include real or simulated examples of how MIA should interact with users in different scenarios."}, "restrictions": {"title": "Restrictions", "description": "Define the limits MIA must respect: topics it can't address, data it shouldn't request, and prohibited behaviors.", "modal_title": "Restrictions", "section_1_title": "Define chatbot restrictions", "section_1_description": "Define the limits MIA must respect: topics it can't address, data it shouldn't request, and prohibited behaviors."}, "handoff": {"title": "Transfer to Human", "description": "Enter the message MIA will send when reassigning a customer to an agent.", "modal_title": "Handoff to Human", "section_1_title": "Configure handoff to human", "section_1_description": "Decide in which situations the chatbot will hand off the conversation to an agent."}, "follow_up": {"title": "Follow up with your clients", "description": "Have the chatbot resume the conversation if the customer doesn't respond within a certain time."}, "client_data": {"title": "Data to be collected", "description": "Define what information to request in order to store it in your client's data."}, "negotiations": {"title": "Negotiation Management", "description": "Let MIA create and manage negotiations automatically in your sales funnel."}, "products": {"title": "Products", "description": "Define the instructions for sending and responding about your products.", "modal_title": "Products", "section_1_title": "Sync your products with MIA", "section_1_description": "Your store’s products will be automatically synced to provide fast and accurate answers.", "sync_pending": "This process may take a couple of hours. You can continue using Mercately.", "sync_complete": "Products synced", "sync_failed": "An error has occurred, please try again. If the error persists, contact the administrator.", "sync_button": "Sync products", "sync_again_button": "Sync products again", "sync_confirm": "Are you sure you want to sync the products?", "not_synced": "Not synced", "disable_sync_title": "Are you sure you want to disable product sync?", "disable_sync_description": "The chatbot will stop responding about the products.", "close": "Close", "confirm": "Confirm", "section_2_title": "Define how to display products", "section_2_description": "Configure how the chatbot will respond when users ask about products, prices, and availability.", "prompt": {"error_loading": "An error occurred while loading the product prompt, please try again."}}, "buy_intention": {"title": "Purchase Intention Parameters", "description": "Set criteria to identify when a client shows purchase interest and when a human agent should intervene.", "modal_title": "Purchase Intention", "section_1_title": "Define purchase intention", "section_1_description": "Tell MIA how to recognize purchase intention", "placeholder": "Example: Assign the conversation to an agent when the client has provided their address and the products they wish to buy.", "message": "MIA detected a purchase intention"}, "feature_flag": {"message": "We’ve made improvements to MIA and you need to update your prompts for it to work better", "button": "Enable new configuration"}, "prompts_modal": {"title": "We've updated MIA's configuration", "subtitle_1": "What's changing?", "description_1": "Your assistant's configuration will no longer be a single prompt. It will be organized into blocks for greater control and customization.", "subtitle_2": "What should you do?", "description_2": "You’ll be able to copy parts of your current prompt and easily assign them to each block.", "info_msg": " We recommend making a copy of your current prompt before updating. Once the change is applied, the prompts you have configured will be lost if not backed up.", "warning_msg": "You must make this change by August 1st", "update": "Update", "cancel": "Don't update", "confirm_update": "The prompts you have configured will be deleted if you don't back them up. Are you sure you want to proceed with the update?"}}, "general": {"main_title": "Mercately Intelligent Assistant", "save": "Save", "save_error": "An error occurred, please try again. If the error persists, contact the administrator.", "save_success": "Configuration saved successfully", "mia_label_title": "MIA responses", "activate_chatbot": "MIA responses activated", "deactivate_chatbot": "MIA responses deactivated", "header_title": "General", "save_button": "Save", "cancel_button": "Cancel", "back_button": "Back", "refresh_success": "Chat session updated", "refresh_error": "An error occurred while refreshing, please try again.", "refresh": "Refresh chat", "refresh_tooltip": "MIA will be updated with the new knowledge", "refresh_tooltip2": "it will start responding with the new information.", "instructions": "Instructions", "source_tooltip": "See response sources", "file_too_large": "The file is too large. The maximum size allowed is 50mb.", "train": {"title": "Train your chatbot with knowledge", "description": "Provide information about your business."}, "config": {"title": "Set up your chatbot", "description": "Define your personality and other functions."}, "stats": {"title": "Your chatbot stats", "messages_by_mia_title": "Messages answered by MIA", "messages_by_mia_tooltip_1": "Number of messages that were sent", "messages_by_mia_tooltip_2": "by the Artificial Intelligence in the month.", "contact_interaction_title": "Contact interaction with MIA", "contact_interaction_tooltip_1": "Number of contacts who had contact with", "contact_interaction_tooltip_2": "artificial intelligence in the month.", "buy_intention_title": "Purchase intention", "buy_intention_tooltip_1": "Number of purchase intentions detected", "comparison_tooltip": "In comparison with 1 ", "load_error": "An error occurred while loading the statistics, please try again. If the error persists, please contact the administrator.", "language": "en"}, "title": "Test your chatbot", "subtitle": "Interact with the chatbot you set up and evaluate its performance with specific questions.", "buttons_header": "Not getting the desired answers?", "chat": {"empty": "Test how your chatbot works after adding knowledge.", "drag_file": "Drop the file here...", "caption_placeholder": "Add a caption", "caption_placeholder_1": "Type your message or drag a file here"}, "human_help": {"label": "Human help"}, "buy_intention": {"label": "Buy intention"}}, "sources_modal": {"title": "Source of the response", "qa": "Question and answers", "text": "Text", "web": "Web", "specific_instruction": "Text", "product": {"title": "Product", "description": "Description", "images": "Images", "variant": {"title": "Variants", "name": "Name", "price": "Price", "stock": "Stock"}}}, "attachment_popup": {"title": "What do you want to attach?", "single_file": "You can only attach one file per question", "file": "Document", "image": "Image"}, "emptyScreen": {"text1": "We are currently in a Closed Beta period for our AI Chatbot (MIA). If you want to be part of this exclusive experience, click the button and complete the registration form.", "text2": "Please note that access to the Beta is limited. While not everyone will be able to join immediately, we will save your information and each week we select new users to grant access.", "title": "What if your AI chatbot were as smart as your best salespeople?", "text3": "Our AI-powered chatbot offers 24/7 support, smooth and personalized conversations, intelligent automation, and it's easy to train and set up.", "textBtn": "I want AI chatbots"}, "improve": {"text": "Improve guide with AI"}}