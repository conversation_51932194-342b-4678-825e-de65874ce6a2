{"monthly": "Monthly", "quarterly": "Quarterly", "yearly": "Yearly", "funnelsTitle": "Funnels", "days": "Days", "menu": {"title": "<PERSON><PERSON>", "funnels": {"title": "Funnels", "automations": "Automations", "record": "History", "logs": "Logs", "deal": {"lead_status": {"hot": "Hot", "warm": "Warm", "cold": "Cold"}}}, "automations": {"title": "Automations", "shopify": {"title": "Shopify", "pill_text": "Boost your store", "configuration": "Configuration", "whatsapp_widget": "<PERSON><PERSON><PERSON><PERSON> Widget"}, "chatbot_ai": {"title": "MIA Chatbots with AI", "guides": {"title": "Train MIA"}, "config": {"title": "Setting"}, "general": {"title": "Playground"}}}, "schedule_message": {"title": "Scheduled messages"}, "stats": {"module": "Statistics", "messaging": "Messaging", "sales": "Sales", "labels": "Tags", "funnels": "Funnels", "whatsapp_templates": "Whatsapp Templates", "conversation_topics": "Conversation Topics"}, "my_business": {"title": "mY Business", "catalog": {"title": "Catalog", "products": "Products", "categories": "Categories"}, "orders": {"title": "Orders", "my_orders": "My Orders", "custom_fields": "Custom Fields", "sales_channels": "Sales Channels", "download_history": "Download History"}, "payments": {"title": "Payments", "digital_payments": "Digital Payments"}, "shipping": {"title": "Shipping", "shipping_methods": "Shipping Methods", "shipping_cost": "Shipping Cost"}, "store_settings": {"title": "Store Settings", "overview": "Overview", "addresses": "Addresses", "contacts": "Contacts", "hours": "Hours", "domain": "Domain", "currency": "<PERSON><PERSON><PERSON><PERSON>"}}}, "sidebar_buttons": {"all": "All", "unread": "Unread", "not_assigned": "Unassigned", "your": "Mine", "no_answer": "Without answers", "alert": "<PERSON><PERSON><PERSON>", "campaign": "Campaigns", "human_help": "Human help", "new_chat": "New chat", "conversations": "Conversations", "by_activity": "By activity", "channels": "Channels", "see_intro": "See intro"}, "funnels": {"add": {"title": "Add", "op1": "Deal", "op2": "Stage", "new_funnel": "Funnel"}, "history": {"title": "Downloads", "no_data": "You don't have exports currently", "name": "Name", "creation_date": "Creation date", "expiration_date": "Expiration date", "link": "Link", "download": "Download", "next_to_expire_text": "Expires on", "expired_text": "Expired on", "file_unavailable": "File unavailable"}}, "deal": {"modal": {"create": "Add new deal", "update": "Edit deal"}, "button": {"create": "Create deal", "update": "Save deal"}}, "step": {"modal": {"create": "Add new stage", "update": "Edit stage", "name": "Stage name", "probability": "Closing probability", "stagnation_legend": "Stagnation time in days", "legend": "Funnel stages represent the steps in your sales process", "name_error": "This stage already exists"}, "button": {"create": "Create stage", "update": "Save stage"}, "menu": {"op1": "Add deal", "op2": "Edit stage", "op3": "Delete stage"}}, "customer": {"view_list": {"title": "Customers", "introduction": "View Introduction", "exported_customers": "Exported Customers", "custom_fields": "Custom Fields", "add_customer": "+ Add Customer", "search_placeholder": "Search by name, phone, or email", "search_button": "Search", "filters": "Filters", "filters_placeholder": "Search by name, phone, or email", "import": "Import", "export": "Export", "csv": "CSV", "excel": "EXCEL", "empty_screen": {"title": "Don't you have any registered customers yet?", "description": "Here you can find all the customers who write to you from the networks you connect. Start by connecting WhatsApp or any social network and start receiving, responding and organizing your contacts easily.", "connect_whatsapp": "Connect WhatsApp", "add_customer": "Add Customer"}, "columns": {"full_name": "Full Name", "phone": "Phone", "assigned_to": "Assigned To", "email": "Email", "channel": "Channel", "actions": "Actions"}, "row": {"view": "View", "edit": "Edit", "start_conversation": "Start Conversation", "send_now": "Send Now", "schedule_message": "Schedule Message", "not_assigned": "Not Assigned"}, "modal_filters": {"apply": "Apply Filters", "title": "Filters", "agent": "Agent", "lists": "Lists", "tags": "Tags", "funnel": "Funnel", "funnel_stage": "Funnel Stage", "creation_date": "Creation Date", "all": "All", "item_not_found": "Option Not Found", "select_date": "Select Date", "cancel_date": "Cancel", "apply_date": "Apply", "loading": "Loading", "search_by_tags": "Search by tags", "search_by_stage": "Search by stage"}}, "exported": {"title": "Exported Customers", "view_introduction": "View Introduction", "columns": {"creation_date": "Creation Date", "expiration_date": "Expiration Date", "link": "Link"}, "row": {"expired_on": "Expired On", "expires_on": "Expires On", "download": "Download", "file_not_available": "File Not Available"}}, "modal": {"create": "Create new customer", "first_name": "First name", "last_name": "Last name", "email": "Email", "country": "Country", "phone": "Phone"}, "title": {"update": "Update customer", "create": "Add customer"}, "errors": {"required": "Required", "email": "You must fill out email or telephone", "invalid_email": "invalid email"}, "fields": {"header": "Basic information", "first_name": "First name", "last_name": "Last name", "email": "Mail", "country": "Country", "phone": "Phone", "permission": "I have explicit permission to send messages to this number", "document": "Document type", "id": "ID", "custom_fields": "Custom fields", "show_custom_fields": "Show custom fields"}, "address": {"error": "This location is not available", "address": "Address", "button_map": "Search on the map", "description": "Description (house, building, etc.)", "state": "State", "city": "City", "zip_code": "Zip code", "country": "Country", "map": {"title": "Select address", "cancel": "Cancel", "save": "Keep"}, "addresses": "Directions", "edit": "Edit", "delete": "Eliminate", "add": "Add address", "confirm": "Are you sure to delete this address?", "not_exist": "No existe dirección"}, "tags": {"title": "Add tags", "create": "Create", "select_agent": "Select the agent", "sync": "Synchronize with"}}, "paginate": {"next": "Next", "previous": "Previous"}, "button": {"active": "Activate", "cancel": "Cancel", "save": "Save"}, "subcategories": {"modal": {"add": "Add subcategory", "edit": "Update subcategory", "create": "Create subcategory", "update": "Save subcategory"}}, "categories": {"header": "Categories", "modal": {"add": "Add category", "edit": "Update category", "create": "Create category", "update": "Save category"}, "add": "Add", "update": "Update", "dropdown": {"category": "Category", "subcategory": "Subcategory"}, "table": {"details": "Categories / Subcategories", "actions": "Actions", "btnAdd": "Add subcategory", "btnEdit": "Edit", "btnDel": "Delete"}}, "chatbot_ai": {"message_status": "Sent by: MIA", "guides": {"your_name_description": "Write the guide name", "name": "Name", "example_name": "E.g.: Shipping information", "instructions": "Instructions", "instruction": "Instruction", "instruction_placeholder": "Ex: If the customer asks for terms and conditions, respond with the following images.", "related_topics": "Related topics", "related_topics_description": "Tag topics that have relevance or complement this guide.", "setup_instructions_title": "📚 What are Knowledge Guides?", "setup_instructions_description": "Knowledge Guides are the foundation that feeds your chatbot with the information it needs to respond accurately. 🧠✨", "setup_instructions_sample": "💡 Example correct:", "setup_instructions_sample_description": "''Our store opens from 8am to 6pm.''", "setup_instructions_disclaimer": "🚫 Avoid writing instructions or prompts:", "setup_instructions_disclaimer_description": "❌ ''If the customer asks about the store hours, respond that it opens from 8am to 6pm.''", "setup_instructions_disclaimer_description_2": "This type of instructions can confuse MIA and affect its responses.", "setup_instructions_recomendation": "✅ Remember: ", "setup_instructions_recomendation_text": "here you should write only facts or specific information about your business.", "setup_instructions_recomendation_description": "Think you are building your assistant's memory, not telling it what to do step by step. 😉", "specific_instructions_title": "Sending documents", "specific_instructions_description": "Add detailed documentation or images so MIA can attach them when using this guide.", "specific_instruction_type_title": "Select the action you want to perform", "specific_instruction_image_title": "Reply with image", "specific_instruction_image_subtitle": "Reply with png or jpg files", "specific_instruction_doc_title": "Reply with document", "specific_instruction_doc_subtitle": "Reply with PDF files", "specific_instructions_step2_description": "Add key information that clearly and usefully details this action.", "specific_instructions_image_title2": "Image", "specific_instruction_image_description_2": "Attach an image that guides your customers to perform the required action.", "specific_instructions_doc_title2": "Document", "specific_instruction_doc_description_2": "Attach a document that guides your customers to perform the required action.", "specific_instruction_max_characters_error": "Maximum 300 characters", "attachment_click_image_label": "Drag your file or  ", "attachment_click_doc_label": "Drag your PDF file or ", "attachment_click_here_label": "click here", "add_instructions_button": "+ Add instruction", "save_guide": "Guide saved successfully", "load_error": "An error occurred while loading the guide, please try again. If the error persists, contact the administrator.", "empty_error": "Required", "empty_question_error": "You must add at least one question and answer", "empty_file_error": "You must attach at least one file", "duplicate_files_error": "You cannot attach duplicate files", "title": "Knowledge guide", "guide": "Kind", "subtitle": "Write the knowledge topic", "input_placeholder": "Ex: Shipping information", "cancel": "Cancel", "type": "Select the format in which you will feed your chatbot", "empty_state": "Add your first guide", "empty_button": "Add guide", "right_button": "+ Create guide", "save": "Train and save", "table": {"list_title": "Knowledge guides", "guide": "Guide", "type": "Kind", "create_by": "Author", "last_update": "Last update", "actions": "Actions", "main_tooltip": "Customize MIA's knowledge to provide specialized and effective answers", "guide_tooltip": "Choose a descriptive name, e.g. 'Shipping'", "type_tooltip": "Select the content format: 'Web', 'Text', or 'Questions and Answers'."}, "text": {"title": "Business information", "description": "Provide a detailed description of your business. Who it is directed to, opening hours, shipping types, return policies, and any other relevant details.", "info": "Information"}, "questions_and_answers": {"title": "Questions and answers", "description": "Organize your business questions for quick and effective answers.", "add_question": "Add question", "question": "Question", "answer": "Answer", "question_placeholder": "Ex: What is the shipping cost?", "answer_placeholder": "Ex: The shipping cost will be free for orders over USD $10.00"}, "tutorial": {"title": "Train MIA", "subtitle": "Feed MIA with the information relevant to your business", "description": "Create knowledge guides, from opening hours to return policies, and customize MIA's responses for each interaction.", "ok": "Understood"}, "optimize_modal": {"title": "Improve your guide with MIA", "fetching_error": "An error occurred while loading the optimized guide, please try later.", "fetching_retry_error": "An error occurred while loading the optimized guide. We are trying again to get it", "waiting_subtitle": "MIA is improving your guide, this may take a few minutes...", "complete_subtitle": "MIA has improved your guide", "complete_message": "Review the new version of your guide. Select “Accept” to keep it or “Cancel” to keep the original version.", "warning_message": "The visual format may vary but the improvement made by MIA maintains the effectiveness of the Guide. You can adjust the style if you consider it necessary."}, "website": {"urls_scraped": "URLs obtained successfully", "trained": "Trained", "training": "Training", "failed": "Error", "url_input": "Web", "url_input_description": "Write the URL of your website", "url": "URL", "select_urls": "Please select the links you want to train MIA with", "not_found": "Url not found, check and try again", "select_all": "Select all", "selected": "selected"}, "type_options": {"questions_and_answers": {"title": "Questions and answers", "description": "Organize your business questions for quick and effective answers."}, "text": {"title": "Text - Information - Memories - Input", "description": "Organize your business questions for quick and effective answers."}, "website": {"title": "Web", "description": "Use the information from your website for quick answers."}}}, "config": {"coming_soon": "Coming Soon!", "tutorial": {"title": "Configure your Agent"}, "header_title": "Configuration", "general_instructions": "General Instructions", "clients_instructions": "Automatic follow-up with your client", "products_instructions": "Product and purchase instructions", "internal_message": "This is an internal message, your client can't see it.", "internal_message_turn": "It’s your turn to follow up with the client.", "save": "Save", "save_success": "Configuration saved successfully", "save_error": "An error has occurred, please try again. If the error persists, contact the administrator.", "error_loading": "An error occurred while loading the prompt, please try again.", "human_help_message": "MIA needs your help", "platforms": {"title": "Activate MIA", "description": "Configure the activation of your chatbot on different platforms.", "modal_title": "Activation", "section_1_title": "Activate AI chatbot", "section_1_description": "Your chatbot will only activate for new chats or chats with more than 24 hours of inactivity.", "section_2_title": "Connect MIA to your messaging tools", "section_2_description": "Select the available platforms.", "error_no_platform": "You must select at least one platform"}, "instructions": {"title": "Instructions (Prompt)", "description": "Adjust the guidelines that will guide your chatbot's responses.", "modal_title": "Instructions", "section_1_title": "Define your agent's instructions", "section_1_description": "Refine the instructions that will guide your agent's responses by detailing how the chatbot should reply."}, "goal": {"title": "Role and Goal", "description": "Define the chatbot’s main purpose within the conversation: what problem it solves and what actions it must perform (inform, qualify, sell, etc).", "modal_title": "Role and Goal", "section_1_title": "Define the role and goal", "section_1_description": "Define the chatbot’s main purpose within the conversation: what problem it solves and what actions it must perform (inform, qualify, sell, etc)."}, "general_parameters": {"title": "Tone, style, and response format", "description": "Set how MIA should express itself: friendly or professional tone, use of emojis, message length, and format (lists, buttons, links, etc.).", "modal_title": "Tone, style, and response format", "section_1_title": "Define tone, style, and response format", "section_1_description": "Set how MIA should express itself: friendly or professional tone, use of emojis, message length, and format (lists, buttons, links, etc.)."}, "step_by_step": {"title": "Conversational Flow (Step-by-step)", "description": "Describe the logical structure of the conversation: steps, conditions, and branches that guide the user toward a specific goal.", "modal_title": "Conversational Flow (Step-by-step)", "section_1_title": "Define the conversational flow", "section_1_description": "Describe the logical structure of the conversation: steps, conditions, and branches that guide the user toward a specific goal."}, "interaction_examples": {"title": "Interaction Examples", "description": "Include real or simulated examples of how MIA should interact with users in different scenarios.", "modal_title": "Interaction Examples", "section_1_title": "Define chatbot interactions", "section_1_description": "Include real or simulated examples of how MIA should interact with users in different scenarios."}, "restrictions": {"title": "Restrictions", "description": "Define the limits MIA must respect: topics it can't address, data it shouldn't request, and prohibited behaviors.", "modal_title": "Restrictions", "section_1_title": "Define chatbot restrictions", "section_1_description": "Information about closing probability. Example: Closing probability refers to the likelihood that a negotiation will be completed."}, "handoff": {"title": "Configure <PERSON><PERSON> to Human", "description": "Set when and how MIA should transfer the conversation to a human agent, including conditions and transition messages.", "modal_title": "Handoff to Human", "section_1_title": "Configure handoff to human", "section_1_description": "Decide in which situations the chatbot will hand off the conversation to an agent."}, "follow_up": {"title": "Set up Client Follow-Up", "description": "Define what information to request in order to store it in your client's data."}, "client_data": {"title": "Set up Data Collection", "description": "Define what information to request in order to store it in your client's data."}, "negotiations": {"title": "Negotiation Management", "description": "Let MIA create and manage negotiations automatically in your sales funnel."}, "products": {"title": "Products", "description": "Define the instructions for sending and responding about your products.", "modal_title": "Products", "section_1_title": "Sync your products with MIA", "section_1_description": "Your store’s products will be automatically synced to provide fast and accurate answers.", "sync_pending": "This process may take a couple of hours. You can continue using Mercately.", "sync_complete": "Products synced", "sync_failed": "An error has occurred, please try again. If the error persists, contact the administrator.", "sync_button": "Sync products", "sync_again_button": "Sync products again", "sync_confirm": "Are you sure you want to sync the products?", "not_synced": "Not synced", "disable_sync_title": "Are you sure you want to disable product sync?", "disable_sync_description": "The chatbot will stop responding about the products.", "close": "Close", "confirm": "Confirm", "section_2_title": "Define how to display products", "section_2_description": "Configure how the chatbot will respond when users ask about products, prices, and availability.", "prompt": {"error_loading": "An error occurred while loading the product prompt, please try again."}}, "buy_intention": {"title": "Purchase Intention Parameters", "description": "Set criteria to identify when a client shows purchase interest and when a human agent should intervene.", "modal_title": "Purchase Intention", "section_1_title": "Define purchase intention", "section_1_description": "Tell MIA how to recognize purchase intention", "placeholder": "Example: Assign the conversation to an agent when the client has provided their address and the products they wish to buy.", "message": "MIA detected a purchase intention"}, "feature_flag": {"message": "We’ve made improvements to MIA and you need to update your prompts for it to work better", "button": "Enable new configuration"}, "prompts_modal": {"title": "We've updated MIA's configuration", "subtitle_1": "What's changing?", "description_1": "Your assistant's configuration will no longer be a single prompt. It will be organized into blocks for greater control and customization.", "subtitle_2": "What should you do?", "description_2": "You’ll be able to copy parts of your current prompt and easily assign them to each block.", "info_msg": " We recommend making a copy of your current prompt before updating. Once the change is applied, the prompts you have configured will be lost if not backed up.", "warning_msg": "You must make this change by August 1st", "update": "Update", "cancel": "Don't update", "confirm_update": "The prompts you have configured will be deleted if you don't back them up. Are you sure you want to proceed with the update?"}}, "general": {"main_title": "Mercately Intelligent Assistant", "save": "Save", "save_error": "An error occurred, please try again. If the error persists, contact the administrator.", "save_success": "Configuration saved successfully", "mia_label_title": "MIA responses", "activate_chatbot": "MIA responses activated", "deactivate_chatbot": "MIA responses deactivated", "header_title": "General", "save_button": "Save", "cancel_button": "Cancel", "back_button": "Back", "refresh_success": "Chat session updated", "refresh_error": "An error occurred while refreshing, please try again.", "refresh": "Refresh chat", "refresh_tooltip": "MIA will be updated with the new knowledge", "refresh_tooltip2": "it will start responding with the new information.", "instructions": "Instructions", "source_tooltip": "See response sources", "file_too_large": "The file is too large. The maximum size allowed is 50mb.", "toggle_popover": {"deactivate_mia": "Deactivate MIA", "understood": "Understood", "body": "If you are going to respond directly, <strong>we recommend deactivating it</strong> to avoid errors or duplicate messages.", "title": "MIA is responding in this chat"}, "train": {"title": "Train your chatbot with knowledge", "description": "Provide information about your business."}, "config": {"title": "Set up your chatbot", "description": "Define your personality and other functions."}, "stats": {"title": "Your chatbot stats", "messages_by_mia_title": "Messages answered by MIA", "messages_by_mia_tooltip_1": "Number of messages that were sent", "messages_by_mia_tooltip_2": "by the Artificial Intelligence in the month.", "contact_interaction_title": "Contact interaction with MIA", "contact_interaction_tooltip_1": "Number of contacts who had contact with", "contact_interaction_tooltip_2": "artificial intelligence in the month.", "buy_intention_title": "Purchase intention", "buy_intention_tooltip_1": "Number of purchase intentions detected", "comparison_tooltip": "In comparison with 1 ", "load_error": "An error occurred while loading the statistics, please try again. If the error persists, please contact the administrator.", "language": "en"}, "title": "Test your chatbot", "subtitle": "Interact with the chatbot you set up and evaluate its performance with specific questions.", "buttons_header": "Not getting the desired answers?", "chat": {"empty": "Test how your chatbot works after adding knowledge.", "drag_file": "Drop the file here...", "caption_placeholder": "Add a caption", "caption_placeholder_1": "Type your message or drag a file here"}, "human_help": {"label": "Human help"}, "buy_intention": {"label": "Buy intention"}}, "sources_modal": {"title": "Source of the response", "qa": "Question and answers", "text": "Text", "web": "Web", "specific_instruction": "Text", "product": {"title": "Product", "description": "Description", "images": "Images", "variant": {"title": "Variants", "name": "Name", "price": "Price", "stock": "Stock"}}}}, "attachment_popup": {"title": "What do you want to attach?", "single_file": "You can only attach one file per question", "file": "Document", "image": "Image"}, "nav": {"buttons": {"back": "Back"}}, "import_customers": {"select_file_error": "Select a file", "required": "Required", "import_customers": "Import Customers", "dashboard": "Dashboard", "customer_list": "Customer List", "customers": "Customers", "import": "Import", "load_file": "Load File", "assign_columns": "Assign <PERSON>", "final_details": "Final Details", "drag_drop_or_search": "Drag and drop or search", "drag_drop_or": "Drag and drop or ", "only_excel_files": "*Only Excel files*", "wrong_file_header_warning": "It appears the file you uploaded does not have the correct headers. We recommend downloading the example file and maintaining the headers we have provided.", "cancel": "Cancel", "download_excel_file": "Download Excel File", "download_example_excel_file": "You can download the example Excel file.", "download": "Download", "update_existing_data_with_this_file": "You can update existing data with this file", "if_customer_exists_by_phone_or_email": "If the customer already exists in your account, they will be located by the Phone Number or Email, and updated with the fields you send in the Excel file.", "create_list_from_import": "Create a list from this import", "customer_list_name": "Customer List Name", "next": "Next", "back": "Back", "process": "Process", "processing": "Processing", "close": "Close", "update": "Update", "excel": "Excel", "header_labels": {"first_name": "First Name", "last_name": "Last Name", "email": "Email", "phone": "Phone", "tags": "Tags", "agent": "Agent", "id_type": "Identification Type", "id_number": "Identification Number", "address": "Address", "description": "Description", "city": "City", "state": "State", "zip_code": "Zip Code", "country_id": "Country Code", "notes": "Notes"}, "map_customers_column": {"verify_file_columns": "Ensure your file's columns are correctly matched with customer properties", "each_column_title_assigned": "Each column title must be assigned to a Mercately customer property.", "some_assigned_by_name": "Some of these are assigned by name", "file_column_header": "File Col<PERSON>n <PERSON>", "preview_information": "Preview Information", "mapped": "Mapped", "mercately_property": "Mercately Property"}, "final_step_import_customers": {"final_details": "Final Details", "import_in_progress": "Your import is in progress. Depending on the volume of contacts, it may take several minutes.", "import_not_instant": "Please be aware that the import is not instant. You will be able to check your imported customers", "check_customers_soon": "in the customer list shortly. You will receive an email with details about the import once the process is completed."}}, "roles": {"new_button": "Add new", "edit_tool_tip": "Edit", "permissions_modal": {"title": "Configure role and permissions", "role_name": "Role Name", "select_permissions": "Select permissions for this role", "errors": {"role_name": {"required": "Required"}, "role_permissions": {"required": "Must select at least one permission"}}}}, "growth_tools": {"title": "Growth Tools", "automation": {"title": "Automations"}}, "qr_modal": {"message": {"title": "Create Message", "message": "Message", "add_file": "Add Image or PDF file", "send": "Send Message"}, "not_valid_file": "Only PNG, JPG/JPEG images, and PDF files are allowed", "required": "required"}, "images_selector": {"title": "Images", "placeholder": "Select, paste, or drag up to 5 images", "button": {"cancel": "Cancel", "send": "Send"}, "buton": {"cancel": "<PERSON><PERSON><PERSON>", "send": "Enviar"}}, "staff": {"modal": {"select_role": "Select role for this user", "select_role_placeholder": "Select"}}, "tags": {"create": "+ Create tag", "title": "Tags", "search_placeholder": "Search by name", "table": {"name": "Tag name", "colour": "Colour", "customers_number": "Customers with the tag"}, "alerts": {"delete_tag": "Are you sure to delete the tag?", "error": "error"}, "sidebar_edit_create": {"name_placeholder": "Name", "name_label": "Tag information", "color_label": "Colour", "cancel": "Cancel", "save": "Save", "name_field_required": "Requiered"}}, "ws_templates": {"header_text_limit": "The header text must be a maximum of 60 characters", "preview": {"example_message": "Example of your message", "preview_message": "This is just a preview, the message view may change when sent via WhatsApp", "expiry_minutes": "Expires in {{minutes}} minutes"}, "help_text": {"enter_phone_number": "Enter phone number", "header_text_limit": "Header text must be maximum 60 characters", "international_format": "in international format", "static_url": "A static url must not contain variables", "variables_must_be_enclosed_in_double_braces": "enclosed in double braces", "variables_must_be_numbers": "Variables must be numbers (starting from 1)"}, "inputs": {"button_text": "Button text", "button_text_limit": "Button text must be maximum 20 characters", "quick_reply_text": "Text", "quick_reply_text_limit": "Quick reply text must be maximum 20 characters", "required": "Required", "max_length": "Maximum {n} characters", "numbers_only": "Numbers only", "url_type": "URL Type", "value": "Value", "dynamic_url_example": "Dynamic URL example", "select_placeholder": "Select", "add_cta": "Add CTA", "add_quick_reply": "Add quick reply", "save": "Save", "add": "Add", "expiry_minutes_range": "Possible values from 1 to 90", "text_cannot_be_empty": "Cannot be empty", "select_file": "Select a file", "static_url_example": "https://mercately.com/prices", "dynamic_url_example_2": "https://mercately.com/{{1}}", "phone_number_example": "Ex: 593234567890", "security_text_example": "For your security, do not share it.", "security_text_example_2": "For your security, do not share this code.", "example_value": "Example value", "expiry_minutes": "Minutes of validity for the sent code (1-90)", "example_variable": "Example of the variable"}, "create_template": {"title": "Create template", "name_label": "Template name", "name_label_help": "Only lowercase letters, alphanumeric characters and underscores(_) are allowed", "choose_category": "Choose category", "marketing_title": "Marketing", "marketing_description": "Send promotions to increase brand awareness and engagement.", "utility_title": "Utility", "utility_description": "Send updates, alerts and more. Share important information.", "authentication_title": "Authentication", "authentication_description": "Send codes that allow your customers to access their accounts.", "basic_information": "Basic information", "language": "Language", "team": "Team", "choose_header": "Choose message header", "text": "Text", "media": "Media", "none": "None", "header_text": "Message header", "note": "Note:", "note_description": "Below are the file formats and their respective sizes that are accepted:", "image_types": "Image: JPEG and PNG (maximum size 5 MB)", "document_types": "Document: (Maximum size 40 MB)", "video_types": "Video: MP4 (maximum size 16 MB)", "content_message": "Message content", "text_message": "Message text", "add_variable": "Add variable", "variables_description": "Variables will allow you to enter editable text to personalize your messages", "button_text_error": "The button texts cannot be the same", "lto": {"title": "Limited time offer", "subtitle": "Show expiration dates and run countdown timers for offer codes.", "offer_title": "Offer title", "offer_type": "Offer type", "offer_button_text": "Button text", "offer_button_value": "Button value", "add_expiration_offer": "Add expiration period to the offer", "offer": "Offer"}, "custom_message": {"title": "Custom message", "subtitle": "Send promotions, ads and more."}}, "authentication_ads": {"title": "Optional information for authentication templates", "add_security_text": "Add code security notice", "add_security_text_2": "Add a security text to the message. Check in the template text above.", "add_expiration_text": "Add code expiration notice", "add_expiration_text_2": "Add a code expiration text. Check in the template text above.", "expiration_minutes_label": "Minutes of validity for the sent code (Between 1 and 90)", "verification_code_label": "Verification code", "copy_code_type_label": "Button type", "copy_code_text_label": "Button text", "copy_code_text_label_2": "Button text must be", "copy_code_text_label_3": "maximum 20 characters"}, "alerts": {"image_types": "Allowed images: jpg, jpeg, png", "document_types": "Must select a pdf document", "video_types": "Must select a video", "file_size": "File size must not exceed {{size}}MB"}}, "chatbot": {"setup": {"modal": {"title": "Setup", "subtitle": "Configure the flow", "cancelLabel": "Back", "saveLabel": "Save"}, "forwarding": {"forwardingLabel": "Information forwarding", "forwardingDescription": "Send a reminder if the customer does not respond to the flow within a certain time.", "labelCustomMessage": "Message", "autoResolveLabel": "Mark as resolved if no response", "autoResolveDescription": "If the customer does not respond after the last message retry, the chat will be automatically marked as resolved.", "customMessageRequired": "Custom message is required", "waitingTimesRequired": "At least one waiting time is required", "labelReminderMessage": "Message type", "placeholderReminderMessage": "Select message", "lastOption": "Resend the current step", "customMessage": "Send custom message", "waitingLabel": "Configure attempts and their waiting times", "waitingDescription": "Define how many attempts you want to send and how long to wait between them. You can add up to 5 attempts, and the total time should not exceed 24 hours.", "waitingAdd": "Add attempt", "waitingSelectLabel": "Waiting time", "waitingUnitSelectLabel": "Unit", "delete": "Delete", "attempts": "Attempt", "minutes": "Minutes", "hours": "Hours", "totalTimeLimit": "The total time should not exceed 24 hours"}}, "config": {"no_active_mia": {"title": "Oops! Your current plan does not have MIA", "description": "Activate our AI-powered assistant to provide faster and more accurate responses. Contact customer support to activate MIA.", "ok_button": "Got it"}, "modal": {"make_a_question": "Make a question", "show _menu": "Show menu", "send_message": "Send message", "go_to": "Go to another step", "add_step": "Add step", "select_step_type": "Select the type of step", "errors": {"select_option": "Select an option", "required": "Required", "add_option": "Add at least one option", "max_options": "Maximum {{amount}} options", "max_characters": "Maximum {{amount}} characters", "select_file": "Select file", "add_action": "Add at least one action"}}, "node_content": {"preview": "Preview", "edit": "Edit", "delete": "Delete"}, "options": {"question": "Question", "question_description": "Ask your contacts open-ended questions using our flow.", "menu": "<PERSON><PERSON>", "menu_description": "Go to different branches of your flow based on your contact's responses.", "message": "Message", "message_description": "Send text messages, documents, images, audios or videos.", "go_to": "Go to", "go_to_description": "If you need to go back to a previous step or build loops, you can do so with this step.", "active_mia": "Active MIA", "active_mia_description": "Activate our AI-powered assistant to continue answering your customers' queries. (WhatsApp only)", "api": "Connect your API", "api_description": "Retrieve or send data to an external system and incorporate the response into the flow", "resolve_chat": "Resolve chat", "resolve_chat_caption": "<PERSON> chat resolved", "resolve_chat_description": "The chat will be marked as resolved automatically."}}}, "products": {"errors": {"image_size": "The image size must be less than 5MB"}}, "inbox": {"whatsapp": {"upgrade_plan": "To view messages older than a year, upgrade your plan"}}, "chatbots": {"options": {"api": {"post": "Post: Make a post application", "form_url": "Coded URL form"}}}, "campaign": {"index": {"title": "Campaigns", "no_campaigns": "No campaigns scheduled.", "no_results": "No campaigns found.", "breadcrumb": {"dashboard": "Dashboard", "campaigns": "Campaigns"}, "create": "+ Create campaign", "search_camp": "Search campaign", "search": "Search", "table": {"headers": {"name": "Name", "contact_group_name": "Associated list", "scope": "<PERSON><PERSON>", "cost": "Cost", "send_at": "Date", "status": "Status"}, "actions": {"cancel": "Cancel", "cancel_alert": "Are you sure you want to cancel this campaign?", "cancel_success": "Campaign successfully canceled.", "cancel_error": "Error canceling the campaign.", "statistics": "Statistics", "download": "Download"}}}, "stats": {"scope": "Alcance", "scope_detail": "Total de clientes con número telefónico.", "errors": "Errores", "errors_detail": "Total de mensajes con errores.", "sent": "Enviados", "sent_detail": "Total de mensajes enviados.", "delivered": "<PERSON><PERSON><PERSON><PERSON>", "delivered_detail": "Total de mensajes entregados.", "read": "<PERSON><PERSON><PERSON><PERSON>", "read_detail": "Total de mensajes abiertos.", "stage": "Etapa", "stage_detail": "Etapa actual de envío de campaña. Cada Etapa envía a {{size}} clientes", "total_campaigns": "Total campaña", "contacts_without_number": "Contactos sin número", "gupshupStatusMessage": {"pending": "Costo estimado. Obtendrás el costo real cuando se envíe la campaña.", "sent": "La campaña fue enviada.", "processing": "Costo estimado. Obtendrás el costo real cuando se termine de enviar la campaña.", "failed": "Costo estimado. La campaña falló.", "cancelled": "Costo estimado. La campaña fue cancelada."}, "result": {"title": "Resultado en ventas", "orders": "Total de órdenes", "total": "Total de ventas", "roi": "Retorno sobre la inversión"}, "agents_sales": {"title": "Ventas por agente", "agents": "<PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON>", "sales": "Ventas", "unknown": "No asignado"}, "chunks": {"stage": "Etapa", "sendDate": "<PERSON><PERSON>", "scope": "Alcance", "status": "Estado"}, "messages": {"name": "Nombre del cliente", "email": "Correo electrónico", "phone": "Teléfono", "status": "Estado", "no_data": "Sin mensajes enviados"}}}, "table_mc": {"actions": "Actions", "view_more": "View more", "view_less": "View less", "read": "Read", "sent": "<PERSON><PERSON>", "pending": "Pending", "cancelled": "Cancelled", "processing": "Processing", "in_process": "In process", "failed": "Failed", "other": "Other"}, "scheduled_messages_mc": {"title": "Scheduled messages", "view_more": "View more", "view_less": "View less", "sent_by": "Sent by", "no_scheduled_messages": "There are no scheduled messages yet. Schedule a message to see it here.", "cancel": "Cancel", "confirm_cancel": "Are you sure you want to cancel this scheduled message?"}, "location_selector": {"title": "Selecciona la ubicación", "subtitle": "Asegúrate que la ubicación marcada en el mapa es la correcta antes de enviarla", "buton": {"cancel": "<PERSON><PERSON><PERSON>", "send": "Enviar"}}, "chat_attach_menu": {"title": "¿Qué quieres hacer?", "file": "Adjuntar un documento", "image": "<PERSON><PERSON><PERSON>", "video": "Enviar videos", "location": "Enviar ubicación", "products": "Ver productos", "order": "<PERSON><PERSON> orden"}, "automations_routes": {"flows": {"title": "Flows"}, "aiChatbots": {"title": "Chatbots with AI"}, "scheduledMessages": {"title": "Scheduled messages"}, "exported_customers": {"title": "Exported Customers"}}, "dropdown_input_mc": {"no_options": "No hay opciones", "label": "Ingresa un valor o selecciona el campo"}, "retailers_routes": {"tags": {"title": "Etiquetas"}, "customers": {"title": "Clientes"}, "customers_list": {"title": "Lista de clientes"}, "customer_related_fields": {"title": "Custom Fields"}}, "button_open_filters": {"label": "<PERSON><PERSON><PERSON>"}, "filters_sidebar": {"title": "<PERSON><PERSON><PERSON>", "agent_label": "<PERSON><PERSON>", "status_label": "Estado", "sender_label": "Remitente", "save_label": "Guardar", "remove_filters_label": "Limpiar <PERSON>"}, "dropdown_mc": {"no_options": "No options", "error": "Error loading options", "loading": "Loading information...", "search": "Look for"}, "file_uploader_mc": {"error_max_size": "El archivo excede el tamaño máximo de {{maxSize}} MB.", "error_invalid_type": "Tipo de archivo no válido.", "label": "Adjuntar archivo"}, "message_detail": {"title": "Detalles del mensaje", "message": "Men<PERSON><PERSON>", "load_error": "Ha ocurrido un error al cargar el mensaje, por favor intenta nuevamente más tarde.", "retry": "Intentar de nuevo", "loading": "Cargando mensaje...", "view_more": "<PERSON>er más", "understand": "Entendido"}, "funnels_routes": {"funnels": {"title": "Sales cycles"}, "automations": {"title": "Automations"}, "downloads": {"title": "Downloads history"}}, "common": {"operator": {"and": ""}}, "operator": {"and": "And", "or": "OR"}}