{"monthly": "<PERSON><PERSON><PERSON>", "quarterly": "Trimestral", "yearly": "<PERSON><PERSON>", "funnelsTitle": "Embudos", "days": "Días", "menu": {"title": "Menú", "funnels": {"title": "Embudos", "automations": "Automatizaciones", "record": "Historial", "logs": "Logs", "deal": {"lead_status": {"hot": "Caliente", "warm": "Tibio", "cold": "Frío"}}}, "automations": {"title": "Automatizaciones", "shopify": {"title": "Mensajes automáticos", "pill_text": "Potencia tu tienda", "configuration": "Configuración", "whatsapp_widget": "Widget de WhatsApp"}, "chatbot_ai": {"title": "MIA Chatbot con IA", "guides": {"title": "Entrena a MIA"}, "config": {"title": "Configuración"}, "general": {"title": "Playground"}}}, "schedule_message": {"title": "Mensajes programados"}, "stats": {"module": "Estadísticas", "messaging": "Mensajería", "sales": "Ventas", "labels": "Etiquetas", "funnels": "Embudos", "whatsapp_templates": "Plantillas WhatsApp", "conversation_topics": "Temas de conversación"}, "my_business": {"title": "<PERSON> comercio", "catalog": {"title": "Catálogo", "products": "Productos", "categories": "Categorías"}, "orders": {"title": "<PERSON><PERSON><PERSON>", "my_orders": "<PERSON><PERSON> ó<PERSON>", "custom_fields": "Campos personalizados", "sales_channels": "Canales de venta", "download_history": "Historial de descargas"}, "payments": {"title": "Pagos", "digital_payments": "Pagos digitales"}, "shipping": {"title": "Env<PERSON><PERSON>", "shipping_methods": "Métodos de envío", "shipping_cost": "Costo de envío"}, "store_settings": {"title": "Configuración del comercio", "overview": "Información general", "addresses": "Direcciones", "contacts": "Contactos", "hours": "<PERSON><PERSON><PERSON>", "domain": "<PERSON>inio", "currency": "Moneda"}}}, "sidebar_buttons": {"all": "Todos", "unread": "No leídos", "not_assigned": "<PERSON>", "your": "<PERSON><PERSON><PERSON>", "no_answer": "Sin respuestas", "alert": "<PERSON><PERSON><PERSON>", "campaign": "Campañas", "human_help": "<PERSON><PERSON><PERSON> humana", "new_chat": "Nuevo chat", "conversations": "Conversaciones", "by_activity": "Por actividad", "channels": "Canales", "see_intro": "Ver introducción"}, "funnels": {"history": {"title": "Descargas", "no_data": "No tienes exportaciones actualmente", "name": "Nombre", "creation_date": "Fecha de creación", "expiration_date": "<PERSON><PERSON>nc<PERSON>o", "link": "Enlace", "download": "<PERSON><PERSON><PERSON>", "next_to_expire_text": "<PERSON><PERSON> el", "expired_text": "Venció el", "file_unavailable": "Archivo no disponible"}}, "deal": {"modal": {"create": "Agregar nueva negociación", "update": "Editar <PERSON>"}, "button": {"create": "<PERSON><PERSON><PERSON>", "update": "Guardar negociación"}}, "step": {"modal": {"create": "Agregar nueva etapa", "update": "Editar etapa", "name": "Nombre etapa", "probability": "Probabilidad de cierre", "stagnation_legend": "Tiempo de estancamiento en días", "legend": "Las etapas del embudo representan los pasos de tu proceso de ventas", "name_error": "Ya existe una etapa con este nombre"}, "button": {"create": "Crear etapa", "update": "Guardar etapa"}, "menu": {"op1": "Agregar negocia<PERSON>", "op2": "Editar etapa", "op3": "Eliminar etapa"}}, "customer": {"view_list": {"title": "Clientes", "introduction": "Ver introducción", "exported_customers": "Clientes exportados", "custom_fields": "Campos personalizados", "add_customer": "+ Crear cliente", "search_placeholder": "Buscar por nombres, teléfono o correo electrónico", "search_button": "Buscar", "filters": "<PERSON><PERSON><PERSON>", "filters_placeholder": "Buscar por nombre, teléfono o correo", "import": "Importar", "export": "Exportar", "csv": "CSV", "excel": "EXCEL", "empty_screen": {"title": "¿Aún no tienes ningún cliente registrado?", "description": "Aquí podrás encontrar a todos los clientes que te escriben desde las redes que conectes. Empieza conectando WhatsApp o cualquier red social y comienza a recibir, responder y organizar tus contactos fácilmente.", "connect_whatsapp": "Conectar WhatsApp", "add_customer": "Crear cliente"}, "columns": {"full_name": "Nombres y apellidos", "phone": "Teléfono", "assigned_to": "Asignado a", "email": "Mail", "channel": "Canal", "actions": "Acciones"}, "row": {"view": "<PERSON>er", "edit": "<PERSON><PERSON>", "start_conversation": "Iniciar conversación", "send_now": "<PERSON><PERSON><PERSON> ahora", "schedule_message": "Mensaje programado", "not_assigned": "No asignado"}, "modal_filters": {"apply": "Aplicar filtros", "title": "<PERSON><PERSON><PERSON>", "agent": "<PERSON><PERSON>", "lists": "Listas", "tags": "Etiquetas", "funnel": "Embudo", "funnel_stage": "Etapa del embudo", "creation_date": "Fecha de creación", "all": "Todos", "item_not_found": "Opción no encontrada", "select_date": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel_date": "<PERSON><PERSON><PERSON>", "apply_date": "Aplicar", "loading": "Cargando", "search_by_tags": "Buscar por etiquetas", "search_by_stage": "Buscar por etapas"}}, "exported": {"title": "Clientes exportados", "view_introduction": "Ver introducción", "columns": {"creation_date": "Fecha de creación", "expiration_date": "<PERSON><PERSON>nc<PERSON>o", "link": "Enlace"}, "row": {"expired_on": "Venció el", "expires_on": "<PERSON><PERSON> el", "download": "<PERSON><PERSON><PERSON>", "file_not_available": "Archivo no disponible"}}, "modal": {"create": "Crear nuevo cliente", "first_name": "Nombres", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "Correo electrónico", "country": "<PERSON><PERSON>", "phone": "Teléfono"}, "title": {"update": "Actualizar cliente", "create": "Agregar cliente"}, "errors": {"email": "<PERSON>be llenar email o teléfono", "invalid_email": "<PERSON><PERSON><PERSON>", "required": "Requerido"}, "fields": {"header": "Información básica", "first_name": "Nombres", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON>", "phone": "Número de teléfono", "permission": "Tengo permiso explícito de enviar mensajes a este número", "document": "Tipo documento", "id": "Identificación", "custom_fields": "Campos personalizados", "show_custom_fields": "Mostrar campos personalizados"}, "address": {"error": "Esta ubicación no está disponible", "not_exist": "No existe dirección", "address": "Dirección", "button_map": "Buscar en el mapa", "description": "Descripción (casa, edificio, etc)", "city": "Ciudad", "state": "Estado/Provincia", "zip_code": "Código postal", "country": "<PERSON><PERSON>", "map": {"title": "Seleccionar <PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar"}, "addresses": "Direcciones", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "add": "Agregar <PERSON>", "confirm": "¿Está seguro de borrar esta dirección?"}, "tags": {"title": "Añadir etiquetas ", "create": "<PERSON><PERSON><PERSON>", "select_agent": "Selecciona el agente", "sync": "Sincronizar con"}}, "paginate": {"next": "Siguient<PERSON>", "previous": "Anterior"}, "button": {"active": "Activar", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar"}, "subcategories": {"modal": {"add": "Agregar subcategoría", "edit": "Actualizar subcategoría", "create": "Crear subcategoría", "update": "Guardar subcategoría"}}, "categories": {"header": "Categorías", "modal": {"add": "Agregar categoría", "edit": "Actualizar categoría", "create": "Crear categoría", "update": "Guardar categoría"}, "add": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "dropdown": {"category": "Categoría", "subcategory": "Subcategoría"}, "table": {"details": "Categorías / Subcategorías", "actions": "Acciones", "btnAdd": "Agregar subcategoría", "btnEdit": "<PERSON><PERSON>", "btnDel": "Eliminar"}}, "chatbot_ai": {"message_status": "Enviado por: MIA", "guides": {"your_name_description": "Escribe el nombre de la guía", "name": "Nombre", "example_name": "Ej: Información sobre envíos", "instructions": "Instrucciones", "instruction": "Instrucción", "instruction_placeholder": "Ej: Si el cliente pregunta por términos y condiciones responde con las siguientes imágenes.", "related_topics": "Tópicos", "related_topics_description": "Etiqueta temas que tengan relevancia o complementen esta guía.", "setup_instructions_title": "📚 ¿Qué son las Guías de Conocimiento?", "setup_instructions_description": "Las Guías de Conocimiento son la base que alimenta a tu chatbot con la información que necesita para responder con precisión. 🧠✨", "setup_instructions_sample": "💡 Eje<PERSON>lo correcto:", "setup_instructions_sample_description": "''Nuestra tienda abre de 8am a 6pm.''", "setup_instructions_disclaimer": "🚫 Evita escribir instrucciones o prompts:", "setup_instructions_disclaimer_description": "❌ ''Si el cliente pregunta por el horario de mi tienda, responde que abrimos de 8am a 6pm.''", "setup_instructions_disclaimer_description_2": "Este tipo de instrucciones pueden confundir a MIA y afectar sus respuestas.", "setup_instructions_recomendation": "✅ Recuerda: ", "setup_instructions_recomendation_text": "aquí debes escribir solo hechos o información concreta sobre tu negocio.", "setup_instructions_recomendation_description": "Piensa que estás construyendo la memoria de tu asistente, no diciéndole qué hacer paso a paso. 😉", "specific_instructions_title": "Envío de documentos", "specific_instructions_description": "Agrega documentación o imágenes detalladas para que MIA pueda adjuntar al usar esta guía.", "specific_instruction_type_title": "Selecciona la acción que deseas realizar", "specific_instruction_image_title": "Responder con imagen", "specific_instruction_image_subtitle": "Responde con archivos png o jpg", "specific_instruction_doc_title": "Responder con documento", "specific_instruction_doc_subtitle": "Responde con archivos PDF", "specific_instructions_step2_description": "Agrega información clave que detalle esta acción de forma clara y útil.", "specific_instructions_image_title2": "Imagen", "specific_instruction_image_description_2": "Adjunta una imagen que guíe a tus clientes a realizar la acción requerida.", "specific_instructions_doc_title2": "Documento", "specific_instruction_doc_description_2": "Adjunta un documento que guíe a tus clientes a realizar la acción requerida.", "specific_instruction_max_characters_error": "Máximo 300 caracteres", "attachment_click_image_label": "Arrastra tu archivo o haz ", "attachment_click_doc_label": "Arrastra tu archivo PDF o haz ", "attachment_click_here_label": "click aquí", "add_instructions_button": "+ Agregar instrucción", "load_error": "Ha ocurrido un error al cargar la guía, por favor intenta nuevamente. Si el error persiste contacte al administrador.", "save_guide": "Guía guardada exitosamente", "empty_error": "Requerido", "empty_question_error": "Debe agregar al menos una pregunta y respuesta", "empty_file_error": "Debe adjuntar al menos un archivo", "duplicate_files_error": "No puedes adjuntar archivos repetidos", "title": "Guía de conocimiento", "guide": "<PERSON><PERSON>", "subtitle": "Escribe sobre qué va a tratar tu guía.", "input_placeholder": "Ej: Información sobre envíos", "cancel": "<PERSON><PERSON><PERSON>", "type": "Selecciona el formato en el que alimentarás a tu chatbot", "empty_state": "Sube tu primera guía", "empty_button": "<PERSON>ñadir <PERSON>", "no_service_text": "No tienes integrado este servicio aún.", "right_button": "+ <PERSON><PERSON><PERSON> g<PERSON>", "save": "Entrenar y guardar", "table": {"list_title": "Guías de conocimiento", "guide": "<PERSON><PERSON>", "type": "Tipo", "create_by": "Autor", "last_update": "Última actualización", "actions": "Acciones", "main_tooltip": "Personaliza el conocimiento de MIA para ofrecer respuestas especializadas y efectivas", "guide_tooltip": "Elige un nombre descriptivo, E<PERSON>. 'Envíos'", "type_tooltip": "Elige el formato del contenido: 'Web', 'Texto' o 'Preguntas y Respuestas'."}, "text": {"title": "Información de tu negocio", "description": "Proporciona una descripción detallada de tu negocio. A quién va dirigido, horarios de atención, tipos de envíos, políticas de devolución y cualquier otro detalle relevante.", "info": "Información"}, "questions_and_answers": {"title": "Preguntas y respuestas", "description": "Compila y resuelve las dudas más comunes de tu negocio, una a una.", "add_question": "Agregar pregunta", "question": "Pregunta", "answer": "Respuesta", "question_placeholder": "Ej: ¿Cuál es el costo por envío?", "answer_placeholder": "Ej: El costo por envío será gratuito en compras mayores a USD $10.00"}, "tutorial": {"title": "Entrena a MIA", "subtitle": "Alimenta tu chatbot con la información relevante de tu negocio.", "description": "Crea guías de conocimiento, desde horarios de atención hasta políticas de devoluciones, y personaliza las respuestas de MIA para cada interacción.", "ok": "Entendido"}, "optimize_modal": {"title": "Mejora tu guía con MIA", "fetching_error": "Ha ocurrido un error al cargar la guía optimizada, por favor intente mas tarde.", "fetching_retry_error": "Ha ocurrido un error al cargar la guía optimizada. Estamos intentando nuevamente", "waiting_subtitle": "MIA está mejorando tu guía, esto puede tomar unos minutos...", "waiting_message": "Esto puede tomar unos minutos...", "complete_subtitle": "MIA ha mejorado tu guía", "complete_message": "Revisa la nueva versión de tu guía. Selecciónna “Aceptar” para mantenerla o “Cancelar” para conservar la versión original.", "warning_message": "El formato visual puede variar pero la mejora realizada por MIA mantiene la efectividad de la Guía. Puedes ajustar el estilo para mejorar el formato si lo consideras necesario."}, "website": {"urls_scraped": "URLs obtenidas exitosamente", "trained": "Entrenado", "training": "<PERSON><PERSON><PERSON><PERSON>", "failed": "Error", "url_input": "Web", "url_input_description": "Escribe la URL de tu sitio web", "url": "URL", "select_urls": "Por favor selecciona los enlaces con los que quieres entrenar a MIA", "not_found": "Url no encontrada, revisa y vuelve a intentar", "select_all": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "selected": "seleccionados"}, "type_options": {"questions_and_answers": {"title": "Preguntas y respuestas", "description": "Organiza las dudas más frecuentes de tu negocio para brindar respuestas rápidas y efectivas."}, "text": {"title": "Información de tu negocio", "description": "Proporciona una descripción detallada de tu negocio. A quién va dirigido, horarios de atención, tipos de envíos, políticas de devolución y cualquier otro detalle relevante."}, "website": {"title": "Web", "description": "¿No tienes tiempo de crear documentos? Conecta directamente la información de tu página web para resolver dudas al instante."}}}, "config": {"coming_soon": "Próximamente", "tutorial": {"title": "Configura tu Agente"}, "header_title": "Configuración", "general_instructions": "Instrucciones Generales", "clients_instructions": "Seguimiento automático a tu cliente", "products_instructions": "Instrucciones de productos y compras", "internal_message": "Este mensaje es interno, no lo puede ver tu cliente.", "internal_message_turn": "Es tu turno de continuar con el cliente.", "save": "Guardar", "save_success": "Configuración guardada exitosamente", "save_error": "Ha ocurrido un error, por favor intenta nuevamente. Si el error persiste contacte al administrador.", "error_loading": "Ha ocurrido un error al cargar el prompt, por favor intenta nuevamente.", "human_help_message": "MIA necesita tu ayuda", "platforms": {"title": "Activa a MIA", "description": "Configura la activación de tu chatbot en las distintas plataformas.", "modal_title": "Activación", "section_1_title": "Activar chatbot con IA", "section_1_description": "Tu chatbot se activará solo para chats nuevos y aquellos con más de 24 horas de inactividad.", "section_2_title": "Conecta con MIA tus herramientas de mensajería", "section_2_description": "Selecciona las plataformas disponibles.", "error_no_platform": "Se debe seleccionar una plataforma"}, "instructions": {"title": "Instrucciones (Prompt)", "description": "Ajusta las directrices que guiarán las respuestas de tu chatbot.", "modal_title": "Instrucciones", "section_1_title": "Define las instrucciones de tu agente", "section_1_description": "Refina las instrucciones que guiarán las respuestas de tu agente, detallando las directrices de cómo responderá tu chatbot."}, "goal": {"title": "Rol y objetivo", "description": "Define el propósito principal del chatbot dentro de la conversación: qué problema resuelve y qué acciones debe cumplir (informar, calificar, vender, etc).", "modal_title": "Rol y objetivo", "section_1_title": "Define el rol y objetivo", "section_1_description": "Define el propósito principal del chatbot dentro de la conversación: qué problema resuelve y qué acciones debe cumplir (informar, calificar, vender, etc)."}, "general_parameters": {"title": "Tono, estilo y formato de respuesta", "description": " Establece cómo debe expresarse MIA: tono cercano o profesional, uso de emojis, longitud del mensaje, y formato (listas, botones, links, etc.).", "modal_title": "Tono, estilo y formato de respuesta", "section_1_title": "Define el tono, estilo y formato de respuesta", "section_1_description": " Establece cómo debe expresarse MIA: tono cercano o profesional, uso de emojis, longitud del mensaje, y formato (listas, botones, links, etc.)."}, "step_by_step": {"title": "Flujo conversacional (paso a paso)", "description": " Describe la estructura lógica de la conversación: pasos, condiciones y ramificaciones que guían al usuario hacia un objetivo concreto.", "modal_title": "Flujo conversacional (paso a paso)", "section_1_title": "Define el flujo conversacional", "section_1_description": "Describe la estructura lógica de la conversación: pasos, condiciones y ramificaciones que guían al usuario hacia un objetivo concreto."}, "interaction_examples": {"title": "Ejemplos de interacción", "description": "Incluye ejemplos reales o simulados de cómo MIA debería interactuar con los usuarios en distintos escenarios.", "modal_title": "Ejemplos de interacción", "section_1_title": "Define las interacciones del chatbot", "section_1_description": "Incluye ejemplos reales o simulados de cómo MIA debería interactuar con los usuarios en distintos escenarios."}, "restrictions": {"title": "Restricciones", "description": "Define límites que debe respetar MIA: temas que no puede abordar, datos que no debe solicitar, y comportamientos prohibidos.", "modal_title": "Restricciones", "section_1_title": "Define las restricciones del chatbot", "section_1_description": "Información sobre probabilidad de cierre. Ejemplo: La probabilidad de cierre se refiere a que posibilidad existe para que se cierre una negociación."}, "handoff": {"title": "Configura la transferencia a humano", "description": "Establece cuándo y cómo MIA debe transferir la conversación a un agente humano, incluyendo condiciones y mensajes de transición.", "modal_title": "Transferencia a humano", "section_1_title": "Configura la transferencia a humano", "section_1_description": "Escribe el mensaje que MIA enviará al reasignar un cliente a un agente.", "message": "Men<PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON><PERSON>: 'He<PERSON> pasado tu inquietud a un agente. En breve te responderemos.'"}, "follow_up": {"title": "Configura el seguimiento a tus clientes", "description": "Define que información solicitar para guardarla en los datos de tu cliente."}, "client_data": {"title": "Configura los datos a recopilar", "description": "Define que información solicitar para guardarla en los datos de tu cliente."}, "negotiations": {"title": "Manejo de negociaciones", "description": "Haz que mía cree y maneje negociaciones entre embudo de venta en automático."}, "products": {"title": "Productos", "description": "Define las instrucciones para el envío y respuesta sobre tus productos.", "modal_title": "Productos", "section_1_title": "Sincroniza tus productos con MIA", "section_1_description": "Los productos de tu tienda se sincronizarán automáticamente para brindar respuestas rápidas y precisas.", "sync_pending": "Este es un proceso que puede tomar un par de horas. Puedes seguir utilizando Mercately", "sync_complete": "Productos sincronizados", "sync_failed": "Ha ocurrido un error, por favor intenta nuevamente. Si el error persiste contacte al administrador.", "sync_button": "Sincronizar productos", "sync_again_button": "Volver a sincronizar productos", "sync_confirm": "¿Estás seguro de que quieres sincronizar los productos?", "not_synced": "No sincronizado", "disable_sync_title": "¿Estás seguro de que quieres desactivar la sincronización de productos?", "disable_sync_description": "El chatbot dejará de responder sobre los productos.", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "section_2_title": "Define cómo mostrar los productos", "section_2_description": "Configura cómo el chatbot responderá cuando los usuarios pregunten sobre productos, precios y disponibilidad.", "prompt": {"error_loading": "Ha ocurrido un error al cargar el prompt de productos, por favor intenta nuevamente."}}, "buy_intention": {"title": "Parámetros de intención de compra", "description": "Configura los criterios para identificar cuándo un cliente muestra interés en comprar y cuándo es necesaria la intervención de un agente humano.", "modal_title": "Intención de compra", "section_1_title": "Define la intención de compra", "section_1_description": "Indícale a MIA cómo reconocer una intención de compra", "placeholder": "Ejemplo: Asigna la conversación a un agente cuando el cliente haya proporcionado su dirección y los productos que desea comprar.", "message": "MIA detectó una intención de compra"}, "feature_flag": {"message": "Hemos realizado mejoras en MIA  y tienes que actualizar tus promts para que funcione mucho mejor", "button": "Activar nueva configuración"}, "prompts_modal": {"title": "Hemos actualizado la configuración de MIA", "subtitle_1": "¿Qué va a cambiar?", "description_1": "La configuración de tu asistente dejará de ser un solo prompt y se organizará en bloques para darte mayor control y personalización.", "subtitle_2": "¿Qué debes hacer?", "description_2": "Podrás copiar las partes de tu prompt actual y asignarlas fácilmente a cada bloque.", "info_msg": " Te recomendamos hacer una copia de tu promt actual antes de actualizar. Una vez aplicado el cambio, los prompts que tengas configurados se perderán si no los respaldas.", "warning_msg": "Debes realizar este cambio hasta el 1 de agosto", "update": "Actualizar", "cancel": "No actualizar", "confirm_update": "Los prompts que hayas configurado se eliminarán si no los respaldas. ¿Estás seguro de que quieres continuar con la actualización?"}}, "general": {"main_title": "Mercately Intelligent Assistant", "save": "Guardar", "save_error": "Ha ocurrido un error, por favor intenta nuevamente. Si el error persiste contacte al administrador.", "save_success": "Configuración guardada exitosamente", "mia_label_title": "Respuestas MIA", "activate_chatbot": "Respuestas con MIA activadas", "deactivate_chatbot": "Respuestas con MIA desactivadas", "header_title": "General", "save_button": "Guardar", "cancel_button": "<PERSON><PERSON><PERSON>", "back_button": "Atrás", "refresh_success": "Sesión de chat actualizada", "refresh_error": "Ha ocurrido un error al refrescar, por favor intenta nuevamente.", "refresh": "Refrescar chat", "refresh_tooltip": "MIA se actualizará con el nuevo conocimiento", "refresh_tooltip2": "empezar<PERSON> a responder con la nueva información.", "instructions": "Instrucciones", "source_tooltip": "Ver fuentes de respuesta", "file_too_large": "El archivo es demasiado grande. El tamaño máximo permitido es 50mb.", "toggle_popover": {"deactivate_mia": "Desactivar MIA", "understood": "Entendido", "body": "Si vas a responder tú directamente, <strong>te recomendamos desactivarla</strong> para evitar errores o mensajes duplicados.", "title": "MIA está respondiendo en este chat"}, "train": {"title": "Entrena a tu chatbot con conocimiento", "description": "Brinda información sobre tu negocio."}, "config": {"title": "Configura tu chatbot", "description": "Define su personalidad y otras funciones."}, "stats": {"title": "Estadísticas de tu chatbot", "messages_by_mia_title": "Mensajes respondidos por MIA", "messages_by_mia_tooltip_1": "Cantidad de mensajes que fueron enviados", "messages_by_mia_tooltip_2": "por la Inteligencia Artificial en el mes.", "contact_interaction_title": "Interacción de contactos con MIA", "contact_interaction_tooltip_1": "Cantidad de contactos que tuvieron contacto con", "contact_interaction_tooltip_2": "la inteligencia artificial en el mes.", "buy_intention_title": "Intención de compra", "buy_intention_tooltip_1": "Cantidad de contactos que mostraron interés en comprar algo", "comparison_tooltip": "En comparación con 1 ", "load_error": "Ha ocurrido un error al cargar las estadísticas, por favor intenta nuevamente. Si el error persiste contacte al administrador.", "language": "es"}, "title": "<PERSON><PERSON><PERSON> tu chatbot", "subtitle": "Interactúa con el chatbot que configuraste y evalúa su rendimiento con preguntas específicas.", "buttons_header": "¿No obtienes las respuestas deseadas?", "chat": {"empty": "Prueba cómo funciona tu chatbot luego de agregar conocimiento.", "drag_file": "Suelta el archivo aquí...", "caption_placeholder": "Agrega un caption", "caption_placeholder_1": "Escribe tu mensaje o arrastra un archivo aquí"}, "human_help": {"label": "<PERSON><PERSON><PERSON> humana"}, "buy_intention": {"label": "Intención de compra"}}, "sources_modal": {"title": "Fuente de la respuesta", "qa": "Preguntas y respuestas", "text": "Texto", "web": "Web", "specific_instruction": "Texto", "product": {"title": "Producto", "description": "Descripción", "images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variants": {"title": "<PERSON><PERSON><PERSON>", "name": "Nombre", "price": "Precio", "stock": "Stock"}}}}, "attachment_popup": {"title": "¿Qué deseas adjuntar?", "single_file": "Solo puedes adjuntar un archivo por pregunta", "file": "Documento", "image": "Imagen"}, "nav": {"buttons": {"back": "Atrás"}}, "import_customers": {"select_file_error": "Seleccione un archivo", "required": "Requerido", "import_customers": "Importar clientes", "dashboard": "Dashboard", "customer_list": "Lista de clientes", "customers": "Clientes", "import": "Importar", "load_file": "Cargar archivo", "assign_columns": "<PERSON>ignar columnas", "final_details": "Detalles finales", "drag_drop_or_search": "Arrastra y suelta o busca", "drag_drop_or": "Arrastra y suelta o ", "only_excel_files": "*Solo archivos excel*", "wrong_file_header_warning": "Parece ser que el archivo que acabas de subir no cuenta con los encabezados adecuados. Te recomendaría descargar el archivo de ejemplo y mantener los encabezados que hemos proporcionado.", "cancel": "<PERSON><PERSON><PERSON>", "download_excel_file": "Descargar archivo Excel", "download_example_excel_file": "<PERSON><PERSON><PERSON> descargar el archivo Excel de ejemplo.", "download": "<PERSON><PERSON><PERSON>", "update_existing_data_with_this_file": "Puedes actualizar datos existentes con este archivo", "if_customer_exists_by_phone_or_email": "Si el cliente ya existe en tu cuenta, será localizado por el Número de teléfono o el Email, y se actualizarán con los campos que envíes en el archivo Excel.", "create_list_from_import": "Crea una lista a partir esta importación", "customer_list_name": "Nombre de la lista de clientes", "next": "Siguient<PERSON>", "back": "Atrás", "process": "Procesar", "processing": "Procesando", "close": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "excel": "Excel", "header_labels": {"first_name": "Nombres", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "Email", "phone": "Teléfono", "tags": "Etiquetas", "agent": "<PERSON><PERSON>", "id_type": "Tipo de identificación", "id_number": "Número de identificación", "address": "Dirección", "description": "Descripción", "city": "Ciudad", "state": "Estado", "zip_code": "Código postal", "country_id": "Código <PERSON>", "notes": "Notas"}, "map_customers_column": {"verify_file_columns": "Revisa que las columnas de tu archivo estén correctas con las propiedades del cliente", "each_column_title_assigned": "Cada título de columna debe estar asignado a una propiedad de cliente de Mercately.", "some_assigned_by_name": "Algunos de estos se asignaron según el nombre", "file_column_header": "Encabezado de archivo", "preview_information": "Información de vista preliminar", "mapped": "Mapeado", "mercately_property": "Propiedad de Mercately"}, "final_step_import_customers": {"final_details": "Detalles finales", "import_in_progress": "Tu importación está en curso. Según el volumen de contactos, puede llevar varios minutos.", "import_not_instant": "Por favor, ten en cuenta que la importación no es inmediata. Podrás verificar tus clientes importados", "check_customers_soon": "en la lista de clientes en breve. Recibirás un correo con detalles sobre la importación una vez concluido el proceso."}}, "roles": {"new_button": "Agregar nuevo", "edit_tool_tip": "<PERSON><PERSON>", "permissions_modal": {"title": "Configurar rol y permisos", "role_name": "Nombre del Rol", "select_permissions": "Selecciona los permisos para el rol", "errors": {"role_name": {"required": "Requerido"}, "role_permissions": {"required": "Debe seleccionar al menos un permiso"}}}}, "growth_tools": {"title": "Herramientas de crecimiento", "automation": {"title": "Automatizaciones"}}, "qr_modal": {"message": {"title": "<PERSON><PERSON><PERSON>", "message": "Men<PERSON><PERSON>", "add_file": "Agregar Imagen o archivo PDF", "send": "<PERSON><PERSON><PERSON> men<PERSON>"}, "not_valid_file": "Solo se permiten imágenes PNG, JPG/JPEG, y archivos PDF", "required": "requerido"}, "images_selector": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": "Se<PERSON><PERSON>ona, pega o arrastra máximo 5 imágenes", "buton": {"cancel": "<PERSON><PERSON><PERSON>", "send": "Enviar"}}, "location_selector": {"title": "Selecciona la ubicación", "subtitle": "Asegúrate que la ubicación marcada en el mapa es la correcta antes de enviarla", "buton": {"cancel": "<PERSON><PERSON><PERSON>", "send": "Enviar"}}, "chat_attach_menu": {"title": "¿Qué quieres hacer?", "file": "Adjuntar un documento", "image": "<PERSON><PERSON><PERSON>", "video": "Enviar videos", "location": "Enviar ubicación", "products": "Ver productos", "order": "<PERSON><PERSON> orden"}, "staff": {"modal": {"select_role": "Selecciona el rol que quieres asignar a este usuario", "select_role_placeholder": "Selecciona"}}, "tags": {"create": "+ Crear etiqueta", "title": "Etiquetas", "search_placeholder": "Buscar por nombre", "table": {"name": "Nombre de la etiqueta", "colour": "Color", "customers_number": "Clientes con la etiqueta"}, "alerts": {"delete_tag": "¿Estás seguro de eliminar la etiqueta?", "error": "Hubo un problema con la solicitud"}, "sidebar_edit_create": {"name_placeholder": "Nombre", "name_label": "Información de la etiqueta", "color_label": "Color", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "name_field_required": "El nombre de la etiqueta es requerido"}}, "ws_templates": {"preview": {"example_message": "Ejemplo de tu mensaje", "preview_message": "Esta solo es una previsualización, puede que la vista del mensaje cambie al enviarlo por Whatsapp.", "expiry_minutes": "<PERSON><PERSON>ra en {{minutes}} minutos"}, "help_text": {"enter_phone_number": "Ingrese el número telefónico", "header_text_limit": "El texto del encabezado debe ser máximo de 60 caracteres", "international_format": "en formato internacional", "static_url": "Una url estática no debe contener variables", "variables_must_be_enclosed_in_double_braces": "encerrados entre llaves dobles", "variables_must_be_numbers": "Las variables deben ser números (a partir del 1)"}, "inputs": {"button_text": "Texto del botón", "button_text_limit": "El texto del botón debe ser máximo de 20 caracteres", "quick_reply_text": "Texto", "quick_reply_text_limit": "El texto de la respuesta rápida debe ser máximo de 20 caracteres", "required": "Requerido", "max_length": "Máximo {n} caracteres", "numbers_only": "Solo números", "url_type": "Tipo de URL", "value": "Valor", "dynamic_url_example": "<PERSON>je<PERSON><PERSON> url <PERSON>", "select_placeholder": "Selecciona", "add_cta": "Agregar CTA", "add_quick_reply": "Agregar respuesta rápida", "save": "Guardar", "add": "Agregar", "expiry_minutes_range": "Valores posibles del 1 al 90", "text_cannot_be_empty": "No puede estar vacío", "select_file": "Selecciona un archivo", "static_url_example": "https://mercately.com/precios", "dynamic_url_example_2": "https://mercately.com/{{1}}", "phone_number_example": "Ejm: 593234567890", "security_text_example": "Por tu seguridad, no lo compartas.", "security_text_example_2": "For your security, do not share this code.", "example_value": "Valor de ejemplo", "expiry_minutes": "Minutos de validez del código enviado (1-90)", "example_variable": "Ejemplo de la variable"}, "create_template": {"title": "Crear plantilla", "name_label": "Nombre de la plantilla", "name_label_help": "Solo se permiten letras minúsculas, caracteres alfanuméricos y guiones bajos(_)", "choose_category": "Elige la categoría", "marketing_title": "Marketing", "marketing_description": "Envía promociones para aumentar el reconocimiento de marca y la interacción.", "utility_title": "Utility", "utility_description": "Envía actualizaciones, alertas y más. Comparte información importante.", "authentication_title": "Autenticación", "authentication_description": "Envía códigos que permiten a tus clientes acceder a sus cuentas.", "basic_information": "Información básica", "language": "Idioma", "team": "Equipo", "choose_header": "Escoge la cabecera del mensaje", "text": "Texto", "media": "Multimedia", "none": "<PERSON><PERSON><PERSON>", "header_text": "Cabecera del mensaje", "note": "Nota:", "note_description": "A continuación se detallan los formatos de archivo y sus respectivos tamaños que se aceptan:", "image_types": "Imagen: JPEG y PNG (tamaño máximo 5 MB)", "document_types": "Documento: (Tamaño máximo 40 MB)", "video_types": "Vídeo: MP4 (ta<PERSON><PERSON> máximo 16 MB)", "content_message": "Contenido del mensaje", "text_message": "Texto del mensaje", "add_variable": "Agregar variable", "variables_description": "Las variables te permitirán ingresar texto editable para personalizar tus mensajes", "example_variable": "Ejemplo de la variable", "buttons": "Botones", "call_to_actions": "Call To Actions", "quick_replies": "Respuestas rápidas", "select": "Selecciona", "type": "Tipo", "text_button": "Texto del botón", "text_button_description": "El texto del botón debe ser", "text_button_description_2": "máximo de 20 caracteres", "url_type": "Tipo de URL", "value": "Valor", "dynamic_url_example": "<PERSON>je<PERSON><PERSON> url <PERSON>", "quick_reply_text_description": "El texto de la respuesta rápida", "quick_reply_text_description_2": "debe ser máximo de 20 caracteres", "choose_offer": "Limited time offer", "button_text_error": "Los textos de los botones no pueden ser iguales", "lto": {"title": "Oferta de tiempo limitado", "subtitle": "Muestra fechas de vencimiento y ejecuta temporizadores de cuenta regresiva para código de oferta.", "offer_title": "<PERSON><PERSON><PERSON><PERSON>", "offer_type": "Tipo", "offer_button_text": "Texto del botón", "offer_button_value": "Valor", "add_expiration_offer": "<PERSON><PERSON><PERSON> de caducidad a esta oferta", "offer": "<PERSON><PERSON><PERSON>"}, "custom_message": {"title": "Mensaje <PERSON>izado", "subtitle": "Envía ofertas promocionales, anuncios y mucho más."}}, "authentication_ads": {"title": "Información opcional para plantillas de autenticación", "add_security_text": "Agregar aviso de seguridad del código", "add_security_text_2": "Agrega un texto de seguridad al mensaje. Verificar en el texto de la plantilla arriba.", "add_expiration_text": "Agregar aviso de expiración del código", "add_expiration_text_2": "Agrega un texto de expiración del código. Verificar en el texto de la plantilla arriba.", "expiration_minutes_label": "Minutos de validez del código enviado (Entre 1 y 90)", "verification_code_label": "Código de verificación", "copy_code_type_label": "Tipo de botón", "copy_code_text_label": "Texto del botón", "copy_code_text_label_2": "El texto del botón debe ser", "copy_code_text_label_3": "máximo de 20 caracteres"}, "alerts": {"image_types": "Imágenes permitidos: jpg, jpeg, png", "document_types": "Debe seleccionar un documento pdf", "video_types": "Debe seleccionar un video", "file_size": "El tamaño del archivo no debe exceder los {{size}} MB"}}, "chatbot": {"setup": {"modal": {"title": "Configurar", "subtitle": "Configura el flujo", "cancelLabel": "Atrás", "saveLabel": "Guardar"}, "forwarding": {"forwardingLabel": "Reenvío de información", "forwardingDescription": "Enviar un recordatorio si el cliente no responde al flujo en cierto tiempo.", "labelCustomMessage": "Men<PERSON><PERSON>", "autoResolveLabel": "Marcar como resuelto si no hay respuesta", "autoResolveDescription": "Si el cliente no responde después del último reintento de mensaje, el chat se marcará como resuelto automáticamente.", "customMessageRequired": "El mensaje personalizado es requerido", "waitingTimesRequired": "Al menos un tiempo de espera es requerido", "labelReminderMessage": "Tipo de mensaje", "placeholderReminderMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastOption": "Reenviar el paso actual", "customMessage": "Enviar mensaje personalizado", "waitingLabel": "Configura los intentos y sus tiempos de espera", "waitingDescription": "Define cuántos intentos deseas enviar y cuánto tiempo esperar entre ellos. Puedes agregar hasta 5 intentos, y el tiempo total no debe superar las 24 horas.", "waitingAdd": "<PERSON><PERSON><PERSON><PERSON> <PERSON>o", "waitingSelectLabel": "Tiempo de espera", "waitingUnitSelectLabel": "Unidad", "delete": "Eliminar", "attempts": "Intento", "minutes": "<PERSON><PERSON><PERSON>", "hours": "<PERSON><PERSON>", "totalTimeLimit": "El tiempo total no debe superar las 24 horas"}}, "config": {"no_active_mia": {"title": "¡Ups! Tu plan actual no posee MIA", "description": "Activa nuestro asistente con inteligencia artificial para ofrecer respuestas más rápidas y precisas. Contacta con atención al cliente para activar MIA.", "ok_button": "Entendido"}, "modal": {"make_a_question": "Hacer una pregunta", "show _menu": "Mostrar menú", "send_message": "<PERSON><PERSON><PERSON> men<PERSON>", "go_to": "Ir a otro paso", "add_step": "<PERSON><PERSON><PERSON> p<PERSON>o", "select_step_type": "Selecciona el tipo de paso", "errors": {"select_option": "Seleccione una opción", "required": "Requerido", "add_option": "Agrega por lo menos una opción", "max_options": "Máximo {{amount}} opciones", "max_characters": "Máximo {{amount}} caracteres", "select_file": "Seleccione un archivo", "add_action": "Agrega por lo menos una acción", "no_api_values": "Debes seleccionar las variables a utilizar"}}, "node_content": {"preview": "Vista previa", "edit": "<PERSON><PERSON>", "delete": "Eliminar"}, "options": {"question": "Pregunta", "question_description": "Realiza preguntas abiertas a tus contactos mediante nuestro flujo.", "menu": "Menú", "menu_description": "Ve a diferentes ramas de tu flujo según las respuestas de tu contacto.", "message": "Men<PERSON><PERSON>", "message_description": "Envía mensajes de texto, documentos, imágenes, audios o videos.", "go_to": "Ir a", "go_to_description": "Si necesitas regresar a un paso anterior o construir ciclos, puedes hacerlo con este paso.", "active_mia": "Activar MIA", "active_mia_description": "Activa nuestro asistente con inteligencia artificial, para que continúe respondiendo las consultas de tus clientes. (Solo WhatsApp)", "api": "Conectar API", "api_description": "Consulta o envía datos a un sistema externo y usa la respuesta en el flujo.", "resolve_chat": "Resolver chat", "resolve_chat_caption": "Marcar chat resuelto", "resolve_chat_description": "El chat se marcará como resuelto automáticamente."}}}, "products": {"errors": {"image_size": "La imagen no puede pesar más de 5 MB"}}, "inbox": {"whatsapp": {"upgrade_plan": "Para poder ver mensajes de más de un año actualiza tu plan"}}, "chatbots": {"options": {"api": {"post": "POST: Realiza una solicitud post", "form_url": "Formulario URL codificado", "content": "Contenido", "headers": "Headers", "method": "<PERSON><PERSON><PERSON> de solicitud", "body_type": "Tipo de contenido", "selection": "Selecciona", "urlPlaceholder": "Ejemplo: https://www.mercately.com", "test": "Prueba", "testRequest": "<PERSON><PERSON> solicitud", "header": {"content": "Contenido", "headers": "Header"}, "object": {"key": "Clave", "value": "Valor", "variable": "O selecciona variable", "add": "+ Agregar", "content": "contenido", "example": "Ejemplo"}, "errors": {"urlRequired": "La URL de la API es requerida", "urlInvalid": "La URL de la API no es válida", "methodRequired": "El método HTTP es requerido", "bodyTypeRequired": "El tipo de cuerpo es requerido", "contentTypeHeaderRequired": "Se requiere el header Content-Type para form-urlencoded", "keyRequired": "La clave es requerida", "valueRequired": "El valor es requerido", "apiNotTested": "Tiene que probar la api antes de guardar", "apiError": "La API no debe responder con error"}, "error": "Error", "response": "Respuesta", "testMessage": {"success": "La solicitud de este endpoint fue exitosa.", "error": "La solicitud de este endpoint falló. Revisa la repuesta de tu endpoint para poder guardar este paso."}, "variable": "Respuesta API"}}}, "button_open_filters": {"label": "<PERSON><PERSON><PERSON>"}, "filters_sidebar": {"title": "<PERSON><PERSON><PERSON>", "agent_label": "<PERSON><PERSON>", "status_label": "Estado", "sender_label": "Remitente", "save_label": "Filtrar", "remove_filters_label": "Limpiar <PERSON>"}, "staticNavbar": {"placeholder": "Buscar por nombre"}, "dropdown_mc": {"no_options": "No hay opciones", "error": "Error al cargar opciones", "loading": "Cargando información...", "label": "Ingresa un valor o selecciona el campo", "search": "Buscar"}, "file_uploader_mc": {"error_max_size": "El archivo excede el tamaño máximo de {{maxSize}} MB.", "error_invalid_type": "Tipo de archivo no válido.", "label": "Adjuntar archivo"}, "message_detail": {"title": "Detalles del mensaje", "message": "Men<PERSON><PERSON>", "load_error": "Ha ocurrido un error al cargar el mensaje, por favor intenta nuevamente más tarde.", "retry": "Intentar de nuevo", "loading": "Cargando mensaje...", "view_more": "<PERSON>er más", "understand": "Entendido"}, "scheduled_messages_mc": {"title": "Mensajes programados", "view_more": "<PERSON>er más", "view_less": "<PERSON>er menos", "sent_by": "Enviado por", "no_scheduled_messages": "Aún no existen mensajes programados. Programa un mensaje para verlo aquí.", "cancel": "<PERSON><PERSON><PERSON>", "confirm_cancel": "¿Estás seguro de cancelar este mensaje programado?"}, "dropdown_input_mc": {"no_options": "No hay opciones", "label": "Ingresa un valor o selecciona el campo"}, "table_mc": {"actions": "Acciones", "view_more": "<PERSON>er más", "view_less": "<PERSON>er menos", "read": "<PERSON><PERSON><PERSON>", "sent": "Enviado", "pending": "Pendiente", "cancelled": "Cancelado", "processing": "En proceso", "in_process": "Procesando", "failed": "Fallido", "other": "<PERSON><PERSON>"}, "common": {"operator": {"and": "Y"}}, "operator": {"and": "Y", "or": "O"}}