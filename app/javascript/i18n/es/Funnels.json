{"header": {"input": "Buscar por nombre de negociación o cliente", "orderBy": "Ordenar por", "orderByOptions": {"op1": "Fecha de creación", "op2": "Última interacción"}, "buttons": {"actions": "Acciones", "add": "<PERSON><PERSON><PERSON>"}, "actions": {"op1": "Exportar embudo a CSV", "op2": "<PERSON><PERSON> embudo", "op3": "Eliminar embudo", "op4": "Exportar todos los embudos a CSV"}, "add": {"op1": "Negociación", "op2": "Etapa", "new_funnel": "Embudo"}}, "sidebar": {"title": "<PERSON><PERSON><PERSON>", "createFromZero": {"stepOne": {"title": "Paso 1 de 2", "submitLabel": "<PERSON><PERSON><PERSON><PERSON>", "cancelLabel": "Atrás", "fields": {"name": {"label": "Información básica", "inputLabel": "Nombre del embudo", "placeholder": "Escribe aquí el nombre del embudo"}}}, "caption": "Nuevo embudo", "stepTwo": {"title": "Paso 2 de 2", "submitLabel": "<PERSON><PERSON><PERSON>", "cancelLabel": "Atrás", "fields": {"title": "Crea las etapas del embudo", "subtitle": "Selecciona el estado que mejor representa el momento actual en el embudo.", "titleOpenSteps": "Etapa abierta", "subtitleOpenSteps": "Ideal para iniciar o continuar el embudo.", "buttonOpenSteps": "Agregar etapa abierta", "titleClosedSteps": "Etapa cerrada", "subtitleClosedSteps": "Marca el cierre de una negociación en el embudo como completada, haya sido exitosa o no.", "buttonClosedSteps": "Agregar etapa cerrada"}, "addButton": "Agregar", "editButton": "<PERSON><PERSON>", "removeButton": "Remover", "submitLabelUpdate": "Guardar"}, "stageOpen": {"titleEdit": "Editar etapa abierta", "titleNew": "Nueva etapa abierta", "description": "Realiza los ajustes necesarios y guarda para aplicar los cambios.", "fields": {"name": {"label": "Información general", "inputLabel": "Nombre de la etapa", "placeholder": "Escribe aquí el nombre de la etapa."}, "probability": {"title": "Probabilidad de cierre", "subtitle": "Define el porcentaje de probabilidad de que esta negociación se cierre exitosamente."}, "stagnationTime": {"label": "Tiempo de estancamiento", "description": "Activa esta opción para establecer cuántos días puede durar esta negociación sin avances.", "inputLabel": "Días", "placeholder": "Escribe aquí la cantidad de días."}}}, "stageClosed": {"titleEdit": "Editar etapa cerrada", "titleNew": "Nueva etapa cerrada", "fields": {"name": {"title": "Información general", "subtitle": "Información sobre etapas del embudo", "inputLabel": "Nombre de la etapa", "placeholder": "Escribe aquí el nombre de la etapa."}, "qualification": {"title": "Calificación de etapa", "subtitle": "Elige ganada si la venta se cerró exitosamente o perdida si no se concretó.", "won": "Ganada", "lost": "Perdida"}, "reasonClosure": {"label": "Agregar motivo de cierre", "description": "Indica por que razón se cerró la etapa", "advice": "La modificación de un motivo de cierre impactará en todas las negociaciones que lo tengan previamente asignado."}, "reason": {"inputLabel": "Motivo", "placeholder": "Ejemplo: Negociación estancada", "add": "Agregar", "delete": "Eliminar", "confirmDelete": "Al eliminar un motivo de cierre, las negociaciones que lo tengan asignado se quedarán sin dicho motivo. ¿Deseas continuar?"}}}, "buttons": {"stepNew": "Agregar etapa", "stepEdit": "Guardar", "cancel": "<PERSON><PERSON><PERSON>"}, "errors": {"name": "Debes asignarle un nombre al embudo", "steps": "Debes agregar al menos una etapa para crear el embudo", "nameExists": "El nombre ya esta en uso", "stagnationTime": "El tiempo de estancamiento debe ser un número de hasta 10 dígitos"}, "captionUpdate": "<PERSON><PERSON> embudo"}}, "confirmation": {"delete": {"funnel": "¿Estás seguro de eliminar este embudo de negociación? (se borrarán todas las negociaciones y etapas dentro de este embudo)", "deal": "¿Estás seguro de eliminar esta negociación?", "step": "¿Estás seguro de eliminar esta etapa de negociación? (se borrarán todas las negociaciones dentro de esta etapa)"}}, "modal": {"creation": {"funnel": {"title": "¿Cómo quieres crear tu embudo?", "subtitle": "Selecciona cómo deseas visualizar y estructurar cada etapa de tu embudo.", "card1": {"title": "<PERSON><PERSON><PERSON> desde plantilla", "alt": "Plantilla", "description": "Elige la plantilla que mejor se adapte a tus objetivos.", "button": "Seleccionar una plantilla"}, "card2": {"title": "<PERSON><PERSON><PERSON> desde cero", "alt": "<PERSON><PERSON> cero", "description": "Diseña un embudo personalizado y configura cada etapa.", "button": "<PERSON><PERSON><PERSON> a crear"}}}}, "board": {"steps": {"header": {"negotiation": "Negociación", "negotiations": "Negociaciones", "tooltip": "Embudo atendido por agentes"}, "button": {"create": "Crear etapa", "update": "Guardar etapa"}, "menu": {"op1": "Agregar negocia<PERSON>", "op2": "Editar etapa", "op3": "Eliminar etapa"}, "card": {"show": "<PERSON>er", "edit": "<PERSON><PERSON>", "trash": "Eliminar", "restrictedChat": "Este chat no se encuentra asignado a ti", "today": "Hoy", "stagnant": "Estancado", "unreadChat": "Mensajes sin leer"}, "platform": {"ws": "WhatsApp", "msn": "<PERSON>", "ig": "Instagram"}, "showDeal": {"modalTitle": "Ver negociación", "name": "Nombre", "opening": "Apertura", "funnel": "Embudo", "amount": "Monto", "closing": "Cierre", "agent": "<PERSON><PERSON>", "customers": "Clientes", "orders": "Ordenes", "negotiations": "Negociaciones", "activities": "Actividades", "cold": "<PERSON><PERSON>", "warm": "Tibio", "hot": "Caliente", "order": "Orden", "total": "Total", "paymentMethod": "Método de pago", "paymentStatus": "Estado del pago", "viewOrder": "<PERSON>er orden", "pending": "Pendiente", "success": "Exitoso", "canceled": "Cancelado", "cash": "Efectivo", "card": "Tarjeta", "newNegotiationCreated": "Nueva negociación creada", "stage": "Etapa", "category": "Categoría", "subcategory": "subcategoría", "fountain": "Fuente", "creation_date": "<PERSON><PERSON> c<PERSON>", "viewMore": "Ver mas", "by": "Por"}, "quickActionsBar": {"delete": "Eliminar", "closedWon": "<PERSON><PERSON><PERSON> ganado", "closedLost": "<PERSON><PERSON><PERSON>"}}, "dragAndDrop": {"hoverMessage": "Arrastra y suelta aquí para mover a"}}, "createModal": {"error": "Ocurrió un error al crear el embudo", "success": "El embudo se creó exitosamente"}, "templatesModal": {"header": "Plantillas de embudos", "navbarTitle": "Plantillas", "selectButton": "Usar plantilla", "help": "¿Necesitas ayuda?", "confirmation": {"confirmCreation": "¿Estás seguro de que deseas crear este embudo?"}, "error": "Ocurrió un error al crear el embudo", "success": "El embudo fue creado exitosamente"}, "filters": {"eq": "Igual a", "lt": "<PERSON><PERSON> que", "gt": "Mayor que", "noAgents": "No asignados", "title": {"csv_export": "Descargar CSV", "funnels_search": "<PERSON><PERSON><PERSON>"}, "buttons": {"csv_export": "Exportar", "funnels_search": "Filtrar"}, "cancel": "Limpiar filtros", "amount": {"title": "Monto", "placeholder": "Escribe el monto"}, "created_at": "Fecha de apertura", "expected_close_date": "<PERSON><PERSON>", "agents": {"title": "<PERSON><PERSON>", "placeholder": "Buscar por nombre de agente", "noResults": "Agente no encontrado"}, "leadStatus": {"title": "Temperatura", "cold": "Frío", "warm": "Tibio", "hot": "Caliente", "placeholder": "Buscar por temperatura"}, "includedTags": {"title": "Incluir etiquetas", "placeholder": "Buscar por nombre de etiqueta", "noResults": "Etiqueta no encontrada"}, "excludedTags": {"title": "Excluir etiquetas", "placeholder": "Buscar por nombre de etiqueta", "noResults": "Etiqueta no encontrada"}, "funnels": {"title": "Embudos", "placeholder": "Buscar por nombre de embudo", "noResults": "Embudo no encontrado"}, "name": {"label": "Nombre de la exportación", "placeholder": "Escribe aquí el nombre de la exportación"}, "tags": {"addButton": "Crear grupo de etiquetas"}}, "history": {"title": "Historial de descargas", "no_data": "No tienes exportaciones actualmente", "name": "Nombre", "creation_date": "Fecha de creación", "expiration_date": "<PERSON><PERSON>nc<PERSON>o", "link": "Enlace", "download": "<PERSON><PERSON><PERSON>", "next_to_expire_text": "<PERSON><PERSON> el", "expired_text": "Venció el", "file_unavailable": "Archivo no disponible"}, "deal": {"title": "Nueva negociación", "titleEdit": "Editar <PERSON>", "save": "Guardar", "add": "<PERSON><PERSON><PERSON>", "cancel": "Atrás", "stage": {"title": "Etapa", "placeholder": "Seleccionar etapa", "required": "Debes seleccionar una etapa"}, "funnel": {"title": "Embudo", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> embudo", "required": "Debes seleccionar un embudo"}, "customer": {"title": "Cliente", "placeholder": "Seleccionar cliente", "required": "Debes seleccionar un cliente", "modal": {"title": "Crear nuevo cliente", "add": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "first_name": {"title": "Nombre", "placeholder": "Escribe aquí el nombre del cliente", "required": "Debes ingresar el nombre del cliente"}, "last_name": {"title": "Apellido", "placeholder": "Escribe aquí el apellido del cliente", "required": "Debes ingresar el apellido del cliente"}, "phone": {"title": "Teléfono", "placeholder": "Ingresar el # teléfono", "required": "Debes ingresar el teléfono del cliente", "validation_format": "El teléfono debe tener un formato válido"}, "email": {"title": "<PERSON><PERSON><PERSON>", "placeholder": "Escribe aquí el correo del cliente", "required": "Debes ingresar el correo del cliente", "validation_format": "El correo debe tener un formato válido"}, "country": {"title": "<PERSON><PERSON>", "placeholder": "Seleccionar país", "required": "Debes seleccionar un país"}}}, "negotiation": {"title": "Nombre de la negociación", "placeholder": "Escribe aquí el nombre que le vas a dar a la negociación", "required": "Debes asignarle un nombre a la negociación", "maxLength": "El nombre de la negociación no puede tener más de 100 caracteres", "cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "edit": "Actualizar negociación"}, "amount": {"title": "Monto", "placeholder": "$", "invalid": "El monto debe ser mayor a 0, por favor ingresa un monto válido"}, "openingDate": {"title": "Fecha de apertura", "placeholder": "DD-MM-AA", "invalid": "La fecha de apertura debe ser válida"}, "expectedCloseDate": {"title": "Fecha estimada de cierre", "placeholder": "DD-MM-AA", "invalid": "La fecha de cierre debe ser válida", "invalidOrder": "La fecha de cierre debe ser mayor a la fecha de apertura"}, "closeReason": {"title": "Motivo de cierre", "placeholder": "Seleccionar motivo de cierre", "required": "Debes seleccionar un motivo de cierre", "noReason": "No hay motivos de cierre disponibles"}, "agent": {"title": "<PERSON><PERSON>", "placeholder": "Seleccionar agente"}, "temperature": {"title": "Temperatura", "cold": "Frío", "warm": "Tibio", "hot": "Caliente"}}, "funnelStepSidebar": {"create": "Crear etapa", "update": "Editar etapa", "required": "Requerido", "typeStep": {"required": "Debes seleccionar un tipo de etapa", "invalid": "El tipo de etapa no es válido"}, "name": {"max": "Nombre no puede tener más de 40 caracteres"}, "time": {"min": "<PERSON><PERSON> debe ser mayor a 0", "max": "Máximo 10 dígitos", "required": "Días es requerido", "number": "Días debe ser un número", "integer": "Días debe ser un número entero"}, "button": {"create": "<PERSON><PERSON><PERSON>", "update": "Guardar", "back": "Atrás"}}, "empty_screen": {"title": "Organiza tu proceso con embudos de venta", "description": "Los embudos en Mercately te permiten seguir el recorrido de tus clientes desde el primer contacto hasta la conversión. Al estructurar tus etapas de venta, puedes identificar oportunidades, mejorar estrategias y aumentar tus conversiones.", "additional_description": "Comienza creando tu primer embudo y lleva un control efectivo de tus negociaciones.", "cta": "Crear nuevo embudo"}, "dealAutomation": {"editTitle": "Editar automatización", "deleteTitle": "Eliminar automatización", "deleteConfirmation": "¿Estás seguro de eliminar esta automatización?", "deletedStep": "Etapa eliminada", "timeFrequency": {"daily": "Diario", "intervalDaily": "Cada {{time_interval}} día{{s}}", "intervalDailyHour": " con {{interval_hour}}", "weekly": "Una vez a la semana", "monthly": "Una vez al mes", "intervalHour": "Cada {{interval_hour}} horas", "programmedIntervalHour": " a las {{interval_hour}}"}, "strategy": {"description": "Al añadir negocio en etapa", "firstStrategy": "Envío de WhatsApp", "secondStrategy": "Mover ne<PERSON>"}}}