import React from 'react';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { InformationCircleIcon } from '@heroicons/react/24/outline';
import ButtonIconMC from './ButtonIconMC';
import StatusTagMC from './StatusTagMC';
import ViewMoreCellMC from './ViewMoreCellMC';
import AvatarMC from './AvatarMC';
import LoadingMC from './LoadingMC';
import CustomIcon from './CustomIconMC';

/*
## Props object for TableMC component

IHeadersProps: Object { ## required
    label: String, ## Column header label text to display in the table header row cell
    key: String, ## Key to access the data in the data array
}

IActionsProps: Object {
    label: String, ## Action label text to display in the cell
    action: Function(dataId: String), ## Function to call when the action button is clicked
    icon: String, ## Icon to display in the action button from Heroicons (https://heroicons.com/)
}

IDataCellProps: Object { ## required
    type: 'text' | 'color' | 'status' | 'view_more' | 'status-label' | 'name-initials' | 'country' | 'custom-component' ## Type of data to display in the cell (text, color: '#000000', STATUS)
    value: String | Number | GLOBAL_STATUS.value, ## Value to display in the cell
    info?: String, ## Optional information to display in a tooltip
    className?: String, ## Optional class name to apply to the cell
    onClick?: () => void; // Optional: Function to execute when clicking on "View more"
    limit?: number; // Optional: Character limit to truncate the text
}

IDataRowProps: Object { ## required
    id: String, ## Unique identifier for the row
    [key: String]: IDataCellProps, ## Key that matches the key in the IHeadersProps object to access the data in the row
    actions?: Array<IActionsProps>, ## Optional Array of action objects to display in the cell
}

ITableMCProps: Object { ## required
    headers: Array<IHeadersProps> ## Array of header objects to display in the table header row
    data: Array<IDataRowProps> ## Array of data objects to display in the table body rows
    title?: String, ## Optional title to display above the table
    noDataMessage?: String, ## Optional message to display when there is no data in the table
}

props: ITableMCProps

*/

const MemoriedRowCell = React.memo((props) => <div>{props.component}</div>);

// TODO: Add min-h-50% to the table to avoid the table to be too small when there is no data and add icon like figma
const TableMC = ({
  headers,
  data,
  borderAll,
  noDataMessage,
  className = '',
  headerClassName = '',
  loading,
  secondaryClassName = '',
  emptyComponent,
  tooltipClass = ''
}) => {
  const tableHeaders = headers || [];
  const tableData = data || [];
  const needActionsColumn = tableData.some((row) => row.actions && !!row.actions.length);

  const buildRowCellByType = (dataItem) => {
    if (!dataItem) return '';
    switch (dataItem.type) {
      case 'status':
        return <StatusTagMC status={dataItem.value} />;
      case 'status_action':
        return <StatusTagMC status={dataItem.value} actions={dataItem.actions || []} />;
      case 'color':
        return (
          <span
            className={`tw-block tw-rounded-full tw-w-5 tw-h-5 tw-text-xs tw-font-medium ${dataItem.value.startsWith('#ffffff') ? 'tw-border' : ''}`}
            style={{ backgroundColor: dataItem.value }}
          />
        );
      case 'view_more':
        return (
          <ViewMoreCellMC
            text={dataItem.value}
            onClick={dataItem.onClick}
            limit={dataItem.limit}
          />
        );
      case 'country':
        return <span>{dataItem.value}</span>;
      case 'name-initials':
        return (
          <span className="tw-flex tw-flex-row tw-items-center">
            <span><AvatarMC fullName={dataItem.value} /></span>
            <span className="tw-pl-4">{dataItem.value}</span>
          </span>
        );
      case 'status-label':
        return <span>{dataItem.value}</span>;
      case 'custom-component':
        return <MemoriedRowCell component={dataItem.value} />;
      default:
        return dataItem.value;
    }
  };

  const cellClasses = [
    'tw-py-2.5',
    'tw-px-4',
    'tw-text-default',
    'tw-text-gray-950',
    'tw-font-normal',
    'first:tw-pl-5',
    'last:tw-pr-5'
  ].join(' ');

  const headerCellClasses = [
    'tw-py-4',
    'tw-px-4',
    'tw-text-default',
    'tw-border-b',
    'tw-text-gray-500',
    'tw-font-normal',
    'tw-font-normal',
    'first:tw-pl-6',
    'first:tw-rounded-ss-xl',
    'last:tw-pr-6',
    'last:tw-rounded-se-xl',
    'tw-truncate',
    headerClassName
  ].join(
    ' '
  );

  return (
    <div className={`tw-wrapper tw-overflow-hidden ${borderAll ? 'tw-border tw-rounded-xl' : ''} ${className}`}>
      <div className={`tw-overflow-x-auto ${secondaryClassName}`}>
        <table className="tw-table-auto tw-min-w-full">
          <thead className="tw-bg-gray-50 tw-rounded-t-xl">
            <tr>
              {(tableHeaders || [])?.map((header) => (
                <th key={header.key} className={headerCellClasses}>
                  <div className="tw-flex">
                    {header.label}
                    {header?.info && (
                      <InfoComponent data={header} tooltipClass={tooltipClass} />
                    )}
                  </div>
                </th>
              ))}
              {needActionsColumn && (
                <th className={headerCellClasses}>
                  Acciones
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {!tableData?.length ? (
              <tr>
                <td colSpan={tableHeaders.length + (needActionsColumn ? 1 : 0)}>
                  <div className="tw-px-6 tw-py-8 tw-text-center tw-w-full">
                    {loading ? (
                      <LoadingMC loading={loading} />
                    ) : (
                      emptyComponent || noDataMessage
                    )}
                  </div>
                </td>
              </tr>
            ) : tableData.map((row, key) => {
              const rowKey = `row-${row.id}-${key}`;
              const cellClass = `${cellClasses} ${key === tableData.length - 1 && borderAll ? '' : 'tw-border-b'}`;
              return (
                <tr key={rowKey}>
                  {tableHeaders?.map((header, headerKey) => {
                    const cellData = row[header.key];
                    const cellKey = `${rowKey}-${header.key}-${headerKey}`;
                    return (
                      <td key={`${cellKey}`} className={`${cellClass} ${cellData.className}`}>
                        {buildRowCellByType(cellData)}
                        {cellData?.info && (
                          <InfoComponent data={cellData} tooltipClass={tooltipClass} />
                        )}
                      </td>
                    );
                  })}
                  {needActionsColumn && (
                    <td className={cellClass}>
                      <div className="tw-flex tw-gap-2 tw-items-center">
                        {(row.actions || []).map((action) => (
                          <ButtonIconMC
                            key={`${rowKey}-${action.label}`}
                            icon={action.icon}
                            size={action.iconSize}
                            label={action.label}
                            onClick={() => action.action(row.id)}
                            iconClassName="tw-gray-700"
                            className="tw-flex tw-items-center tw-justify-center"
                          />
                        ))}
                      </div>
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const InfoComponent = ({ data, tooltipClass }) => (
  <>
    <span
      data-tooltip-id={data.info}
      data-tooltip-content={data.info}
      className="tw-ml-2 tw-cursor-pointer tw-flex tw-items-center"
    >
      <CustomIcon
        name="info-outline"
        className="tw-text-gray-500"
        size="lg"
      />
    </span>
    <ReactTooltip
      place="right"
      type="dark"
      effect="solid"
      id={data.info}
      className={`tw-max-w-xs ${tooltipClass}`}
    />
  </>
);

export default TableMC;
