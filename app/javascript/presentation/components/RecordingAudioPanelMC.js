/* eslint-disable react/jsx-no-useless-fragment */
import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import RecordAudioIcon from '../../components/icons/RecordAudioOutlineIconContainer';
import IconMC from './IconMC';

let totalSeconds = 0;
let cancelledAudio;
let timer;
let chunks;
const RecordingAudioPanelMC = ({
  onMobile,
  sendAudio
}) => {
  const mediaRecorderRef = useRef();
  const dispatch = useDispatch();
  const { recordingAudio } = useSelector((reduxState) => reduxState.chatsReducer);

  const [state, setState] = useState({
    totalSeconds: 0,
    audioMinutes: '00',
    audioSeconds: '00',
    mediaRecorder: null
  });

  useEffect(() => {
    emptyGlobalVariables();

    return () => {
      if (mediaRecorderRef.current) cancelAudio();
      resetAudio();
    };
  }, []);

  useEffect(() => {
    if (recordingAudio) return;

    if (state.mediaRecorder) cancelAudio();
    resetAudio();
  }, [recordingAudio]);

  const setRecordingAudio = (value) => {
    dispatch({ type: 'UPDATE_RECORDING_AUDIO', data: value });
  };

  const cancelAudio = () => {
    cancelledAudio = true;
    mediaRecorderRef.current.stop();
  };

  const resetAudio = (stream = null) => {
    setState((prevState) => ({
      ...prevState,
      totalSeconds: 0,
      audioMinutes: '00',
      audioSeconds: '00'
    }));
    if (recordingAudio) dispatch({ type: 'UPDATE_RECORDING_AUDIO', data: false })

    setRecordingAudio(false);
    if (stream) emptyGlobalVariables(stream);
  };

  const emptyGlobalVariables = (stream = null) => {
    totalSeconds = 0;
    if (stream) stream.getTracks()[0].stop();
    chunks = [];

    setState((prevState) => ({
      ...prevState,
      mediaRecorder: null
    }));

    if (timer) clearInterval(timer);
    timer = null;
  };

  const recordAudio = () => {
    if (navigator.mediaDevices) {
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then((stream) => {
          setRecordingAudio(true);
          timer = setInterval(() => {
            // eslint-disable-next-line react/no-access-state-in-setstate
            totalSeconds += 1;

            setState((prevState) => ({
              ...prevState,
              totalSeconds,
              audioSeconds: pad(totalSeconds % 60),
              audioMinutes: pad(parseInt(totalSeconds / 60, 10))
            }));
          }, 1000);

          cancelledAudio = false;

          const mediaRecorder = new MediaRecorder(stream);
          mediaRecorder.start();

          chunks = [];
          mediaRecorder.ondataavailable = (e) => {
            chunks.push(e.data);
          };

          mediaRecorder.onstop = () => {
            if (cancelledAudio) {
              resetAudio(stream);
              return;
            }

            const blob = new Blob(chunks, { 'type': 'audio/mpeg' });

            if (blob.size > 10485760) { // 10 * 1024 * 1024
              resetAudio(stream);
              alert('Error: La nota de voz debe ser menor a 10MB');
              return;
            }

            const url = URL.createObjectURL(blob);
            sendAudio(blob, url, stream);
            resetAudio(stream);
          }

          mediaRecorderRef.current = mediaRecorder;
          setState((prevState) => ({
            ...prevState,
            mediaRecorder: mediaRecorder
          }));
        }).catch(() => {
          alert('Para enviar notas de voz, debes permitir el acceso al micrófono');
        });
    } else {
      alert('La grabación de audio no está soportada en este navegador');
    }
  };

  const pad = (val) => {
    const valString = val.toString();

    if (valString.length < 2) {
      return `0${valString}`;
    }
    return valString;
  };

  return (
    <>
      {!recordingAudio ? (
        <div className="tw-mt-1">
          <RecordAudioIcon onMobile={onMobile} recordAudio={recordAudio} />
        </div>
      ) : (
        <div className="tw-inline-flex tw-m-0 tw-items-center">
          <div className="tw-relative tw-inline-block">
            <IconMC
              name="close-circle-outline"
              className="tw-text-red-500"
              onClick={() => cancelAudio()}
            />
            {onMobile === false
              && <div className="tooltiptext">Cancelar</div>}
          </div>
          <div>
            <IconMC
              name="radio-button-on-fill"
              size="base"
              className="tw-text-red-500"
            />
            <span className="tw-text-gray-950 tw-text-sm">
              {state.audioMinutes}
              :
            </span>
            <span
              className="tw-text-gray-950 tw-text-sm"
            >
              {state.audioSeconds}
            </span>
          </div>
          <div className="tooltip-top send-audio-counter">
            <IconMC
              name="checkmark-circle-2-outline"
              className="tw-text-green-500"
              onClick={() => state.mediaRecorder.stop()}
            />
            {onMobile === false
              && <div className="tooltiptext">Enviar</div>}
          </div>
        </div>
      )}
    </>
  );
};

export default RecordingAudioPanelMC;
