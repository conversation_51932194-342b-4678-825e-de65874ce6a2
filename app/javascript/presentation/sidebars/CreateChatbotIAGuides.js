import React from 'react';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import RightModalLayout from '../layouts/Modal/RightModalLayout';
import { SIDEBAR_ACTIONS } from '../../constants/actionsConstants';
import GuideOptionsMC from '../modules/Automations/ChatbotIA/Guides/GuideOptionsMC';

const CreateChatbotIAGuides = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const navigate = useNavigate();

  const handleOptionSelect = (route) => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
    navigate(route);
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('guides.sidebar_create.title.create')}
        cancelAction={() => dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR })}
        cancelLabel={t('guides.sidebar_create.cancel')}
      >
        <div className="tw-flex tw-flex-col tw-gap-2.5">
          <span className="tw-font-semibold tw-text-sm">
            {t('guides.sidebar_create.type')}
          </span>
          <div className="tw-flex tw-flex-col tw-gap-4">
            <GuideOptionsMC onClick={handleOptionSelect} />
          </div>
        </div>
      </RightModalLayout>
    </div>
  );
};

export default CreateChatbotIAGuides;
