import React, { useMemo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { merge } from 'lodash';
import ButtonMC from '../../../../../components/ButtonMC';
import TitleMC from '../../../../../components/TitleMC';
import TableMC from '../../../../../components/TableMC';
import CustomerRow from './CustomerRow';
import usePagination from '../../../../../../hooks/usePagination';
import customerModel from '../../../../../../components/Customers/CustomerModel';
import customHistory from '../../../../../../customHistory';
import ContactIcons from './ContactIcons';
import { getTags } from '../../../../../../actions/tagsActions';
import { getFunnels } from '../../../../../../actions/funnels';
import { getCurrentRetailerUserInfo, getRetailerInfo } from '../../../../../../actions/retailerUsersActions';
import { fetchCurrentRetailerUser } from '../../../../../../actions/actions';
import { fetchCustomerList } from '../../../../../../actions/ContactGroupActions';
import { fetchAndPersistActiveAgentsByRole } from '../../../../../../actions/agents';
import NavbarLayoutMC from '../../../../../layouts/NavbarLayoutMC';
import ActionsButton from '../../../../../components/DropdownButtonMC';
import TemplateSelection from '../../../../../../components/WhatsApp/TemplateSelection';
import CustomersEmptyScreenMC from './CustomersEmptyScreenMC';

const CustomersScreenMC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const [customerSelected, setCustomerSelected] = useState(false);
  const { retailer_info } = useSelector((state) => state.retailerUsersReducer);
  const { currentRetailerUser } = useSelector((state) => state.mainReducer);
  const { agentList } = useSelector((reduxState) => reduxState.agentsReducer);
  const { customerList } = useSelector((reduxState) => reduxState.customerListReducer);
  const { tags } = useSelector((state) => state.tagsReducer);
  const { funnels } = useSelector((state) => state.funnelsReducer);
  const [showEmptyScreen, setShowEmptyScreen] = useState(false);

  const formatOptions = (list, getLabel) => [
    { value: '', label: t('customer.view_list.modal_filters.all') },
    ...list.map((item) => ({ ...item, value: item.id, label: getLabel(item) }))
  ];

  const funnelOptions = formatOptions(funnels, (funnel) => funnel.name);
  const customerListOptions = formatOptions(customerList, (group) => group.name);
  const agentListOptions = formatOptions(agentList, (agent) => `${agent.first_name} ${agent.last_name || ''} - ${agent.email}`);

  const buildFormattedParams = (params) => {
    const _created_at = params.created_at ? JSON.parse(params.created_at) : null;
    const created_at = _created_at ? `${moment(_created_at.startDate).format('DD/MM/YYYY')} - ${moment(_created_at.endDate).format('DD/MM/YYYY')}` : '';

    return {
      ...(params.textSearch && { 'q[first_name_or_last_name_or_phone_or_email_or_whatsapp_name_cont]': params.textSearch }),
      ...(params.agent_id && { 'q[agent_id]': params.agent_id }),
      ...(params.contact_group_id && { 'q[contact_group_id]': params.contact_group_id }),
      ...(params.customer_tags_tag_id_in?.length && { 'q[customer_tags_tag_id_in][]': params.customer_tags_tag_id_in }),
      ...(params.funnel_id && { 'q[funnel_id]': params.funnel_id }),
      ...(params.funnel_steps_id_in?.length && { 'q[funnel_steps_id_in][]': params.funnel_steps_id_in }),
      ...(_created_at && { 'q[created_at]': created_at }),
      'q[s]': 'created_at desc',
      page: params.page || 1
    };
  };

  const buildExportFormattedParams = (params) => {
    const _created_at = params.created_at ? JSON.parse(params.created_at) : null;
    let created_at = '';
    if (_created_at) {
      created_at = `${moment(_created_at.startDate).format('DD/MM/YYYY')} - ${moment(_created_at.endDate).format('DD/MM/YYYY')}`;
    }

    return {
      q: {
        ...(params.textSearch?.length && {
          first_name_or_last_name_or_phone_or_email_or_whatsapp_name_cont: params.textSearch
        }),
        ...(params.agent_id && { agent_id: params.agent_id }),
        ...(params.contact_group_id && { contact_group_id: params.contact_group_id }),
        ...(params.customer_tags_tag_id_in?.length && {
          customer_tags_tag_id_in: params.customer_tags_tag_id_in.split(',')
        }),
        ...(params.funnel_id && { funnel_id: params.funnel_id }),
        ...(params.funnel_steps_id_in?.length && {
          funnel_steps_id_in: params.funnel_steps_id_in.split(',')
        }),
        ...(_created_at && { created_at }),
        s: 'created_at desc'
      }
    };
  };

  const {
    data,
    paginationComponent,
    activeQueryParams,
    filtersComponent,
    loading
  } = usePagination({
    formatParams: buildFormattedParams,
    modelService: customerModel.getCustomers,
    filters: [
      {
        type: 'search',
        key: 'textSearch',
        label: t('customer.view_list.filters_placeholder'),
        containerClassName: 'tw-w-48 lg:tw-w-96'
      }
    ],
    asideFilters: [
      {
        type: 'select', key: 'agent_id', label: t('customer.view_list.modal_filters.agent'), options: agentListOptions
      },
      {
        type: 'select', key: 'contact_group_id', label: t('customer.view_list.modal_filters.lists'), options: customerListOptions
      },
      {
        type: 'multi-select', key: 'customer_tags_tag_id_in', label: t('customer.view_list.modal_filters.tags'), options: tags, placeholder: t('customer.view_list.modal_filters.search_by_tags')
      },
      {
        type: 'select', key: 'funnel_id', label: t('customer.view_list.modal_filters.funnel'), options: funnelOptions
      },
      {
        type: 'multi-select', key: 'funnel_steps_id_in', label: t('customer.view_list.modal_filters.funnel_stage'), dependencyKey: 'funnel_id', parentOptionsKey: 'funnel_steps', placeholder: t('customer.view_list.modal_filters.search_by_stage')
      },
      { type: 'date-range', key: 'created_at', label: t('customer.view_list.modal_filters.creation_date') }
    ]
  });

  const createExport = (params, type) => {
    const formattedParams = merge({}, buildExportFormattedParams(params), { type });

    customerModel
      .createExport(formattedParams)
      .then((result) => {
        showtoast(result.message);
      })
      .catch((errs) => {
        showtoast(errs.message);
      });
  };

  const tableCustomersHeaders = [
    { key: 'customer', label: t('customer.view_list.columns.full_name') },
    { key: 'phone', label: t('customer.view_list.columns.phone') },
    { key: 'email', label: t('customer.view_list.columns.email') },
    { key: 'channel', label: t('customer.view_list.columns.channel') }
  ];

  const handleCreateNewCustomer = () => {
    customHistory.push(`/retailers/${retailer_info.slug}/customers/new`);
  };

  const createTableAction = (action, customer) => {
    switch (action) {
      case 'view':
        return {
          icon: 'eye-outline',
          label: 'Ver',
          action: () => {
            window.location.pathname = `/retailers/${retailer_info.slug}/customers/${customer.web_id}`;
          }
        };
      case 'edit':
        return {
          icon: 'edit-outline',
          label: 'Editar',
          action: () => {
            customHistory.push(`/retailers/${retailer_info.slug}/customers/${customer.web_id}/edit`);
          }
        };
      default:
        return null;
    }
  };

  const getCustomersRows = useMemo(() => () => (data.customers || []).map((customer) => ({
    customer: { type: 'text', value: customer.full_names, className: 'tw-max-w-64 tw-truncate' },
    phone: { type: 'text', value: <CustomerRow customer={customer} currentRetailerUser={currentRetailerUser} retailer_info={retailer_info} setCustomerSelected={setCustomerSelected} /> },
    email: { type: 'text', value: customer?.email || '' },
    channel: { type: 'custom-component', value: <ContactIcons customer={customer} retailerSlug={retailer_info.slug} /> },
    actions: [createTableAction('view', customer), createTableAction('edit', customer)]
  })), [data.customers, retailer_info]);

  const importExportOptions = [
    {
      id: 'import',
      label: 'Importar',
      icon: 'plus-outline',
      isIconComponent: true,
      disabled: (currentRetailerUser.agent && !currentRetailerUser.allow_import),
      onClick: () => customHistory.push(`/retailers/${retailer_info.slug}/import_customers`)
    },
    {
      id: 'export',
      label: 'Exportar',
      isIconComponent: true,
      icon: 'ci-share-ios-export',
      disabled: (currentRetailerUser.agent && !currentRetailerUser.allow_export),
      submenu: [
        { id: 'export_csv', label: 'CSV', onClick: () => createExport(activeQueryParams, 'csv') },
        { id: 'export_excel', label: 'Excel', onClick: () => createExport(activeQueryParams, 'excel') }
      ]
    }
  ];

  useEffect(() => {
    dispatch(getTags());
    dispatch(getFunnels());
    dispatch(getRetailerInfo());
    dispatch(getCurrentRetailerUserInfo());
    dispatch(fetchCurrentRetailerUser());
    dispatch(fetchCustomerList());
    dispatch(fetchAndPersistActiveAgentsByRole());
  }, []);

  useEffect(() => {
    if (data.customers?.length === 0 && !loading) {
      setShowEmptyScreen(true);
    } else {
      setShowEmptyScreen(false);
    }
  }, [data.customers, loading]);

  return (
    <NavbarLayoutMC>
      <div className="tw-wrapper">
        <div className="tw-flex tw-flex-row tw-h-full tw-min-h-screen">
          <div className="tw-flex-1 tw-bg-white tw-flex tw-flex-col tw-p-4">
            <div className="tw-flex tw-flex-row tw-justify-between tw-mb-5">
              <div className="tw-flex tw-gap-4">
                <TitleMC text={t('customer.view_list.title')} variant="H5" textClassName="tw-font-semibold" />
              </div>
              <div className="tw-flex tw-items-center tw-gap-2">
                {filtersComponent}
                <ActionsButton
                  text="Acciones"
                  variant="GRAY"
                  items={importExportOptions}
                  outlined
                />
                <ButtonMC
                  className="tw-w-auto tw-flex-shrink-0"
                  text={t('customer.view_list.add_customer')}
                  onClick={handleCreateNewCustomer}
                  outlined
                />
              </div>
            </div>
            <div>
              <TableMC
                headers={tableCustomersHeaders}
                data={getCustomersRows()}
                loading={loading}
                borderAll
                className={'tw-overflow-visible'}
                secondaryClassName={'tw-overflow-x-visible'}
                emptyComponent={<CustomersEmptyScreenMC onCreateCustomer={handleCreateNewCustomer} />}
              />
              {data?.pages > 1 && (
                <div>
                  {paginationComponent}
                </div>
              )}
              <TemplateSelection customer={customerSelected} />
            </div>
          </div>
        </div>
      </div>
    </NavbarLayoutMC>
  );
};

export default CustomersScreenMC;
