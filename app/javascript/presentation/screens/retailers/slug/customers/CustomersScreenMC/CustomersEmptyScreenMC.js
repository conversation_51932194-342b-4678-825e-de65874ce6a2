import React, { useEffect } from "react";
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import ButtonComponent from "../../../../../components/ButtonMC";
import emptyScreenCustomers from "images/emptyScreenCustomers.png";
import { getRetailerInfo } from '../../../../../../actions/retailerUsersActions';

const CustomersEmptyScreenMC = ({ onCreateCustomer }) => {
  const { t } = useTranslation();
  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);
  const dispatch = useDispatch();
  const whatsappIntegrated = retailer_info.whatsapp_integrated;
  const slug = retailer_info.slug;

  useEffect(() => {
    dispatch(getRetailerInfo());
  }, []);

  const handleCreateCustomer = () => {
    if (onCreateCustomer) {
      onCreateCustomer();
    }
  };

  const handleConnectWhatsApp = () => {
    window.location.href = `/retailers/${slug}/integrations/whatsapp_business`;
  };

  return (
    <div className="tw-bg-white tw-rounded-lg tw-shadow-sm tw-p-12 tw-mx-8 tw-my-4">
      <div className="tw-flex tw-items-center tw-gap-4 tw-pl-10 tw-justify-between tw-mx-12 tw-px-12 ">
        <div className="tw-text-justify tw-w-3/4 tw-max-w-3xl">
          <h2 className="mb-24 text-gray-900 tw-text-lg tw-font-medium">
            {t('customer.view_list.empty_screen.title')}
          </h2>

          <p className="text-gray-500 mb-24 fs-16 line-height-24 ">
            {t('customer.view_list.empty_screen.description')}
          </p>

          <div className="mt-32 tw-flex tw-justify-start tw-items-center tw-gap-4">
            {!whatsappIntegrated && (
              <ButtonComponent
                text={t('customer.view_list.empty_screen.connect_whatsapp')}
                className="tw-w-64 tw-h-10 tw-gap-2"
                iconPosition="right"
                iconClassName="tw-text-green-400 !tw-h-5 !tw-w-5"
                icon="whatsapp-fill-outline"
                onClick={handleConnectWhatsApp}
                outlined
              />
            )}
            <ButtonComponent
              text={t('customer.view_list.empty_screen.add_customer')}
              variant="SECONDARY"
              className="tw-w-64 tw-h-10"
              iconPosition="right"
              icon="plus-outline"
              iconClassName=""
              onClick={handleCreateCustomer}
              outlined
            />
          </div>
        </div>

        <div className="tw-px-12 tw-pr-12 tw-w-1/3 tw-flex tw-justify-end">
          <img src={emptyScreenCustomers} alt="Clientes" className="tw-w-64 tw-h-auto tw-object-contain" />
        </div>
      </div>
    </div>
  );
};

export default CustomersEmptyScreenMC; 