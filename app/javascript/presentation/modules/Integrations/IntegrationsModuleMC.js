import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep } from 'lodash';
import { useDispatch } from 'react-redux';
import ButtonMC from '../../components/ButtonMC';
import { INTEGRATIONS } from '../../../constants/integrationsConstants';
import TitleMC from '../../components/TitleMC';
import IntegrationsModel from '../../models/IntegrationsModel';
import { SIDEBAR_ACTIONS } from '../../../constants/actionsConstants';

const IntegrationsMC = ({ country, retailerInfo }) => {
  const { t } = useTranslation('integrations');
  const [filteredIntegrations, setFilteredIntegrations] = useState([]);

  const dispatch = useDispatch();

  useEffect(() => {
    if (!country) return;

    IntegrationsModel.get({ country_code: country })
      .then((response) => {
        const { mercado_pago: enabledMercadoPago, payphone: enabledPayphone } = response || {};

        const mpIntegrated = retailerInfo['mercado_pago_integrated?'];
        const payphoneIntegrated = retailerInfo['payphone_connected?'];

        const integrations = cloneDeep(INTEGRATIONS);
        integrations.ELECTRONIC_PAYMENT.integrations.MERCADO_PAGO.url += `?mp_site=${country}`;

        if (mpIntegrated) {
          delete integrations.ELECTRONIC_PAYMENT.integrations.STRIPE;
          delete integrations.ELECTRONIC_PAYMENT.integrations.PAYPHONE;
        } else if (payphoneIntegrated) {
          delete integrations.ELECTRONIC_PAYMENT.integrations.STRIPE;
          delete integrations.ELECTRONIC_PAYMENT.integrations.MERCADO_PAGO;
        } else if (retailerInfo.stripe_integrated) {
          delete integrations.ELECTRONIC_PAYMENT.integrations.MERCADO_PAGO;
          delete integrations.ELECTRONIC_PAYMENT.integrations.PAYPHONE;
        } else {
          if (!enabledMercadoPago) delete integrations.ELECTRONIC_PAYMENT.integrations.MERCADO_PAGO;
          if (!enabledPayphone) delete integrations.ELECTRONIC_PAYMENT.integrations.PAYPHONE;
        }

        if (retailerInfo.created_at && new Date(retailerInfo.created_at) >= new Date(2023, 11, 11)) {
          delete integrations.COMMUNICATION_CHANNELS.integrations.MERCADO_LIBRE;
        }

        setFilteredIntegrations(integrations);
      })
      .catch(() => { });
  }, [country, retailerInfo.country_code]);

  const navigate = (path) => {
    window.location.href = `integrations/${path}`;
  };

  const openVideoModal = (integration) => {
    dispatch({
      type: SIDEBAR_ACTIONS.OPEN_SIDEBAR,
      payload: {
        componentKey: 'IntegrationVideo',
        props: {
          videoId: integration.videoId,
          title: t(integration.translationLabel)
        }
      }
    });
  };

  return (
    <div className="tw-wrapper">
      {Object.values(filteredIntegrations).map(({ id, translationLabel, integrations }) => (
        <div key={id}>
          <TitleMC text={t(translationLabel)} variant="H5" className="tw-mb-8" textClassName="tw-font-semibold" />
          <div className="tw-grid tw-grid-cols-3 tw-gap-4 tw-mb-8">
            {Object.values(integrations).map((integration) => {
              const isConnected = integration.connectedFields.some((field) => Boolean(retailerInfo[field]));
              return (
                <div key={integration.id} className="tw-min-h-full tw-border tw-px-6 tw-py-8 tw-rounded-3xl tw-flex tw-flex-col tw-justify-between">
                  <div className="tw-flex tw-flex-col tw-gap-4 tw-flex-grow">
                    <div className="tw-flex tw-flex-row tw-items-center">
                      {integration.icon && (
                        <div className="tw-pr-2">
                          <img src={integration.icon} alt={integration.id} className="tw-w-12 tw-h-12" />
                        </div>
                      )}
                      <div className="tw-flex tw-flex-1 tw-justify-between">
                        <div className="tw-flex tw-text-base tw-font-semibold">
                          {t(integration.translationLabel)}
                        </div>
                        {isConnected && (
                          <div className="tw-bg-green-50 tw-text-green-500 tw-px-2 tw-py-1 tw-text-xs tw-rounded tw-h-fit">
                            {t('integration.connected')}
                          </div>
                        )}
                      </div>
                      <div>
                      </div>
                    </div>
                    <div className="tw-w-full tw-h-auto">
                      {t(integration.translationDescription)}
                    </div>
                  </div>
                  <div className="tw-flex tw-flex-row tw-pt-4">
                    <div className="tw-flex tw-flex-col tw-flex-1">
                      <ButtonMC
                        variant="GRAY"
                        text={isConnected ? t('IntegrationsModuleMC.config') : t('IntegrationsModuleMC.connect')}
                        onClick={() => navigate(integration.url.replace('{{retailer}}', ENV.SLUG))}
                        className="!tw-px-4 tw-h-full"
                        icon="settings-outline"
                        iconPosition="left"
                      />
                    </div>
                    <div className="tw-flex tw-flex-col tw-flex-1 tw-pl-3">
                      {integration.videoId && (
                        <ButtonMC
                          variant="GRAY"
                          text={t('IntegrationsModuleMC.viewVideo')}
                          onClick={() => openVideoModal(integration)}
                          className="!tw-px-4"
                          icon="play-circle-outline"
                          iconPosition="left"
                        />
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

export default IntegrationsMC;
