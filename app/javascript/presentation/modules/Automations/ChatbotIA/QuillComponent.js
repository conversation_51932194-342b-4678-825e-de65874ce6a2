import React from "react";
import ReactQuill from "react-quill";

import 'react-quill/dist/quill.snow.css';

const QuillComponent = ({
  name = '',
  theme = 'snow',
  value = '',
  onChange,
  placeholder = '',
  style = { height: 'calc(100vh - 300px)' }
}) => (
  <div className="tw-flex tw-flex-col tw-mt-4">
    <ReactQuill
      name={name}
      theme={theme}
      value={value || ''}
      onChange={(newValue) => { onChange(newValue); }}
      placeholder={placeholder}
      style={style}
      modules={{
        toolbar: [
          [{ 'header': [1, 2, false] }],
          ['bold', 'italic', 'underline'],
          [{ 'list': 'bullet' }, { 'list': 'ordered' }]
        ]
      }}
    />
  </div>
);

export default QuillComponent;
