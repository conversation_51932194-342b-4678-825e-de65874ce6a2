import React from 'react';
import PropTypes from 'prop-types';

const ModalSection = ({
  title,
  description,
  beforeContent,
  afterContent,
  inlineTitle = false,
  children
}) => (
  <div className="tw-flex tw-flex-col tw-w-full sm:tw-w-full">
    {!inlineTitle && beforeContent}

    <div className={`${inlineTitle ? "tw-flex tw-items-center" : ''}`}>
      {inlineTitle && beforeContent}
      <span className="tw-font-semibold tw-text-gray-950 tw-text-sm tw-inline-block">
        {title}
      </span>
    </div>

    <span className="tw-font-regular tw-text-xs tw-text-gray-950">
      {description}
    </span>

    {afterContent}
    {children}
  </div>
);

ModalSection.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  beforeContent: PropTypes.node,
  afterContent: PropTypes.node,
  inlineTitle: PropTypes.bool,
  children: PropTypes.node
};

export default ModalSection;
