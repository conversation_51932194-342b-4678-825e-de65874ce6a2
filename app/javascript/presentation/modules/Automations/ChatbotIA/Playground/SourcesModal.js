import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ModalMC from '../../../../components/ModalMC';

const SourcesModal = ({ onClose, sources }) => {
  const { t } = useTranslation('ChatbotIA');

  const [products, setProducts] = useState([]);

  useEffect(() => {
    setProducts(sources.products.map((product) => processProduct(product)))
  }, [sources]);

  const processProduct = (product) => {
    const name = product.title;
    const description = product.result.detail;
    const { images } = product.result;

    return {
      name,
      description,
      images
    };
  };

  return (
    <ModalMC
      title={t('sources_modal.title')}
      visible
      onClose={onClose}
      containerClassName="!tw-mx-auto !tw-w-fit !tw-max-w-full tw-flex tw-justify-center"
      className="!tw-h-[85vh] tw-overflow-y-auto tw-w-[606px]"
    >
      <div>
        <div className="tw-px-2.5 tw-py-4">
          <div>
            <div className="tw-mt-2.5 ts-h-[590px] tw-overflow-y-auto">
              {sources.guides.map((guide) => (
                <div key={guide.id} className="tw-p-5 tw-rounded-[16px]tw- mb-2.5 tw-bf-[#F7F8FD]">
                  <div className="tw-text-sm">
                    <div className="tw-font-semibold tw-pb-[5px]">
                      {t(`sources_modal.${guide.type.toLowerCase()}`)}
                      :
                      {guide.title}
                    </div>
                    {(guide.type === 'web' && guide.url) && <div>{guide.url}</div>}
                    <p className="tw-mb-[1px]">{guide.result}</p>
                  </div>
                </div>
              ))}

              {products.map((product) => (
                <div key={product.id} className="tw-p-5 tw-rounded-[16px] tw-mb-2.5" style={{ backgroundColor: '#F7F8FD' }}>
                  <div className="tw-text-sm">
                    <div className="tw-font-semibold tw-pb-[5px]">
                      {t('sources_modal.product.title')}
                      :
                      {product.name}
                    </div>
                    <div>{product.description}</div>
                    <div className="pb-2">
                      <strong>
                        {t('sources_modal.product.images')}
                        :
                      </strong>
                      {' '}
                      {product.images.length}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </ModalMC>
  );
};

export default SourcesModal;
