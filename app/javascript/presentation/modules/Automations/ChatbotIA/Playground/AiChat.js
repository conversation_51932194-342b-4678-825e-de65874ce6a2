/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import RobotIcon from 'images/chatbot_ai/robot_icon.svg';
import { useTranslation } from 'react-i18next';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { useSelector } from 'react-redux';
import { useDropzone } from 'react-dropzone';
import AiChatMessages from './AiChatMessages';
import { uuid } from '../../../../../util/utils';
import ChatModel from '../../../../models/ChatModelMC';
import ButtonMC from '../../../../components/ButtonMC';
import ButtonIconMC from '../../../../components/ButtonIconMC';
import IconMC from '../../../../components/IconMC';
import RecordingAudioPanelMC from '../../../../components/RecordingAudioPanelMC';

const FILE_SIZE_LIMIT = 50 * 1024 * 1024;

const csrfToken = document.querySelector('[name=csrf-token]').content;

const AiChat = () => {
  const [messages, setMessages] = useState([]);
  const [text, setText] = useState('');
  const [audioBlob, setAudioBlob] = useState(null);
  const [sessionId, setSessionId] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const { t } = useTranslation('ChatbotIA');
  const { recordingAudio } = useSelector((reduxState) => reduxState.chatsReducer);
  const chatContainerRef = useRef(null);

  const addMessage = (message) => {
    setMessages((oldMgs) => [...oldMgs, message]);
  };

  const onDrop = useCallback((files) => {
    if (files?.[0]) {
      const file = files[0];

      if (file.size > FILE_SIZE_LIMIT) {
        showtoast(t('general.file_too_large'));
        return;
      }

      setSelectedFile(file);
    }
  }, [t]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', 'avif'],
      'application/pdf': ['.pdf']
    },
    maxFiles: 1,
    noClick: true,
    noKeyboard: true
  });

  const getFileType = (file) => {
    if (file.type.startsWith('image/')) {
      return 'image';
    } if (file.type.startsWith('audio/')) {
      return 'audio';
    } if (file.type === 'application/pdf') {
      return 'file';
    }
  };

  const sendMessage = (audioToSend = null, fileToSend = null) => {
    const fileToUse = fileToSend || selectedFile;
    const audioToUse = audioToSend || audioBlob;

    let contentType = 'text';
    let contentFile = null;

    if (fileToUse) {
      contentType = getFileType(fileToUse);
      contentFile = fileToUse;
    } else if (audioToUse) {
      contentType = 'audio';
      contentFile = audioToUse;
    }

    if (text.trim().length || contentFile) {
      const newMessage = {
        message_identifier: uuid(),
        content_text: text,
        content_audio: contentType === 'audio' ? URL.createObjectURL(contentFile) : null,
        content_file: contentType !== 'audio' && contentFile ? URL.createObjectURL(contentFile) : null,
        content_file_type: contentFile?.type,
        content_file_name: contentFile?.name,
        direction: 'outbound',
        status: 'read',
        created_time: new Date()
      };

      setText('');
      setAudioBlob(null);
      setSelectedFile(null);
      addMessage(newMessage);

      const formData = new FormData();
      formData.append('session_id', sessionId);
      formData.append('question[type]', contentType);

      if (contentType === 'text') {
        formData.append('question[content]', text);
      } else if (contentType === 'audio') {
        formData.append('file', contentFile);
      } else {
        formData.append('file', contentFile);

        if (text.trim().length > 0) {
          formData.append('question[caption]', text);
        }

        if (contentType === 'file') {
          formData.append('question[file_name]', contentFile.name);
        }
      }

      ChatModel.save(formData, csrfToken)
        .then((res) => {
          const { response, flag, sources } = res;

          if (['help', 'buy'].includes(flag)) {
            inboundMessage(response, flag);
          } else {
            addMessages(response, flag, sources);
          }
        })
        .catch(() => {
          showtoast(t('general.save_error'));
        });
    }
  };

  const addMessages = (messages, flag, sources) => {
    messages.forEach((message, index) => {
      if (index !== messages.length - 1) {
        processMessage(message, flag);
      } else {
        processMessage(message, flag, sources);
      }
    });
  };

  const processMessage = (message, flag, sources = null) => {
    const { text, references, char_count } = message;

    if (references?.length > 0) {
      const charCount = char_count || text.length;
      referencesMessages(text, references, charCount, flag, sources);
    } else {
      inboundMessage(text, flag, sources);
    }
  };

  const referencesMessages = (text, references, charCount, flag, sources) => {
    if (references.length > 1 || charCount > 1024) {
      inboundMessage(text, flag);
      references.forEach((reference, index) => {
        if (index !== references.length - 1) {
          inboundMessage(reference, flag);
        } else {
          inboundMessage(reference, flag, sources);
        }
      });
    } else {
      const message = { ...references[0], text };
      inboundMessage(message, flag, sources);
    }
  };

  const inboundMessage = (message, flag, sources = null) => {
    const identifier = uuid();

    const messageToAdd = {
      message_identifier: identifier,
      content_text: typeof message === 'string' ? message : message.text,
      content_audio: null,
      direction: 'inbound',
      status: 'delivered',
      created_time: new Date(),
      flag,
      sources,
      id: identifier
    };

    if (typeof message === 'object' && message.url && message.content_type) {
      messageToAdd.content_file = message.url;
      messageToAdd.content_file_type = message.content_type;
    }

    addMessage(messageToAdd);
  };

  const onPressEnter = (event) => {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        return;
      }
      event.preventDefault();
      sendMessage(null, null);
    }
  };

  const handleAudioRecording = (audio) => {
    sendMessage(audio, null);
  };

  const generateNewSession = () => {
    const newSessionId = `test-${uuid()}`;
    setSessionId(newSessionId);
  };

  useEffect(() => {
    generateNewSession();
  }, []);

  const handleRefresh = () => {
    generateNewSession();

    setMessages([]);
    setText('');
    setAudioBlob(null);
    setSelectedFile(null);
    showtoast(t('general.refresh_success'));
  };

  return (
    <div className="tw-mr-8 tw-border tw-border-gray-200 tw-shadow-lg tw-rounded-xl tw-overflow-hidden tw-w-[641px]">
      <div className="tw-flex tw-flex-col tw-border-b tw-border-gray-200">
        <div className="tw-items-center tw-bg-white tw-flex tw-px-7 tw-py-5 tw-justify-between tw-w-full">
          <div className="tw-flex tw-items-center">
            <div className="tw-bg-blue-500 tw-rounded-full tw-w-10 tw-h-10 tw-flex tw-items-center tw-justify-center">
              <img src={RobotIcon} alt="Chatbot" className="tw-w-6" />
            </div>
            <span className="tw-font-semibold tw-text-sm tw-text-gray-950 tw-ml-2">
              {t('general.main_title')}
            </span>
          </div>
          <div className="tw-flex text-gray-light fz-12">
            <div data-tooltip-id="infoRefresh">
              <ButtonMC
                text={t('general.refresh')}
                onClick={handleRefresh}
              />
            </div>
            <ReactTooltip id="infoRefresh" place="top" backgroundColor="#3C4348" className="tw-text-left tw-rounded-xl">
              <span>{t('general.refresh_tooltip')}</span>
              <br />
              <span>{t('general.refresh_tooltip2')}</span>
            </ReactTooltip>
          </div>
        </div>
      </div>

      <div ref={chatContainerRef} className="tw-flex tw-flex-col tw-h-[50vh] tw-overflow-y-auto" style={{ backgroundColor: "#F9F7F5" }}>
        {!messages.length && (
          <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-w-full">
            <div className="tw-bg-blue-500 tw-rounded-full tw-w-[70px] tw-h-[70px] tw-flex tw-items-center tw-justify-center tw-mt-9">
              <img src={RobotIcon} alt="Chatbot" className="tw-w-[45px]" />
            </div>
            <span className="tw-text-center tw-mt-[36px] tw-text-base tw-font-semibold tw-px-8 tw-text-gray-950">
              {t('general.chat.empty')}
            </span>
          </div>
        )}
        {!!messages.length && <AiChatMessages messages={messages} scrollContainerRef={chatContainerRef} />}
      </div>

      <div className="tw-tw-flex tw-border-t tw-border-gray-200 tw-p-6">
        <div className="text-input tw-flex tw-border tw-border-gray-200 tw-rounded-xl tw-pl-4 tw-pt-[2px] tw-pb-1 tw-w-full tw-mb-0 tw-min-h-12 tw-justify-between tw-items-center tw-bg-gray-50">
          <div className="tw-flex tw-mr-auto tw-p-[2px] tw-flex-grow" {...getRootProps()}>
            <input {...getInputProps()} />
            {selectedFile && (
              <div className="tw-flex tw-items-center tw-mr-[2px]">
                <span className="tw-inline-block tw-px-[0.4em] tw-py-[0.25em] tw-text-xs tw-font-semibold tw-leading-none tw-text-center tw-whitespace-nowrap tw-align-baseline tw-rounded tw-transition-colors tw-duration-150 tw-ease-in-out tw-text-white" style={{ backgroundColor: "#17a2b8" }}>
                  {selectedFile.type.startsWith('image/') ? 'Imagen: ' : 'Archivo: '}
                  {selectedFile.name}
                </span>
                <div className="tw-w-8 tw-h-8 tw-flex tw-items-center tw-justify-center">
                  <IconMC
                    name="close-circle-outline"
                    className="tw-text-red-500"
                    size="xl"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedFile(null);
                    }}
                  />
                </div>
              </div>
            )}
            <textarea
              type="text"
              value={text}
              onChange={(e) => setText(e.target.value)}
              onKeyDown={onPressEnter}
              placeholder={
                isDragActive
                  ? t('general.chat.drag_file')
                  : (selectedFile ? t('general.chat.caption_placeholder') : t('general.chat.caption_placeholder_1'))
              }
              className={`tw-min-h-[48px] tw-w-full tw-flex tw-content-center tw-mt-0 tw-py-[15px] tw-h-[53px] tw-overflow-y-auto tw-whitespace-pre-wrap ${isDragActive ? 'drag-active' : ''}`}
              style={{
                border: 'none',
                outline: 'none',
                resize: 'none',
                backgroundColor: isDragActive ? '#f0f8ff' : 'transparent'
              }}
            />
          </div>
          {!text && !selectedFile && (
            <div className="tw-flex tw-p-2">
              <RecordingAudioPanelMC sendAudio={handleAudioRecording} />
              <div className="tw-cursor-pointer tw-ml-[2px]">
                <ButtonIconMC
                  icon="attach-fill"
                  className="tw-flex tw-items-center tw-bg-blue-500 tw-w-8 tw-h-8 tw-p-2 tw-rounded-[8px]"
                  iconClassName="tw-text-white"
                  onClick={() => {
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = '.jpeg,.jpg,.png,.webp,.avif,.pdf';
                    fileInput.onchange = (e) => {
                      if (e.target.files && e.target.files[0]) {
                        const file = e.target.files[0];

                        if (file.size > FILE_SIZE_LIMIT) {
                          showtoast(t('general.file_too_large'));
                          return;
                        }

                        onDrop([file]);
                      }
                    };
                    fileInput.click();
                  }}
                />
              </div>
            </div>
          )}

          {!recordingAudio && (
            <ButtonIconMC
              icon="paper-plane-outline"
              className="tw-flex tw-items-center tw-bg-blue-500 tw-w-8 tw-h-8 tw-p-2 tw-mr-4 tw-rounded-[8px]"
              iconClassName="tw-text-white"
              onClick={() => sendMessage(null, null)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default AiChat;
