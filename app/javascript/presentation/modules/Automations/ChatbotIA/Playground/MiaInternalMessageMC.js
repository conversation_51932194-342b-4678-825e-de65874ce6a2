import React from 'react';
import { useTranslation } from 'react-i18next';
import IconMC from '../../../../components/IconMC';

const MiaInternalMessageMC = ({
  messageKey,
  message
}) => {
  const { t } = useTranslation('ChatbotIA');

  const colors = () => {
    if (message.flag === 'buy') {
      return {
        bg: 'tw-bg-seagull-50',
        border: 'tw-border-seagull-300',
        icon: 'material-symbols-work-outline',
        iconBg: 'tw-bg-seagull-500',
        textColor: 'tw-text-seagull-700'
      };
    }

    return {
      bg: 'tw-bg-violet-50',
      border: 'tw-border-violet-300',
      icon: 'hand',
      iconBg: 'tw-bg-violet-500',
      textColor: 'tw-text-violet-700'
    };
  };

  const {
    bg, border, icon, iconBg, textColor
  } = colors();

  return (
    <div className={`tw-flex tw-flex-col tw-p-4 tw-gap-2 tw-rounded-lg ${bg}`}>
      <div className={`tw-flex tw-items-center tw-py-1 tw-px-2 tw-gap-1 tw-bg-white tw-border tw-rounded-lg ${border}`}>
        <IconMC name="alert-circle-outline" className="tw-text-gray-500" size="base" />
        <span className="tw-font-medium tw-text-gray-500 tw-text-3xs">{t('config.internal_message')}</span>
      </div>

      <div className="tw-flex tw-gap-2 tw-items-center">
        <div className={`tw-flex tw-items-center tw-justify-center tw-w-[40px] tw-h-[40px] tw-rounded-full ${iconBg}`}>
          <IconMC
            name={icon}
            className="tw-text-white"
          />
        </div>
        <div className={`tw-flex tw-flex-col ${textColor}`}>
          <span className="tw-font-bold tw-text-xs">{t(messageKey)}</span>
          <span className="tw-text-xs">{t('config.internal_message_turn')}</span>
        </div>
      </div>
    </div>
  );
};

export default MiaInternalMessageMC;
