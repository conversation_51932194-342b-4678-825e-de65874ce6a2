/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import ModalSection from '../ModalSection';
import RetailerModel from '../../../../../components/Retailers/RetailerModel';
import PlatformModel from '../../../../models/PlatformModel';
import PlatformsOptions from './PlatformsOptions';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import { getRetailerInfo } from '../../../../../actions/retailerUsersActions';
import ToggleSwitchMC from '../../../../components/ToggleSwitchMC';

const PlatformsModal = () => {
  const dispatch = useDispatch();

  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);
  const { t } = useTranslation('ChatbotIA');

  const [saving, setSaving] = useState(false);
  const [selectedPlatforms, setSelectedPlatforms] = useState([]);
  const [platformError, setPlatformError] = useState(false);
  const [chatbotEnabled, setChatbotEnabled] = useState(retailer_info.mia_chatbot_active || false);
  const [platformOptions, setPlatformOptions] = useState([]);
  const [loadingPlatforms, setLoadingPlatforms] = useState(false);

  useEffect(() => {
    setChatbotEnabled(retailer_info.mia_chatbot_active);

    if (retailer_info.retailer_mia_platforms && retailer_info.retailer_mia_platforms.length > 0) {
      const relatedPlatforms = retailer_info.retailer_mia_platforms.map((platform) => platform.mia_platform_id);
      setSelectedPlatforms(relatedPlatforms);
    }
  }, [retailer_info.mia_chatbot_active, retailer_info.retailer_mia_platforms]);

  useEffect(() => {
    loadPlatforms();
  }, []);

  const refreshRetailerInfo = () => {
    dispatch(getRetailerInfo());
  };

  const togglePlatformsModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const loadPlatforms = () => {
    setLoadingPlatforms(true);
    PlatformModel.get()
      .then((res) => {
        const { platforms } = res;

        const combinedOptions = platforms.map((platform) => ({
          key: platform.id,
          label: platform.name,
          description: platform.name,
          icon: platform.svg_content || '',
          hasCheckbox: platform.active,
          enabled: platform.active,
          checked: platform.active
        }));
        setLoadingPlatforms(false);
        setPlatformOptions(combinedOptions);
      })
      .catch((err) => {
        setLoadingPlatforms(false);
        showtoast(err.error || t('config.error_loading'));
      });
  };

  const onSubmit = () => {
    if (chatbotEnabled && selectedPlatforms.length === 0) {
      setPlatformError(true);
      return;
    }

    setPlatformError(false);
    setSaving(true);

    const platformsToUpdate = retailer_info.retailer_mia_platforms.map((platform) => {
      const isSelected = selectedPlatforms.includes(platform.mia_platform_id);
      return {
        id: platform.id,
        _destroy: !isSelected
      };
    });

    const platformsToAdd = selectedPlatforms
      .filter((platformId) => !retailer_info.retailer_mia_platforms.some((p) => p.mia_platform_id === platformId))
      .map((platformId) => ({ mia_platform_id: platformId }));

    const params = {
      retailer: {
        mia_chatbot_active: chatbotEnabled,
        retailer_mia_platforms_attributes: platformsToUpdate.concat(platformsToAdd)
      }
    };

    RetailerModel.toggleOrAddMiaPlatform(params)
      .then(() => {
        setTimeout(() => {
          showtoast(t('config.save_success'));
          setSaving(false);
          togglePlatformsModal(null);
          refreshRetailerInfo();
        }, 1000);
      })
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
        togglePlatformsModal(null);
      });
  };

  const handlePlatformsChange = (platformId) => {
    setPlatformError(false);

    setSelectedPlatforms((prevSelected) => {
      if (prevSelected.includes(platformId)) {
        return prevSelected.filter((id) => id !== platformId);
      }

      return [...prevSelected, platformId];
    });
  };

  const toggleChatbot = () => {
    const newValue = !chatbotEnabled;

    setChatbotEnabled(newValue);
    if (!newValue) {
      setSelectedPlatforms([]);
      setPlatformError(false);
    }
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.platforms.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <div className="tw-flex tw-flex-col tw-gap-6">
          <div className="tw-flex tw-flex-col">
            <ToggleSwitchMC
              isOn={chatbotEnabled}
              handleToggle={toggleChatbot}
              label={t('config.platforms.section_1_title')}
              labelClassName="!tw-font-semibold"
            />
            <span className="tw-text-gray-950 tw-text-xs">{t('config.platforms.section_1_description')}</span>
          </div>

          <ModalSection
            title={t('config.platforms.section_2_title')}
            description={t('config.platforms.section_2_description')}
            afterContent={(
              <PlatformsOptions
                selectedPlatforms={selectedPlatforms}
                selectPlatforms={handlePlatformsChange}
                options={platformOptions}
                loadingPlatforms={loadingPlatforms}
              />
            )}
          />
        </div>
      </RightModalLayout>
    </div>
  );
};

export default PlatformsModal;
