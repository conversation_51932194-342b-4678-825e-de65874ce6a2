import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

import ModalSection from '../ModalSection';
import InstructionsModelMC from '../../../../models/InstructionsModelMC';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import QuillComponent from '../QuillComponent';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const InstructionsModal = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const [instructions, setInstructions] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadInstructions();
  }, []);

  const toggleInstructionsModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const loadInstructions = () => {
    InstructionsModelMC.get().then((res) => {
      setInstructions(res.response);
      setSaving(false);
    }).catch((err) => {
      setSaving(false);
      showtoast(err.error || t('config.error_loading'));
    });
  };

  const onSubmit = () => {
    const params = { prompt: instructions };
    setSaving(true);

    InstructionsModelMC.update(params, csrfToken).then(() => {
      setTimeout(() => {
        showtoast(t('config.save_success'));
        setSaving(false);
        toggleInstructionsModal();
      }, 1000);
    }).catch((err) => {
      setSaving(false);
      showtoast(err.error || t('config.save_error'));
    });
  };

  const updateInfo = (value) => {
    setInstructions(value);
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.instructions.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <ModalSection
          title={t('config.instructions.section_1_title')}
          description={t('config.instructions.section_1_description')}
          afterContent={(
            <QuillComponent
              name="instructions"
              value={instructions || ''}
              onChange={(value) => { updateInfo(value); }}
            />
          )}
        />
      </RightModalLayout>
    </div>
  );
};

export default InstructionsModal;
