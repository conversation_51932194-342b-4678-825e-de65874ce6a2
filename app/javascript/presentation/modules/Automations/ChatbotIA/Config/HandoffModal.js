import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';

import ModalSection from '../ModalSection';
import SectionModel from '../../../../models/SectionModel';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import InputComponent from '../../../../components/InputMC';
import FormProviderMC from '../../../../components/FormProviderMC';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const HandoffModal = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const [handoffMessage, setHandoffMessage] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadHandoffMessage();
  }, []);

  const toggleHandoffModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const loadHandoffMessage = () => {
    SectionModel.getByType('handoff')
      .then((res) => {
        setHandoffMessage(res.handoff || '');
        setSaving(false);
      })
      .catch((err) => {
        setSaving(false);
        showtoast(err.error || t('config.error_loading'));
      });
  };

  const methods = useForm();

  const updateInfo = (value) => {
    setHandoffMessage(value);
  };

  const onSubmit = () => {
    const params = { type: 'handoff', handoff: handoffMessage };
    setSaving(true);

    SectionModel.update(params, csrfToken)
      .then(() => {
        setTimeout(() => {
          showtoast(t('config.save_success'));
          setSaving(false);
          toggleHandoffModal();
        }, 1000);
      })
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
        toggleHandoffModal();
      });
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.handoff.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <FormProviderMC methods={methods}>
          <ModalSection
            title={t('config.handoff.section_1_title')}
            description={t('config.handoff.section_1_description')}
            afterContent={(
              <InputComponent
                name="handoff_message"
                value={handoffMessage || ''}
                label={t('config.handoff.message')}
                placeholder={t('config.handoff.example') || ''}
                onChange={(_, value) => { updateInfo(value); }}
                className="tw-min-h-[120px] tw-resize-none !tw-placeholder-gray-500 tw-h-auto tw-mt-4"
                textarea
                useFloatingLabel
              />
            )}
          />
        </FormProviderMC>
      </RightModalLayout>
    </div>
  );
};

export default HandoffModal;
