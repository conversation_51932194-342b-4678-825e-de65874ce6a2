import React from "react";
import NextFeatureLabel from "../NextFeatureLabel";
import CheckBoxMC from "../../../../components/CheckBoxMC";
import LoadingMC from "../../../../components/LoadingMC";

const PlatformsOptions = ({
  selectedPlatforms,
  selectPlatforms,
  options,
  loadingPlatforms
}) => {
  const borderClass = (key) => {
    if (selectedPlatforms.includes(key)) return 'tw-border-blue-300';
    return 'tw-border-gray-300';
  };

  return (
    <div className="tw-flex tw-flex-col tw-gap-4 tw-mt-4">
      {options.map((option) => (
        <div
          key={`activation-option-${option.key}`}
          className={`md:tw-w-full tw-flex tw-flex-row tw-items-center tw-rounded-xl tw-py-2.5 tw-mt-[5px] tw-border tw-px-[15px]
            ${option.enabled ? 'tw-cursor-pointer' : 'tw-cursor-not-allowed'}
            ${borderClass(option.key)}`}
          onClick={() => option.enabled && selectPlatforms(option.key)}
          style={{ cursor: option.enabled ? 'tw-cursor-pointer' : 'tw-cursor-not-allowed' }}
        >
          <div className="tw-flex tw-w-full tw-justify-between">
            <div className="tw-flex">
              <div className="tw-self-center">
                <img src={`data:image/svg+xml;base64,${btoa(option.icon)}`} alt={`${option.label} icon`} className="tw-w-31" />
              </div>
              <div className="tw-self-center">
                <span className="tw-inline-block tw-text-sm tw-ml-[2px]">
                  {option.label}
                </span>
              </div>
            </div>
            <div className="tw-flex tw-items-center">
              {option.hasCheckbox ? (
                <CheckBoxMC
                  checked={selectedPlatforms.includes(option.key)}
                  onChange={() => {}}
                  className="tw-w-5 tw-h-5"
                />
              ) : (
                <NextFeatureLabel />
              )}
            </div>
          </div>
        </div>
      ))}
      {loadingPlatforms && (
        <LoadingMC loading={loadingPlatforms} />
      )}
    </div>
  );
};

export default PlatformsOptions;
