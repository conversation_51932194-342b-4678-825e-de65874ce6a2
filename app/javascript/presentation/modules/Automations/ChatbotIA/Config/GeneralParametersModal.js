import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

import ModalSection from '../ModalSection';

import SectionModel from '../../../../models/SectionModel';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import QuillComponent from '../QuillComponent';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const GeneralParametersModal = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');
  const [toneStyleAndResponseFormat, setToneStyleAndResponseFormat] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadToneStyleAndResponseFormat();
  }, []);

  const toggleGeneralParametersModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const loadToneStyleAndResponseFormat = () => {
    SectionModel.getByType('tone_style_and_response_format')
      .then((res) => {
        setToneStyleAndResponseFormat(res.tone_style_and_response_format || '');
        setSaving(false);
      })
      .catch((err) => {
        setSaving(false);
        showtoast(err.error || t('config.error_loading'));
      });
  };

  const updateInfo = (value) => {
    setToneStyleAndResponseFormat(value);
  };

  const onSubmit = () => {
    const params = {
      type: 'tone_style_and_response_format',
      tone_style_and_response_format: toneStyleAndResponseFormat
    };
    setSaving(true);

    SectionModel.update(params, csrfToken)
      .then(() => {
        setTimeout(() => {
          showtoast(t('config.save_success'));
          setSaving(false);
          toggleGeneralParametersModal();
        }, 1000);
      })
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
        toggleGeneralParametersModal();
      });
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.general_parameters.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <ModalSection
          title={t('config.general_parameters.section_1_title')}
          description={t('config.general_parameters.section_1_description')}
          afterContent={(
            <QuillComponent
              name="tone_style_and_response_format"
              value={toneStyleAndResponseFormat || ''}
              onChange={(value) => { updateInfo(value); }}
            />
          )}
        />
      </RightModalLayout>
    </div>
  );
};

export default GeneralParametersModal;
