import React, {
  forwardRef, useEffect, useState, useRef
} from 'react';
import ReactQuill from 'react-quill';
import { isEmpty } from 'lodash';
import 'react-quill/dist/quill.snow.css';
import ImproveGuideButton from './ImproveGuideButton';

const CustomToolbar = ({ improveGuideButtonFunction, disabledImproveGuideButton }) => (
  <div id="toolbar" className="tw-flex">
    <span className="tw-flex tw-inline-block tw-align-middle">
      <select className="ql-header"></select>
    </span>
    <span className="tw-flex tw-inline-block tw-align-middle">
      <button className="ql-bold" />
      <button className="ql-italic" />
      <button className="ql-underline" />
    </span>
    <span className="tw-flex tw-inline-block tw-align-middle">
      <button className="ql-list" value="bullet" />
      <button className="ql-list" value="ordered" />
    </span>
    <div className="tw-inline-block tw-w-px tw-h-6 tw-bg-[#ccc] tw-mr-2.5"></div>
    <span className="tw-flex tw-inline-block tw-align-middle">
      <ImproveGuideButton
        onClick={improveGuideButtonFunction}
        disabledImproveGuideButton={disabledImproveGuideButton}
      />
    </span>
  </div>
);

const GuideQuill = forwardRef(({ value, onChange, improveGuideButtonFunction, error }, ref) => {
  const [disabledImproveGuideButton, setDisabledImproveGuideButton] = useState(true);
  const editorRef = useRef(null);

  useEffect(() => {
    if (editorRef.current) {
      const editor = editorRef.current.getEditor();
      const editorText = editor.getText().trim();
      setDisabledImproveGuideButton(isEmpty(editorText));

      if (ref) {
        ref.current = {
          getEditor: () => editor,
          getText: () => editorText
        };
      }
    }
  }, [value, ref]);

  const modules = {
    toolbar: {
      container: "#toolbar"
    }
  };

  return (
    <div className="tw-wrapper">
      <CustomToolbar
        improveGuideButtonFunction={improveGuideButtonFunction}
        disabledImproveGuideButton={disabledImproveGuideButton}
      />
      <ReactQuill
        ref={editorRef}
        value={value || ''}
        onChange={onChange}
        modules={modules}
        theme="snow"
        style={{ height: '300px' }}
      />
      {error && <span className="tw-text-red-500 tw-text-xs">{error}</span>}
    </div>
  );
});

GuideQuill.displayName = 'GuideQuill';

export default GuideQuill;
