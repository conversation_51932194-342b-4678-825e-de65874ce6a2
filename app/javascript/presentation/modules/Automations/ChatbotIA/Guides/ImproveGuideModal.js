import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'lodash';

import { useDispatch } from 'react-redux';

import GuideModel from '../../../../models/GuideModel';

import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import CustomIcon from '../../../../components/CustomIconMC';
import LoadingMC from '../../../../components/LoadingMC';

const ImproveGuideModal = ({ updateInfo, guide }) => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');
  const [saving, setSaving] = useState(false);
  const [newInstructions, setNewInstructions] = useState("");
  const [retryCount, setRetryCount] = useState(0);
  const [fetchingImproveGuide, setFetchingImproveGuide] = useState(true);

  const maxRetries = 1;

  useEffect(() => {
    GuideModel.optimize(guide.instructions).then((res) => {
      setNewInstructions(res.instructions);
      setFetchingImproveGuide(false);
    }).catch((err) => {
      setFetchingImproveGuide(false);

      if (retryCount < maxRetries) {
        setTimeout(() => {
          showtoast(t("guides.optimize_modal.fetching_retry_error"));
          setRetryCount(retryCount + 1);
          setFetchingImproveGuide(true);
        }, 500);
      } else {
        showtoast(t("guides.optimize_modal.fetching_error"));
      }
    });
  }, [retryCount]);

  const toggleImproveGuideModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const updateInstructions = () => {
    if (!isEmpty(newInstructions)) updateInfo('instructions', newInstructions);

    toggleImproveGuideModal(null);
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t("guides.optimize_modal.title")}
        submitLabel={t('buttonTexts.accept')}
        submitAction={updateInstructions}
        cancelLabel={t('buttonTexts.cancel')}
        cancelAction={toggleImproveGuideModal}
        submitDisabled={saving}
      >
        <div className="tw-w-full">
          <div className="tw-flex tw-flex-col tw-align-center tw-text-gray-950 tex-text-xs">
            <div className="tw-w-full">
              <span className="tw-font-bold tw-text-gray-950 tw-text-sm">
                {t(`guides.optimize_modal.${fetchingImproveGuide ? 'waiting_subtitle' : 'complete_subtitle'}`)}
              </span>
            </div>

            <div className="tw-w-full tw-leading-[16px] tw-pb-2.5">
              {fetchingImproveGuide ? '' : t(`guides.optimize_modal.complete_message`)}
            </div>

            <div className="tw-w-full tw-leading-[16px] tw-rounded-lg tw-my-2.5 tw-bg-orange-50">
              <div className="tw-flex tw-flex-row tw-p-2">
                <div className="tw-mr-2">
                  <CustomIcon name="info-outline" className="tw-text-[#BB7C32]" />
                </div>
                <span className="tw-text-[#BB7C32]">{t("guides.optimize_modal.warning_message")}</span>
              </div>
            </div>

            {fetchingImproveGuide ? (
              <div className="tw-flex tw-items-center tw-justify-center tw-h-[50vh]">
                <p>
                  <LoadingMC loading />
                </p>
              </div>
            ) : (
              <div className="tw-flex tw-flex-col">
                <div className="tw-w-full">
                  <div className="tw-mb-4 tw-bg-gray-50 tw-rounded-lg tw-px-3 tw-pt-[2px] tw-pb-1 tw-overflow-y-auto tw-max-h-[710px]">
                    <div className="tw-my-[15px]" dangerouslySetInnerHTML={{ __html: newInstructions }}>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </RightModalLayout>
    </div>
  );
};

export default ImproveGuideModal;
