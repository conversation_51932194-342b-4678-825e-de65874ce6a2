/* eslint-disable import/no-unresolved */
import React, { useEffect, useRef, useState } from 'react';
import Dropzone from "react-dropzone";
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import DocumentPdfOutline16 from 'images/document-pdf-outline-16.svg';
import { useDispatch } from 'react-redux';
import fileUtils from '../../../../../../util/fileUtils';

import PreviewPdfInstruction from "../PreviewPdfInstruction";
import { SIDEBAR_ACTIONS } from '../../../../../../constants/actionsConstants';
import RightModalLayout from '../../../../../layouts/Modal/RightModalLayout';
import CustomIcon from '../../../../../components/CustomIconMC';
import CharCounterMC from '../../../../../components/CharCounterMC';

const FILE_SIZE_LIMIT = 50 * 1024 * 1024;

const SpecificInstructionsModal = ({ editingIndex, specificInstructions, setSpecificInstructions }) => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');
  const textareaRef = useRef(null);

  const { register, handleSubmit, setError, clearErrors, formState: { errors } } = useForm();
  const [instructionId, setInstructionId] = useState(null);
  const [instructionValue, setInstructionValue] = useState('');
  const [instructionLimitReached, setInstructionLimitReached] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedOption, setSelectedOption] = useState('');
  const [files, setFiles] = useState([]);
  const maxFiles = 5;
  const allowedImageTypes = ['image/jpeg', 'image/png'];
  const allowedDocTypes = ['application/pdf'];
  const maxChars = 300;

  useEffect(() => {
    if (editingIndex !== null) {
      const instructionToEdit = specificInstructions[editingIndex];

      if (instructionToEdit) {
        setInstructionId(instructionToEdit.instruction_id || null);
        setInstructionValue(instructionToEdit.instruction || '');
        setSelectedOption(instructionToEdit.instruction_type || '');
        setFiles(instructionToEdit.files || []);
        setCurrentStep(2);
      }
    }
  }, [editingIndex, specificInstructions]);

  const toggleSpecificInstructionsModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    setFiles([]);
    setCurrentStep(2);
  };

  const handleBack = () => {
    setCurrentStep(1);
    setSelectedOption('');
    setFiles([]);
  };

  const handleFileDrop = (acceptedFiles) => {
    const isImage = selectedOption === 'image';

    const validFiles = acceptedFiles.filter(file => {
      const isValidType = isImage ? allowedImageTypes.includes(file.type) : allowedDocTypes.includes(file.type);
      return isValidType;
    });

    const sizeValidFiles = validFiles.filter(file => {
      if (file.size > FILE_SIZE_LIMIT) {
        showtoast(t('general.file_too_large'));
        return false;
      }
      return true;
    });

    const remainingSlots = maxFiles - files.length;
    const filesToAdd = sizeValidFiles.slice(0, remainingSlots);

    setFiles((prevFiles) => {
      const updatedFiles = [...prevFiles, ...filesToAdd];
      if (updatedFiles.length > 0) {
        clearErrors('files');
      }
      return updatedFiles;
    });
  };

  const handleFileRemove = (index) => {
    clearErrors('files');
    setFiles(files.filter((_, i) => i !== index));
  };

  const handleInstructionChange = (e) => {
    const { value } = e.target;
    setInstructionValue(value);
    setInstructionLimitReached(value.length > maxChars);
  };

  const onSubmit = () => {
    if (files.length === 0) {
      setError('files', { type: 'manual', message: t('guides.empty_file_error') });
      return;
    }

    const currentFileNames = files.map(file =>
      file instanceof File ? file.name : file.filename
    );

    const hasDuplicatesInCurrent = new Set(currentFileNames).size !== currentFileNames.length;
    if (hasDuplicatesInCurrent) {
      setError('files', { type: 'manual', message: t('guides.duplicate_files_error') });
      return;
    }

    const newInstruction = {
      files: [...files],
      instruction: instructionValue.trim(),
      instruction_type: selectedOption,
      instruction_id: instructionId
    };

    if (editingIndex !== null) {
      setSpecificInstructions((prevInstructions) =>
        prevInstructions.map((instruction, index) =>
          index === editingIndex ? newInstruction : instruction
        )
      );
    } else {
      setSpecificInstructions((prevInstructions) => [...prevInstructions, newInstruction]);
    }

    setInstructionValue('');
    setFiles([]);
    setSelectedOption('');
    toggleSpecificInstructionsModal(null);
  };

  const formatFileSize = (size) => {
    return fileUtils.formatFileSize(size)
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={
          currentStep === 1
            ? t(`guides.specific_instructions_title`)
            : t(`guides.specific_instruction_${selectedOption}_title`)
        }
        cancelLabel={currentStep === 1 ? t('general.cancel_button') : t('general.back_button')}
        cancelAction={currentStep === 1 ? toggleSpecificInstructionsModal : handleBack}
        submitLabel={currentStep !== 1 ? t('general.save_button') : ''}
        submitAction={currentStep !== 1 ? () => handleSubmit(onSubmit)() : null}
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          {currentStep === 1 && (
            <>
              <div className="tw-w-full">
                <p className="tw-text-sm tw-mb-2.5">{t('guides.specific_instructions_description')}</p>
                <p className="tw-font-semibold tw-text-gray-950 tw-text-base">
                  {t('guides.specific_instruction_type_title')}
                </p>
              </div>
              <div className="tw-mt-2.5">
                <div className="tw-w-full tw-flex tw-flex-row tw-rounded-xl tw-py-1.5 tw-px-[15px] tw-mt-2 tw-cursor-pointer tw-border tw-border-gray-200" onClick={() => handleOptionSelect('image')}>
                  <div className="tw-flex tw-mt-[5px] tw-w-[90%] tw-items-left">
                    <div className="tw-pt-2 tw-mr-2">
                      <CustomIcon name="image-outline" className="tw-text-3xl" />
                    </div>
                    <div className="tw-w-5/6">
                      <span className="tw-font-semibold tw-text-gray-950 tw-text-sm !mb-[-10px]">{t(`guides.specific_instruction_image_title`)}</span>
                      <p className="tw-my-[5px] index__desc">{t('guides.specific_instruction_image_subtitle')}</p>
                    </div>
                  </div>
                </div>
                <div className="tw-w-full tw-flex tw-flex-row tw-rounded-xl tw-py-1.5 tw-px-[15px] tw-mt-2 tw-cursor-pointer tw-border tw-border-gray-200" onClick={() => handleOptionSelect('doc')}>
                  <div className="tw-flex tw-mt-[5px] tw-w-[90%] tw-items-left">
                    <div className="tw-pt-2 tw-mr-2">
                      <img className="tw-w-[30px]" src={DocumentPdfOutline16}></img>
                    </div>
                    <div className="tw-w-5/6">
                      <span className="tw-font-semibold tw-text-gray-950 tw-text-sm !mb-[-10px]">{t(`guides.specific_instruction_doc_title`)}</span>
                      <p className="tw-my-[5px] index__desc">{t('guides.specific_instruction_doc_subtitle')}</p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {currentStep === 2 && (
            <div className="tw-mt-1">
              <div className="tw-w-full tw-px-0">
                <span className="tw-font-semibold tw-text-gray-950 tw-text-sm">
                  {t('guides.instruction')}
                </span>
                <p className="tw-text-sm tw-mb-2.5">{t('guides.specific_instructions_step2_description')}</p>
                <div className="tw-bg-[#F7F8FD] tw-rounded-xl tw-pl-4 tw-pt-[2px] tw-pb-1 tw-mb-2 tw-relative">
                  <label htmlFor="instruction" className="tw-text-sm tw-text-blue-500">
                    {t('guides.instruction')}
                  </label>
                  {errors.instruction && <span className="tw-ml-2.5 tw-text-red-500 tw-text-xs">{errors.instruction.message}</span>}
                  <textarea
                    ref={textareaRef}
                    className="mercately-input mercately-input-textarea textarea-counter"
                    style={{ height: 'auto', resize: 'none' }}
                    rows="6"
                    placeholder={t('guides.instruction_placeholder')}
                    {...register("instruction", {
                      required: t('guides.empty_error'),
                      maxLength: { value: 300, message: t('guides.specific_instruction_max_characters_error') },
                      validate: value => value.trim() !== '' || t('guides.empty_error')
                    })}
                    value={instructionValue}
                    onChange={handleInstructionChange}
                  />
                  <CharCounterMC counter={instructionValue.length} limit={maxChars} className="tw-b-0" />
                </div>
              </div>
              <div className="tw-w-full tw-mt-2.5 tw-px-0">
                <label className="tw-font-semibold tw-text-gray-950 tw-text-sm">
                  {selectedOption === 'image'
                    ? t('guides.specific_instructions_image_title2')
                    : t('guides.specific_instructions_doc_title2')}
                </label>
                <p className="tw-text-sm tw-mb-2.5">
                  {selectedOption === 'image'
                    ? t('guides.specific_instruction_image_description_2')
                    : t('guides.specific_instruction_doc_description_2')}
                </p>
                {errors.files && <span className="tw-ml-2.5 tw-text-red-500 tw-text-xs">{errors.files.message}</span>}
                <div className="tw-mt-2.5">
                  {files.map((file, index) => {
                    const isExistingFile = file.url && file.filename;
                    const previewSrc = isExistingFile ? file.url : URL.createObjectURL(file);
                    const fileName = isExistingFile ? file.filename : file.name;

                    return (
                      <div key={index} className="tw-w-full tw-flex tw-flex-row tw-rounded-xl tw-pt-[5px] tw-pl-5 tw-mt-2 tw-cursor-pointer tw-border tw-border-gray-200">
                        <div className="tw-flex tw-w-full tw-items-left">
                          <div className="tw-pt-2.5 tw-mr-2">
                            {selectedOption === 'image' ? (
                              <img
                                src={previewSrc}
                                alt={`preview-${index}`}
                                className="tw-w-[40px] tw-h-[40px] tw-object-cover"
                              />
                            ) : (
                              <PreviewPdfInstruction loadedFile={previewSrc} />
                            )}
                          </div>
                          <div className="tw-w-5/6 tw-pt-[2px]">
                            <label className="tw-font-semibold tw-text-gray-950 tw-text-xs !mb-[-10px]">{fileName}</label>
                            <p className="tw-text-xs">{formatFileSize(file.size)}</p>
                          </div>
                          <div className="tw-w-1/12 tw-mt-5">
                            <div onClick={() => handleFileRemove(index)}> 
                              <CustomIcon name="trash-outline" className="tw-text-lg tw-cursor-pointer tw-text-gray-500" />
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {files.length < maxFiles && (
                  <Dropzone
                    onDrop={(acceptedFiles) => {
                      handleFileDrop(acceptedFiles);
                    }}
                    accept={
                      selectedOption === 'image'
                        ? { 'image/jpeg': [], 'image/png': [] }
                        : { 'application/pdf': [] }
                    }
                    maxFiles={5}
                  >
                    {({ getRootProps, getInputProps }) => (
                      <section>
                        <div
                          {...getRootProps()}
                          className="tw-flex tw-h-[130px] tw-bg-white tw-border tw-border-dashed tw-border-[#B2B3BD] tw-rounded-xl tw-items-center tw-justify-center tw-text-[#B2B3BD] tw-py-1.5 tw-mt-2 tw-cursor-pointer tw-border-gray-200"
                        >
                          <input {...getInputProps()} />
                          <div className="tw-flex tw-flex-col tw-justify-center">
                            <span className="tw-flex tw-justify-center">
                              <CustomIcon name="attach-fill" className="tw-text-2xl tw-text-gray-400" />
                            </span>
                            <p className="tw-text-gray-400 tw-text-sm">
                              {t(`guides.attachment_click_${selectedOption}_label`)}
                              <span className="tw-text-blue-500">{t(`guides.attachment_click_here_label`)}</span>
                            </p>
                          </div>
                        </div>
                      </section>
                    )}
                  </Dropzone>
                )}
              </div>
            </div>
          )}
        </form>
      </RightModalLayout>
    </div>
  );
};

export default SpecificInstructionsModal;
