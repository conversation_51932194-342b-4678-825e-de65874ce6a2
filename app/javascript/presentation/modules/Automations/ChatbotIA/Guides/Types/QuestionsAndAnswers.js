import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import pdfIcon from 'images/new_design/pdf.svg';
import imageIcon from 'images/new_design/image.svg';

import FilePreviewItem from '../../../../../../components/shared/FilePreviewItem';
import CustomIconMC from '../../../../../components/CustomIconMC';
import InputComponent from '../../../../../components/InputMC';
import FormProviderMC from '../../../../../components/FormProviderMC';
import ButtonMC from '../../../../../components/ButtonMC';
import CharCounterMC from '../../../../../components/CharCounterMC';

const FILE_SIZE_LIMIT = 50 * 1024 * 1024;

const QuestionsAndAnswers = ({
  questions,
  setQuestions,
  attachedFiles,
  setAttachedFiles,
  errors,
  setErrors
}) => {
  const { t } = useTranslation('ChatbotIA');
  const [openPopupId, setOpenPopupId] = useState(true);
  const popupRef = useRef(null);
  const methods = useForm();

  const handleQuestionChange = (id, field, value) => {
    setQuestions(questions.map((q) => (q.id === id ? { ...q, [field]: value } : q)));
  };

  const addQuestion = () => {
    const newId = questions.length + 1;
    setQuestions([...questions, { id: newId, question: '', answer: '' }]);

    if (errors.qa) {
      setErrors((prevErrors) => {
        const newErrors = { ...prevErrors };
        delete newErrors.qa;
        return newErrors;
      });
    }
  };

  const removeQuestion = (id) => {
    if (attachedFiles[id]) {
      setAttachedFiles((prev) => {
        const newFiles = { ...prev };
        delete newFiles[id];
        return newFiles;
      });
    }

    setQuestions(questions.filter(q => q.id !== id));
  };

  const handlePaperclipClick = (id, e) => {
    e.preventDefault();
    e.stopPropagation();
    setOpenPopupId(openPopupId === id ? null : id);
  };

  const handleClickOutside = (e) => {
    if (!e.target.closest('.attachment-area')) {
      setOpenPopupId(null);
    }
  };

  const handleFileSelected = (file) => {
    if (file) {
      if (file.size > FILE_SIZE_LIMIT) {
        showtoast(t('general.file_too_large'));
        return;
      }

      setAttachedFiles(prev => ({
        ...prev,
        [openPopupId]: file
      }));
      setOpenPopupId(null);
    }
  };

  const removeFile = (questionId) => {
    setAttachedFiles(prev => {
      const newFiles = { ...prev };
      delete newFiles[questionId];
      return newFiles;
    });
  };

  const handleFileUpload = (type, itemId) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = type === 'document' ? '.pdf,.doc,.docx,.txt,.rtf' : 'image/*';

    input.onchange = (e) => {
      handleFileSelected(e.target.files[0], itemId);
    };

    input.click();
  };

  return (
    <div onClick={handleClickOutside}>
      <div className="tw-w-full tw-px-0 tw-text-gray-950">
        <span className="tw-font-semibold tw-text-sm">{t('guides.questions_and_answers.title')}</span>
        <p className="tw-text-xs tw-font-regular tw-mb-2">{t('guides.questions_and_answers.description')}</p>
      </div>

      <FormProviderMC methods={methods}>
        {questions.map((item) => (
          <div key={item.id} className="tw-flex tw-flex-col tw-text-sm tw-mb-2.5 tw-border tw-rounded-xl tw-px-5 tw-py-[19px]">
            <div className="tw-flex tw-justify-end">
              <div className="tw-relative">
                <span
                  className="tw-cursor-pointer"
                  onClick={(e) => handlePaperclipClick(item.id, e)}
                >
                  <CustomIconMC name="attach-fill" className="tw-text-2xl tw-pl-[5px]" />
                </span>
                {openPopupId === item.id && (
                  <div ref={popupRef}>
                    <li className="tw-flex tw-flex-col tw-w-[304px] tw-max-w-[304px] tw-py-6 tw-px-4 tw-absolute tw-z-[1000] tw-bg-white tw-text-gra-950 tw-rounded-[16px] -tw-right-[35px] tw-border tw-border-gray-200">
                      <div className="tw-font-semibold tw-text-sm">{t('attachment_popup.title')}</div>
                      <div className="tw-w-full tw-bg-blue-100 tw-rounded-xl tw-py-1 tw-px-2 tw-mt-2">
                        <div className="tw-flex tw-items-center">
                          <CustomIconMC name="info-outline" className="tw-text-blue-500 tw-text-base" size="base" />
                          <span className="tw-font-medium tw-text-3xs tw-ml-[5px] tw-text-blue-500">{t('attachment_popup.single_file')}</span>
                        </div>
                      </div>

                      <div className="tw-flex tw-flex-col tw-mt-1.5">
                        <div
                          className="tw-flex tw-items-center tw-py-2 tw-cursor-pointer tw-transition-colors tw-duration-200 tw-ease-in-out"
                          onClick={() => handleFileUpload('document', item.id)}
                        >
                          <div className="tw-bg-violet-500 tw-flex tw-items-center tw-justify-center tw-h-[32px] tw-w-[32px] tw-mr-[15px] tw-rounded-[8px]">
                            <img src={pdfIcon} alt="PDF" />
                          </div>
                          <span className="tw-font-regular tw-text-sm">{t('attachment_popup.file')}</span>
                        </div>
                        <div
                          className="tw-flex tw-items-center tw-py-2 tw-cursor-pointer tw-transition-colors tw-duration-200 tw-ease-in-out"
                          onClick={() => handleFileUpload('image', item.id)}
                        >
                          <div className="tw-bg-blue-950 tw-flex tw-items-center tw-justify-center tw-h-[32px] tw-w-[32px] tw-mr-[15px] tw-rounded-[8px]">
                            <img src={imageIcon} />
                          </div>
                          <span className="tw-font-regular tw-text-sm">{t('attachment_popup.image')}</span>
                        </div>
                      </div>
                    </li>
                  </div>
                )}
              </div>
              <div className="tw-cursor-pointer" onClick={() => removeQuestion(item.id)}>
                <CustomIconMC name="trash-2-outline" className="tw-text-2xl tw-pl-[5px]" />
              </div>
            </div>

            <div className="tw-w-full tw-px-0 tw-mt-2">
              <div className="tw-relative">
                <InputComponent
                  id={`question-${item.id}`}
                  name="question"
                  value={item.question}
                  placeholder={t('guides.questions_and_answers.question_placeholder')}
                  label={t('guides.questions_and_answers.question')}
                  onChange={(_, value) => handleQuestionChange(item.id, 'question', value)}
                  className="tw-min-h-[80px] tw-resize-none !tw-placeholder-gray-500"
                  customError={errors[`question_${item.id}`]}
                  textarea
                  useFloatingLabel
                  maxLength={200}
                />
                <CharCounterMC counter={item.question.length} limit={200} className="tw-bottom-2" />
              </div>
            </div>

            <div className="tw-w-full tw-px-0 tw-mt-2">
              <div className="tw-relative">
                <InputComponent
                  id={`answer-${item.id}`}
                  name="answer"
                  value={item.answer}
                  placeholder={t('guides.questions_and_answers.answer_placeholder')}
                  label={t('guides.questions_and_answers.answer')}
                  onChange={(_, value) => handleQuestionChange(item.id, 'answer', value)}
                  className="tw-min-h-[80px] tw-resize-none !tw-placeholder-gray-500"
                  customError={errors[`answer_${item.id}`]}
                  textarea
                  useFloatingLabel
                  maxLength={600}
                  rows={6}
                />
                <CharCounterMC counter={item.answer.length} limit={600} className="tw-bottom-2" />
              </div>
            </div>

            {(attachedFiles[item.id] || item.filePreview) && (
              <div className="tw-w-full tw-border tw-border-gray-200 tw-rounded-xl tw-mt-2">
                <FilePreviewItem
                  file={attachedFiles[item.id] || item.filePreview}
                  onRemove={() => removeFile(item.id)}
                />
              </div>
            )}
          </div>
        ))}
      </FormProviderMC>

      <div className="tw-mb-2 tw-flex tw-items-center">
        <ButtonMC
          text={t('guides.questions_and_answers.add_question')}
          variant="SECONDARY"
          onClick={addQuestion}
          outlined
          className="!tw-font-medium"
        />
        {errors.qa && (
          <span className="tw-ml-2.5 tw-text-red-500 tw-text-xs">
            {errors.qa}
          </span>
        )}
      </div>
    </div>
  );
};

export default QuestionsAndAnswers;
