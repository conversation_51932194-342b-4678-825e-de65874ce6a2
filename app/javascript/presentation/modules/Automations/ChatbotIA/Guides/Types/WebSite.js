import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import GuideModel from '../../../../../models/GuideModel';
import InputComponent from '../../../../../components/InputMC';
import FormProviderMC from '../../../../../components/FormProviderMC';
import ButtonMC from '../../../../../components/ButtonMC';
import ProgressBarMC from '../../../../../components/ProgressBarMC';
import CustomIcon from '../../../../../components/CustomIconMC';

const WebSite = ({
  selectedUrls: selectedUrlsProp,
  setSelectedUrls,
  urlToScrape,
  setUrlToScrape,
  errors,
  isSubmitting,
  removedUrlIds,
  setRemovedUrlIds,
  handleSave,
  isEditing: isEditingProp
}) => {
  const { t } = useTranslation('ChatbotIA');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [progress, setProgress] = useState(0);
  const [urlsScraped, setUrlsScraped] = useState([]);
  const [localErrors, setLocalErrors] = useState({});
  const [urlStatuses, setUrlStatuses] = useState({});
  const [showTrainingStatus, setShowTrainingStatus] = useState(false);
  const csrfToken = document.querySelector('[name=csrf-token]').content;
  const [isEditing, setIsEditing] = useState(isEditingProp);
  const methods = useForm();

  useEffect(() => {
    if (selectedUrlsProp?.length > 0 && urlsScraped.length === 0) {
      const urls = selectedUrlsProp.map(item => item.url);
      setUrlsScraped(urls);
    }
  }, [selectedUrlsProp]);

  useEffect(() => {
    if (isSubmitting && selectedUrlsProp) {
      const updatedUrls = selectedUrlsProp.map(urlObj => {
        if (urlObj.selected === true) {
          return { ...urlObj, status: 'training' };
        }
        return urlObj;
      });
      setSelectedUrls(updatedUrls);
      setShowTrainingStatus(true);
    }
  }, [isSubmitting]);

  useEffect(() => {
    if (isLoading) {
      setIsEditing(false);
      setIsLoaded(true);
    }
  }, [isLoading]);

  const handleScrape = async () => {
    if (!urlToScrape || urlToScrape.trim() === '') {
      setLocalErrors({
        urlToScrape: 'Requerido'
      });
      return;
    }

    setLocalErrors({});
    setIsLoading(true);
    setProgress(10);
    setUrlsScraped([]);

    const progressInterval = setInterval(() => {
      setProgress(currentProgress => {
        if (currentProgress >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return currentProgress + 10;
      });
    }, 500);

    GuideModel.scrape({ url: urlToScrape }, csrfToken)
      .then((response) => {
        clearInterval(progressInterval);
        setProgress(100);

        const newUrls = response.urls || [];

        if (selectedUrlsProp?.length > 0) {
          const urlsWithStatus = selectedUrlsProp.filter(url => url.status === 'trained' || url.status === 'failed');
          const urlsWithStatusMarked = urlsWithStatus.map(url => ({
            ...url,
            selected: false
          }));
          const urlsWithStatusList = urlsWithStatus.map(item => item.url);
          const uniqueNewUrls = newUrls.filter(url => !urlsWithStatusList.includes(url));

          setUrlsScraped([...urlsWithStatusList, ...uniqueNewUrls]);
          setSelectedUrls(urlsWithStatusMarked);
        } else {
          setUrlsScraped(newUrls);
          setSelectedUrls([]);
        }
        showtoast(t('guides.website.urls_scraped'));
      })
      .catch((error) => {
        clearInterval(progressInterval);
        if (error.status === 404) {
          showtoast(t('guides.website.not_found'));
        } else {
          showtoast(t('guides.load_error'));
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleUrlSelect = (url) => {
    const existingUrlData = selectedUrlsProp.find(item => item.url === url);
    const isUrlSelected = existingUrlData && existingUrlData.selected !== false;

    let newSelectedUrls;

    if (isUrlSelected) {
      newSelectedUrls = selectedUrlsProp.map(item =>
        item.url === url ? { ...item, selected: false } : item
      );
    } else {
      if (existingUrlData) {
        newSelectedUrls = selectedUrlsProp.map(item =>
          item.url === url ? { ...item, selected: true } : item
        );
      } else {
        newSelectedUrls = [...selectedUrlsProp, { url, selected: true }];
      }
      if (!existingUrlData || !existingUrlData.status) {
        setUrlStatuses(prev => ({
          ...prev,
          [url]: 'training'
        }));
      }
    }

    setSelectedUrls(newSelectedUrls);
  };

  const handleDelete = (urlToDelete) => {
    const urlToRemove = selectedUrlsProp.find(item => item.url === urlToDelete);

    if (urlToRemove?.url_id) {
      setRemovedUrlIds(prevIds => [...prevIds, urlToRemove.url_id]);
    }

    setSelectedUrls(selectedUrlsProp.filter(item => item.url !== urlToDelete));
    setUrlsScraped(prev => prev.filter(url => url !== urlToDelete));
  };

  const handleSelectAll = () => {
    const urlsWithStatus = selectedUrlsProp.filter(url => url.status === 'trained' || url.status === 'failed');
    const urlsWithStatusList = urlsWithStatus.map(item => item.url);

    let newSelectedUrls = [];

    urlsWithStatus.forEach(urlObj => {
      newSelectedUrls.push({ ...urlObj, selected: true });
    });

    urlsScraped.forEach(url => {
      if (!urlsWithStatusList.includes(url)) {
        const existingUrl = selectedUrlsProp.find(item => item.url === url);
        if (existingUrl) {
          newSelectedUrls.push({ ...existingUrl, selected: true });
        } else {
          newSelectedUrls.push({ url, selected: true });
        }
      }
    });

    setSelectedUrls(newSelectedUrls);

    const newStatuses = {};
    urlsScraped.forEach(url => {
      const existingUrl = selectedUrlsProp.find(item => item.url === url);
      if (!existingUrl || !existingUrl.status) {
        newStatuses[url] = 'training';
      }
    });
    setUrlStatuses(prev => ({ ...prev, ...newStatuses }));
  };

  const handleDeselectAll = () => {
    const newSelectedUrls = selectedUrlsProp.map(urlObj => ({
      ...urlObj,
      selected: false
    }));

    setSelectedUrls(newSelectedUrls);
  };

  const renderUrlItem = (url, index) => {
    const urlData = selectedUrlsProp.find(item => item.url === url);
    const isSelected = urlData && urlData.selected !== false;
    const status = urlData?.status;
    const isTrained = status === 'trained';
    const isFailed = status === 'failed';
    const isTraining = isSelected && !isTrained && !isFailed && isSubmitting;
    const getStatusStyle = (_status) => {
      switch (_status) {
        case 'trained':
          return { backgroundColor: '#EEF7F2', color: '#39896A' };
        case 'training':
          return { backgroundColor: '#FDF5EC', color: '#BB7C32' };
        case 'failed':
          return { backgroundColor: '#FAF0F0', color: '#EB686D' };
        default:
          return {};
      }
    };

    const getStatusText = (_status) => {
      switch (_status) {
        case 'trained':
          return t('guides.website.trained');
        case 'training':
          return t('guides.website.training');
        case 'failed':
          return t('guides.website.failed');
        default:
          return '';
      }
    };

    {/* URL Checkbox */ }
    return (
      <div key={index} className="tw-flex tw-mb-2.5 tw-pr-2.5 tw-gap-3">
        {!isEditing && !isSubmitting && (
          <input
            type="checkbox"
            className="tw-align-top tw-h-5 tw-w-5"
            id={`url-${index}`}
            checked={isSelected}
            onChange={() => handleUrlSelect(url)}
          />
        )}
        {(isTrained || isTraining || isFailed) && (
          <span
            className="tw-pl-2 tw-w-[100px] tw-text-center tw-rounded-[5px] tw-text-sm"
            style={{
              ...getStatusStyle(status || (isTraining ? 'training' : null)),
              fontWeight: 500
            }}
          >
            {getStatusText(status || (isTraining ? 'training' : null))}
          </span>
        )}
        <label htmlFor={`url-${index}`} className="tw-mb-0">{url}</label>
        {(isTrained || isFailed) && (
          <div className="tw-cursor-pointer tw-ml-auto tw-pr-2.5" onClick={() => handleDelete(url)}>
            <CustomIcon name="trash-2-outline" className="tw-text-gray-950" />
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Website Header */}
      <div className="tw-w-full tw-px-0">
        <span className="tw-font-semibold tw-text-sm te-text-gray-950">{t('guides.website.url_input')}</span>
        <p className="tw-text-xs tw-text-gray-950 tw-mb-2.5">{t('guides.website.url_input_description')}</p>
      </div>
      <FormProviderMC methods={methods}>
        <div className="tw-flex twa-flex-col tw-text-sm">
          <div className="tw-w-full tw-px-0">
            {/* URLInput */}
            <div className="tw-flex tw-gap-2">
              <div className={`tw-relative tw-w-[100%] tw-mb-0 ${(isLoading || isLoaded || isEditing) ? 'tw-mb-4 tw-bg-white tw-rounded-xl tw-py-2.5 tw-border tw-border-gray-200 tw-pl-2.5' : ''}`}>
                {(isLoading || isLoaded || isEditing) ? (<div>{urlToScrape}</div>) : (
                  <InputComponent
                    id="urlToScrape"
                    name="urlToScrape"
                    value={urlToScrape}
                    placeholder="Ej: https://www.mercately.com"
                    label={t('guides.website.url')}
                    onChange={(_, value) => {
                      setUrlToScrape(value);
                      if (localErrors.urlToScrape) setLocalErrors({});
                    }}
                    customError={(localErrors.urlToScrape || errors.urlToScrape) && (
                      localErrors.urlToScrape || errors.urlToScrape
                    )}
                    useFloatingLabel
                  />
                )}
              </div>

              {/* Search Button */}
              {(!isLoading && (isEditing || !urlsScraped.length)) && (
                <ButtonMC
                  text={isLoading ? 'Buscando...' : 'Buscar'}
                  variant="SECONDARY"
                  onClick={handleScrape}
                  outlined
                  className="tw-px-6 tw-max-h-[45px]"
                />
              )}
            </div>

            {/* Progress Bar */}
            {isLoading && (
              <ProgressBarMC progress={progress} />
            )}

            {/* URLs Field */}
            {urlsScraped.length > 0 && (
              <div className="tw-mt-2.5 tw-border tw-border-gray-200 tw-rounded-2 tw-pl-5 tw-py-5">
                {/* URLs Field Label */}
                {!isEditing && (
                  <div className="tw-flex tw-items-center tw-mb-2.5">
                    {!isSubmitting && <div className="fz-14">{t('guides.website.select_urls')}</div>}
                    {errors.urls && (
                      <span className="tw-ml-2.5 tw-text-red-500 tw-text-xs">
                        {errors.urls}
                      </span>
                    )}
                  </div>
                )}
                {/* URLs Field List */}
                <div className="tw-max-h-[300px] tw-overflow-y-auto">
                  {!isEditing && !isSubmitting && (
                    <div className="tw-flex tw-mb-2.5 tw-pr-2.5 tw-gap-3">
                      <input
                        type="checkbox"
                        className="tw-align-top tw-h-5 tw-w-5"
                        id="select-all"
                        checked={urlsScraped.length > 0 && urlsScraped.every(url => {
                          const urlData = selectedUrlsProp.find(item => item.url === url);
                          return urlData && urlData.selected !== false;
                        })}
                        ref={input => {
                          if (input) {
                            const selectedCount = selectedUrlsProp.filter(url =>
                              url.selected !== false
                            ).length;
                            const allSelected = urlsScraped.every(url => {
                              const urlData = selectedUrlsProp.find(item => item.url === url);
                              return urlData && urlData.selected !== false;
                            });

                            input.indeterminate = selectedCount > 0 && !allSelected;
                          }
                        }}
                        onChange={() => {
                          const allSelected = urlsScraped.every(url => {
                            const urlData = selectedUrlsProp.find(item => item.url === url);
                            return urlData && urlData.selected !== false;
                          });

                          allSelected ? handleDeselectAll() : handleSelectAll();
                        }}
                      />
                      <label htmlFor="select-all" className="tw-mb-0 tw-font-semibold">
                        {(() => {
                          const selectedCount = selectedUrlsProp.filter(url =>
                            url.selected !== false
                          ).length;
                          const allSelected = urlsScraped.every(url => {
                            const urlData = selectedUrlsProp.find(item => item.url === url);
                            return urlData && urlData.selected !== false;
                          });

                          if (selectedCount === 0 || allSelected) {
                            return t('guides.website.select_all');
                          }

                          return (
                            <>
                              <span className="tw-text-gray-500">{selectedCount}</span>
                              <span className="tw-ml-2.5">{t('guides.website.selected')}</span>
                            </>
                          );
                        })()}
                      </label>
                    </div>
                  )}
                  {urlsScraped.map((url, index) => renderUrlItem(url, index))}
                </div>
              </div>
            )}

            {/* Train and Save Button */}
            {urlsScraped.length > 0 && (
              <div className="tw-mt-5">
                <ButtonMC
                  text={t('guides.save')}
                  onClick={handleSave}
                  className="tw-text-sm"
                  disabled={isSubmitting}
                />
              </div>
            )}
          </div>
        </div>
      </FormProviderMC>
    </>
  );
};

export default WebSite;
