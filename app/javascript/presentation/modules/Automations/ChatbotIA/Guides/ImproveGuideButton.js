/* eslint-disable react/button-has-type */
import React from 'react';
import { useTranslation } from 'react-i18next';

import MagicIcon from '../../../../../components/icons/MagicIcon';

const ImproveGuideButton = ({ onClick, disabledImproveGuideButton = false }) => {
  const { t } = useTranslation('ChatbotIA');

  return (
    <button className={`tw-flex tw-w-full !tw-w-[150px] ${disabledImproveGuideButton ? 'tw-text-gray-300 hover:!tw-text-gray-300' : ''}`} onClick={onClick} disabled={disabledImproveGuideButton}>
      <div className="tw-flex tw-flex-row tw-flex-wrap tw-text-xs tw-content-center">
        <MagicIcon className="tw-mr-[2px]" width={19} height={19} />
        <span>{t('improve.text')}</span>
      </div>
    </button>
  );
};

export default ImproveGuideButton;