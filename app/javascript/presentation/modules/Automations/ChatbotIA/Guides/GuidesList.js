/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable import/no-unresolved */

import React from "react";
import { useTranslation } from 'react-i18next';
import moment from "moment";
import { useDispatch } from "react-redux";
import { Tooltip as ReactTooltip } from 'react-tooltip';
import customHistory from '../../../../../customHistory';

import GuideModel from '../../../../models/GuideModel';
import TableMC from "../../../../components/TableMC";
import usePagination from "../../../../../hooks/usePagination";
import EmptyScreenMC from './EmptyScreenMC';
import TitleMC from "../../../../components/TitleMC";
import ButtonMC from "../../../../components/ButtonMC";

import { SIDEBAR_ACTIONS } from "../../../../../constants/actionsConstants";
import { GUIDE_OPTIONS } from "./Types/GuidesOptionsSource";
import CustomIcon from "../../../../components/CustomIconMC";
import IconMC from "../../../../components/IconMC";

const GuideTypeRow = ({ type, t }) => {
  const typeConfig = GUIDE_OPTIONS.find((option) => option.type === type) || GUIDE_OPTIONS[1];

  return (
    <div className="tw-flex tw-items-center">
      {typeConfig.isImage ? (
        <img
          src={typeConfig.grayIcon}
          alt=""
          className="tw-mr-2 tw-w-5 tw-h-5"
        />
      ) : (
        <IconMC name={typeConfig.icon} size="xl" />
      )}
      <span className="tw-ml-1">{t(typeConfig.title)}</span>
    </div>
  );
};

const GuidesList = ({ miaGuidesIntegrated }) => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const {
    data,
    filtersComponent,
    loading,
    refetch
  } = usePagination({
    modelService: GuideModel.get,
    filters: []
  });

  const headers = [
    { key: 'topic', label: t('guides.table.guide'), info: t('guides.table.guide_tooltip') },
    { key: 'kind', label: t('guides.table.type'), info: t('guides.table.type_tooltip') },
    { key: 'author', label: t('guides.table.create_by') },
    { key: 'lastUpdate', label: t('guides.table.last_update') }
  ];

  const handleEdit = (guide) => {
    const url = `/retailers/${ENV.SLUG}/chatbot_ai/guides/${guide.guide_id}/edit`;
    customHistory.push(url);
  };

  const deleteGuide = (guide_id) => {
    if (confirm(t('guides.confirmDelete'))) {
      GuideModel.remove({ id: guide_id }).then(() => {
        refetch();
        showtoast(t('guides.successDelete'));
      }).catch(() => {
        showtoast(t('guides.errorDelete'));
      });
    }
  };

  const toggleGuideModal = () => {
    dispatch({
      type: SIDEBAR_ACTIONS.OPEN_SIDEBAR,
      payload: {
        componentKey: 'CreateChatbotIAGuides',
        props: {
          VARIANT: 'CREATE',
          refetch
        }
      }
    });
  };

  const parseData = () => {
    const items = data?.guides || [];

    return items.map((guide) => ({
      ...guide,
      created_by: typeof guide.created_by === 'string'
        ? JSON.parse(guide.created_by)
        : guide.created_by
    }));
  };

  const getTagRows = () => (parseData() || []).map(formatRowData);

  const formatRowData = (row) => ({
    topic: { type: 'text', value: row.name },
    kind: { type: 'custom-component', value: <GuideTypeRow type={row.guide_type} t={t} /> },
    author: { type: 'text', value: `${row.created_by?.first_name} ${row.created_by?.last_name}` },
    lastUpdate: { type: 'text', value: moment(row.timestamp).format('MM/DD/YYYY') },
    actions: [createAction('edit', row), createAction('delete', row)]
  });

  const createAction = (action, row) => {
    switch (action) {
      case 'edit':
        return {
          icon: 'edit-outline',
          label: t('actions.edit'),
          action: () => handleEdit(row)
        };
      case 'delete':
        return {
          icon: 'trash-2-outline',
          label: t('actions.delete'),
          action: () => deleteGuide(row.guide_id)
        };
      default:
        return null;
    }
  };

  const emptyComponent = data?.guides?.length === 0 ? <EmptyScreenMC handleCreateGuide={toggleGuideModal} refetch={refetch} /> : null;

  return (
    <>
      {miaGuidesIntegrated ? (
        <>
          <div className="tw-flex tw-flex-row tw-justify-between tw-items-center tw-mb-5">
            <div className="tw-flex tw-flex-row">
              <TitleMC
                text={t('title')}
                variant="H5"
                className="!tw-gap-1"
                textClassName="tw-font-semibold"
                suffixClassName="tw-font-semibold tw-text-base"
              />
              <span
                data-tooltip-id="title_guide"
                data-tooltip-content={t('guides.table.main_tooltip')}
                className="tw-ml-2 tw-cursor-pointer tw-flex tw-items-center"
              >
                <CustomIcon
                  name="info-outline"
                  className="tw-text-gray-500"
                  size="lg"
                />
              </span>
              <ReactTooltip
                place="right"
                type="dark"
                effect="solid"
                id="title_guide"
                className="tw-max-w-xs"
              />
            </div>
            <div className="tw-flex tw-flex-row tw-items-center tw-gap-2">
              {filtersComponent}
              <ButtonMC text={t('guides.emptyScreen.buttonCreateNewLabel')} onClick={toggleGuideModal} />
            </div>
          </div>

          <TableMC
            headers={headers}
            data={getTagRows()}
            loading={loading}
            borderAll
            emptyComponent={emptyComponent}
            tooltipClass="!tw-max-w-full"
          />
        </>
      ) : (
        <div className="tw-flex tw-items-center tw-justify-center tw-h-[50vh]">
          {t('chatbot_ai.guides.no_service_text')}
        </div>
      )}
    </>
  );
};

export default GuidesList;
