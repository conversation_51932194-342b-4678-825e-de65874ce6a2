import React from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import BackToComponent from "../../../../../components/shared/BackToComponent";
import ButtonMC from '../../../../components/ButtonMC';
import InputComponent from '../../../../components/InputMC';
import FormProviderMC from '../../../../components/FormProviderMC';
import CharCounterMC from '../../../../components/CharCounterMC';

const ManageGuidesHeader = ({
  backTo,
  handleSave,
  submitted,
  guide,
  guideType,
  handleInputChange,
  errors
}) => {
  const { t } = useTranslation('ChatbotIA');
  const methods = useForm();

  return (
    <div className="tw-flex tw-flex-col tw-gap-y-4">
      <div className="tw-w-full tw-px-[13px]">
        <BackToComponent onClick={backTo} />
      </div>

      <div className="tw-w-full sm:tw-w-full md:tw-w-2/3 tw-flex tw-justify-between tw-px-[15px]">
        <span className="tw-font-medium tw-text-gray-700 tw-text-lg">{t('guides.title')}</span>
        {guideType !== 'website' && (
          <ButtonMC
            text={t('guides.save')}
            onClick={handleSave}
            className="tw-font-medium tw-px-6"
            disabled={submitted}
          />
        )}
      </div>

      <div className="tw-w-full sm:tw-w-1/2 md:tw-w-2/3 tw-px-[15px]">
        <div className="tw-w-full tw-px-0">
          <span className="tw-font-semibold tw-text-gray-950 tw-text-sm">{t('guides.guide')}</span>
          <p className="tw-font-regular tw-text-xs tw-text-gray-950 tw-mb-2">{t('guides.subtitle')}</p>
        </div>
        <FormProviderMC methods={methods}>
          <div className="tw-flex tw-flex-col tw-text-sm">
            <div className="tw-w-full tw-px-0">
              <div className="tw-relative">
                <InputComponent
                  id="name"
                  name="name"
                  value={guide.name || ''}
                  placeholder={t('guides.input_placeholder')}
                  label={t('guides.questions_and_answers.guide')}
                  onChange={(_, value) => handleInputChange('name', value)}
                  customError={errors.name}
                  useFloatingLabel
                  maxLength={60}
                />
                <CharCounterMC counter={guide.name?.length || 0} limit={60} className="tw-top-[25px]" />
              </div>
            </div>
          </div>
        </FormProviderMC>
      </div>
    </div>
  );
};

export default ManageGuidesHeader;
