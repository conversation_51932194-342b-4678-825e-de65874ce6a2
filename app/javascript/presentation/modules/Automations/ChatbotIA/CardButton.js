import React from "react";
import NewFeatureLabel from "./NextFeatureLabel";
import IconMC from "../../../components/IconMC";
import CustomIcon from "../../../components/CustomIconMC";

const CardButton = ({
  icon,
  title,
  description,
  futureFeature = false,
  isIcon = false
}) => {
  const handleClick = (e) => {
    if (futureFeature) {
      e.stopPropagation();
    }
  };

  return (
    <div
      className={`tw-items-center tw-border tw-border-gray-200 tw-rounded-xl tw-px-4 tw-py-3 ${!futureFeature ? 'tw-cursor-pointer' : ''}`}
      onClick={handleClick}
    >
      <div className="tw-flex tw-flex-row tw-items-center">
        {isIcon ? (
          <div className="tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-[5px] tw-bg-blue-50 tw-mr-2">
            <IconMC name={icon} className="tw-text-blue-500" size="lg" />
          </div>
        ) : (<img src={icon} className="tw-w-[35px] tw-mr-2.5" />)}

        <div className="tw-flex tw-flex-col tw-w-full">
          <div className="tw-flex tw-w-full">
            <div className="tw-flex-grow">
              <div className="tw-flex tw-items-center tw-pb-[2px] tw-gap-2">
                <span className="tw-font-semibold tw-text-gray-950">{title}</span>
                {futureFeature && <NewFeatureLabel />}
              </div>
              <div className="tw-font-regular tw-text-gray-950 tw-text-xs tw-w-[90%]">{description}</div>
            </div>
            {!futureFeature
              && (
                <div className="tw-flex tw-items-center">
                  <IconMC
                    name="arrow-ios-downward-outline"
                    size="base"
                    className="tw-text-xl tw-text-gray-700 tw-rotate-[270deg]"
                  />
                </div>
              )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardButton;
