import React from 'react';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';

import ModalMC from '../../components/ModalMC';
import ButtonComponent from '../../components/ButtonMC';
import { updateUserAcademyProgress } from '../../../actions/academyActions';

/**
 * AcademyModalMC component renders a modal with a video player and a button to mark the video as completed.
 *
 * @param {Object} props - The properties object.
 * @param {string} props.title - The title of the modal.
 * @param {boolean} props.visible - Determines if the modal is visible.
 * @param {Function} props.onClose - Function to call when the modal is closed.
 * @param {string} props.videoSrc - The source URL of the video to be played.
 * @param {Object} props.progress - The progress object containing the current video's progress information.
 * @param {number} props.progress.id - The ID of the current video progress.
 * @param {number} [props.nextVideoId] - The ID of the next video to be watched (optional).
 *
 * @returns {JSX.Element} The AcademyModalMC component.
 */
const AcademyModalMC = ({
  title,
  visible,
  onClose,
  videoSrc,
  progress,
  nextVideoId,
}) => {
  const dispatch = useDispatch();
  const { t } = useTranslation('Academy');

  const onClick = () => {
    dispatch(updateUserAcademyProgress({ id: progress.id, watched: true, completed_at: new Date() }));
    if (nextVideoId !== undefined) {
      dispatch(updateUserAcademyProgress({ academy_video_id: nextVideoId, watched: false }));
    }
    onClose();
  };

  return (
    <ModalMC
      visible={visible}
      onClose={onClose}
      title="Academy"
      containerClassName="!tw-max-w-2xl"
      className="tw-fixed tw-top-0 tw-right-0 tw-w-1/3 tw-h-full !tw-rounded-none tw-flex tw-flex-col"
      headerClassName="tw-border-b tw-border-gray-300"
    >
      <div className="tw-flex-1 tw-overflow-auto">
        <div className="tw-text-xs tw-font-medium tw-pt-4">{title}</div>
        <div className="tw-w-full tw-h-auto tw-max-h-[70vh] tw-mt-4 tw-flex tw-justify-center">
          <iframe
            src={videoSrc}
            width="100%"
            height="400"
            title="Video externo"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
            className="tw-w-full tw-h-[40vh]"
          />
        </div>
      </div>

      <div className="tw-p-4 tw-flex tw-justify-between tw-mt-auto tw-border-t tw-border-gray-300">
        <ButtonComponent
          text={t('modal.complete_video')}
          onClick={onClick}
          className="tw-w-full"
        />
      </div>
    </ModalMC>
  );
};

export default AcademyModalMC;
