const hours = [];
const hoursWithHalf = [];
const times = [];
const monthDays = [];
let label;

for (let i = 0; i <= 24; i++) {
  label = `${i < 12 ? i : (i == 12 ? i : i - 12)} ${i < 12 ? 'a.m.' : (i == 12 ? 'm.' : 'p.m.')}`;
  hours.push({
    value: i,
    label
  });
  times.push({
    value: i == 24 ? '23:59:59' : `${i.toString().padStart(2, '0')}:00:00`,
    label
  });

  if (i < 24) {
    hoursWithHalf.push({
      value: `${i}:00`,
      label: `${i.toString().padStart(2, '0')}:00`
    });
    hoursWithHalf.push({
      value: `${i.toString().padStart(2, '0')}:30`,
      label: `${i.toString().padStart(2, '0')}:30`
    });
  }
}

for (let i = 1; i <= 30; i++) {
  monthDays.push({ value: i, label: i });
}

export const HOURS_OPTIONS = hours;
export const TIME_OPTIONS = times;
export const HOURS_WITH_HALF_OPTIONS = hoursWithHalf;
export const MONTH_DAYS = monthDays;

export const INTERVALS = [
  { value: 1, label: '1 hora' },
  { value: 2, label: '2 horas' },
  { value: 4, label: '4 horas' },
  { value: 8, label: '8 horas' },
  { value: 12, label: '12 horas' },
  { value: 24, label: '24 horas' },
  { value: 48, label: '48 horas' },
  { value: 72, label: '72 horas' },
  { value: 96, label: '96 horas' }
];

export const SENDING_FREQUENCY = [
  { value: 'immediately', label: 'Inmediato' },
  { value: 'interval', label: 'Intervalo' },
  { value: 'programmed', label: 'Programado' }
];

export const TIME_INTERVALS = [
  "0 - 1 a.m.", "1 - 2 a.m.", "2 - 3 a.m.", "3 - 4 a.m.",
  "4 - 5 a.m.", "5 - 6 a.m.", "6 - 7 a.m.", "7 - 8 a.m.",
  "8 - 9 a.m.", "9 - 10 a.m.", "10 - 11 a.m.", "11 a.m. - 12 m.",
  "12 m. - 1 p.m.", "1 - 2 p.m.", "2 - 3 p.m.", "3 - 4 p.m.",
  "4 - 5 p.m.", "5 - 6 p.m.", "6 - 7 p.m.", "7 - 8 p.m.",
  "8 - 9 p.m.", "9 - 10 p.m.", "10 - 11 p.m.", "11 p.m. - 12 p.m."
];

export const DOCUMENT_TYPES = [
  { value: 'cedula', label: 'Cédula de identidad' },
  { value: 'pasaporte', label: 'Pasaporte' },
  { value: 'ruc', label: 'Ruc' },
  { value: 'rut', label: 'Rut' },
  { value: 'otro', label: 'Otro' }
];

export const ORDER_STATUS = [
  { value: 'pending', label: 'Pendiente' },
  { value: 'canceled', label: 'Cancelado' },
  { value: 'success', label: 'Exitoso' }
];

export const SALES_CHANNELS = [
  { value: 'whatsapp', label: 'WhatsApp' },
  { value: 'facebook', label: 'Facebook' },
  { value: 'instagram', label: 'Instagram' },
  { value: 'mercadolibre', label: 'Mercado Libre' }
];

export const TYPE_OF_ANSWERS = [
  { value: 'text', label: 'Texto' },
  { value: 'text_image', label: 'Imagen' },
  { value: 'text_pdf', label: 'PDF' },
  { value: 'audio', label: 'Audio' },
  { value: 'video', label: 'Video' }
];

export const REACTIVATION_TIME_OPTIONS = [
  { value: 0, label: 'Seleccionar' },
  { value: 0.0167, label: '1 minuto' },
  { value: 0.0833, label: '5 minutos' },
  { value: 0.1667, label: '10 minutos' },
  { value: 0.25, label: '15 minutos' },
  { value: 0.5, label: '30 minutos' },
  { value: 1, label: '1 hora' },
  { value: 2, label: '2 horas' },
  { value: 4, label: '4 horas' },
  { value: 8, label: '8 horas' },
  { value: 10, label: '10 horas' },
  { value: 12, label: '12 horas' },
  { value: 24, label: '24 horas' },
  { value: 48, label: '48 horas' },
  { value: 72, label: '72 horas' }
];

export const CUSTOM_FIELD_KINDS = [
  { value: 'string', label: 'Texto' },
  { value: 'integer', label: 'Número entero' },
  { value: 'float', label: 'Número decimal' },
  { value: 'boolean', label: 'Booleano' },
  { value: 'date', label: 'Fecha' },
  { value: 'list', label: 'Lista' }
];

export const MERCATELY_API = '/api/v1';
export const PLATFORMS = Object.freeze({
  WHATSAPP: 'whatsapp',
  MESSENGER: 'messenger',
  INSTAGRAM: 'instagram',
  FACEBOOK_COMMENST: 'facebook_comments',
  INSTAGRAM_COMMENTS: 'instagram_comments',
  FACEBOOK: 'facebook'
});

export const DAYS_OPTIONS = [
  { value: 1, label: 'Lunes' },
  { value: 2, label: 'Martes' },
  { value: 3, label: 'Miércoles' },
  { value: 4, label: 'Jueves' },
  { value: 5, label: 'Viernes' },
  { value: 6, label: 'Sábado' },
  { value: 7, label: 'Domingo' }
];

export const DEFAULT_COLORS = [
  '#3CAAE1', '#2A4494', '#39896A', '#7BD6A2', '#EB686D',
  '#E084A8', '#824547', '#EFBA18', '#FCB86A', '#8D7EA7',
  '#364FE0', '#6A93FC', '#00B9AE', '#6AFCD9', '#BF46AD',
  '#9F6AFC', '#7F6240', '#A7947E', '#767B7D', '#ABEBFF'
];

export const AUTOMATION_ACTIONS_TYPE = [
  { value: 'assign_team', label: 'Asignar equipo' },
  { value: 'assign_agent', label: 'Asignar agente' },
  { value: 'add_tag', label: 'Agregar etiquetas' },
  { value: 'add_deal', label: 'Crear negociación' }
];

export const AUTOMATION_ACTIONS_TYPE_FUNNELS = [
  { value: 'assign_team', label: 'Asignar equipo' },
  { value: 'assign_agent', label: 'Asignar agente' },
  { value: 'add_tag', label: 'Agregar etiquetas' },
  { value: 'remove_tag', label: 'Eliminar etiquetas' }
];

export const MERCATELY_API_V2 = '/api/v2';

export const CHATBOT_OPTIONS_TYPE = Object.freeze({
  MENU: 'decision',
  QUESTION: 'form',
  MESSAGE: 'message',
  JUMP_TO: 'jump_to',
  ACTIVE_MIA: 'active_mia',
  API: 'api',
  RESOLVE_CHAT: 'resolve_chat'
});

export const CHATBOT_OPTION_ACTIONS_TYPE = [
  { value: 'add_tag', label: 'Agregar etiqueta' },
  { value: 'assign_agent', label: 'Asignar agente' },
  { value: 'get_out_bot', label: 'Salir del Flujo' },
  { value: 'save_on_db', label: 'Guardar respuesta en cliente' },
  { value: 'assign_team', label: 'Asignar equipo' },
  { value: 'ctx_notify', label: 'Notificar objetivo alcanzado' },
  { value: 'create_deal', label: 'Crear Negociación' }
];

export const DEFAULT_MAX_CHARS = 1024;

export const FIRST_PURCHASE_UPSELL_VARIABLES = {
  minTimeToAd: 14,
  maxTimeToAd: 180,
  defineTitle: 'AutomationShopify:modal.first-purchase-upsell-text',
  timeWarning: 'AutomationShopify:modal.time_to_ad_warning_first_purchase_upsell'
};

export const CUSTOMER_WINBACK_VARIABLES = {
  minTimeToAd: 60,
  maxTimeToAd: 365,
  defineTitle: 'AutomationShopify:modal.customer-winback-text',
  timeWarning: 'AutomationShopify:modal.time_to_ad_warning_customer_winback'
};

export const FLOW_KINDS_OPTIONS = [
  { value: 'inbound', label: 'Mensaje de entrada' }
];

export const FLOW_KINDS = {
  inbound: 'Mensaje de entrada',
  outbound: 'Mensaje de salida'
};

export const OUTBOUND_STALING_TIME_OPTIONS = [
  { value: 0, label: 'Inmediatamente' },
  { value: 12, label: 'Esperar 12 horas' },
  { value: 24, label: 'Esperar 24 horas' },
  { value: 48, label: 'Esperar 48 horas' }
];
export const CHARGEBEE_SEAT_ADDON_IDS = ['Extra-Agent-USD-Monthly', 'Extra-Agent-USD-Every-3-months', 'Extra-Agent-USD-Yearly'];

export const GLOBAL_STATUS = Object.freeze({
  pending: { value: 'pending', label: 'status.pending' },
  cancelled: { value: 'cancelled', label: 'status.cancelled' },
  sent: { value: 'sent', label: 'status.sent' },
  processing: { value: 'processing', label: 'status.processing' },
  in_process: { value: 'in_process', label: 'status.in_process' },
  failed: { value: 'failed', label: 'status.failed' },
  scheduled: { value: 'scheduled', label: 'status.scheduled' }
});

export const CUSTOMER_RELATED_FIELDS = Object.freeze({
  text: 'string',
  number: 'integer',
  float: 'float',
  boolean: 'boolean',
  date: 'date',
  list: 'list'
});
export const CHARGEBEE_GROWTH_ADDON_IDS = ['Growth-Tools-USD-Monthly', 'Growth-Tools-USD-Every-3-months', 'Growth-Tools-USD-Yearly']
export const MAX_PRODUCT_IMAGE_SIZE = 5 * 1024 * 1024;
