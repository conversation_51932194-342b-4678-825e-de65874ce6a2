import {
  cloneDeep, find, forEach, isEmpty
} from "lodash";
import { formatPhoneNumber } from '../services/phoneService';
import { getCountryFlagEmoji } from "../services/countryFlag";

import {
  SET_CUSTOMERS,
  SET_WHATSAPP_CUSTOMERS,
  SET_WHATSAPP_CUSTOMERS_REQUEST,
  LOAD_DATA_FAILURE, SET_CUSTOMERS_REQUEST,
  ERASE_DEAL, SET_ORDERS, SET_ORDERS_REQUEST,
  CHANGE_DEAL_COLUMN,
  SET_COLUMNS, SET_WHATSAPP_MESSAGES,
  SET_WHATSAPP_MESSAGES_REQUEST, SET_MESSAGES,
  SET_MESSAGES_REQUEST,
  ADD_DEALS_TO_COLUMN,
  SET_BLOCK_USER
} from "../actionTypes";

const initialState = {
  currentRetailerUser: {},
  customers: [],
  messages: [],
  total_pages: 0,
  total_fb_pages: 0,
  total_customers: 0,
  errorSendMessageStatus: null,
  errorSendMessageText: null,
  tags: [],
  reminders: [],
  customerFields: [],
  customFields: [],
  retailerCustomFields: [],
  loadingMoreCustomers: false,
  funnelSteps: {},
  orders: [],
  customerNotes: [],
  totalOrders: 0,
  mlChats: [],
  totalMlChats: 0,
  loadingMoreMessages: false,
  submitted: false,
  customerDeals: [],
  customerDealsCount: 0,
  loadingCustomerDeals: false,
  currentOrder: null,
  orderToShow: null,
  categories: [],
  products: [],
  closeManageProductModal: false,
  closeManageCustomFieldModal: false,
  orderLoading: false,
  loadingProducts: false,
  recentInboundMessageDate: null,
  customer: {},
  message: '',
  templates: [],
  total_template_pages: 0,
  agents: [],
  agent_list: [],
  handle_message_events: false,
  fetching_funnels: false,
  loadingTemplates: false,
  customerId: null,
  concatMessages: false,
  fast_answers: [],
  concatCustomers: false,
  unreadWhatsappChats: 0,
  unreadMessengerChats: 0,
  unreadInstagramChats: 0,
  unreadChats: false,
  filter_tags: [],
  currentCustomerId: null,
  ordersCount: 0
};

const mainReducer = (state = initialState, action) => {
  switch (action.type) {
    case 'GET_CURRENT_USER':
      return {
        ...state,
        currentUser: action.response.data.user
      };
    case 'SET_CUSTOMER':
      return {
        ...state,
        customer: action.data.customer,
        errors: action.data.errors,
        reminders: action.data.reminders,
        tags: action.data.tags,
        ordersCount: action.data.orders_count
      };
    case 'INCREMENT_ORDER_COUNT':
      return {
        ...state,
        ordersCount: state.ordersCount + 1
      };
    case 'SET_NOTES':
      return {
        ...state,
        customerNotes: action.data.notes
      };
    case 'ADD_NOTE':
      return {
        ...state,
        customerNotes: [action.body, ...state.customerNotes]
      };
    case 'CREATE_REMINDER':
      return {
        ...state,
        reminders: action.data.reminders
      };
    case SET_CUSTOMERS:
      return {
        ...state,
        customers: action.data.customers,
        total_customers: action.data.total_customers,
        agents: action.data.agents,
        storageId: action.data.storage_id,
        agent_list: action.data.agent_list,
        filter_tags: action.data.filter_tags,
        loadingMoreCustomers: false
      };
    case 'SET_SELECTED_CUSTOMERS':
      return {
        ...state,
        selectedCustomers: action.data.customers,
        totalSelectedCustomersPages: action.data.total_customers
      };
    case 'SET_SELECTED_CUSTOMER_IDS':
      return {
        ...state,
        contactGroupName: action.data.name,
        selectedCustomers: action.data.customers,
        selectedCustomerIds: action.data.customer_ids,
        totalSelectedCustomersPages: action.data.total_customers
      };
    case 'SET_CUSTOM_FIELDS':
      return {
        ...state,
        customerFields: action.data.customer_fields,
        customFields: action.data.custom_fields
      };
    case 'CONFIG_CUSTOM_FIELDS':
      return {
        ...state,
        customFields: action.data.custom_fields
      };
    case 'GET_RETAILER_CUSTOM_FIELDS':
      return {
        ...state,
        retailerCustomFields: action.data.custom_fields,
        total_pages: action.data.total_pages
      };
    case 'GET_RETAILER_CUSTOM_FIELDS_REQUEST_START':
      return {
        ...state,
        loadingCustomFields: true
      };
    case 'GET_RETAILER_CUSTOM_FIELDS_REQUEST_END':
      return {
        ...state,
        loadingCustomFields: false
      };
    case 'CREATE_RETAILER_CUSTOM_FIELD': {
      const { retailerCustomFields } = state;
      const { retailerCustomField } = action.payload;
      retailerCustomFields.push(retailerCustomField);

      return {
        ...state,
        retailerCustomFields,
        closeManageCustomFieldModal: true,
        submitted: false
      };
    }
    case 'UPDATE_RETAILER_CUSTOM_FIELD': {
      let { retailerCustomFields } = state;
      const customField = action.payload;
      retailerCustomFields = retailerCustomFields.map((mappedCustomField) => (mappedCustomField.id === customField.id ? customField : mappedCustomField));

      return {
        ...state,
        retailerCustomFields,
        closeManageCustomFieldModal: true,
        submitted: false
      };
    }
    case 'DELETE_RETAILER_CUSTOM_FIELD': {
      let { retailerCustomFields } = state;
      const customField = action.payload;
      retailerCustomFields = retailerCustomFields.filter((mappedCustomField) => (mappedCustomField.id !== customField.id));

      return {
        ...state,
        retailerCustomFields
      };
    }
    case 'TOGGLE_MANAGE_RETAILER_CUSTOM_FIELD_MODAL':
      return {
        ...state,
        closeManageCustomFieldModal: action.data.toggle
      };
    case SET_MESSAGES:
      if (state.currentCustomerId != action.data?.messages[0]?.customer_id) {
        return { ...state, loadingMoreMessages: false };
      }

      var balance_error = { status: null, message: null };

      if (action.data.balance_error_info) balance_error = action.data.balance_error_info;

      return {
        ...state,
        messages: action.data.messages,
        agent_list: action.data.agent_list,
        total_pages: action.data.total_pages,
        agents: action.data.agents,
        storageId: action.data.storage_id,
        errorSendMessageStatus: balance_error.status,
        errorSendMessageText: balance_error.message,
        recentInboundMessageDate: action.data.recent_inbound_message_date || state.recentInboundMessageDate,
        customerId: action.data.customer_id,
        filter_tags: action.data.filter_tags,
        loadingMoreMessages: false
      };
    case 'SET_SEND_MESSAGE':
      return {
        ...state,
        message: action.data.message,
        recentInboundMessageDate: action.data.recent_inbound_message_date || state.recentInboundMessageDate
      };
    case SET_WHATSAPP_CUSTOMERS:
      return {
        ...state,
        customers: action.data.customers,
        total_customers: action.data.total_customers,
        agents: action.data.agents,
        storageId: action.data.storage_id,
        agent_list: action.data.agent_list,
        filter_tags: action.data.filter_tags,
        loadingMoreCustomers: false
      };
    case SET_WHATSAPP_MESSAGES:
      if (state.currentCustomerId != action.data?.messages[0]?.customer_id) {
        return { ...state, loadingMoreMessages: false };
      }

      if (action.data && action.data.messages) {
        action.data.messages = action.data.messages.filter((message, index, self) => index === self.findIndex((m) => (
          m.account_uid === message.account_uid
        )));
      }
      var balance_error = { status: null, message: null };

      if (action.data.balance_error_info) balance_error = action.data.balance_error_info;

      return {
        ...state,
        messages: action.data.messages,
        total_pages: action.data.total_pages,
        agents: action.data.agents,
        storageId: action.data.storage_id,
        agent_list: action.data.agent_list,
        handle_message_events: action.data.handle_message_events,
        errorSendMessageStatus: balance_error.status,
        errorSendMessageText: balance_error.message,
        recentInboundMessageDate: action.data.recent_inbound_message_date || state.recentInboundMessageDate,
        customerId: action.data.customer_id,
        filter_tags: action.data.filter_tags,
        loadingMoreMessages: false,
        hasOlderMessages: action.data.has_older_messages
      };
    case 'SET_LAST_MESSAGES':
      if (state.currentCustomerId != action.data?.messages[0]?.customer_id) return { ...state };

      const newMessages = action.data.messages.filter((message, index, self) => index === self.findIndex((m) => (
        m.account_uid === message.account_uid
      )));

      return {
        ...state,
        messages: newMessages,
        total_pages: action.data.totalPages,
        handle_message_events: action.data.handleMessageEvents,
        recentInboundMessageDate: action.data.recentInboundMessageDate,
        hasOlderMessages: action.data.hasOlderMessages
      };
    case 'SET_LAST_FB_MESSAGES':
      if (state.currentCustomerId != action.data?.messages[0]?.customer_id) return { ...state };

      return {
        ...state,
        messages: action.data.messages,
        total_fb_pages: action.data.total_fb_pages
      };
    case 'SET_WHATSAPP_TEMPLATES':
      return {
        ...state,
        templates: action.data.templates,
        total_template_pages: action.data.total_pages
      };
    case 'CHANGE_CUSTOMER_AGENT':
      return {
        ...state,
        status: action.data.status,
        message: action.data.message
      };
    case 'CHANGE_CHAT_STATUS':
      return {
        ...state,
        status: action.data.status,
        message: action.data.message
      };
    case 'UNAUTHORIZED_SEND_MESSAGE':
      return {
        ...state,
        errorSendMessageStatus: action.data.status,
        errorSendMessageText: action.data.message
      };
    case 'SET_FAST_ANSWERS':
      return {
        ...state,
        fast_answers: action.data.templates.data,
        total_pages: action.data.total_pages
      };
    case 'SET_WHATSAPP_FAST_ANSWERS':
      return {
        ...state,
        fast_answers: action.data.templates.data,
        total_pages: action.data.total_pages
      };
    case 'SET_MESSENGER_FAST_ANSWERS':
      return {
        ...state,
        fast_answers: action.data.templates.data,
        total_pages: action.data.total_pages
      };
    case 'SET_INSTAGRAM_FAST_ANSWERS':
      return {
        ...state,
        fast_answers: action.data.templates.data,
        total_pages: action.data.total_pages
      };
    case 'SET_COMMENTS_FAST_ANSWERS':
      return {
        ...state,
        fast_answers: action.data.templates.data,
        total_pages: action.data.total_pages
      };
    case 'CREATE_FAST_ANSWER': {
      const newAnswersList = cloneDeep(state.fast_answers);
      newAnswersList.unshift(action.data.template);

      return {
        ...state,
        fast_answers: newAnswersList
      };
    }
    case 'SET_TAGS':
      return {
        ...state,
        tags: action.data.tags
      };
    case 'SET_CREATE_CUSTOMER_TAG':
      return {
        ...state,
        customer: action.data.customer,
        tags: action.data.tags
      };
    case 'SET_REMOVE_CUSTOMER_TAG':
      return {
        ...state,
        customer: action.data.customer,
        tags: action.data.tags
      };
    case 'SET_CREATE_TAG':
      return {
        ...state,
        customer: action.data.customer,
        tags: action.data.tags,
        filter_tags: action.data.filter_tags
      };
    case 'SET_PRODUCTS':
      return {
        ...state,
        products: action.data.products.data,
        total_pages: action.data.total_pages
      };
    case 'SET_CHAT_BOT':
      return {
        ...state,
        customer: action.data.customer
      };
    case 'SET_CONTACT_GROUP_ERRORS':
      return {
        ...state,
        nameValidationText: action.data.errors.name?.shift(),
        customersValidationText: action.data.errors.customer_ids?.shift()
      };
    case SET_WHATSAPP_CUSTOMERS_REQUEST:
    case SET_CUSTOMERS_REQUEST:
    case SET_ORDERS_REQUEST:
      return {
        ...state,
        loadingMoreCustomers: true
      };
    case LOAD_DATA_FAILURE:
      return {
        ...state,
        loadingMoreCustomers: false,
        loadingMoreMessages: false
      };
    case 'GET_FUNNELS':
      return {
        ...state,
        funnelSteps: action.data?.funnelSteps || {},
        fetching_funnels: true
      };
    case 'SET_FUNNEL_DEAL':
      return {
        ...state
      };
    case 'CLEAR_FUNNELS':
      return {
        ...state,
        ...(action?.data || {}),
        fetching_funnels: false
      };
    case 'CLEAR_NEW_DEAL':
      return {
        ...state,
        newDealSuccess: false
      };
    case 'SET_NEW_DEAL':
      if (Object.keys(action.data).length > 0) {
        let newDealsCount;
        try {
          newDealsCount = state.funnelSteps?.newDeals[action.data.deal.funnel_step_id];
        } catch {}
        return {
          ...state,
          newDealSuccess: true,
          funnelSteps: {
            ...state.funnelSteps,
            deals: {
              ...state.funnelSteps.deals,
              [action.data.deal.id]: action.data.deal
            },
            newDeals: {
              [action.data.deal.funnel_step_id]: newDealsCount ? newDealsCount + 1 : 0
            },
            columns: {
              ...state.funnelSteps.columns,
              [action.column]: {
                ...state.funnelSteps.columns[action.column],
                deals: state.funnelSteps.columns[action.column].deals + 1,
                amount: action.data.amount,
                dealIds: [action.data.deal.id].concat(state.funnelSteps.columns[action.column].dealIds)
              }
            }
          },
          fetching_funnels: true
        };
      }
    case 'SET_NEW_STEP':
      if (Object.keys(action.data).length > 0) {
        const new_steps = Object.assign(state.funnelSteps.columns, { [action.data.step.id]: action.data.step });
        return {
          ...state,
          newStepSuccess: true,
          funnelSteps: {
            ...state.funnelSteps,
            columnOrder: state.funnelSteps.columnOrder.concat(action.data.step.id),
            columns: new_steps
          },
          fetching_funnels: true
        };
      }
      return {
        ...state
      };
    case 'CLEAR_NEW_STEP':
      return {
        ...state,
        newStepSuccess: false
      };
    case 'ERASE_DEAL_STEP':
      if (Object.keys(action.data).length > 0) {
        const newColumns = { ...state.funnelSteps.columns };
        delete newColumns[action.column];
        return {
          ...state,
          funnelSteps: {
            ...state.funnelSteps,
            columnOrder: state.funnelSteps.columnOrder.filter((step) => (step !== action.column)),
            columns: newColumns
          },
          fetching_funnels: true
        };
      }
      break;
    case SET_ORDERS:
      return {
        ...state,
        orders: action.data.orders,
        totalOrders: action.data.total_orders,
        loadingMoreCustomers: false
      };
    case 'SET_MLCHATS':
      return {
        ...state,
        mlChats: action.data.ml_chats,
        totalMlChats: action.data.total_ml_chats
      };
    case 'SET_MLCHAT':
      return {
        ...state,
        ml_chat: action.data.ml_chat
      };
    case ERASE_DEAL: {
      const newDeals = { ...state.funnelSteps.deals };
      delete newDeals[action.dealId];

      const newColumns = { ...state.funnelSteps.columns };
      newColumns[action.column].dealIds = newColumns[action.column]
        .dealIds.filter((deal) => deal !== action.dealId);

      newColumns[action.column].deals = newColumns[action.column].dealIds.length;
      newColumns[action.column].amount = action.data.amount;
      newColumns[action.column].total = action.data.total;

      return {
        ...state,
        funnelSteps: {
          ...state.funnelSteps,
          deals: newDeals,
          columns: newColumns
        },
        fetching_funnels: true
      };
    }
    case CHANGE_DEAL_COLUMN: {
      // find column web id to remove the deal
      const columnToRemoveDeal = Object.keys(state.funnelSteps.columns)
        .find((colId) => state.funnelSteps.columns[colId].dealIds
          .filter((deal) => deal === action.data.deal_id).length);
      // find column object to add the deal
      const columnToAddDeal = state.funnelSteps.columns[action.data.funnel_step_id];
      // copy columns object to make changes
      const newColumns = { ...state.funnelSteps.columns };
      // set deals
      // remove deal from previous column
      newColumns[columnToRemoveDeal].dealIds = newColumns[columnToRemoveDeal].dealIds.filter((deal) => deal !== action.data.deal_id);

      // set total deals amount
      newColumns[columnToRemoveDeal].amount = action.data.previous_funnel_step_amount;

      // set total count of deals on DB
      newColumns[columnToRemoveDeal].total = action.data.previous_funnel_step_total;

      // set total count of deals loaded on each column
      newColumns[columnToRemoveDeal].deals = action.data.previous_funnel_step_total;

      if (columnToAddDeal) {
        // add deal on new column at selected position
        newColumns[columnToAddDeal.id].dealIds.splice(action.data.position, 0, action.data.deal_id);

        // set total deals amount
        newColumns[columnToAddDeal.id].amount = action.data.amount;

        // set total count of deals on DB
        newColumns[columnToAddDeal.id].total = action.data.total;

        // set total count of deals loaded on each column
        newColumns[columnToAddDeal.id].deals = action.data.total;
      }

      return {
        ...state,
        funnelSteps: {
          ...state.funnelSteps,
          columns: newColumns,
          deals: {
            ...state.funnelSteps.deals,
            [action.data.deal_id]: action.data.deal
          }
        },
        deals: {
          ...state.deals,
          [action.data.deal_id]: action.data.deal
        },
        newDealSuccess: Boolean(action.data.deal_id && action.data.deal),
        fetching_funnels: true
      };
    }
    case SET_COLUMNS: {
      return {
        ...state,
        funnelSteps: {
          ...state.funnelSteps,
          columnOrder: action.columns.columns
        }
      };
    }
    case ADD_DEALS_TO_COLUMN: {
      const newColumns = state.funnelSteps.columns;
      newColumns[action.column].dealIds = [
        ...newColumns[action.column].dealIds,
        ...Object.keys(action.data.deals)
      ];

      return {
        ...state,
        funnelSteps: {
          ...state.funnelSteps,
          columns: newColumns,
          deals: {
            ...state.funnelSteps.deals,
            ...action.data.deals
          }
        },
        fetching_funnels: false
      };
    }
    case SET_WHATSAPP_MESSAGES_REQUEST:
    case SET_MESSAGES_REQUEST:
      return {
        ...state,
        loadingMoreMessages: true
      };
    case 'SET_CURRENT_RETAILER_USER':
      return {
        ...state,
        currentRetailerUser: action.data.current_retailer_user
      };
    case 'SET_UNREAD_CHATS':
      return {
        ...state,
        unreadWhatsappChats: Number.isInteger(action.data.unread_whatsapp_chats) ? action.data.unread_whatsapp_chats : state.unreadWhatsappChats,
        unreadMessengerChats: Number.isInteger(action.data.unread_messenger_chats) ? action.data.unread_messenger_chats : state.unreadMessengerChats,
        unreadInstagramChats: Number.isInteger(action.data.unread_instagram_chats) ? action.data.unread_instagram_chats : state.unreadInstagramChats,
        unreadChats: Number.isInteger(action.data.unread_chats) ? action.data.unread_chats : state.unreadChats
      };
    case 'SET_UNREAD_WHATSAPP_CHATS':
      return {
        ...state,
        unreadWhatsappChats: Number.isInteger(action.data.unread_whatsapp_chats) ? action.data.unread_whatsapp_chats : state.unreadWhatsappChats,
        unreadChats: Number.isInteger(action.data.unread_chats) ? action.data.unread_chats : state.unreadChats
      };
    case 'SET_UNREAD_MESSENGER_CHATS':
      return {
        ...state,
        unreadMessengerChats: Number.isInteger(action.data.unread_messenger_chats) ? action.data.unread_messenger_chats : state.unreadMessengerChats,
        unreadChats: Number.isInteger(action.data.unread_chats) ? action.data.unread_chats : state.unreadChats
      };
    case 'SET_UNREAD_INSTAGRAM_CHATS':
      return {
        ...state,
        unreadInstagramChats: Number.isInteger(action.data.unread_instagram_chats) ? action.data.unread_instagram_chats : state.unreadInstagramChats,
        unreadChats: Number.isInteger(action.data.unread_chats) ? action.data.unread_chats : state.unreadChats
      };
    case 'SET_WS_TEMPLATE_ERRORS':
      return {
        ...state,
        labelValidationText: action.errors.label?.shift(),
        textValidationText: action.errors.text?.shift(),
        fileValidationText: action.errors.file?.shift(),
        templateValuesValidationText: action.errors.templateValues,
      };
    case 'TOGGLE_SUBMITTED':
      return {
        ...state,
        submitted: action.submitted
      };
    case 'SET_CUSTOMER_DEALS':
      return {
        ...state,
        customerDeals: action.data
      };
    case 'SET_CUSTOMER_DEALS_COUNT':
      return {
        ...state,
        customerDealsCount: action.data
      }
    case 'START_SET_CUSTOMER_DEALS':
      return {
        ...state,
        loadingCustomerDeals: true
      };
    case 'END_SET_CUSTOMER_DEALS':
      return {
        ...state,
        loadingCustomerDeals: false
      };
    case 'ERASE_SIMPLE_DEAL': {
      return {
        ...state,
        customerDeals: state.customerDeals.filter((item) => item.id !== action.data.deal.id)
      };
    }
    case SET_BLOCK_USER:
      return {
        ...state,
        customer: action.data.customer
      };
    case 'GET_ORDERS_REQUEST_START':
      return {
        ...state,
        orderLoading: true
      };
    case 'GET_ORDERS_REQUEST_END':
      return {
        ...state,
        orderLoading: false
      };
    case 'GET_ORDERS':
      return {
        ...state,
        orders: action.data.orders,
        total_pages: action.data.total_pages
      };
    case 'SET_CURRENT_ORDER':
      return {
        ...state,
        currentOrder: action.data
      };
    case 'GET_CATEGORIES':
      return {
        ...state,
        categories: action.data
      };
    case 'CREATE_CATEGORY': {
      const newCategories = cloneDeep(state.categories);
      newCategories.push(action.data);
      return {
        ...state,
        categories: newCategories
      };
    }
    case 'UPDATE_CATEGORY':
      let { categories } = state;
      categories = categories.map((category) => (action.data.web_id === category.web_id ? action.data : category));
      return {
        ...state,
        categories
      };
    case 'DELETE_CATEGORY': {
      let { categories } = state;
      categories = categories.filter((category) => (category.web_id !== action.data.web_id));
      return {
        ...state,
        categories
      };
    }
    case 'CREATE_SUBCATEGORY': {
      const newCategories = cloneDeep(state.categories);
      const categoryIndex = newCategories.findIndex((category) => (category.web_id === action.data.category_id));
      newCategories[categoryIndex].subcategories.push(action.data);
      return {
        ...state,
        categories: newCategories
      };
    }
    case 'UPDATE_SUBCATEGORY': {
      const { categories } = state;
      const categoryIndex = categories.findIndex((category) => (category.web_id === action.data.category_id));
      if (action.data.original_category_id) {
        // remover la subcateogria de original_category_id
        const originalCategoryIndex = categories.findIndex((category) => (category.web_id === action.data.original_category_id));
        const originalSubcategoryIndex = categories[originalCategoryIndex].subcategories.findIndex((subcategory) => (subcategory.web_id === action.data.web_id));
        categories[originalCategoryIndex].subcategories.splice(originalSubcategoryIndex, 1);
        // quitar los productos del products_count
        categories[originalCategoryIndex].products_count -= action.data.products_count;
        // añadirla a la category_id
        categories[categoryIndex].subcategories.push(action.data);
        // sumar los productos al products_count
        categories[categoryIndex].products_count += action.data.products_count;
      }
      const subcategoryIndex = categories[categoryIndex].subcategories.findIndex((subcategory) => (subcategory.web_id === action.data.web_id));
      categories[categoryIndex].subcategories[subcategoryIndex] = action.data;
      return {
        ...state,
        categories
      };
    }
    case 'DELETE_SUBCATEGORY': {
      const { categories } = state;
      const categoryIndex = categories.findIndex((category) => (category.web_id === action.data.category_id));
      categories[categoryIndex].products_count -= action.data.products_count;
      categories[categoryIndex].subcategories = categories[categoryIndex].subcategories.filter((subcategory) => (subcategory.web_id !== action.data.web_id));
      return {
        ...state,
        categories
      };
    }
    case 'GET_PRODUCTS_REQUEST_START':
      return {
        ...state,
        loadingProducts: true
      };
    case 'GET_PRODUCTS_REQUEST_END':
      return {
        ...state,
        loadingProducts: false
      };
    case 'GET_PRODUCTS':
      let newProducts;
      if (action.canConcatProducts) {
        newProducts = cloneDeep(state.products).concat(action.data.products);
      } else {
        newProducts = action.data.products;
      }

      return {
        ...state,
        products: newProducts,
        total_pages: action.data.total_pages
      };
    case 'CREATE_PRODUCT': {
      const { products } = state;
      const { product, showSection } = action.payload;
      if (showSection === 'products' || (product.active && showSection === 'active_products') || (!product.active && showSection === 'inactive_products')) {
        products.unshift(product);
      }
      return {
        ...state,
        products,
        closeManageProductModal: true,
        submitted: false
      };
    }
    case 'UPDATE_PRODUCT':
      let { products } = state;
      products = products.map((product) => (action.data.product.web_id === product.web_id ? action.data.product : product));
      return {
        ...state,
        products,
        closeManageProductModal: true,
        submitted: false
      };
    case 'DELETE_PRODUCT': {
      let { products } = state;
      products = products.filter((product) => (product.web_id !== action.data.web_id));
      return {
        ...state,
        products
      };
    }
    case 'TOGGLE_MANAGE_PRODUCT_MODAL':
      return {
        ...state,
        closeManageProductModal: action.data.toggle
      };
    case 'UPDATE_ORDER_STATUS': {
      let newOrders = cloneDeep(state.orders);
      if (action.data.currentTab !== 'orders') {
        newOrders = newOrders.filter((order) => (order.web_id !== action.data.order.web_id));
      } else {
        forEach(newOrders, (order) => {
          if (order.web_id === action.data.order.web_id) {
            order.status = action.data.order.status;
            order.payment_status = action.data.order.payment_status;
            order.payment_transaction = action.data.order.payment_transaction;
          }
        });
      }

      return {
        ...state,
        orders: newOrders
      };
    }
    case 'UPDATE_WHATSAPP_CUSTOMER': {
      let newCustomer = cloneDeep(state.customer);
      const customerData = action.data;

      newCustomer = {
        ...newCustomer,
        first_name: customerData.first_name,
        last_name: customerData.last_name,
        email: customerData.email,
        id_type: customerData.id_type,
        id_number: customerData.id_number,
        phone: formatPhoneNumber(customerData.phone, customerData.country_id),
        emoji_flag: getCountryFlagEmoji(customerData.country_id),
        country_id: customerData.country_id,
        phone_without_country_code: customerData.phone
      };

      return {
        ...state,
        customer: newCustomer
      };
    }
    case 'SET_REMINDERS':
      return {
        ...state,
        reminders: action.data.reminders
      };
    case 'SET_CAMPAIGNS':
      return {
        ...state,
        campaigns: action.data.campaigns
      };
    case 'SET_WHATSAPP_TEMPLATES_START':
      return {
        ...state,
        loadingTemplates: true
      };
    case 'SET_WHATSAPP_TEMPLATES_END':
      return {
        ...state,
        loadingTemplates: false
      };
    case 'SET_CUSTOM_FIELDS_START':
      return {
        ...state,
        loadingCustomFields: true
      };
    case 'SET_CUSTOM_FIELDS_END':
      return {
        ...state,
        loadingCustomFields: false
      };
    case 'SET_REMINDERS_START':
      return {
        ...state,
        loadingReminders: true
      };
    case 'SET_REMINDERS_END':
      return {
        ...state,
        loadingReminders: false
      };
    case 'SET_NOTES_START':
      return {
        ...state,
        loadingNotes: true
      };
    case 'SET_NOTES_END':
      return {
        ...state,
        loadingNotes: false
      };
    case 'EMPTY_MESSAGES': {
      return {
        ...state,
        messages: []
      };
    }
    case 'EMPTY_CUSTOMER_NOTES':
      return {
        ...state,
        customerNotes: []
      };
    case 'SET_CURRENT_CUSTOMER_ID':
      return {
        ...state,
        currentCustomerId: action.data.id
      };
    case 'SET_RETAILER_ONBOARDING':
      return {
        ...state,
        retailerOnboarding: action.data
      };
    case 'RESET_TEMPORAL_STATE':
      return {
        ...state,
        ...initialState,
        currentRetailerUser: state.currentRetailerUser
      };
    case 'START_FETCHING_FUNNELS':
      return {
        ...state,
        fetching_funnels: true
      }
      case 'END_FETCHING_FUNNELS':
        return {
          ...state,
          fetching_funnels: false
        }
    default:
      return state;
  }
};

export default mainReducer;
