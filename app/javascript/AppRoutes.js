import React, { lazy } from 'react';
import {
  unstable_HistoryRouter as HistoryRouter, Routes, Route, Navigate
} from 'react-router-dom';
import customHistory from './customHistory';
import ChatbotOptions from './components/chatbots/Options';
import ErrorBoundary from './ErrorBoundary';
import ProtectedRoute from './ProtectedRoute';
import { CRM_ROUTES } from './constants/RoutesConstants';
import SidebarContainerMC from './presentation/components/SidebarContainerMC';
import RemindersScreenMC from './presentation/screens/retailers/slug/RemindersScreenMC';
import ModuleTitleMC from './presentation/layouts/ModuleTitleMC';

const ConversationTopics = lazy(() => import('./components/ConversationTopics'));
const ContactGroup = lazy(() => import('./components/ContactGroup'));
const Funnels = lazy(() => import('./components/Funnels'));
const FunnelAutomations = lazy(() => import('./components/Funnels/Automations'));
const DownloadHistory = lazy(() => import('./presentation/screens/Funnels/DownloadHistory'));
const MLChat = lazy(() => import('./components/MLChat'));
const WsTemplatesNew = lazy(() => import('./components/WsTemplates/WsTemplatesNew'));
const MessagingStats = lazy(() => import('./components/Stats/Messaging'));
const SalesStats = lazy(() => import('./components/Stats/Sales'));
const TagsStats = lazy(() => import('./components/Stats/Tags'));
const FunnelsStats = lazy(() => import('./components/Stats/Funnels'));
const ConversationTopicStatsPage = lazy(() => import('./components/Stats/ConversationTopic'));
const AutomaticAnswers = lazy(() => import('./components/AutomaticAnswers'));
const Products = lazy(() => import('./components/Products'));
const Inventories = lazy(() => import('./components/ProductVariantCombinations'));
const ImportProducts = lazy(() => import('./components/Products/ImportProducts'));
const Orders = lazy(() => import('./components/Orders'));
const ShowOrder = lazy(() => import('./components/Orders/ShowOrder'));
const EditOrder = lazy(() => import('./components/Orders/EditOrder'));
const Categories = lazy(() => import('./components/Categories'));
const NewOrder = lazy(() => import('./components/Orders/NewOrder'));
const OrderDownloadHistory = lazy(() => import('./components/Orders/DownloadHistory'));
const CustomerList = lazy(() => import('./components/CustomerList'));
const CreateCustomerList = lazy(() => import('./components/CustomerList/CreateCustomerList'));
const DigitalPayments = lazy(() => import('./components/DigitalPayments'));
const Chatbots = lazy(() => import('./components/chatbots'));
const WsTemplates = lazy(() => import('./components/WsTemplates'));
const CustomFields = lazy(() => import('./components/CustomFields'));
const Messaging = lazy(() => import('./components/Messaging'));
const Staff = lazy(() => import('./components/Staff'));
const SalesChannels = lazy(() => import('./components/SalesChannels'));
const ImportCustomers = lazy(() => import('./components/Customers/ImportCustomers'));
const Automations = lazy(() => import('./components/Automations'));
const AutomationsShopifyHistory = lazy(() => import('./components/Automations/shopify/ShopifyHistory'));
const AutomationsShopifyConfig = lazy(() => import('./components/Automations/shopify/Config'));
const ShopifyWhatsAppWidget = lazy(() => import('./components/Automations/shopify/WsWidget'));
const ChatbotAIGuides = lazy(() => import('./presentation/modules/Automations/ChatbotIA/Guides'));
const ChatbotAIManageGuide = lazy(() => import('./presentation/modules/Automations/ChatbotIA/Guides/ManageGuides'));
const ChatbotAIConfig = lazy(() => import('./presentation/modules/Automations/ChatbotIA/Config'));
const ChatbotAIPlayground = lazy(() => import('./presentation/modules/Automations/ChatbotIA/Playground'));
const ManageProduct = lazy(() => import('./components/Products/ManageProduct'));
const Teams = lazy(() => import('./components/Teams'));
const Roles = lazy(() => import('./components/Roles'));
const QuickAnswers = lazy(() => import('./components/QuickAnswers'));
const ManageQuickAnswer = lazy(() => import("./components/QuickAnswers/ManageQuickAnswer"));
const ExportedCustomers = lazy(() => import('./presentation/screens/retailers/slug/ExportedCustomers'));
const GrowthToolAutomation = lazy(() => import('./components/GrowthTools/Automations'));
const GrowthToolAutomationHistory = lazy(() => import('./components/GrowthTools/Automations/History'));
const GrowthToolFunnelHistory = lazy(() => import('./components/GrowthTools/Automations/Funnels/History'));
const GrowthToolAutomationFunnels = lazy(() => import('./components/GrowthTools/Automations/Funnels'));
const Notifications = lazy(() => import('./components/Settings/Notifications'));
const Plans = lazy(() => import('./components/Plans'));
const PaymentPlanDetails = lazy(() => import('./components/Plans/PaymentPlanDetails'));
const RegionalConfigs = lazy(() => import('./components/RegionalConfigs'));
const CustomerFromRb = lazy(() => import('./components/shared/CustomerFromRb'));
const ManageCustomer = lazy(() => import('./components/Customers/ManageCustomer'));
const ShippingCostLayout = lazy(() => import('./components/ShippingCost'));
const ShippingCost = lazy(() => import('./components/ShippingCost/ShippingCost'));
const FixedCost = lazy(() => import('./components/ShippingCost/FixedCost'));
const CostsByLocation = lazy(() => import('./components/ShippingCost/CostsByLocation'));
const EditVariantsPage = lazy(() => import('./components/Products/Variants/EditVariantsPage'));
const WhatsAppIntegrations = lazy(() => import('./components/Integrations/Whatsapp'));
const OnboardingApp = lazy(() => import('./components/onboarding/OnboardingApp'));
const WhatsAppWidget = lazy(() => import('./components/WidgetConfigs/WsWidget'));
// TODO: Fix this views
// const CampaignsMC = lazy(() => import('./presentation/screens/retailers/slug/campaigns/CampaignsScreenMC'));
// const CampaignsStatusScreenMC = lazy(() => import('./presentation/screens/retailers/slug/campaigns/slug/CampaignsStatusScreenMC'));
// const ShowCustomersScreenMC = lazy(() => import('./presentation/screens/retailers/slug/customers/ShowCustomerScreen/ShowCustomersScreenMC'));
const TagMC = lazy(() => import('./presentation/screens/retailers/slug/TagsScreenMC'));
const IntegrationsScreenMC = lazy(() => import('./presentation/screens/retailers/slug/integrations/IntegrationsScreenMC'));
const CustomerRelatedFieldsMC = lazy(() => import('./presentation/screens/retailers/slug/CustomerRelatedFieldsScreenMC'));
const RegistrationsEditProfileScreenMC = lazy(() => import('./presentation/screens/retailers/slug/RegistrationsEditProfileScreenMC'));
const CustomersScreenMC = lazy(() => import('./presentation/screens/retailers/slug/customers/CustomersScreenMC/CustomersScreenMC'));
const WoocommerceScreenMC = lazy(() => import('./presentation/screens/retailers/slug/integrations/WoocommerceScreenMC'));

const AppRoutes = () => (
  <ErrorBoundary>
    <HistoryRouter history={customHistory}>
      <Routes>
        <Route
          exact
          path="/retailers/:slug/customer_related_fields"
          element={<CustomerRelatedFieldsMC />}
        />
        <Route
          exact
          path="/retailers/:slug/edit"
          element={<RegistrationsEditProfileScreenMC />}
        />
        <Route path="/retailers/:slug/customers/:id/edit" element={<ProtectedRoute element={ManageCustomer} />} />
        <Route exact path="/retailers/:slug/customers/:id" element={<ProtectedRoute element={CustomerFromRb} />} />
        <Route
          exact
          path="/retailers/:slug/integrations"
          element={<IntegrationsScreenMC />}
        />
        <Route
          exact
          path="/retailers/:slug/mercadolibre_chats"
          element={<ProtectedRoute element={MLChat} />}
        />
        <Route
          exact
          path="/retailers/:slug/contact_groups/new"
          element={<ProtectedRoute element={ContactGroup} />}
        />
        <Route
          exact
          path="/retailers/:slug/contact_groups/:id/edit"
          element={<ProtectedRoute element={ContactGroup} />}
        />
        <Route
          exact
          path="/retailers/:slug/funnels"
          element={<ProtectedRoute element={Funnels} />}
        />
        <Route
          exact
          path="/retailers/:slug/funnels/:funnelId"
          element={<ProtectedRoute element={Funnels} />}
        />
        <Route exact path="/retailers/:slug/funnels/download-history" element={<ProtectedRoute element={DownloadHistory} />} />
        <Route exact path="/retailers/:slug/funnels/automations" element={<ProtectedRoute element={FunnelAutomations} />} />
        <Route
          exact
          path="/retailers/:slug/products"
          element={<ProtectedRoute element={Products} />}
        />
        <Route
          path="/retailers/:slug/products/new"
          element={<ProtectedRoute element={ManageProduct} />}
        />
        <Route
          path="/retailers/:slug/products/:id/edit"
          element={<ProtectedRoute element={ManageProduct} />}
        />
        <Route
          exact
          path="/retailers/:slug/products/import"
          element={<ProtectedRoute element={ImportProducts} />}
        />
        <Route exact path="/retailers/:slug/inventories" element={<ProtectedRoute element={Inventories} />} />
        <Route path="/retailers/:slug/inventories/:id/edit" element={<ProtectedRoute element={EditVariantsPage} />} />
        <Route
          exact
          path="/retailers/:slug/orders"
          element={<ProtectedRoute element={Orders} />}
        />
        <Route
          exact
          path="/retailers/:slug/custom_fields"
          element={<ProtectedRoute element={CustomFields} />}
        />
        <Route
          exact
          path="/retailers/:slug/orders/new"
          element={<ProtectedRoute element={NewOrder} />}
        />
        <Route
          exact
          path="/retailers/:slug/orders/:id"
          element={<ProtectedRoute element={ShowOrder} />}
        />
        <Route
          exact
          path="/retailers/:slug/orders/:id/edit"
          element={<ProtectedRoute element={EditOrder} />}
        />
        <Route exact path="/retailers/:slug/orders_download_history" element={<ProtectedRoute element={OrderDownloadHistory} />} />
        <Route
          exact
          path="/retailers/:slug/categories"
          element={<ProtectedRoute element={Categories} />}
        />
        <Route
          exact
          path="/retailers/:slug/whatsapp_templates"
          element={<ProtectedRoute element={WsTemplates} />}
        />
        <Route
          exact
          path="/retailers/:slug/whatsapp_templates/new"
          element={<ProtectedRoute element={WsTemplatesNew} />}
        />
        <Route
          exact
          path="/retailers/:slug/new_stats"
          element={<ProtectedRoute element={MessagingStats} />}
        />
        <Route
          exact
          path="/retailers/:slug/sales_stats"
          element={<ProtectedRoute element={SalesStats} />}
        />
        <Route
          exact
          path="/retailers/:slug/tags_stats"
          element={<ProtectedRoute element={TagsStats} />}
        />
        <Route
          exact
          path="/retailers/:slug/funnels_stats"
          element={<ProtectedRoute element={FunnelsStats} />}
        />
        <Route
          exact
          path="/retailers/:slug/conversation_stats"
          element={<ProtectedRoute element={ConversationTopicStatsPage} />}
        />
        <Route
          exact
          path="/retailers/:slug/new_manage_automatic_answers"
          element={<ProtectedRoute element={AutomaticAnswers} />}
        />
        <Route exact path="/retailers/:slug/customers" element={<ProtectedRoute element={CustomersScreenMC} />} />
        <Route exact path="/retailers/:slug/exported_customers" element={<ProtectedRoute element={ExportedCustomers} />} />
        <Route path="/retailers/:slug/customers/new" element={<ProtectedRoute element={ManageCustomer} />} />
        <Route path="/retailers/:slug/customers/:id/edit" element={<ProtectedRoute element={ManageCustomer} />} />
        <Route
          exact
          path="/retailers/:slug/customer_list"
          element={<ProtectedRoute element={CustomerList} />}
        />
        <Route
          exact
          path="/retailers/:slug/customer_list/add"
          element={<ProtectedRoute element={CreateCustomerList} />}
        />
        <Route
          exact
          path="/retailers/:slug/customer_list/import"
          element={<ProtectedRoute element={ImportCustomers} />}
        />
        <Route
          exact
          path="/retailers/:slug/digital_payments"
          element={<ProtectedRoute element={DigitalPayments} />}
        />
        <Route
          exact
          path="/retailers/:slug/chatbots"
          element={<ProtectedRoute element={Chatbots} />}
        />
        <Route
          exact
          path="/retailers/:slug/chatbots/:id/options"
          element={<ProtectedRoute element={ChatbotOptions} />}
        />
        <Route
          exact
          path="/retailers/:slug/messaging"
          element={<ProtectedRoute element={Messaging} />}
        />
        <Route
          exact
          path="/retailers/:slug/staff"
          element={<ProtectedRoute element={Staff} />}
        />
        <Route
          exact
          path="/retailers/:slug/sales_channels"
          element={<ProtectedRoute element={SalesChannels} />}
        />
        <Route exact path="/retailers/:slug/teams" element={<ProtectedRoute element={Teams} />} />
        <Route exact path="/retailers/:slug/roles" element={<ProtectedRoute element={Roles} />} />
        <Route
          exact
          path="/retailers/:slug/conversation_topics"
          element={<ProtectedRoute element={ConversationTopics} />}
        />
        <Route
          exact
          path="/retailers/:slug/quick_answers"
          element={<ProtectedRoute element={QuickAnswers} />}
        />
        <Route
          exact
          path="/retailers/:slug/quick_answers/new"
          element={<ProtectedRoute element={ManageQuickAnswer} />}
        />
        <Route
          exact
          path="/retailers/:slug/quick_answers/:id/edit"
          element={<ProtectedRoute element={ManageQuickAnswer} />}
        />
        <Route exact path="/retailers/:slug/plans" element={<ProtectedRoute element={Plans} />} />
        <Route exact path="/retailers/:slug/plans/:id" element={<ProtectedRoute element={PaymentPlanDetails} />} />
        <Route exact path="/retailers/:slug/import_customers" element={<ProtectedRoute element={ImportCustomers} />} />
        <Route exact path="/retailers/:slug/shopify-automations" element={<ProtectedRoute element={Automations} />} />
        <Route exact path="/retailers/:slug/shopify-automations/config" element={<ProtectedRoute element={AutomationsShopifyConfig} />} />
        <Route exact path="/retailers/:slug/shopify-automations/whatsapp-widget" element={<ProtectedRoute element={ShopifyWhatsAppWidget} />} />
        <Route exact path="/retailers/:slug/chatbot_ai/guides" element={<ProtectedRoute element={ChatbotAIGuides} />} />
        <Route exact path="/retailers/:slug/chatbot_ai/guides/new" element={<ProtectedRoute element={ChatbotAIManageGuide} />} />
        <Route exact path="/retailers/:slug/chatbot_ai/guides/:id/edit" element={<ProtectedRoute element={ChatbotAIManageGuide} />} />
        <Route exact path="/retailers/:slug/chatbot_ai/config" element={<ProtectedRoute element={ChatbotAIConfig} />} />
        <Route exact path="/retailers/:slug/chatbot_ai/general" element={<ProtectedRoute element={ChatbotAIPlayground} />} />
        <Route exact path="/retailers/:slug/shopify-automations/:webId/history" element={<ProtectedRoute element={AutomationsShopifyHistory} />} />
        <Route exact path="/retailers/:slug/growth_tools/automations" element={<ProtectedRoute element={GrowthToolAutomation} />} />
        <Route exact path="/retailers/:slug/growth_tools/automations/:id/history" element={<ProtectedRoute element={GrowthToolAutomationHistory} />} />
        <Route exact path="/retailers/:slug/growth_tools/automations/funnels/:id/history" element={<ProtectedRoute element={GrowthToolFunnelHistory} />} />
        <Route exact path="/retailers/:slug/growth_tools/automations/funnels/:id" element={<ProtectedRoute element={GrowthToolAutomationFunnels} />} />
        <Route exact path="/retailers/:slug/notification_settings" element={<ProtectedRoute element={Notifications} />} />
        <Route exact path="/retailers/:slug/regional_configs" element={<ProtectedRoute element={RegionalConfigs} />} />
        <Route path="/retailers/:slug/shipping_cost" element={<ProtectedRoute element={ShippingCostLayout} />}>
          <Route index element={<ProtectedRoute element={ShippingCost} />} />
          <Route path="fixed_cost" element={<ProtectedRoute element={FixedCost} />} />
          <Route path="costs_by_location" element={<ProtectedRoute element={CostsByLocation} />} />
        </Route>

        <Route exact path="/retailers/:slug/integrations/whatsapp_business" element={<WhatsAppIntegrations />} />
        <Route exact path="/retailers/:slug/integrations/woocommerce" element={<WoocommerceScreenMC />} />
        <Route exact path="/retailers/:slug/onboarding" element={<OnboardingApp />} />
        <Route exact path="/retailers/:slug/whatsapp_widget" element={<ProtectedRoute element={WhatsAppWidget} />} />
        <Route path="/retailers/:slug/reminders" element={<RemindersScreenMC />} />
        <Route
          path="/retailers/:slug/whatsapp_chats"
          element={<Navigate to="/retailers/:slug/messaging" replace />}
        />
        <Route
          path="/retailers/:slug/facebook_chats"
          element={<Navigate to="/retailers/:slug/messaging" replace />}
        />
        <Route
          path="/retailers/:slug/instagram_chats"
          element={<Navigate to="/retailers/:slug/messaging" replace />}
        />
        <Route
          path={CRM_ROUTES.TAGS.path}
          element={<TagMC />}
        />
      </Routes>
      <ModuleTitleMC />
      <SidebarContainerMC />
    </HistoryRouter>
  </ErrorBoundary>
);

export default AppRoutes;
