/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { useState, useEffect, useRef } from 'react';
import Select from 'react-select';
import { useSelector, useDispatch } from 'react-redux';
import {
  cloneDeep, find, forEach, isEmpty
} from 'lodash';
import { Tooltip } from 'react-tooltip';
import { useNavigate } from 'react-router-dom';
import { createWsTemplate } from '../../actions/wsTemplatesActions';
import { getRetailerInfo } from '../../actions/retailerUsersActions';
import { useTranslation } from 'react-i18next';

import AttachEmojiIcon from "../shared/AttachEmojiIcon";
import EmojisContainer from "../shared/EmojisContainer";
import stringUtils from '../../util/string.utils';
import selectStyles from '../../util/selectStyles';
import CloseIcon from '../icons/CloseIcon';
import TeamSelect from './TeamSelect';
import CategorySelector from './CategorySelector';
import HeaderMediaButton from './HeaderMediaButton';
import BackToComponent from '../shared/BackToComponent';
import WsTemplatePreview from './WsTemplatePreview';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const languages = [
  { value: 'spanish', label: 'Español' },
  { value: 'english', label: 'Inglés' }
];

const gsTemplatesTypes = [
  { value: 'text', label: 'TEXTO' },
  { value: 'image', label: 'IMAGEN' },
  { value: 'document', label: 'DOCUMENTO' },
  { value: 'video', label: 'VIDEO' }
];

const authenticationOptions = [
  { value: 'COPY_CODE', label: 'Copiar código' }
];

const offerUrlTypeOptions = [
  { value: 'static', label: 'Estático' },
  { value: 'dynamic', label: 'Dinámico' }
];

const WsTemplatesNew = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { retailer_info = {} } = useSelector((reduxState) => reduxState.retailerUsersReducer);
  const {
    labelValidationText,
    textValidationText,
    fileValidationText,
    templateValuesValidationText,
    submitted
  } = useSelector((reduxState) => reduxState.mainReducer);

  const [label, setLabel] = useState('');
  const [category, setCategory] = useState('MARKETING');
  const [language, setLanguage] = useState(languages[0]);
  const [type, setType] = useState(gsTemplatesTypes[0]);
  const [templateText, setTemplateText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [caretPosition, setCaretPosition] = useState(0);
  const [templateValues, setTemplateValues] = useState([]);
  const [emoji, setEmoji] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [dynamicUrlValues, setDinamicUrlValues] = useState({});
  const [interactiveAction, setInteractiveAction] = useState('not_apply');
  const [callToActions, setCallToActions] = useState([]);
  const [quickReplies, setQuickReplies] = useState([]);
  const [security, setSecurity] = useState(false);
  const [expiration, setExpiration] = useState(false);
  const [expiryMinutes, setExpiryMinutes] = useState(1);
  const [expiryMinutesError, setExpiryMinutesError] = useState('');
  const [authenticationActions, setAuthenticationActions] = useState({ text: '', error: '' });
  const [selectedTeam, setSelectedTeam] = useState(false);
  const [headerType, setHeaderType] = useState('text');
  const [headerText, setHeaderText] = useState('');
  const [headerValidationText, setHeaderValidationText] = useState('');
  const templateTextRef = useRef(null);
  const navigate = useNavigate();
  const [verificationCode, setVerificationCode] = useState('');
  const [headerOfferTitle, setHeaderOfferTitle] = useState('');
  const [offerType, setOfferType] = useState('');
  const [offerButtonText, setOfferButtonText] = useState('');
  const [offerButtonValue, setOfferButtonValue] = useState('');
  const [expirationOffer, setExpirationOffer] = useState(false);
  const [offerUrlType, setOfferUrlType] = useState('static');
  const [offerUrlValue, setOfferUrlValue] = useState('');
  const [offerUrlButtonText, setOfferUrlButtonText] = useState('');
  const [offerUrlVariables, setOfferUrlVariables] = useState([]);
  const offerUrlRef = useRef(null);
  const [offerUrlError, setOfferUrlError] = useState('');
  const [formErrors, setFormErrors] = useState({});
  const [templateType, setTemplateType] = useState('custom_message');

  useEffect(() => {
    dispatch(getRetailerInfo());

    return () => dispatch({ type: 'RESET_TEMPORAL_STATE' });
  }, []);

  const toggleEmojiPicker = () => {
    setShowEmojiPicker(!showEmojiPicker);
    templateTextRef.current.focus();
  };

  const insertEmoji = (infoEmoji) => {
    const _emoji = infoEmoji.native;
    const cursor = templateTextRef.current.selectionStart;
    const text = templateText.slice(0, cursor) + _emoji + templateText.slice(cursor);
    setEmoji(infoEmoji);
    setTemplateText(text);
    setCaretPosition(cursor);
    templateTextRef.current.focus();
  };

  const handleInputLabel = () => {
    let newLabelText = label;
    newLabelText = newLabelText.replace(/ /g, "_");
    newLabelText = newLabelText.toLowerCase();
    newLabelText = newLabelText.replace(/[^a-z0-9_]/gi, '');

    setLabel(newLabelText);
  };

  const initialOfferFields = () => {
    setHeaderOfferTitle('');
    setOfferUrlButtonText('');
    setOfferUrlValue('');
    setOfferButtonText('');
    setOfferButtonValue('');
    setExpiryMinutes('');
  };

  useEffect(() => {
    templateTextRef.current.selectionStart = caretPosition;
    templateTextRef.current.selectionEnd = caretPosition;

    if (emoji) {
      templateTextRef.current.selectionStart = caretPosition + emoji.native.length;
      templateTextRef.current.selectionEnd = caretPosition + emoji.native.length;
    }
  }, [emoji, caretPosition]);

  useEffect(() => {
    setTemplateValues([]);
    setHeaderText('');
    setExpiryMinutes('');
    setExpiryMinutesError('');
    setOfferUrlError('');
    setHeaderOfferTitle('');
    
    if (category === 'AUTHENTICATION') {
      setType('text');
      setSelectedFile(null);
      setAuthenticationText();
      setCallToActions([]);
      setQuickReplies([]);
      setInteractiveAction('copy_code');
      setHeaderType('text');
      setExpirationOffer(false);
      setOfferType('');
      setOfferButtonText('');
      setOfferButtonValue('');
    } else if (category === 'UTILITY') {
      setExpirationOffer(false);
      setOfferType('');
      setOfferButtonText('');
      setOfferButtonValue('');
      setOfferUrlButtonText('');
      setTemplateText('');
    } else if (category === 'MARKETING') {
      setTemplateText('');
    } else {
      setAuthenticationText();
      setInteractiveAction('not_apply');
    }
  }, [category]);

  useEffect(() => {
    if (category === 'AUTHENTICATION') {
      setAuthenticationText();
    }
  }, [language, category]);

  useEffect(() => {
    if (security && expiration && expiryMinutes && verificationCode) {
      setAuthenticationText('');
    }
  }, [security, expiration, expiryMinutes, verificationCode]);

  useEffect(() => {
    if (headerType === 'media') {
      setType('image');
      setHeaderText('');
    } else {
      setType('text');
      if (headerType === 'none') {
        setHeaderText('');
      }
      setSelectedFile(null);
    }
  }, [headerType]);

  useEffect(() => {
    if (templateType === 'lto') {
      if (type === 'document') {
        setType('image');
        setSelectedFile(null);
      }
      setQuickReplies([]);
    } else {
      initialOfferFields();
      setFormErrors({});
      setExpiryMinutesError('');
    }
  }, [templateType, headerType, type]);

  useEffect(() => {
    if (offerUrlButtonText && callToActions.length > 0) {
      const duplicateUrl = callToActions.some(
        (cta) => cta.type === 'URL' && cta.text.toLowerCase() === offerUrlButtonText.toLowerCase()
      );
      setOfferUrlError(duplicateUrl ? t('ws_templates.create_template.button_text_error') : '');
    }
  }, [offerUrlButtonText, callToActions, t]);
  
  useEffect(() => {
    if (expirationOffer && (expiryMinutes < 0 || expiryMinutes > 90 || expiryMinutes?.trim() === '')) {
      setExpiryMinutesError(t('ws_templates.inputs.expiry_minutes_range'));
    } else {
      setExpiryMinutesError('');
    }
  }, [expirationOffer, expiryMinutes, t]);  

  const getId = (key) => key.replaceAll('{{', '').replaceAll('}}', '');

  const isEmptyValue = (value) => !value?.trim();

  const validateOfferFields = () => {
    let formIsValid = true;
    const errors = {};

    const expirationFieldsValid = expirationOffer ? !isEmptyValue(offerButtonText) && !isEmptyValue(offerButtonValue) : true;

    const allFieldsPresent =
      !isEmptyValue(headerOfferTitle) && !isEmptyValue(offerUrlButtonText) && !isEmptyValue(offerUrlValue) && expirationFieldsValid;
  
    if (templateType === 'lto' && !allFieldsPresent) {
      formIsValid = false;

      if (isEmptyValue(headerOfferTitle)) {
        errors.headerOfferTitle = [t('ws_templates.inputs.text_cannot_be_empty')];
      }
      if (isEmptyValue(offerUrlButtonText)) {
        errors.offerUrlButtonText = [t('ws_templates.inputs.text_cannot_be_empty')];
      }
      if (isEmptyValue(offerUrlValue)) {
        errors.offerUrlValue = [t('ws_templates.inputs.text_cannot_be_empty')];
      }

      if (expirationOffer) {
        if (isEmptyValue(offerButtonText)) {
          errors.offerButtonText = [t('ws_templates.inputs.text_cannot_be_empty')];
        }
        if (isEmptyValue(offerButtonValue)) {
          errors.offerButtonValue = [t('ws_templates.inputs.text_cannot_be_empty')];
        }
        if (expiryMinutes < 0 || expiryMinutes > 90 || isEmptyValue(expiryMinutes)) {
          errors.expiryMinutes = [t('ws_templates.inputs.expiry_minutes_range')];
          setExpiryMinutesError(t('ws_templates.inputs.expiry_minutes_range'));
        } else {
          setExpiryMinutesError('');
        }
      }
  
      if (offerUrlError) {
        formIsValid = false;
        errors.offerUrlError = [offerUrlError];
      }
    } else {
      setFormErrors({});
      setExpiryMinutesError('');
      return true;
    }
  
    setFormErrors(errors);
    return formIsValid;
  };

  const handleValidation = () => {
    let formIsValid = true;
    const errors = {};
  
    if (isEmptyValue(label)) {
      formIsValid = false;
      errors.label = [t('ws_templates.inputs.text_cannot_be_empty')];
    }

    if (isEmptyValue(templateText)) {
      formIsValid = false;
      errors.text = [t('ws_templates.inputs.text_cannot_be_empty')];
    }
  
    if (['image', 'document', 'video'].includes(type) && !selectedFile) {
      formIsValid = false;
      errors.file = [t('ws_templates.inputs.select_file')];
    }

    if (templateValues.length > 0) {
      errors.templateValues = [];
      templateValues.forEach((value, index) => {
        if (isEmptyValue(value)) {
          formIsValid = false;
          errors.templateValues[index] = t('ws_templates.inputs.text_cannot_be_empty');
        }
      });
    }

    if (!validateOfferFields()) formIsValid = false;

    if (!formIsValid) {
      dispatch({ type: 'SET_WS_TEMPLATE_ERRORS', errors });
    }

    return formIsValid;
  };

  const checkSpecialChars = (text) => {
    const invalidWhatsappFormats = [
      /\*[^*]+\*/,
      /_[^_]+_/,
      /~[^~]+~/,
      /```[^`]+```/,
      /[\u{1F300}-\u{1F9FF}]|[\u{1F600}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u
    ];

    const hasInvalidFormat = invalidWhatsappFormats.some(pattern => pattern.test(text));

    if (hasInvalidFormat) {
      setHeaderValidationText('Texto sin emoticones o caracteres especiales');
    } else {
      setHeaderValidationText('');
    }

    return text;
  };

  const sanitizeText = (text) => {
    return text.replace(/<[^>]*>?/gm, '')
               .replace(/[*_~`]/g, '')
               .replace(/[\u{1F300}-\u{1F9FF}]|[\u{1F600}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '')
               .trim();
  };

  const submitGsTemplate = (evt) => {
    evt.preventDefault();

    if (!handleValidation()) {
      return;
    }

    if (!isValidCallToActions()) return;
    if (!isValidQuickReplies()) return;

    let exampleText = templateText;

    Object.entries(templateValues).forEach(([key, value]) => {
      const templateIndex = parseInt(key) + 1;
      exampleText = exampleText.replace(`{{${templateIndex}}}`, `[${value}]`);
    });

    const formData = new FormData();

    formData.append('ws_template[name]', label);
    formData.append('ws_template[category]', category);
    formData.append('ws_template[language]', language.value);
    formData.append('ws_template[template_type]', type);
    formData.append('ws_template[text]', templateText);
    formData.append('ws_template[example]', exampleText);

    if (category === 'MARKETING') {
      const buttons = [];
      formData.append('ws_template[expiration_offer]', expirationOffer);
      formData.append('ws_template[header_offer_title]', headerOfferTitle);
      if (offerUrlButtonText?.trim() && offerUrlValue?.trim()) {
        const urlButton = {
          type: 'URL',
          text: offerUrlButtonText,
          url: offerUrlValue
        };
        if (offerUrlType === 'dynamic' && offerUrlVariables.length > 0) {
          urlButton.example = offerUrlVariables;
        }
        buttons.push(urlButton);
      }

      if (expirationOffer) {
        buttons.push({
          type: 'COPY_CODE',
          text: offerButtonText,
          example: offerButtonValue
        });
        formData.append('ws_template[offer_type]', offerType);
        formData.append('ws_template[offer_button_text]', offerButtonText);
        formData.append('ws_template[offer_button_value]', offerButtonValue);
        formData.append('ws_template[code_expiry_minutes]', expiryMinutes);
      }

      buttons.forEach((btn, idx) => {
        Object.entries(btn).forEach(([key, val]) => {
          if (Array.isArray(val)) {
            val.forEach((item) => {
              formData.append(`ws_template[buttons][][${key}][]`, item);
            });
          } else {
            formData.append(`ws_template[buttons][][${key}]`, val);
          }
        });
      });
    }

    if (headerText) {
      formData.append('ws_template[header_text]', sanitizeText(headerText));
      formData.append('ws_template[header_example]', sanitizeText(headerText));
    }

    if (selectedTeam && selectedTeam.team_id !== 'all') {
      formData.append('ws_template[team_id]', selectedTeam.team_id);
    }

    if (['image', 'document', 'video'].includes(type)) {
      let alertMessage;

      switch (type) {
        case 'image':
          if (!['image/jpg', 'image/jpeg', 'image/png'].includes(selectedFile.type)) {
            alertMessage = t('ws_templates.alerts.image_types');
          }
          break;
        case 'document':
          if (selectedFile.type !== 'application/pdf') {
            alertMessage = t('ws_templates.alerts.document_types');
          }
          break;
        case 'video':
          if (!selectedFile.type.includes('video')) {
            alertMessage = t('ws_templates.alerts.video_types');
          }
          break;
        default:
      }

      if (alertMessage) {
        alert(alertMessage);
        setSelectedFile(null);
        return;
      }

      formData.append('ws_template[file]', selectedFile);
    }

    formData.append('ws_template[interactive_action]', interactiveAction);

    if (interactiveAction === 'call_to_action') {
      forEach(callToActions, (cta) => {
        formData.append('ws_template[buttons][][type]', cta.type);
        formData.append('ws_template[buttons][][text]', cta.text);

        switch (cta.type) {
          case 'PHONE_NUMBER':
            formData.append('ws_template[buttons][][phone_number]', `+${cta.value}`);
            break;
          case 'URL':
            formData.append('ws_template[buttons][][url]', cta.value);
            break;
          default:
        }

        if (cta.url_type === 'dynamic') {
          formData.append('ws_template[enable_sample]', true);
        }

        if (cta.type === 'URL' && cta.url_type === 'dynamic') {
          let exampleDinamicUrl = cta.value;
          Object.entries(dynamicUrlValues).forEach(([key, value]) => {
            exampleDinamicUrl = exampleDinamicUrl.replace(`{{${key}}}`, `${value}`);
          });

          formData.append('ws_template[buttons][][example][]', exampleDinamicUrl);
        }
      });
    }

    if (interactiveAction === 'quick_reply') {
      forEach(quickReplies, (qr) => {
        formData.append('ws_template[buttons][][type]', 'QUICK_REPLY');
        formData.append('ws_template[buttons][][text]', qr.text);
      });
    }

    if (interactiveAction === 'copy_code') {
      formData.append('ws_template[add_security_recommendation]', security);
      formData.append('ws_template[add_expiration_text]', expiration);
      formData.append('ws_template[code_expiry_minutes]', expiryMinutes);
      formData.append('ws_template[buttons][][type]', 'OTP');
      formData.append('ws_template[buttons][][otp_type]', 'COPY_CODE');
      formData.append('ws_template[buttons][][text]', authenticationActions.text);
    }

    dispatch({ type: 'TOGGLE_SUBMITTED', submitted: true });
    dispatch(createWsTemplate(formData, csrfToken, retailer_info));
  };

  const renderButtonLabel = (buttonLabel) => (
    <label className="border-12 border-blue btn btn--cta label-for ml-0 py-12 text-blue w-100">
      <i className="mi-upload-fill mr-5 fs-16"></i>
      {t('ws_templates.inputs.add')}
      {' '}
      {buttonLabel}
    </label>
  );

  const renderPreviewFile = () => {
    if (selectedFile) {
      switch (selectedFile.type) {
        case 'image/jpg':
        case 'image/jpeg':
        case 'image/png':
          return (
            <div className="p-relative mt-10 d-flex">
              <img src={URL.createObjectURL(selectedFile)} className="h-100" />
              <i className="mi-close-circle-fill cursor-pointer text-red" onClick={removeFile}></i>
            </div>
          );
        case 'application/pdf':
        case 'video/mp4':
          return (
            <div className="p-relative mt-10">
              <i className={`fas fa-file-${selectedFile.type === 'application/pdf' ? 'pdf' : 'video'} mr-8`}></i>
              <span className="text-blue fs-14">{stringUtils.truncate(selectedFile.name, 40)}</span>
              <i className="mi-close-circle-fill cursor-pointer text-red ml-5" style={{ zIndex: '0', position: 'relative' }} onClick={removeFile}></i>
            </div>
          );
        default:
          return null;
      }
    }

    return false;
  };

  const removeFile = () => {
    setSelectedFile(null);
    const fileInput = document.getElementById('file-input');
    fileInput.value = '';
  };

  const handleInputFile = (e) => {
    if (isEmpty(e.target.files)) return;

    const file = e.target.files[0];
    const fileType = type;

    const SIZE_LIMITS = {
      image: 5242880,  // 5MB
      document: 41943040, // 40MB
      video: 16777216 // 16MB
    };

    if (file.size > SIZE_LIMITS[fileType]) {
      const sizeMB = SIZE_LIMITS[fileType] / (1024 * 1024);
      alert(t("ws_templates.alerts.file_size").replace("{{size}}", sizeMB));
      e.target.value = '';
      return;
    }

    setSelectedFile(file);
  };

  const renderInputFile = () => {
    switch (type) {
      case 'image':
        return (
          <>
            <div className="custom-file mt-10 mb-12" style={{ zIndex: 0 }}>
              <input
                type="file"
                id="file-input"
                className="custom-file-input"
                accept="image/jpg, image/jpeg, image/png"
                onChange={handleInputFile}
              />
              {renderButtonLabel('imágen')}
              <small className="d-block text-muted">Formatos soportados: PNG y JPEG (Tamaño máximo 5 MB)</small>
            </div>
            {!selectedFile && fileValidationText && <span className="error-msg fz-12">{fileValidationText}</span>}
            {renderPreviewFile()}
          </>
        );
      case 'document':
        return (
          <>
            <div className="custom-file mt-10 mb-12" style={{ zIndex: 0 }}>
              <input
                type="file"
                id="file-input"
                className="custom-file-input"
                accept="application/pdf"
                onChange={handleInputFile}
              />
              {renderButtonLabel('documento')}
              <small className="d-block text-muted">Formato soportado: PDF (Tamaño máximo 100 MB)</small>
            </div>
            {!selectedFile && fileValidationText && <span className="error-msg fz-12">{fileValidationText}</span>}
            {renderPreviewFile()}
          </>
        );
      case 'video':
        return (
          <>
            <div className="custom-file mt-10 mb-12" style={{ zIndex: 0 }}>
              <input
                type="file"
                id="file-input"
                className="custom-file-input"
                accept="video/mp4"
                onChange={handleInputFile}
              />
              {renderButtonLabel('video')}
              <small className="d-block text-muted">Formato soportado: MP4 (Tamaño máximo 16 MB)</small>
            </div>
            {!selectedFile && fileValidationText && <span className="error-msg fz-12">{fileValidationText}</span>}
            {renderPreviewFile()}
          </>
        );
      default:
        return null;
    }
  };

  const handleInputChange = (value) => {
    setInteractiveAction(value);
    switch (value) {
      case 'call_to_action':
        setQuickReplies([]);
        addCallToAction();
        break;
      case 'quick_reply':
        setCallToActions([]);
        addQuickReply();
        break;
      default:
        setCallToActions([]);
        setQuickReplies([]);
    }
  };

  const updateQuickReplies = (qr, name, value) => {
    const newQuickReplies = cloneDeep(quickReplies);
    forEach(newQuickReplies, (newQr) => {
      if (newQr.index === qr.index) {
        newQr[name] = value;
      }
    });

    setQuickReplies(newQuickReplies);
  };

  const handleInputChangeQRText = (e, qr) => {
    e.preventDefault();
    const input = e.target;
    const { name, value } = input;
    updateQuickReplies(qr, name, value);
  };

  const removeQR = (qr) => {
    const newQuickReplies = quickReplies.filter((qrItem) => (qrItem.index !== qr.index));
    forEach(newQuickReplies, (qrItem, index) => {
      qrItem.index = index + 1;
    });

    setQuickReplies(newQuickReplies);
  };

  const handleUrlTypeOptions = (cta, isDisabled) => {
    forEach(cta.urlTypeOptions, (option) => {
      option.isDisabled = isDisabled;
    });
  };

  const handleSelect = ({ value }, { name }, cta) => {
    const newCallToActions = cloneDeep(callToActions);
    forEach(newCallToActions, (newCta) => {
      if (newCta.index === cta.index) {
        newCta[name] = value;
        if (value === 'PHONE_NUMBER') {
          newCta.url_type = null;
          newCta.value = '';
          handleUrlTypeOptions(newCta, true);
        } else {
          handleUrlTypeOptions(newCta, false);
        }
      }
    });

    setCallToActions(newCallToActions);
  };

  const updateCallToActions = (cta, name, value) => {
    const newCallToActions = cloneDeep(callToActions);
    forEach(newCallToActions, (newCta) => {
      if (newCta.index === cta.index) {
        newCta[name] = value;
      }
    });

    setCallToActions(newCallToActions);
  };

  const handleInputChangeCtaText = (e, cta) => {
    e.preventDefault();
    const input = e.target;
    const { name, value } = input;
    updateCallToActions(cta, name, value);
  };

  const handleInputChangeCtaValue = (e, cta) => {
    e.preventDefault();
    const input = e.target;
    const { name, value } = input;
    if (cta.type === 'PHONE_NUMBER' && value !== '') {
      const re = /^[0-9\b]+$/;
      if (!re.test(e.target.value)) {
        return;
      }
    }
 
    updateCallToActions(cta, name, value);
  };

  const renderDinamicUrlExample = (cta) => {
    const currentText = cta.value;
    const textArray = currentText.split(/({{[1-9]+[0-9]*}})/g);

    if (!textArray) {
      return;
    }

    return textArray.map((key, index) => {
      if (/{{[1-9]+[0-9]*}}/.test(key)) {
        const id = getId(key);
        return (
          <input
            key={`url-dynamic-input-${index}`}
            value={dynamicUrlValues[id] || ''}
            onChange={(e) => setDinamicUrlValues({ ...dynamicUrlValues, [id]: e.target.value })}
            required
          />
        );
      }

      return (<span key={`url-dynamic-input-${index}`}>{key}</span>);
    });
  };

  const handleCtaTypesOptions = (ctas) => {
    if (ctas.length === 0) return;

    forEach(ctas, (cta) => {
      if (cta.type) {
        forEach(cta.typeOptions, (option) => {
          if (cta.type === option.value) {
            option.isDisabled = false;
          } else {
            option.isDisabled = true;
          }
        });
      } else {
        forEach(cta.typeOptions, (option) => {
          if (find(ctas, { type: option.value })) {
            option.isDisabled = true;
          } else {
            option.isDisabled = false;
          }
        });
      }
    });
  };

  const isValidCallToActions = () => {
    let isValid = true;

    if (isAuthentication()) {
      const newAuthenticationActions = cloneDeep(authenticationActions);
      let error = '';
      let expirationError = '';

      if (!newAuthenticationActions.text || newAuthenticationActions.text.length === 0) {
        error = t('ws_templates.inputs.required');
        isValid = false;
      }

      if (newAuthenticationActions.text && newAuthenticationActions.text.length > 20) {
        error = t('ws_templates.inputs.max_length', { n: 20 });
        isValid = false;
      }

      if (expiration) {
        if (!expiryMinutes || expiryMinutes.length === 0) {
          expirationError = t('ws_templates.inputs.required');
          isValid = false;
        }

        if (expiryMinutes < 1 || expiryMinutes > 90) {
          expirationError = t('ws_templates.inputs.expiry_minutes_range');
          isValid = false;
        }
      }

      setAuthenticationActions({ ...newAuthenticationActions, error });
      setExpiryMinutesError(expirationError);
    } else {
      const newCallToActions = cloneDeep(callToActions);

      forEach(newCallToActions, (cta) => {
        cta.errors = {};

        if (!cta.type) { cta.errors.type = t('ws_templates.inputs.required'); isValid = false; }
        if (!cta.text) { cta.errors.text = t('ws_templates.inputs.required'); isValid = false; }
        if (cta.text && cta.text.length > 20) { cta.errors.text = t('ws_templates.inputs.max_length', { n: 20 }); isValid = false; }
        if (cta.type && cta.type === 'URL' && !cta.url_type) { cta.errors.url_type = t('ws_templates.inputs.required'); isValid = false; }
        if (!cta.value) { cta.errors.value = t('ws_templates.inputs.required'); isValid = false; }
        if (cta.type && cta.type === 'PHONE_NUMBER' && cta.value && cta.value.length > 20) {
          cta.errors.value = t('ws_templates.inputs.max_length', { n: 20 }); isValid = false;
        }
        if (cta.type && cta.type === 'PHONE_NUMBER') {
          if (!/^[0-9]+$/.test(cta.value)) {
            cta.errors.value = t('ws_templates.inputs.numbers_only');
            isValid = false;
          }
        }
        if (offerUrlError) {
          cta.errors.text = offerUrlError;
          isValid = false;
        }
      });

      setCallToActions(newCallToActions);
    }

    return isValid;
  };

  const handleCallToAction = (cta) => {
    cta.errors = {};
  };

  const addCallToAction = () => {
    if (!isValidCallToActions()) return;

    const newCallToAction = {
      index: (callToActions.length + 1),
      type: null,
      text: '',
      url_type: null,
      value: '',
      errors: {},
      typeOptions: [
        { value: 'PHONE_NUMBER', label: 'Número de teléfono' },
        { value: 'URL', label: 'URL' }
      ],
      urlTypeOptions: [
        { value: 'static', label: 'Estático' },
        { value: 'dynamic', label: 'Dinámico' }
      ]
    };

    if (callToActions.length === 0) {
      setCallToActions([newCallToAction]);
    } else {
      const newCallToActions = cloneDeep(callToActions);
      handleCallToAction(newCallToActions[0]);
      newCallToActions.push(newCallToAction);
      handleCtaTypesOptions(newCallToActions);
      setCallToActions(newCallToActions);
    }
  };

  const removeQrErrors = () => {
    const newQuickReplies = cloneDeep(quickReplies);
    forEach(newQuickReplies, (qr) => {
      qr.errors = {};
      if (!qr.text) { qr.errors.text = t('ws_templates.inputs.required'); }
      if (qr.text && qr.text.length > 20) { qr.errors.text = t('ws_templates.inputs.max_length', { n: 20 }); }
    });

    return newQuickReplies;
  };

  const isValidQuickReplies = () => {
    let isValid = true;
    const newQuickReplies = cloneDeep(quickReplies);
    forEach(newQuickReplies, (qr) => {
      qr.errors = {};
      if (!qr.text) { qr.errors.text = 'Requerido'; isValid = false; }
      if (qr.text && qr.text.length > 20) { qr.errors.text = 'Máximo de 20 caracteres'; isValid = false; }
    });

    setQuickReplies(newQuickReplies);
    return isValid;
  };

  const addQuickReply = () => {
    if (!isValidQuickReplies()) return;

    const newQuickReply = {
      index: (quickReplies.length + 1),
      text: '',
      errors: {}
    };

    if (quickReplies.length === 0) {
      setQuickReplies([newQuickReply]);
    } else {
      const newQuickReplies = removeQrErrors();
      newQuickReplies.push(newQuickReply);
      setQuickReplies(newQuickReplies);
    }
  };

  const renderHelpText = (cta) => {
    if (cta.type === 'URL' && cta.url_type === 'static') {
      return (
        <center>
          <span>{t('ws_templates.help_text.static_url')}</span>
        </center>
      );
    } if (cta.type === 'URL' && cta.url_type === 'dynamic') {
      return (
        <center>
          <span>{t('ws_templates.help_text.variables_must_be_numbers')}</span>
          <br />
          <span>{t('ws_templates.help_text.variables_must_be_enclosed_in_double_braces')}</span>
        </center>
      );
    } if (cta.type === 'PHONE_NUMBER') {
      return (
        <center>
          <span>{t('ws_templates.help_text.enter_phone_number')}</span>
          <br />
          <span>{t('ws_templates.help_text.international_format')}</span>
        </center>
      );
    }

    return (<></>);
  };

  const renderHelp = (cta) => {
    if (cta.type === 'PHONE_NUMBER' || (cta.type === 'URL' && !!cta.url_type)) {
      return (
        <>
          <i className="fa fa-question-circle ml-4 fs-14" aria-hidden="true" data-tooltip-id={`tooltip-cta-${cta.index}`} />
          <Tooltip id={`tooltip-cta-${cta.index}`} place="top" effect="solid">
            {renderHelpText(cta)}
          </Tooltip>
        </>
      );
    }
    return null;
  };

  const getPlaceholderCTA = (cta) => {
    let example = '';
    let showHelp = false;

    if (cta.type === 'URL') {
      switch (cta.url_type) {
        case 'static':
          example = t('ws_templates.inputs.static_url_example');
          showHelp = true;
          break;
        case 'dynamic':
          example = t('ws_templates.inputs.dynamic_url_example_2');
          showHelp = true;
          break;
        default:
      }
    }

    if (cta.type === 'PHONE_NUMBER') {
      example = t('ws_templates.inputs.phone_number_example');
      showHelp = true;
    }

    if (!showHelp) {
      return '';
    }

    return example;
  };

  const enableCtaTypeOptions = (ctas) => {
    forEach(ctas, (cta) => {
      forEach(cta.typeOptions, (option) => {
        option.isDisabled = false;
      });
    });
  };

  const removeCta = (cta) => {
    const newCallToActions = callToActions.filter((ctaItem) => (ctaItem.index !== cta.index));
    newCallToActions[0].index = 1;
    enableCtaTypeOptions(newCallToActions);
    setCallToActions(newCallToActions);
  };

  const isAuthentication = () => category === 'AUTHENTICATION';

  const setAuthenticationText = () => {
    if (!isAuthentication() ) {
      setTemplateText('');
      return;
    }

    const content = [`Tu código de verificación es: ${verificationCode}`, `${verificationCode} is your verification code.`];
    const securityContent = [t('ws_templates.inputs.security_text_example'), t('ws_templates.inputs.security_text_example_2')];
    const expirationText = [`Este código caduca en ${expiryMinutes} minutos.`, `This code expires in ${expiryMinutes} minutes.`];

    let text = '';
    language.value === 'spanish' ? text = content[0] : text = content[1];

    if (security) {
      language.value === 'spanish' ? text += (`\n${securityContent[0]}`) : text += (`\n${securityContent[1]}`);
    }

    if (expiration) {
      language.value === 'spanish' ? text += `\n${expirationText[0]}` : text += `\n${expirationText[1]}`;
    }

    setTemplateText(text);
  };

  const handleOfferSwitch = (e) => {
    setExpirationOffer(e.target.checked);
    if (e.target.checked) {
      setOfferType('Tipo');
      setOfferButtonText('Copiar código de oferta');
      setOfferButtonValue('');
    } else {
      setOfferType(null);
      setOfferButtonText(null);
      setOfferButtonValue(null);
    }
  };

  const renderAuthenticationAds = () => (
    <>
      <div className="row">
        <h6 className="ff-poppins-semibold fz-16 mt-16">{t('ws_templates.authentication_ads.title')}</h6>
      </div>
      <div className="col-md-6 pl-6 pr-0">
        <div className="figma-field">
          <label htmlFor="verification-code">{t('ws_templates.authentication_ads.verification_code_label')}</label>
          <input
            id="verification-code"
            type="text"
            name="verificationCode"
            className="mercately-input"
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
          />
        </div>
      </div>
      <div className="row">
        <div className="col-md-6 col-sm-6 col-xs-12 pl-0 pr-15">
          <label className="switch">
            <input
              type="checkbox"
              id="add_security"
              name="security"
              checked={security}
              onChange={(e) => setSecurity(e.target.checked)}
            />
            <span className="slider round" />
          </label>
          <label className="all-day-text fz-14" htmlFor="add_security">{t('ws_templates.authentication_ads.add_security_text')}</label>
          <br />
          <i className="text-gray-lighter fs-11">{t('ws_templates.authentication_ads.add_security_text_2')}</i>
        </div>
        <div className="col-md-6 col-sm-6 col-xs-12 pl-15 pr-0">
          <label className="switch">
            <input
              type="checkbox"
              id="add_expiration"
              name="expiration"
              checked={expiration}
              onChange={(e) => setExpiration(e.target.checked)}
            />
            <span className="slider round" />
          </label>
          <label className="all-day-text fz-14" htmlFor="add_expiration">{t('ws_templates.authentication_ads.add_expiration_text')}</label>
          <br />
          <i className="text-gray-lighter fs-11">{t('ws_templates.authentication_ads.add_expiration_text_2')}</i>
          {expiration && (
          <>
            <div className="figma-field mb-0">
              <label htmlFor="expirationMinutes">{t('ws_templates.authentication_ads.expiration_minutes_label')}</label>
              {expiryMinutesError && expiryMinutesError.length > 0
                    && <span className="ml-10 error-msg fz-12">{expiryMinutesError}</span>}
              <input
                id="expirationMinutes"
                type="number"
                name="text"
                className="mercately-input"
                value={expiryMinutes}
                onChange={(e) => setExpiryMinutes(e.target.value)}
              />
            </div>
          </>
          )}
        </div>
      </div>
    </>
  );

  const renderAuthenticationButton = () => {
    const authCta = {
      index: 1,
      type: 'COPY_CODE',
      text: authenticationActions.text
    };

    return (
      <div className="row">
        <div className="col-md-6 pl-0 pr-6">
          <div className="figma-field">
            <label htmlFor="copy-code-type">{t('ws_templates.authentication_ads.copy_code_type_label')}</label>
            <Select
              options={authenticationOptions}
              placeholder="Selecciona"
              id="copy-code-type"
              name="copy-code-type"
              className="select-tag"
              classNamePrefix="select-tag"
              value={authenticationOptions[0]}
              components={{
                IndicatorSeparator: () => null
              }}
            />
          </div>
        </div>
        <div className="col-md-6 pl-6 pr-0">
          <div className="figma-field">
            <label htmlFor="copy-code-input">{t('ws_templates.authentication_ads.copy_code_text_label')}</label>
            <i className="fa fa-question-circle ml-4 fs-14" aria-hidden="true" data-tooltip-id={`tooltip-cta-${authCta.index}`} />
            <Tooltip id={`tooltip-cta-${authCta.index}`} place="top">
              <center>
                <span>{t('ws_templates.authentication_ads.copy_code_text_label_2')}</span>
                <br />
                <span>{t('ws_templates.authentication_ads.copy_code_text_label_3')}</span>
              </center>
            </Tooltip>

            {authenticationActions.error && authenticationActions.error.length > 0
                && <span className="ml-10 error-msg fz-12">{authenticationActions.error}</span>}
            <input
              id="copy-code-input"
              type="text"
              name="text"
              className="mercately-input"
              value={authenticationActions.text}
              onChange={(e) => handleInputChangeAuthActions(e)}
            />
          </div>
        </div>
      </div>
    );
  };

  const handleInputChangeAuthActions = (e) => {
    e.preventDefault();
    const input = e.target;
    setAuthenticationActions({ ...authenticationActions, text: input.value });
  };

  const addVariable = () => {
    const cursor = templateTextRef.current.selectionStart;
    const newVariable = `{{${templateValues.length + 1}}}`;
    const newText = templateText.slice(0, cursor) + newVariable + templateText.slice(cursor);

    setTemplateText(newText);
    setTemplateValues((oldTV) => [...oldTV, '']);

    setTimeout(() => {
      templateTextRef.current.selectionStart = templateTextRef.current.selectionEnd = cursor + newVariable.length;
      templateTextRef.current.focus();
    }, 0);
  };

  const removeVariable = (index) => {
    const newTemplateValues = templateValues.filter((_, i) => i !== index);
    setTemplateValues(newTemplateValues);

    const variableToRemove = `{{${index + 1}}}`;
    const newText = templateText.replace(variableToRemove, '').trim();
    setTemplateText(newText);
  };

  const setTemplateTextOrder = (newTT) => {
    const variables = newTT.match(/\{\{\d+\}\}/g);
    if (!variables) {
      setTemplateText(newTT);
      setTemplateValues([]);
    } else {
      const sortedVariables = variables.map((v, idx) => `{{${idx + 1}}}`);
      let index = 0;
      // eslint-disable-next-line no-plusplus
      const result = newTT.replace(/\{\{\d+\}\}/g, () => sortedVariables[index++]);
      const oldVariables = templateText.match(/\{\{\d+\}\}/g);

      if (oldVariables) {
        if (variables.length > oldVariables.length) {
          setTemplateValues((oldTV) => oldTV.concat(Array(variables.length - oldVariables.length).fill('')));
        } else {
          setTemplateValues((oldTV) => oldTV.slice(0, sortedVariables.length));
        }
      } else {
        setTemplateValues(Array(variables.length).fill(''));
      }
      setTemplateText(result);
    }
  };

  const onChangeVariableValue = (newValue, idx) => {
    const newTV = [...templateValues];
    newTV[idx] = newValue;
    setTemplateValues(newTV);
  };

  const renderVariables = () => templateValues.map((value, idx) => (
    <div className="align-items-center d-flex mb-12 mt-12" key={`variable-${idx}`}>
      <span className="mr-12 fs-14">{`{{${idx + 1}}}`}</span>
      <div className="figma-field mb-0 p-relative">
        <label htmlFor={`variable-${idx}`}>{t('ws_templates.inputs.example_value')}</label>
        {templateValuesValidationText?.[idx] && templateValuesValidationText?.[idx]?.length > 0
                && <span className="ml-10 error-msg fz-12">{templateValuesValidationText?.[idx]}</span>}
        <input
          type="text"
          id={`variable-${idx}`}
          className="mercately-input"
          value={value}
          onChange={(e) => onChangeVariableValue(e.target.value, idx)}
        />
      </div>
    </div>
  ));

  const backTo = () => {
    navigate(-1);
  };

  const handleHeaderTypeChange = (type) => {
    setHeaderType(type);

    if (type === 'none') {
      setHeaderText('');
    }
  };

  const addOfferUrlVariable = () => {
    if (offerUrlVariables.length >= 1) return;
    const cursor = offerUrlRef.current.selectionStart;
    const newVariable = `{{1}}`;
    const newText = offerUrlValue.slice(0, cursor) + newVariable + offerUrlValue.slice(cursor);

    setOfferUrlValue(newText);
    setOfferUrlVariables(['']);

    setTimeout(() => {
      offerUrlRef.current.selectionStart = offerUrlRef.current.selectionEnd = cursor + newVariable.length;
      offerUrlRef.current.focus();
    }, 0);
  };

  const onChangeOfferUrlVariableValue = (newValue, idx) => {
    const newVars = [...offerUrlVariables];
    newVars[idx] = newValue;
    setOfferUrlVariables(newVars);
  };

  const renderOfferUrlVariables = () => offerUrlVariables.map((value, idx) => (
    <div className="align-items-center d-flex mb-12 mt-12" key={`offer-url-variable-${idx}`}>
      <span className="mr-12 fs-14">{`{{${idx + 1}}}`}</span>
      <div className="figma-field mb-0 p-relative">
        <label htmlFor={`offer-url-variable-${idx}`}>{t('ws_templates.inputs.example_value')}</label>
        <input
          type="text"
          id={`offer-url-variable-${idx}`}
          className="mercately-input"
          value={value}
          onChange={(e) => onChangeOfferUrlVariableValue(e.target.value, idx)}
        />
      </div>
    </div>
  ));

  const setOfferUrlValueOrder = (newValue) => {
    const variables = newValue.match(/\{\{\d+\}\}/g);
    if (!variables) {
      setOfferUrlValue(newValue);
      setOfferUrlVariables([]);
    } else {
      const firstVariable = variables[0];
      const result = newValue.replace(/\{\{\d+\}\}/g, (match, offset) => {
        if (offset === newValue.indexOf(firstVariable)) {
          return '{{1}}';
        }
        return '';
      });
      setOfferUrlVariables(['']);
      setOfferUrlValue(result);
    }
  };

  const handleTemplateTypeChange = (type) => {
    setTemplateType(type);
    if (type === 'lto') {
      setHeaderType('media');
      setType('image');
      setHeaderText('');
    } else {
      setHeaderType('text');
      setHeaderText('');
      setType('');
    }
  };

  const handleCategoryChange = (category) => {
    setCategory(category);
    if (category === 'MARKETING') {
      setTemplateType('custom_message');
    } else {
      setTemplateType('');
    }
  };

  return (
    <div className="ml-sm-60 no-left-margin-xs bg-white new-templates">
      <div className="container-fluid-content text-gray-dark">
        <div className="row">
          <div className="col-12 pt-30">
            <BackToComponent onClick={backTo} />
          </div>
          <div className="col-9">
            <form onSubmit={submitGsTemplate}>
              <div className="form-container pb-40 pt-24">
                <div className="row">
                  <h3 className="page__title fz-24 mb-16">{t('ws_templates.create_template.title')}</h3>
                </div>
                <div className="row-cols-1 mb-15">
                  <div className="figma-field mb-0">
                    <label htmlFor="label">{t('ws_templates.create_template.name_label')}</label>
                    <span className="ml-10 error-msg fz-12">{labelValidationText}</span>
                    <input
                      id="label"
                      type="text"
                      name="label"
                      className="mercately-input"
                      value={label}
                      maxLength={512}
                      onKeyUp={handleInputLabel}
                      onChange={(e) => setLabel(e.target.value)}
                    />
                  </div>
                  <i className="text-gray-lighter fs-11">{t('ws_templates.create_template.name_label_help')}</i>
                </div>

                <h5 className="ff-poppins-semibold fs-18 mb-16 mt-40">{t('ws_templates.create_template.choose_category')}</h5>
                <div className="row mb-15">
                  <div className="col-sm-4 col-xs-12 pl-0 pr-12">
                    <CategorySelector
                      title={t('ws_templates.create_template.marketing_title')}
                      description={t('ws_templates.create_template.marketing_description')}
                      icon="mi-marketing"
                      onPress={() => handleCategoryChange('MARKETING')}
                      selected={category === 'MARKETING'}
                    />
                  </div>
                  <div className="col-sm-4 col-xs-12 px-12">
                    <CategorySelector
                      title={t('ws_templates.create_template.utility_title')}
                      description={t('ws_templates.create_template.utility_description')}
                      icon="mi-message-information"
                      onPress={() => handleCategoryChange('UTILITY')}
                      selected={category === 'UTILITY'}
                    />
                  </div>
                  <div className="col-sm-4 col-xs-12 pl-12 pr-0">
                    <CategorySelector
                      title={t('ws_templates.create_template.authentication_title')}
                      description={t('ws_templates.create_template.authentication_description')}
                      icon="mi-lock-check-outline"
                      onPress={() => handleCategoryChange('AUTHENTICATION')}
                      selected={category === 'AUTHENTICATION'}
                    />
                  </div>
                </div>

                {category === 'MARKETING' && (
                  <div className="col-16">
                    <div
                      className={`col-8 border-8 px-16 py-12 cursor-pointer ${templateType === 'custom_message' ? 'bg-light-cian border-blue' : 'border-gray'}`}
                      onClick={() => handleTemplateTypeChange('custom_message')}>
                      <div className="d-flex align-items-center position-relative">
                        <div className="checkcontainer d-flex align-items-center" style={{ paddingLeft: '24px' }}>
                          <input
                            id="template_type_custom_message"
                            type="radio"
                            name="template_type"
                            value="custom_message"
                            checked={templateType === 'custom_message'}
                            onChange={() => setTemplateType('custom_message')}
                          />
                          <span className="radiobtn"></span>
                          <div className="d-flex flex-column">
                            <span className="fz-14 ml-20">{t('ws_templates.create_template.custom_message.title')}</span>
                            <span className="fz-12 ml-20 text-gray-lighter">
                              {t('ws_templates.create_template.custom_message.subtitle')}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div
                      className={`col-8 border-8 px-16 py-12 cursor-pointer mt-12 ${templateType === 'lto' ? 'bg-light-cian border-blue' : 'border-gray'}`}
                      onClick={() => handleTemplateTypeChange('lto')}>
                      <div className="d-flex align-items-center position-relative">
                        <div className="checkcontainer d-flex align-items-center" style={{ paddingLeft: '24px' }}>
                          <input
                            id="template_type_lto"
                            type="radio"
                            name="template_type"
                            value="lto"
                            checked={templateType === 'lto'}
                            onChange={() => handleTemplateTypeChange('lto')}
                          />
                          <span className="radiobtn"></span>
                          <div className="d-flex flex-column">
                            <span className="fz-14 ml-20">{t('ws_templates.create_template.lto.title')}</span>
                            <span className="fz-12 ml-20 text-gray-lighter">
                              {t('ws_templates.create_template.lto.subtitle')}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <h5 className="ff-poppins-semibold fs-18 mb-16 mt-40">{t('ws_templates.create_template.basic_information')}</h5>
                <div className="row mb-40">
                  <div className="col-sm-6 col-xs-12 pl-0 pr-6">
                    <div className="figma-field mb-0">
                      <label htmlFor="language">{t('ws_templates.create_template.language')}</label>
                      <Select
                        options={languages}
                        value={language}
                        id="language"
                        className="select-tag"
                        classNamePrefix="select-tag"
                        styles={selectStyles}
                        onChange={(value) => setLanguage(value)}
                      />
                    </div>
                  </div>
                  <div className="col-sm-6 col-xs-12 pl-6 pr-0">
                    <div className="figma-field mb-0">
                      <label>{t('ws_templates.create_template.team')}</label>
                      <TeamSelect
                        state={selectedTeam}
                        handleSelect={({ value }, { name }) => {
                          setSelectedTeam({ [name]: value });
                        }}
                      />
                    </div>
                  </div>
                </div>

                {!isAuthentication() && (
                <>
                  <h5 className="ff-poppins-semibold fs-18 mb-16">{t('ws_templates.create_template.choose_header')}</h5>
                  <div className="row">
                    {templateType !== 'lto' && (
                      <div className="col-auto row">
                        <label className="checkcontainer">
                          <input
                            id="header_type_text"
                            type="radio"
                            name="header_type"
                            value="text"
                            checked={headerType === 'text'}
                            onChange={() => handleHeaderTypeChange('text')}
                            disabled={templateType === 'lto'}
                          />
                          <span className="radiobtn"></span>
                        </label>
                        <label htmlFor="header_type_text" className="fz-14 ml-20">{t('ws_templates.create_template.text')}</label>
                      </div>
                    )}
                    <div className="col-auto row">
                      <label className="checkcontainer">
                        <input
                          id="header_type_media"
                          type="radio"
                          name="header_type"
                          value="media"
                          checked={headerType === 'media'}
                          onChange={() => handleHeaderTypeChange('media')}
                        />
                        <span className="radiobtn"></span>
                      </label>
                      <label htmlFor="header_type_media" className="fz-14 ml-20">{t('ws_templates.create_template.media')}</label>
                    </div>

                    <div className="col-auto row">
                      <label className="checkcontainer">
                        <input
                          id="header_type_none"
                          type="radio"
                          name="header_type"
                          value="none"
                          checked={headerType === 'none'}
                          onChange={() => handleHeaderTypeChange('none')}
                        />
                        <span className="radiobtn"></span>
                      </label>
                      <label htmlFor="header_type_none" className="fz-14 ml-20">{t('ws_templates.create_template.none')}</label>
                    </div>
                  </div>
                </>
                )}
                {(!isAuthentication() && headerType === 'text') && (
                  <div className="row-cols-1 mt8">
                    <div className="figma-field mb-0 p-relative">
                      <label htmlFor="header_text">{t('ws_templates.create_template.header_text')}</label>
                      <span className="ml-10 error-msg fz-12">{headerValidationText}</span>
                      <input
                        id="header_text"
                        type="text"
                        name="header_text"
                        className="mercately-input"
                        value={headerText}
                        maxLength={60}
                        onChange={(e) => {
                          setHeaderText(e.target.value);
                          checkSpecialChars(e.target.value);
                        }}
                        disabled={templateType === 'lto'}
                      />
                      <span className="limit b-pos-5 r-16">
                        <span className="counter">{headerText.length}</span>
                        /60
                      </span>
                    </div>
                  </div>
                )}
                {headerType === 'media' && (
                <>
                  <div className="row mt-12">
                    <div className="col-sm-4 col-xs-12 pl-0 pr-8">
                      <HeaderMediaButton
                        selected={type === 'image'}
                        title="Imágen"
                        icon="mi-image-outline"
                        onPress={() => setType('image')}
                      />
                    </div>
                    <div className="col-sm-4 col-xs-12 px-8">
                      <HeaderMediaButton
                        selected={type === 'video'}
                        title="Video"
                        icon="mi-video-line"
                        onPress={() => setType('video')}
                      />
                    </div>
                    {templateType !== 'lto' && (
                      <div className="col-sm-4 col-xs-12 pl-8 pr-0">
                        <HeaderMediaButton
                          selected={type === 'document'}
                          title="Documento"
                          icon="mi-document-outline"
                          onPress={() => setType('document')}
                        />
                      </div>
                    )}
                  </div>
                  <div className="row-cols-1">
                    {renderInputFile()}
                  </div>
                  <div className="row-cols-1 mt-12">
                    <div className="warm-status border-12 py-12 px-32 fs-14">
                      <p className="mb-0">
                        <span className="ff-poppins-semibold">{t('ws_templates.create_template.note')}</span>
                        {' '}
                        {t('ws_templates.create_template.note_description')}
                      </p>
                      <ul className="mb-0 pl-24">
                        <li>{t('ws_templates.create_template.image_types')}</li>
                        <li>{t('ws_templates.create_template.video_types')}</li>
                        <li>{t('ws_templates.create_template.document_types')}</li>
                      </ul>
                    </div>
                  </div>
                </>
                )}

                <h5 className="ff-poppins-semibold fs-18 mb-16 mt-40">{t('ws_templates.create_template.content_message')}</h5>
                <div className="row-cols-1">
                  <div className="figma-field mb-0">
                    <div className="emojis-container-textarea">
                      <label htmlFor="templateText">{t('ws_templates.create_template.text_message')}</label>
                      <span className="ml-10 error-msg fz-12 capitalize">{textValidationText}</span>
                      <div className="emoji-icon">
                        {showEmojiPicker
                        && (
                          <EmojisContainer insertEmoji={insertEmoji} />
                        )}
                        {!isAuthentication() && <AttachEmojiIcon toggleEmojiPicker={toggleEmojiPicker} />}
                      </div>
                      <div className="d-relative">
                        <textarea
                          ref={templateTextRef}
                          value={templateText}
                          onChange={(e) => setTemplateTextOrder(e.target.value)}
                          className="form-control textarea-form bg-light not-box-shadow textarea-counter"
                          maxLength="1024"
                          style={{ height: '118px' }}
                          disabled={isAuthentication()}
                        />
                        <span className="limit b-pos-5 r-16">
                          <span className="counter">{templateText.length}</span>
                          /1024
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {!isAuthentication() && (
                <div className="d-flex align-items-center mt-16 mb-16">
                  <button
                    type="button"
                    className="btn border-blue mx-0 border-5 py-2 text-blue"
                    onClick={addVariable}
                  >
                    {t('ws_templates.create_template.add_variable')}
                  </button>
                  <span className="text-gray-lighter fs-11 ml-16">
                    {t('ws_templates.create_template.variables_description')}
                  </span>
                </div>
                )}

                {templateValues.length > 0 && (
                <span className="ff-poppins-semibold fs-14">{t('ws_templates.create_template.example_variable')}</span>
                )}
                {renderVariables()}

                {isAuthentication() && renderAuthenticationAds()}

                {/* LTO */}
                {!isAuthentication() && category === 'MARKETING' && templateType === 'lto' && (
                  <>
                    <h5 className="ff-poppins-semibold fs-18 mb-16 mt-40">{t('ws_templates.create_template.lto.title')}</h5>
                    <div className="row-cols-1 mt8">
                      <div className="figma-field mb-0 p-relative">
                        <label htmlFor="header_text">{t('ws_templates.create_template.lto.offer_title')}</label>
                        <span className="ml-10 error-msg fz-12">{formErrors?.headerOfferTitle}</span>
                        <input
                          id="header_text"
                          type="text"
                          name="header_text"
                          className="mercately-input"
                          value={headerOfferTitle}
                          maxLength={16}
                          onChange={(e) => {
                            setHeaderOfferTitle(e.target.value);
                            checkSpecialChars(e.target.value);
                          }}
                        />
                        <span className="limit b-pos-5 r-16">
                          <span className="counter">{headerOfferTitle.length}</span>
                          /16
                        </span>
                      </div>

                      <div className='row mt-8 mb-12'>
                        <div className="col-auto pl-0 pr-6">
                          <div className="figma-field">
                            <label htmlFor="offer_button_text">{t('ws_templates.create_template.lto.offer_button_text')}</label>
                            {offerUrlError && <span className="ml-10 error-msg fz-12">{offerUrlError}</span>}
                            {formErrors?.offerUrlButtonText && <span className="ml-10 error-msg fz-12">{formErrors?.offerUrlButtonText}</span>}
                            <input
                              id="offer_button_text"
                              type="text"
                              className="mercately-input"
                              value={offerUrlButtonText}
                              onChange={(e) => setOfferUrlButtonText(e.target.value)}
                            />
                          </div>
                        </div>
                        <div className="col-auto pl-6 pr-0">
                          <div className="figma-field">
                            <label htmlFor="offer_url_type">{t('ws_templates.inputs.url_type')}</label>
                            <Select
                              options={offerUrlTypeOptions}
                              value={offerUrlTypeOptions.find(opt => opt.value === offerUrlType)}
                              onChange={(option) => setOfferUrlType(option.value)}
                              className="select-tag"
                              classNamePrefix="select-tag"
                            />
                          </div>
                        </div>
                        <div className="col-auto pl-6 pr-0">
                          <div className="figma-field">
                            <label htmlFor="offer_url_value">{t('ws_templates.create_template.lto.offer_button_value')}</label>
                            {formErrors?.offerUrlValue && <span className="ml-10 error-msg fz-12">{formErrors?.offerUrlValue}</span>}
                            <input
                              id="offer_url_value"
                              type="text"
                              className="mercately-input"
                              value={offerUrlValue}
                              onChange={(e) => setOfferUrlValueOrder(e.target.value)}
                              ref={offerUrlRef}
                            />
                            {offerUrlType === 'dynamic' && (
                              <button
                                type="button"
                                className="btn border-blue mx-0 border-5 py-2 text-blue mt-8"
                                onClick={addOfferUrlVariable}
                                disabled={offerUrlVariables.length >= 1}
                              >
                                {t('ws_templates.create_template.add_variable')}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>

                      {offerUrlType === 'dynamic' && offerUrlValue && (
                        <div className="col-12 mt-12">
                          <label className="ff-poppins-semibold form-label fs-14">{t('ws_templates.inputs.example_variable')}</label>
                          <div id="dynamic-url-example" className="dynamic-cta-example text-pre-line">
                            {renderOfferUrlVariables()}
                          </div>
                        </div>
                      )}
                      <div className="row-cols-1 mt-12">
                        <div className="d-flex justify-content-start align-items-start row pl-0 pr-6">
                          <label className="switch">
                            <input
                              type="checkbox"
                              id="expiration_offer"
                              name="expiration_offer"
                              checked={expirationOffer}
                              onChange={(e) => handleOfferSwitch(e)}
                            />
                            <span className="slider round" />
                          </label>
                          <label className="all-day-text fz-14" htmlFor="expiration_offer">{t('ws_templates.create_template.lto.add_expiration_offer')}</label>
                        </div>

                        {expirationOffer && (
                          <div className='row mt-8 mb-12'>
                            <div className="col-auto pl-0 pr-6">
                              <div className="figma-field">
                                <label htmlFor="offer_type">{t('ws_templates.create_template.lto.offer_type')}</label>
                                {formErrors?.offerType && <span className="ml-10 error-msg fz-12">{formErrors?.offerType}</span>}
                                <input
                                  id="offer_type"
                                  type="text"
                                  name="offer_type"
                                  className="mercately-input"
                                  value={offerType}
                                  onChange={(e) => setOfferType(e.target.value)}
                                  disabled={expirationOffer}
                                />
                              </div>
                            </div>

                            <div className="col-auto pl-6 pr-0">
                              <div className="figma-field">
                                <label htmlFor="offer_type">{t('ws_templates.create_template.lto.offer_button_text')}</label>
                                {formErrors?.offerButtonText && <span className="ml-10 error-msg fz-12">{formErrors?.offerButtonText}</span>}
                                <input
                                  id="offer_button_text"
                                  type="text"
                                  name="offer_button_text"
                                  className="mercately-input"
                                  value={offerButtonText}
                                  onChange={(e) => setOfferButtonText(e.target.value)}
                                  disabled={expirationOffer}
                                />
                              </div>
                            </div>

                            <div className="col-auto pl-6 pr-0">
                              <div className="figma-field">
                                <label htmlFor="offer_value">{t('ws_templates.create_template.lto.offer_button_value')}</label>
                                {formErrors?.offerButtonValue && <span className="ml-10 error-msg fz-12">{formErrors?.offerButtonValue}</span>}
                                <i className="fa fa-question-circle ml-4 fs-14" aria-hidden="true" data-tooltip-id={`tooltip-offer-button-value`} />
                                <Tooltip id={`tooltip-offer-button-value`} place="top">
                                  <center>
                                    <span>{t('ws_templates.authentication_ads.copy_code_text_label_2')}</span>
                                    <br />
                                    <span>{t('ws_templates.authentication_ads.copy_code_text_label_3')}</span>
                                  </center>
                                </Tooltip>
                                <input
                                  id="offer_value"
                                  type="text"
                                  name="offer_value"
                                  className="mercately-input"
                                  value={offerButtonValue}
                                  onChange={(e) => setOfferButtonValue(e.target.value)}
                                />
                              </div>
                            </div>
                            <div className="col-auto pl-6 pr-0">
                              <div className="figma-field">
                                <label htmlFor="expirationMinutes">{t('ws_templates.inputs.expiry_minutes')}</label>
                                {expiryMinutesError && <span className="ml-10 error-msg fz-12">{expiryMinutesError}</span>}
                                <input
                                  id="expirationMinutes"
                                  type="number"
                                  name="text"
                                  className="mercately-input"
                                  value={expiryMinutes}
                                  onChange={(e) => setExpiryMinutes(e.target.value)}
                                />
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                )}

                <h5 className="ff-poppins-semibold fs-18 mb-16 mt-40">{t('ws_templates.create_template.buttons')}</h5>

                {isAuthentication() && renderAuthenticationButton()}

                {!isAuthentication() && (
                  <div className="d-flex">
                    <div className="col-auto row">
                      <label className="checkcontainer">
                        <input
                          id="interactive_action_not_apply"
                          type="radio"
                          name="interactive_action"
                          value="not_apply"
                          checked={interactiveAction === 'not_apply'}
                          onChange={() => handleInputChange('not_apply')}
                        />
                        <span className="radiobtn"></span>
                      </label>
                      <label htmlFor="interactive_action_not_apply" className="fz-14 ml-20">{t('ws_templates.create_template.none')}</label>
                    </div>

                    <div className="col-auto row">
                      <label className="checkcontainer">
                        <input
                          id="interactive_action_call_to_action"
                          type="radio"
                          name="interactive_action"
                          value="call_to_action"
                          checked={interactiveAction === 'call_to_action'}
                          onChange={() => handleInputChange('call_to_action')}
                        />
                        <span className="radiobtn"></span>
                      </label>
                      <label htmlFor="interactive_action_call_to_action" className="fz-14 ml-20">{t('ws_templates.create_template.call_to_actions')}</label>
                    </div>

                    {templateType !== 'lto' && (
                      <div className="col-auto row">
                        <label className="checkcontainer">
                        <input
                          id="interactive_action_quick_reply"
                          type="radio"
                          name="interactive_action"
                          value="quick_reply"
                          checked={interactiveAction === 'quick_reply'}
                          onChange={() => handleInputChange('quick_reply')}
                        />
                        <span className="radiobtn"></span>
                        </label>
                        <label htmlFor="interactive_action_quick_reply" className="fz-14 ml-20">{t('ws_templates.create_template.quick_replies')}</label>
                      </div>
                    )}
                  </div>
                )}

                {/* Call to actions */}
                {!isAuthentication() && callToActions.map((cta, index) => (
                  <div key={`call-to-action-${index}`}>
                    <div className="row col-md-12 px-0">
                      <div className="col-md-3 pl-0 pr-4">
                        <div className="figma-field">
                          <label htmlFor="cta-type">{t('ws_templates.create_template.type')}</label>
                          {cta.errors.type && <span className="ml-10 error-msg fz-12">{cta.errors.type}</span>}
                          <Select
                            options={cta.typeOptions}
                            onChange={(option, tag) => handleSelect(option, tag, cta)}
                            placeholder={t('ws_templates.create_template.select')}
                            id="cta-type"
                            name="type"
                            className="select-tag"
                            classNamePrefix="select-tag"
                            value={[cta.typeOptions.find((el) => el.value === cta.type)]}
                            components={{
                              IndicatorSeparator: () => null
                            }}
                          />

                        </div>
                      </div>
                      <div className="col-md-3 px-4">
                        <div className="figma-field">
                          <label htmlFor="cta-text-button">{t('ws_templates.create_template.text_button')}</label>
                          {index === 0 && (
                          <>
                            <i className="fa fa-question-circle ml-4 fs-14" aria-hidden="true" data-tooltip-id={`tooltip-cta-button-text-${cta.index}`} />
                            <Tooltip id={`tooltip-cta-button-text-${cta.index}`} place="top">
                              <center>
                                <span>{t('ws_templates.create_template.text_button_description')}</span>
                                <br />
                                <span>{t('ws_templates.create_template.text_button_description_2')}</span>
                              </center>
                            </Tooltip>
                          </>
                          )}
                          {cta.errors.text && <span className="ml-10 error-msg fz-12">{cta.errors.text}</span>}
                          <input
                            id="cta-text-button"
                            type="text"
                            name="text"
                            className="mercately-input"
                            value={cta.text}
                            onChange={(e) => handleInputChangeCtaText(e, cta)}
                          />
                        </div>
                      </div>
                      {cta.type !== 'PHONE_NUMBER' && (
                        <div className="col-md-3 px-4">
                          <div className="figma-field">
                            <label htmlFor="cta-url-type">{t('ws_templates.create_template.url_type')}</label>
                            {cta.errors.url_type && <span className="ml-10 error-msg fz-12">{cta.errors.url_type}</span>}
                            <Select
                              options={cta.urlTypeOptions}
                              onChange={(option, tag) => handleSelect(option, tag, cta)}
                              placeholder={t('ws_templates.create_template.select')}
                              id="cta-url-type"
                              name="url_type"
                              className="select-tag"
                              classNamePrefix="select-tag"
                              value={[cta.urlTypeOptions.find((el) => el.value === cta.url_type)]}
                              components={{
                                IndicatorSeparator: () => null
                              }}
                            />
                          </div>
                        </div>
                      )}
                      <div className="col-md-3 pl-4 pr-0">
                        <div className="figma-field">
                          <div className="d-flex justify-content-between">
                            <div>
                              <label htmlFor="ctaValue">{t('ws_templates.create_template.value')}</label>
                              {renderHelp(cta)}
                              {cta.errors.value && <span className="ml-10 error-msg fz-12">{cta.errors.value}</span>}
                            </div>
                            {callToActions.length > 1
                            && (
                            <a className="pr-8" type="button" onClick={() => removeCta(cta)}>
                              <CloseIcon className="fill-dark" />
                            </a>
                            )}
                          </div>
                          <input
                            id="ctaValue"
                            type="text"
                            name="value"
                            placeholder={getPlaceholderCTA(cta)}
                            className="mercately-input"
                            value={cta.value}
                            onChange={(e) => handleInputChangeCtaValue(e, cta)}
                          />
                        </div>
                      </div>
                    </div>
                    {(cta.url_type === 'dynamic' && !!cta.value)
                    && (
                    <div className="row col-md-12 d-flex justify-content-end">
                      <div className="col-md-3">
                        <div className="form-group mb-0 pl-10 mb-16">
                          <label htmlFor="ctaDinamicUrlExample" className="ff-poppins-semibold form-label fs-14">{t('ws_templates.create_template.dynamic_url_example')}</label>
                          <div id="dynamic-url-example" className="dynamic-cta-example text-pre-line">{renderDinamicUrlExample(cta)}</div>
                        </div>
                      </div>
                    </div>
                    )}
                  </div>
                ))}

                {!isAuthentication() && callToActions.length === 1
                && (
                <div className="row">
                  <button
                    type="button"
                    className="blue-button btn mx-0"
                    onClick={addCallToAction}
                  >
                    {t('ws_templates.inputs.add_cta')}
                  </button>
                </div>
                )}

                {/* Quick replies */}
                {!isAuthentication() && templateType !== 'lto' && quickReplies.map((qr, index) => (
                  <div key={`quick-reply-${index}`}>
                    <div className="row col-md-12 px-0">
                      <div className="figma-field w-50">
                        <div className="d-flex justify-content-between">
                          <div>
                            <label htmlFor={`quick-reply-${index}`}>{t('ws_templates.create_template.text')}</label>
                            {index === 0 && (
                            <>
                              <i className="fa fa-question-circle ml-4 fs-14" aria-hidden="true" data-tooltip-id={`tooltip-qr-${qr.index}`} />
                              <Tooltip id={`tooltip-qr-${qr.index}`} place="top">
                                <center>
                                  <span>{t('ws_templates.create_template.quick_reply_text_description')}</span>
                                  <br />
                                  <span>{t('ws_templates.create_template.quick_reply_text_description_2')}</span>
                                </center>
                              </Tooltip>
                            </>
                            )}
                            {qr.errors.text && <span className="ml-10 error-msg fz-12">{qr.errors.text}</span>}
                          </div>
                          {quickReplies.length > 1
                            && (
                            <a className="pr-8" type="button" onClick={() => removeQR(qr)}>
                              <CloseIcon className="fill-dark" />
                            </a>
                            )}
                        </div>
                        <input
                          id={`quick-reply-${index}`}
                          type="text"
                          name="text"
                          className="mercately-input"
                          value={qr.text}
                          onChange={(e) => handleInputChangeQRText(e, qr)}
                        />
                      </div>
                    </div>
                  </div>
                ))}

                {!isAuthentication() && quickReplies.length > 0 && quickReplies.length < 3
                && (
                <div className="row">
                  <button
                    type="button"
                    className="blue-button btn mx-0"
                    onClick={addQuickReply}
                  >
                    {t('ws_templates.inputs.add_quick_reply')}
                  </button>
                </div>
                )}

                {/* Save button */}
                <div className="row mt-30">
                  <div className="text-right w-100">
                    <button
                      type="submit"
                      className="blue-button btn mx-0 px-40"
                      disabled={submitted}
                    >
                      { submitted && <span className="pr-5"><i className="fas fa-spinner"></i></span> }
                      {t('ws_templates.inputs.save')}
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>

          <div className="vertical-divider"></div>

          {/* Preview */}
          <div className="col-3 pl-0">
            <WsTemplatePreview
              type={type}
              headerText={headerText}
              templateText={templateText}
              templateValues={templateValues}
              quickReplies={quickReplies}
              callToActions={callToActions}
              file={selectedFile}
              authButton={authenticationActions}
              expirationOffer={expirationOffer}
              headerOfferTitle={headerOfferTitle}
              offerButtonText={offerButtonText}
              offerButtonValue={offerButtonValue}
              offerUrlButtonText={offerUrlButtonText}
              expiryMinutes={expiryMinutes}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default WsTemplatesNew;
