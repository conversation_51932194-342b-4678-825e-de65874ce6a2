import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ScrollingCarousel } from '@trendyol-js/react-carousel';
import { filter, matches } from 'lodash';
import { Tooltip as ReactTooltip } from 'react-tooltip';

import MessageInput from './MessageInput';
import EmojisContainer from './EmojisContainer';

import AttachEmojiIcon from './AttachEmojiIcon';
import RecordingAudioPanel from '../shared/RecordingAudioPanel';
import SendButton from './SendButton';

import SelectedProductImageContainerAWS from '../shared/SelectedProductImageContainerAWS';
import SelectedFastAnswerImageContainer from './SelectedFastAnswerImageContainer';
import AddProductsButton from '../shared/AddProductsButton';
import FastAnswerButton from '../shared/FastAnswerButton';
import DealButton from '../shared/DealButton';
import GenerateOrderButton from '../shared/GenerateOrderButton';
import FastAnswersShortcut from '../shared/FastAnswersShortcut';
import ChatAttachMenu from '../shared/ChatAttachMenu';
import ChatAdditionalMenu from '../shared/ChatAdditionalMenu';
import ChatNoteToggle from '../shared/ChatNoteToggle';
import AlertIcon from '../icons/AlertIcon';

import fileUtils from '../../util/fileUtils';
import {formatProductMessage, handleOnPasteInput} from '../../util/messageUtil';
import { collectionWidthContainer as collectionWidthContainerUtils } from '../../util/widthUtils';

import { MAX_FILE_SIZE_TRANSFER_MSN_IG } from '../../constants/chatFileSizes';

import PaperclipIcon from '../icons/PaperclipIcon';

import { getInstagramFastAnswersForShortcut } from '../../actions/fastAnswers';
import SuggestedAnswers from '../shared/SuggestedAnswers';
import AiSuggestionsButton from '../shared/AiSuggestionsButton';
import ManageCustomerEvent from '../shared/CustomerDetails/ManageCustomerEvent';
import ChatNoteComponent from '../shared/ChatNoteComponent';
import ToggleMiaResponses from '../chat/ToggleMiaResponses';

const MessageForm = ({
  currentCustomer,
  handleSubmitMessage,
  handleSubmitImg,
  handleVideoSubmit,
  toggleMap,
  objectPresence,
  pasteImages,
  getCaretPosition,
  selectedProduct,
  removeSelectedProduct,
  selectedFastAnswer,
  removeSelectedFastAnswer,
  insertEmoji,
  onMobile,
  toggleLoadImages,
  toggleFastAnswers,
  toggleProducts,
  counter,
  setCounter,
  maximizeInputText,
  inputFilled,
  openDealModal,
  openGenerateOrderModal,
  resetShortcutAction,
  sendAudio,
  customer,
  processNote
}) => {
  const dispatch = useDispatch();

  const { recordingAudio } = useSelector((reduxState) => reduxState.chatsReducer);
  const { igFastAnswers } = useSelector((reduxState) => reduxState.fastAnswersReducer);
  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);

  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [collectionWidthContainer, setCollectionWidthContainer] = useState(60);
  const [imagesUrls, setImagesUrls] = useState([]);
  const [enabledShortcutAction, setEnabledShortcutAction] = useState(true);
  const [enabledShortcutFastAnswers, setEnabledShortcutFastAnswers] = useState(false);
  const [openedAttach, setOpenedAttach] = useState(false);
  const [openedSuggestedAnswers, setOpenedSuggestedAnswers] = useState(false);
  const [openedAdditionalMenu, setOpenedAdditionalMenu] = useState(false);
  const [openedChatNoteToggle, setOpenedChatNoteToggle] = useState(false);
  const [selectedOption, setSelectedOption] = useState('chats');
  const [openedCustomerEvent, setOpenedCustomerEvent] = useState(false);

  let textContent = '';

  const handleSubmit = (e) => {
    const input = $('#divMessage');
    const text = input.text();
    if (text.trim() === '' && selectionPresent() === false) return;

    const txt = getText();
    handleSubmitMessage(e, txt);

    setShowEmojiPicker(false);
    input.html(null);
    maximizeInputText();
    resetFastAnswers();
  };

  const handleFileSubmit = (e) => {
    const el = e.target;
    const file = el.files[0];

    if (file.type !== 'audio/mpeg') {
      alert('Error: El archivo debe ser de tipo audio MP3');
      return;
    }

    // Max DYNAMIC Mb allowed
    const isValidSizeFile = fileUtils.isValidFileSizeForMsnOrIg(file);

    if (!isValidSizeFile) {
      const allowedFileSize = fileUtils.sizeFileInMB(MAX_FILE_SIZE_TRANSFER_MSN_IG);

      alert(`Error: Máximo permitido ${allowedFileSize}MB`);
      return;
    }

    const data = new FormData();
    data.append('file_data', file);
    handleSubmitImg(el, data);
    $('#attach-file').val('');
  };

  const onKeyPress = (e) => {
    if (e.which === 13 && e.shiftKey && e.target.innerText.trim() === '') e.preventDefault();
    if (e.which === 13 && !e.shiftKey) {
      e.preventDefault();
      resetFastAnswers();
      handleSubmit(e);
    }
    setCounter(document.getElementById('divMessage')?.innerText.length);
  };

  const handleOnKeyDown = (e) => {
    const { textContent: _textContent } = e.currentTarget;
    const maxlength = 1000;
    if (_textContent.length >= maxlength && ![8, 13, 37, 38, 39, 40].includes(e.which)) {
      e.preventDefault();
      return;
    }

    e.persist();

    if (e.which === 27) {
      resetFastAnswers();
      if (enabledShortcutFastAnswers) setEnabledShortcutAction(false);
    }
  };

  const handleOnChange = (e) => {
    textContent = e.currentTarget.textContent;
    setCounter(textContent.length);

    if (textContent.length === 0) {
      resetFastAnswers();
      setEnabledShortcutAction(true);
      return;
    }

    if (!enabledShortcutAction) return;

    if (textContent.charAt(0).charCodeAt(0) === 47) {
      if (textContent.length === 1) {
        dispatch(
          getInstagramFastAnswersForShortcut({ page: 1, limit: 3, customer: currentCustomer })
        );
        setEnabledShortcutFastAnswers(true);
        return;
      }

      textContent = textContent.substring(1).trim();

      if (textContent.length > 0) {
        dispatch(
          getInstagramFastAnswersForShortcut({
            page: 1,
            search: textContent,
            limit: 5,
            customer: currentCustomer
          })
        );
        setEnabledShortcutFastAnswers(true);
      }
    } else if (enabledShortcutFastAnswers) {
      resetFastAnswers();
    }
  };

  const handleAdditionalMenu = () => {
    setOpenedAdditionalMenu(!openedAdditionalMenu);
  };

  const handleChatNoteToggle = () => {
    setOpenedChatNoteToggle(!openedChatNoteToggle);
  };

  const handleOptionChange = (option) => {
    setSelectedOption(option);
    dispatch({
      type: 'OPENED_NOTES',
      openedNote: option !== 'chats'
    });
    setOpenedChatNoteToggle(!openedChatNoteToggle);
  };

  useEffect(() => {
    if (selectedProduct) {
      const productString = formatProductMessage(
        selectedProduct,
        retailer_info.tax_amount,
        'instagram'
      );
      $('#divMessage').html(productString);
      maximizeInputText();

      const newImageUrls = filter(selectedProduct.image_urls, matches({ display: true }));

      setImagesUrls(newImageUrls);

      const newWidth = collectionWidthContainerUtils(newImageUrls.length);
      setCollectionWidthContainer(newWidth);
    }
  }, [selectedProduct]);

  useEffect(() => {
    setOpenedSuggestedAnswers(false);
  }, [customer]);

  useEffect(() => {
    dispatch({ type: 'OPENED_NOTES', openedNote: false });
  }, [dispatch]);

  useEffect(() => {
    if (!resetShortcutAction) return;
    resetFastAnswers();
  }, [resetShortcutAction]);

  const getText = () => {
    const input = $('#divMessage');
    const txt = input.html();

    return txt.replace(/<br>/g, '\n').replace(/&amp;/g, '&');
  };

  const selectionPresent = () => objectPresence();

  const getLocation = () => {
    if (navigator.geolocation) {
      toggleMap();
    } else {
      alert('La geolocalización no está soportada en este navegador');
    }
  };

  const toggleEmojiPicker = () => {
    setShowEmojiPicker(!showEmojiPicker);
  };

  const resetFastAnswers = () => {
    const data = { templates: { data: [] } };
    dispatch({ type: 'SET_SHORTCUT_INSTAGRAM_FAST_ANSWERS', data });
    setEnabledShortcutFastAnswers(false);
  };

  const handleAttachMenu = () => {
    setOpenedAttach(!openedAttach);
  };

  const manageFileSubmit = (e) => {
    handleFileSubmit(e);
    handleAttachMenu();
  };

  const openAiSuggestionsModal = () => {
    setOpenedSuggestedAnswers(true);
  };

  const openCustomerEventModal = () => {
    setOpenedCustomerEvent(!openedCustomerEvent);
  };

  const handleOnPaste = (e) => {
    handleOnPasteInput(e, handleOnChange, pasteImages);
  };

  return (
    <div className="col-xs-12 chat-input chat-input-container-toggle">
      {enabledShortcutFastAnswers && (
        <FastAnswersShortcut fastAnswers={igFastAnswers} onMobile={onMobile} />
      )}

      {openedSuggestedAnswers && (
        <SuggestedAnswers
          openSuggestedAnswers={setOpenedSuggestedAnswers}
          onMobile={onMobile}
          currentCustomer={customer}
          platform="instagram"
        />
      )}
      {openedAttach && (
        <ChatAttachMenu
          handleAttachMenu={handleAttachMenu}
          canAttachFile={false}
          handleFileSubmit={manageFileSubmit}
          toggleLoadImages={toggleLoadImages}
          handleVideoSubmit={handleVideoSubmit}
          getLocation={getLocation}
          openProducts={toggleProducts}
          openGenerateOrderModal={openGenerateOrderModal}
        />
      )}
      {openedAdditionalMenu && (
        <ChatAdditionalMenu
          handleAdditionalMenu={handleAdditionalMenu}
          openActivityModal={openCustomerEventModal}
          miaIntegration={retailer_info?.mia_suggestions_integrated || false}
          whatsapp={false}
          comment={false}
        />
      )}
      {openedChatNoteToggle && (
        <ChatNoteToggle
          handleChatNoteToggle={handleChatNoteToggle}
          selectedOption={selectedOption}
          selectOption={handleOptionChange}
        />
      )}
      {openedCustomerEvent && (
        <ManageCustomerEvent
          hideNotification
          customer={customer}
          toggleModal={openCustomerEventModal}
        />
      )}
      <div className="col-6 mb-6">
        <div
          onClick={handleChatNoteToggle}
          className="align-items-center cursor-pointer border-30 d-inline-flex fz-12 pl-4 pr-6 pt-2 pb-2 text-blue bg-light-gray"
        >
          <div className="align-items-center ml-2">
            <span className="fz-12">{selectedOption === 'chats' ? 'Chat' : 'Agregar nota'}</span>
          </div>
          <i
            className={
              openedChatNoteToggle ? 'mi-arrow-ios-upward-outline' : 'mi-arrow-ios-downward-outline'
            }
          />
        </div>
        {
          customer?.id && (
            <ToggleMiaResponses customer={customer} platform="instagram" />
          )
        }
      </div>
      {selectedOption === 'chats' ? (
        <>
          <div className="text-input row mx-0 no-gutters pt-6 pb-6 px-15 border-input-top">
            <div className="d-flex col-7 col-md-9">
              <span className="bg-light border-left-8 flex-grow-1">
                <MessageInput
                  pasteImages={handleOnPaste}
                  onKeyPress={onKeyPress}
                  getCaretPosition={getCaretPosition}
                  inputFilled={inputFilled}
                  handleOnChange={handleOnChange}
                  handleOnKeyDown={handleOnKeyDown}
                />
              </span>
              {selectedProduct && imagesUrls.length > 0 && (
                <span
                  className="bg-light d-flex flex-wrap"
                  style={{ minWidth: collectionWidthContainer, maxWidth: collectionWidthContainer }}
                >
                  <SelectedProductImageContainerAWS
                    onRemove={removeSelectedProduct}
                    urls={imagesUrls}
                  />
                </span>
              )}
              {selectedFastAnswer && selectedFastAnswer.attributes.image_url && (
                <span className="bg-light">
                  <SelectedFastAnswerImageContainer
                    selectedFastAnswer={selectedFastAnswer}
                    removeSelectedFastAnswer={removeSelectedFastAnswer}
                  />
                </span>
              )}
            </div>
            <div className="col-5 col-md-3 bg-light border-right-8 d-flex">
              <div className="p-relative flex-grow-1 pr-8 d-flex justify-content-end align-items-center space-input-icons">
                {!recordingAudio && (
                  <>
                    <span className="subtitle">{`${counter}/1000`}</span>
                    {showEmojiPicker && <EmojisContainer insertEmoji={insertEmoji} />}
                  </>
                )}
                {counter <= 0 && <RecordingAudioPanel onMobile={onMobile} sendAudio={sendAudio} />}
                {!recordingAudio && (
                  <>
                    <AttachEmojiIcon onMobile={onMobile} toggleEmojiPicker={toggleEmojiPicker} />
                    {counter > 0 && <SendButton onMobile={onMobile} handleSubmit={handleSubmit} />}
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="scrolling-carousel-container">
            <ScrollingCarousel className="scrolling-carousel">
              <div className="d-inline-block">
                <div
                  onClick={handleAttachMenu}
                  className="ml-12 cursor-pointer fs-12 border-30"
                  data-tooltip-id="PaperclipIcon-tooltip"
                >
                  <PaperclipIcon className="h-20 m-10" />
                  <ReactTooltip
                    id="PaperclipIcon-tooltip"
                    place="top"
                    style={{ maxWidth: '250px', whiteSpace: 'normal' }}
                  >
                    <center>
                      <span>Adjuntar</span>
                    </center>
                  </ReactTooltip>
                </div>
              </div>
              <div className="d-inline-block">
                <div className="vertical-hr"></div>
              </div>
              <FastAnswerButton reduced toggleFastAnswers={toggleFastAnswers} />
              <AddProductsButton reduced openProducts={toggleProducts} />
              <GenerateOrderButton reduced openGenerateOrderModal={openGenerateOrderModal} />
              <DealButton reduced openDealModal={openDealModal} />
              {retailer_info?.mia_suggestions_integrated && (
                <AiSuggestionsButton reduced openAiSuggestionsModal={openAiSuggestionsModal} />
              )}
              <div className="d-inline-block mt-10">
                <span
                  onClick={handleAdditionalMenu}
                  className="mi-more-horizontal-fill m-12 cursor-pointer fs-24 pt-3 pl-2 pr-2 border-15 bg-dark-gray text-white"
                />
              </div>
            </ScrollingCarousel>
          </div>
        </>
      ) : (
        <ChatNoteComponent
          processNote={processNote}
          setSelectedOption={setSelectedOption}
          AlertIcon={AlertIcon}
          currentCustomer={customer}
          whatsapp={false}
          comment={false}
        />
      )}
    </div>
  );
};

export default MessageForm;
