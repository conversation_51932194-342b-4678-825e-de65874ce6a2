/* eslint-disable react/jsx-props-no-spreading */
import React, {
  useEffect, useState, useMemo, useCallback
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import ReactFlow, {
  Background,
  Controls,
  ReactFlowProvider,
  useReactFlow,
  useNodesState,
  useEdgesState,
  getIncomers,
  getOutgoers,
  getConnectedEdges
} from 'reactflow';
import {
  cloneDeep, filter, forEach, isEmpty, orderBy, truncate
} from 'lodash';
import { forkJoin } from 'rxjs';

import CustomNode from './Types/CustomNode';
import useAutoLayout from './useAutoLayout';
import PreviewBot from './PreviewBot';

import 'reactflow/dist/style.css';
import { getRetailerInfo } from '../../../actions/retailerUsersActions';

import NodeOptionComponent from './NodeOptionComponent';
import ChatbotModel from '../ChatbotModel';
import ChatbotOptionModel from '../ChatbotOptionModel';
import OptionListNode from './Types/OptionListNode';
import InitialNode from './Types/InitialNode';

import { CHATBOT_OPTIONS_TYPE } from '../../../constants/AppConstants';
import { uuid } from './utils';

const proOptions = {
  account: 'paid-pro',
  hideAttribution: true
};

const defaultEdgeOptions = {
  type: 'step',
  pathOptions: { offset: 5 }
};

const fitViewOptions = {
  padding: 0.95
};

const Flow = ({ direction = 'TB' }) => {
  const dispatch = useDispatch();

  const { id } = useParams();

  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);
  const { getNode, deleteElements, getNodes } = useReactFlow();

  // this hook handles the computation of the layout once the elements or the direction changes
  const { fitView } = useReactFlow();

  useAutoLayout({ direction });

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [chatbot, setChatbot] = useState(null);
  const [nodeOption, setNodeOption] = useState(null);
  const [openNodeOption, setOpenNodeOption] = useState(false);
  const [openPreviewBot, setOpenPreviewBot] = useState(false);
  const [paramsForNewNode, setParamsForNewNode] = useState({});
  const [counterOptionTypes, setCounterOptionTypes] = useState({});

  const nodeTypes = useMemo(
    () => ({ custom: CustomNode, optionList: OptionListNode, initialNode: InitialNode }),
    []
  );

  // Obtener el estado del grafo usando useStoreState
  // const allNodes = useStore((store) => store.nodeInternals);

  const onNodesDelete = useCallback(
    (deleted) => {
      setEdges(
        deleted.reduce((acc, node) => {
          const incomers = getIncomers(node, nodes, edges);
          const outgoers = getOutgoers(node, nodes, edges);
          const connectedEdges = getConnectedEdges([node], edges);

          const remainingEdges = acc.filter((edge) => !connectedEdges.includes(edge));

          const createdEdges = incomers.flatMap(({ id: source }) => outgoers.map(({ id: target }) => ({ id: `${source}->${target}`, source, target })));

          return [...remainingEdges, ...createdEdges];
        }, edges)
      );

      setNodes(
        deleted
          .reduce((_, node) => filter(nodes, (o) => o.id !== node.id), nodes)
          .map((node) => {
            const nodeData = {
              ...node.data,
              option: {
                ...node.data.option,
                timeline: new Date().getTime()
              }
            };

            return {
              ...node,
              data: nodeData
            };
          })
      );
    },
    [nodes, edges]
  );

  useEffect(() => {
    loadOptionsSaved();
    dispatch(getRetailerInfo());
  }, []);

  const loadOptionsSaved = () => {
    const promises = [ChatbotModel.findById(id), ChatbotOptionModel.get(id)];
    forkJoin(promises).subscribe((results) => {
      if (results[0].chatbot.version !== 2) {
        document.location.href = `/`;
      } else {
        setChatbot(results[0].chatbot);
        if (isEmpty(results[1].options)) {
          addInitialNode();
        } else {
          buildOptionsSavedHowNodes(results[1].options);
        }
      }
    });
  };

  const addInitialNode = () => {
    const newNode = buildInitialNode();

    setNodes([newNode]);
    setEdges([]);
  };

  const buildInitialNode = () => {
    setCounterOptionTypes({
      menus: 0, questions: 0, messages: 0, jumps: 0, apis: 0
    });

    return buildNode({ node_id: uuid() }, 'initialNode');
  };

  const buildOptionsSavedHowNodes = (options) => {
    const newNodes = [];
    const newEdges = [];
    let counterMenus = 0;
    let counterQuestions = 0;
    let counterMessages = 0;
    let counterJumps = 0;
    let counterApis = 0;
    let counterResolveChats = 0;

    forEach(
      orderBy(options, ['id'], ['asc']).filter(
        (el) => el.option_deleted === false && !el.fake_option
      ),
      (opt) => {
        const newOptionsList = orderBy(opt.options_list, ['position'], ['asc']).map((subList) => ({
          ...subList,
          option_deleted: false
        }));
        const newOptionsSubList = orderBy(opt.option_sub_lists, ['position'], ['asc']);

        if (opt.option_type === CHATBOT_OPTIONS_TYPE.MENU && !opt.fake_option) counterMenus += 1;
        if (opt.option_type === CHATBOT_OPTIONS_TYPE.QUESTION && !opt.fake_option) counterQuestions += 1;
        if (opt.option_type === CHATBOT_OPTIONS_TYPE.API && !opt.fake_option) counterApis += 1;
        if (opt.option_type === CHATBOT_OPTIONS_TYPE.MESSAGE && !opt.fake_option) counterMessages += 1;
        if (opt.option_type === CHATBOT_OPTIONS_TYPE.JUMP_TO && !opt.fake_option) counterJumps += 1;
        if (opt.option_type === CHATBOT_OPTIONS_TYPE.RESOLVE_CHAT && !opt.fake_option) counterResolveChats += 1;

        if (!opt.ancestry) {
          const initialNode = buildInitialNode();
          newNodes.push(initialNode);

          const newOption = {
            ...opt,
            source: initialNode.id,
            target: opt.node_id,
            options_list: newOptionsList,
            option_sub_lists: newOptionsSubList
          };
          const newNode = buildNode(newOption);
          newNodes.push(newNode);

          const connectingEdge = {
            id: `e${initialNode.id}->${opt.node_id}`,
            source: initialNode.id,
            target: opt.node_id,
            style: { opacity: 0 }
          };

          newEdges.push(connectingEdge);
        } else {
          const newOption = {
            ...opt,
            options_list: newOptionsList,
            option_sub_lists: newOptionsSubList
          };
          let targetNode;

          if (opt.fake_option) {
            targetNode = buildNode(newOption, 'optionList');
          } else {
            targetNode = buildNode(newOption);
          }

          newNodes.push(targetNode);

          const connectingEdge = {
            id: `e${opt.source}->${opt.target}`,
            source: opt.source,
            target: opt.target,
            style: { opacity: 0 }
          };
          newEdges.push(connectingEdge);
        }

        newOptionsList.forEach((subOptionList) => {
          const newNode = buildOptionListNode(subOptionList);
          newNodes.push(newNode[0]);
          newEdges.push(newNode[1]);
        });
      }
    );

    if (!isEmpty(newNodes)) setNodes(newNodes);
    if (!isEmpty(newEdges)) setEdges(newEdges);

    setCounterOptionTypes({
      menus: counterMenus,
      questions: counterQuestions,
      apis: counterApis,
      messages: counterMessages,
      jumps: counterJumps,
      resolveChats: counterResolveChats
    });
  };

  const buildOptionListNode = (subOptionList) => {
    const newOption = { ...subOptionList, options_list: [], option_sub_lists: [] };

    const targetNode = buildNode(newOption, 'optionList');

    const connectingEdge = {
      id: `e${subOptionList.source}->${subOptionList.target}`,
      source: subOptionList.source,
      target: subOptionList.target,
      style: { opacity: 0 }
    };

    return [targetNode, connectingEdge];
  };

  const buildNode = (option, type = 'custom') => {
    const newNode = {
      id: option.node_id,
      source: option.source,
      target: option.target,
      type,
      data: {
        option,
        addNode: addNewNode,
        editNode,
        previewBot,
        removeNode
      },
      position: { x: 0, y: 0 }, // no need to pass a position as it is computed by the layout hook
      style: { opacity: 0 }
    };

    return newNode;
  };

  const addNodeToGraph = (option) => {
    const newNodes = nodes.map((node) => {
      const nodeData = {
        ...node.data,
        option: {
          ...node.data.option,
          timeline: new Date().getTime()
        }
      };

      return {
        ...node,
        data: nodeData
      };
    });

    const optionData = { ...option };

    if (!option.ancestry) {
      optionData.source = newNodes[0].id;
      optionData.target = option.node_id;
    }

    const newEdges = [];

    const newNode = buildNode(optionData);

    newNodes.push(newNode);

    if (optionData.source) {
      newEdges.push({
        id: `e${optionData.source}->${optionData.target}`,
        source: optionData.source,
        target: optionData.target,
        style: { opacity: 0 }
      });
    }

    forEach(optionData.options_list, (opt) => {
      newNodes.push(buildNode(opt, 'optionList'));

      newEdges.push({
        id: `e${opt.source}->${opt.target}`,
        source: opt.source,
        target: opt.target,
        style: { opacity: 0 }
      });
    });

    if (!isEmpty(newNodes)) setNodes((prev) => prev.concat(newNodes));
    if (!isEmpty(newEdges)) setEdges((prev) => prev.concat(newEdges));
  };

  // this function adds a new node and connects it to the source node
  const createConnection = (option) => {
    // create an incremental ID based on the number of elements already in the graph
    const parentNode = getNode(option.source);
    if (!parentNode) return;

    const sourceId = parentNode.id;
    const targetId = option.node_id;
    const newOption = { ...option };
    const targetNode = buildNode(newOption);

    const connectingEdge = {
      id: `e${sourceId}->${targetId}`,
      source: sourceId,
      target: targetId,
      style: { opacity: 0 }
    };

    setNodes((prev) => prev.concat([targetNode]));
    setEdges((prev) => prev.concat([connectingEdge]));
  };

  // this function is called once the node from the sidebar is dropped onto a node in the current graph
  const onDrop = (evt) => {
    // make sure that the event target is a DOM element
    if (evt.target instanceof Element) {
      // from the target element search for the node wrapper element which has the node id as attribute
      const targetId = evt.target.closest('.react-flow__node')?.getAttribute('data-id');

      if (targetId) {
        // now we can create a connection to the drop target node
        createConnection(targetId);
      }
    }
  };

  const hasChildren = (nodeId) => getNodes().some((element) => element.data?.option?.source === nodeId && element.data?.option?.target !== nodeId);

  const addNewNode = (parentNodeId, data, isRootNode = false) => {
    if (hasChildren(parentNodeId) && data.option?.option_type === CHATBOT_OPTIONS_TYPE.QUESTION) {
      alert('En pasos de tipo Pregunta, se permite sólo un paso hijo');
      return;
    }

    const newNodeId = uuid();

    setParamsForNewNode({
      newNode: true,
      node_id: newNodeId,
      source: isRootNode ? null : parentNodeId,
      target: isRootNode ? null : newNodeId,
      option: data.option
    });

    setOpenNodeOption(true);
  };

  const editNode = (nodeId) => {
    const node = getNode(nodeId);
    if (!node) return;

    setParamsForNewNode({
      newNode: false
    });

    setNodeOption(cloneDeep(node.data?.option));
    setOpenNodeOption(true);
  };

  const previewBot = (nodeId) => {
    const node = getNode(nodeId);
    if (!node) return;

    setNodeOption(cloneDeep(node.data?.option));
    setOpenPreviewBot(true);
  };

  const closePreviewBot = () => {
    setOpenPreviewBot(false);
    setNodeOption(null);
  };

  const removeNode = (nodeId, data) => {
    const reloadOptions = hasChildren(nodeId);

    if (
      confirm('¿Estás seguro de eliminar este paso? Se eliminarán todos los pasos hijos también.')
    ) {
      ChatbotOptionModel.remove(id, { id: data.option?.id })
        .then((res) => {
          showtoast(res.message);
          if (reloadOptions || !res.chatbot_option?.ancestry) {
            loadOptionsSaved();
          } else {
            deleteElements({ nodes: [{ id: nodeId }] });
          }
        })
        .catch((error) => {
          showtoast(error.message);
        });
    }
  };

  // every time our nodes change, we want to center the graph again
  useEffect(() => {
    fitView({ duration: 400 });
  }, [nodes, fitView]);

  const resetActions = () => {
    setNodeOption(null);
    setOpenNodeOption(false);
  };

  const cancelOption = () => {
    resetActions();
  };

  const saveNodeOption = (option) => {
    if (!option.fake_option) {
      let {
        menus, questions, apis, messages, jumps
      } = counterOptionTypes;

      if (option.option_type === CHATBOT_OPTIONS_TYPE.QUESTION) questions += 1;
      if (option.option_type === CHATBOT_OPTIONS_TYPE.API) apis += 1;
      if (option.option_type === CHATBOT_OPTIONS_TYPE.MENU) menus += 1;
      if (option.option_type === CHATBOT_OPTIONS_TYPE.MESSAGE) messages += 1;
      if (option.option_type === CHATBOT_OPTIONS_TYPE.JUMP_TO) jumps += 1;

      setCounterOptionTypes({
        questions, apis, menus, messages, jumps
      });
    }

    if (paramsForNewNode.newNode) {
      addNodeToGraph(option);
    } else {
      const node = getNode(option.node_id);
      if (node) loadOptionsSaved();
    }

    resetActions();
  };

  return (
    <div className="ml-sm-60 no-left-margin-xs">
      <div className="chatbot-pannel">
        <div className="d-flex flex-column px-20 p-absolute mt-20" style={{ zIndex: 10 }}>
          <Link
            className="btn-btn btn-secondary-style bg-light back-button text-decoration-none w-101"
            to={`/retailers/${retailer_info.slug}/chatbots?status=all`}
          >
            Atrás
          </Link>

          <div className="mt-20 w-400">
            <p className="chatbot-title">{truncate(chatbot?.name, { length: 65 })}</p>
          </div>
        </div>
        <div className="flow-container">
          <ReactFlow
            proOptions={proOptions}
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onNodesDelete={onNodesDelete}
            onEdgesChange={onEdgesChange}
            fitViewOptions={fitViewOptions}
            fitView
            nodeTypes={nodeTypes}
            onDrop={onDrop}
            style={{ paddingLeft: '10px', paddingRight: '10px' }}
            // newly added edges get these options automatically
            defaultEdgeOptions={defaultEdgeOptions}
            minZoom={0.2}
            nodesDraggable={false}
            nodesConnectable={false}
            zoomOnDoubleClick={false}
          >
            <Background variant="dots" gap={12} size={1} />
            <Controls />
          </ReactFlow>
        </div>
      </div>
      {openNodeOption && (
        <NodeOptionComponent
          chatbot={chatbot}
          counterOptionTypes={counterOptionTypes}
          paramsForNewNode={paramsForNewNode}
          option={nodeOption}
          cancelOption={cancelOption}
          saveNodeOption={saveNodeOption}
        />
      )}

      {openPreviewBot && (
        <PreviewBot chatbot={chatbot} option={nodeOption} toggleModal={closePreviewBot} />
      )}
    </div>
  );
};

// as we are accessing the internal React Flow state in our component, we need to wrap it with the ReactFlowProvider
const ChatbotOptions = (props) => (
  <ReactFlowProvider>
    <Flow {...props} />
  </ReactFlowProvider>
);

export default ChatbotOptions;
