/* eslint-disable no-param-reassign */
import React, { useEffect, useState } from "react";
import { useSelector } from 'react-redux';
import { useParams } from "react-router-dom";
import {
  forEach, filter, orderBy, isEmpty
} from "lodash";
import { useTranslation } from 'react-i18next';

import RightModal from "../../shared/RightModal";
import ModalHeaderComponent from "../../shared/ModalHeaderComponent";
import ModalBodyComponent from "../../shared/ModalBodyComponent";
import ModalFooterComponent from "../../shared/ModalFooterComponent";
import ModalFooterOnlyCancelComponent from "../../shared/ModalFooterOnlyCancelComponent";
import OptionBodyComponent from "./OptionBodyComponent";

import ChatbotOptionModel from "../ChatbotOptionModel";
import StepHeader from "./StepHeader";

import { uuid } from "./utils";
import {
  CHATBOT_OPTIONS_TYPE,
  DEFAULT_MAX_CHARS
} from "../../../constants/AppConstants";
import fileUtils from "../../../util/fileUtils";

const defaultOption = Object.freeze({
  id: null,
  option_type: "",
  text: "",
  answer: "",
  skip_option: false,
  answer_type: "",
  interactive: "text",
  message_type: "",
  options_list: [],
  attached_file: null,
  file_deleted: false,
  selectedFile: null,
  originalAttachedFile: null,
  chat_bot_actions: [],
  has_step_actions: false,
  option_sub_lists: [],
  jump_to_option: null
});

const NodeOptionComponent = ({
  chatbot,
  counterOptionTypes,
  paramsForNewNode,
  option,
  cancelOption,
  saveNodeOption
}) => {
  const { id } = useParams();
  const { t } = useTranslation();

  const [chatbotOption, setChatbotOption] = useState({});
  const [errors, setErrors] = useState({});
  const [displayOptionTypes, setDisplayOptionTypes] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);

  useEffect(() => {
    if (option) {
      setChatbotOption({
        ...option,
        originalAttachedFile: option.attached_file,
        has_step_actions: !!option.chat_bot_actions?.length,
        jump_to_option:
          option.option_type === "jump_to"
            ? option.chat_bot_actions[0]?.jump_option_id
            : null
      });
    } else {
      setDisplayOptionTypes(true);
      setChatbotOption({
        ...defaultOption,
        options_list: [],
        chat_bot_actions: [],
        node_id: paramsForNewNode.node_id,
        source: paramsForNewNode.source,
        target: paramsForNewNode.target
      });
    }
  }, [option]);

  useEffect(() => {
    switch (chatbotOption.option_type) {
      case CHATBOT_OPTIONS_TYPE.ACTIVE_MIA:
        if (retailer_info.chatbot_ai) {
          handleSaveOption();
        } else {
          setDisplayOptionTypes(false);
        }
        break;
      case CHATBOT_OPTIONS_TYPE.RESOLVE_CHAT:
        handleSaveOption();
        break;
    }
  }, [chatbotOption]);

  const handleOptionClick = (value) => {
    let { text } = chatbotOption;

    if (!chatbotOption.option_type && value === CHATBOT_OPTIONS_TYPE.QUESTION) text = `Hacer una pregunta ${counterOptionTypes.questions + 1}`;
    if (!chatbotOption.option_type && value === CHATBOT_OPTIONS_TYPE.MENU) text = `Mostrar menú ${counterOptionTypes.menus + 1}`;
    if (!chatbotOption.option_type && value === CHATBOT_OPTIONS_TYPE.MESSAGE) text = `Enviar mensaje ${counterOptionTypes.messages + 1}`;
    if (!chatbotOption.option_type && value === CHATBOT_OPTIONS_TYPE.JUMP_TO) text = `Ir a otro paso ${counterOptionTypes.jumps + 1}`;
    if (!chatbotOption.option_type && value === CHATBOT_OPTIONS_TYPE.API) text = `Llamada a API ${counterOptionTypes.apis + 1}`;
    if (!chatbotOption.option_type && value === CHATBOT_OPTIONS_TYPE.RESOLVE_CHAT) text = `Marcar chat resuelto ${counterOptionTypes.resolveChats + 1}`;

    const newChatbotOption = { ...chatbotOption, option_type: value, text };

    if (value === CHATBOT_OPTIONS_TYPE.MENU && !newChatbotOption.options_list.length) {
      const nodeId = uuid();

      newChatbotOption.options_list.push({
        id: null,
        text: "",
        option_deleted: false,
        node_id: nodeId,
        source: chatbotOption.node_id,
        target: nodeId,
        parent_id: chatbotOption.id,
        fake_option: true
      });
    }

    setChatbotOption(newChatbotOption);
    setDisplayOptionTypes(false);
  };

  const handleActiveMiaOptionClick = (value) => {
    setChatbotOption((prevChatbotOption) => ({ ...chatbotOption, option_type: value }));
  };

  const handleCancelOption = () => {
    setDisplayOptionTypes(true);
    setChatbotOption((prevChatbotOption) => ({ ...chatbotOption, option_type: '' }));
  };

  const isValidCommonFields = (newErrors) => {
    let isValid = true;

    if (!chatbotOption.option_type) {
      newErrors.option_type = t('chatbot.config.modal.errors.select_option');
      isValid = false;
    }

    if (!chatbotOption.text) {
      newErrors.text = t('chatbot.config.modal.errors.required');
      isValid = false;
    }

    return isValid;
  };

  const isValidQuestionOption = (newErrors, isValid) => {
    const { interactive, fail_path_active } = chatbotOption;
    if (!chatbotOption.answer) {
      newErrors.answer = t('chatbot.config.modal.errors.required');
      isValid = false;
    } else {
      isValid = validateMaxAnswerLenght(
        newErrors,
        chatbotOption.answer,
        isValid,
        'menu'
      );
    }

    if (!chatbotOption.answer_type) {
      newErrors.answer_type = t('chatbot.config.modal.errors.required');
      isValid = false;
    }

    if (interactive === "list") {
      isValid = isValidListInteractive(newErrors, isValid);
    }

    const filteredOptionsSublist = chatbotOption.option_sub_lists.filter(
      (el) => el._destroy !== true
    );

    if (chatbotOption.answer_type === "unique") {
      if (isEmpty(filteredOptionsSublist) && !chatbotOption.use_api) {
        newErrors.options_sublist_error = t('chatbot.config.modal.errors.add_option');
        isValid = false;
      } else {
        if (!chatbotOption.use_api) {
          isValid = isValidNumberOptionsAdded(
            filteredOptionsSublist,
            newErrors,
            isValid
          );

          chatbotOption.option_sub_lists = chatbotOption.option_sub_lists.map(
            (option) => {
              if (option._destroy) return option;

              if (!option.value_to_show) {
                isValid = false;
                return { ...option, error: t('chatbot.config.modal.errors.required') };
              }

              return {
                ...option,
                error: ""
              };
            }
          );
        } else if (isEmpty(chatbotOption.form_options)
          || !chatbotOption.form_options?.data_source
          || !chatbotOption.form_options?.value_source
          || !chatbotOption.form_options?.title_source
        ) {
          newErrors.options_sublist_error = t('chatbot.config.modal.errors.no_api_values');
          isValid = false;
        }

        setChatbotOption(chatbotOption);
      }
      if (isEmpty(chatbotOption.failure_text)) {
        newErrors.failure_text = t('chatbot.config.modal.errors.required');
        isValid = false;
      }

      if (
        fail_path_active
        && (!chatbotOption.max_attempts || !chatbotOption.max_attempts > 0)
      ) {
        newErrors.max_attempts = t('chatbot.config.modal.errors.required');
        isValid = false;
      }
    }

    return isValid;
  };

  const isValidNumberOptionsAdded = (optionsList, newErrors, isValid) => {
    const { interactive } = chatbotOption;

    if (interactive === "buttons" && optionsList.length > 3) {
      newErrors.options_list_error = t('chatbot.config.modal.errors.max_options', { amount: 3 });
      isValid = false;
    } else if (interactive === "list" && optionsList.length > 10) {
      newErrors.options_list_error = t('chatbot.config.modal.errors.max_options', { amount: 10 });
      isValid = false;
    }

    return isValid;
  };

  const isValidMenuOption = (newErrors, isValid) => {
    const { interactive, fail_path_active } = chatbotOption;

    if (!chatbotOption.answer) {
      newErrors.answer = t('chatbot.config.modal.errors.required');
      isValid = false;
    } else {
      isValid = validateMaxAnswerLenght(
        newErrors,
        chatbotOption.answer,
        isValid,
        'menu'
      );
    }

    if (interactive === "list") {
      isValid = isValidListInteractive(newErrors, isValid);
    }

    const filteredOptionsList = chatbotOption.options_list.filter(
      (el) => el.option_deleted !== true && el.option_type != "failure"
    );

    if (isEmpty(filteredOptionsList)) {
      newErrors.options_list_error = t('chatbot.config.modal.errors.add_option');

      return false;
    }

    isValid = isValidNumberOptionsAdded(
      filteredOptionsList,
      newErrors,
      isValid
    );

    if (isEmpty(chatbotOption.failure_text)) {
      newErrors.failure_text = t('chatbot.config.modal.errors.required');
      isValid = false;
    }

    chatbotOption.options_list = chatbotOption.options_list.map((opt) => {
      if (opt.option_deleted) return opt;

      if (!opt.text) {
        isValid = false;
        return { ...opt, error: t('chatbot.config.modal.errors.required') };
      }

      if (interactive === "buttons" && opt.text.length > 20) {
        isValid = false;
        return { ...opt, error: t('chatbot.config.modal.errors.max_characters', { amount: 20 }) };
      }

      if (interactive === "list" && opt.text.length > 24) {
        isValid = false;
        return { ...opt, error: t('chatbot.config.modal.errors.max_characters', { amount: 24 }) };
      }

      return { ...opt, error: "" };
    });

    if (
      fail_path_active
      && (!chatbotOption.max_attempts || chatbotOption.max_attempts < 1)
    ) {
      newErrors.max_attempts = t('chatbot.config.modal.errors.required');
      isValid = false;
    }

    setChatbotOption(chatbotOption);

    return isValid;
  };

  const isValidListInteractive = (newErrors, isValid) => {
    // if (!chatbotOption.list_title) {
    //   newErrors.list_title = 'Requerido';
    //   isValid = false;
    // } else if (chatbotOption.list_title.length > 60) {
    //   newErrors.list_title = 'Máximo 60 caracteres';
    //   isValid = false;
    // }

    if (!chatbotOption.button_title) {
      newErrors.button_title = t('chatbot.config.modal.errors.required');
      isValid = false;
    } else if (chatbotOption.button_title.length > 20) {
      newErrors.button_title = t('chatbot.config.modal.errors.max_characters', { amount: 20 });
      isValid = false;
    }

    if (!chatbotOption.selection_title) {
      newErrors.selection_title = t('chatbot.config.modal.errors.required');
      isValid = false;
    } else if (chatbotOption.selection_title.length > 24) {
      newErrors.selection_title = t('chatbot.config.modal.errors.max_characters', { amount: 20 });
      isValid = false;
    }

    return isValid;
  };

  const isValidMessageOption = (newErrors, isValid) => {
    if (!chatbotOption.message_type) {
      newErrors.message_type = t('chatbot.config.modal.errors.required');
      isValid = false;
    }

    if (
      ["image", "document", "audio", "video"].includes(
        chatbotOption.message_type
      )
    ) {
      if (!chatbotOption.selectedFile && isEmpty(chatbotOption.attached_file)) {
        newErrors.file_error = t('chatbot.config.modal.errors.select_file');
        isValid = false;
      }

      if (
        chatbotOption.answer
        && chatbotOption.answer.length > chatbot.max_size_text
      ) {
        newErrors.answer = t('chatbot.config.modal.errors.max_characters', { amount: DEFAULT_MAX_CHARS });
        isValid = false;
      }
    }

    if (["text"].includes(chatbotOption.message_type)) {
      if (!chatbotOption.answer) {
        newErrors.answer = t('chatbot.config.modal.errors.required');
        isValid = false;
      } else {
        isValid = validateMaxAnswerLenght(
          newErrors,
          chatbotOption.answer,
          isValid
        );
      }
    }

    return isValid;
  };

  const validateMaxAnswerLenght = (msgError, text, isValid, type = 'text') => {
    const maxLenght = type === 'text' ? chatbot.max_size_text : chatbot.max_button_size_text;

    if (text.length > maxLenght) {
      msgError.answer = t('chatbot.config.modal.errors.max_characters', { amount: DEFAULT_MAX_CHARS });
      isValid = false;
    }

    return isValid;
  };

  const isValidChatBotActions = (newErrors, isValid) => {
    if (!chatbotOption.has_step_actions) {
      return isValid;
    }

    if (
      isEmpty(
        chatbotOption.chat_bot_actions.filter((el) => el._destroy !== true)
      )
    ) {
      newErrors.actions_error = t('chatbot.config.modal.errors.add_action');
      isValid = false;
      return isValid;
    }

    chatbotOption.chat_bot_actions = chatbotOption.chat_bot_actions.map(
      (action) => {
        if (action._destroy) return action;

        if (!action.action_type) {
          isValid = false;
          return { ...action, action_type_error: t('chatbot.config.modal.errors.required') };
        }

        if (action.action_type === "add_tag" && isEmpty(action.selected_tags)) {
          isValid = false;
          return {
            ...action,
            action_type_error: "",
            selected_tags_error: t('chatbot.config.modal.errors.required')
          };
        }

        if (action.action_type === "assign_agent" && !action.retailer_user_id) {
          isValid = false;
          return {
            ...action,
            action_type_error: "",
            retailer_user_id_error: t('chatbot.config.modal.errors.required')
          };
        }

        if (action.action_type === "get_out_bot" && !action.exit_message) {
          isValid = false;
          return {
            ...action,
            action_type_error: "",
            exit_message_error: t('chatbot.config.modal.errors.required')
          };
        }

        if (action.action_type === "save_on_db" && !action.target_field) {
          isValid = false;
          return {
            ...action,
            action_type_error: "",
            target_field_error: t('chatbot.config.modal.errors.required')
          };
        }

        if (action.action_type === "assign_team" && !action.team_assignment_id) {
          isValid = false;
          return {
            ...action,
            action_type_error: "",
            team_assignment_id_error: t('chatbot.config.modal.errors.required')
          };
        }

        if (action.action_type === "create_deal") {
          if (!action.funnel_id) {
            isValid = false;
            return {
              ...action,
              action_type_error: "",
              funnel_id_error: t('chatbot.config.modal.errors.required')
            };
          }

          if (!action.funnel_step_id) {
            isValid = false;
            return {
              ...action,
              action_type_error: "",
              funnel_step_id_error: t('chatbot.config.modal.errors.required')
            };
          }
        }

        if (action.action_type === "ctx_notify") {
          let goal_index_error = "";
          let tracker_error = "";
          let tracker_value_error = "";

          if (action.goal_index === null) {
            isValid = false;
            goal_index_error = t('chatbot.config.modal.errors.required');
          }

          if (action.tracker === null || action.tracker === "") {
            isValid = false;
            tracker_error = t('chatbot.config.modal.errors.required');
          }

          if (action.tracker_value === null || action.tracker_value === "") {
            isValid = false;
            tracker_value_error = t('chatbot.config.modal.errors.required');
          }

          return {
            ...action,
            goal_index_error,
            tracker_error,
            tracker_value_error
          };
        }

        return {
          ...action,
          action_type_error: "",
          selected_tags_error: "",
          retailer_user_id_error: "",
          target_field_error: "",
          exit_message_error: "",
          funnel_id_error: "",
          funnel_step_id_error: "",
          team_assignment_id_error: "",
          goal_index_error: "",
          tracker_error: ""
        };
      }
    );

    setChatbotOption(chatbotOption);

    return isValid;
  };

  const isValidJumpToStep = (newErrors, isValid) => {
    if (!chatbotOption.jump_to_option) {
      newErrors.jump_to_error = t('chatbot.config.modal.errors.required');
      isValid = false;
    }

    return isValid;
  };

  const isValidApiOption = (newErrors, isValid) => {
    // Validar status de la API
    if (chatbotOption?.status !== "success") {
      newErrors.api_status = chatbotOption.status === "error" ? t('chatbots.options.api.errors.apiError') : t('chatbots.options.api.errors.apiNotTested');
      isValid = false;
    }

    const {
      api_url,
      method,
      body_type,
      headers,
      body
    } = chatbotOption.api_options || {};

    // Validar URL de la API
    if (!api_url?.trim()) {
      newErrors.api_url = t('chatbots.options.api.errors.urlRequired');
      isValid = false;
    }

    // Validar método HTTP
    if (!method) {
      newErrors.method = t('chatbots.options.api.errors.methodRequired');
      isValid = false;
    }

    // Validar tipo de cuerpo
    if (!body_type) {
      newErrors.body_type = t('chatbots.options.api.errors.bodyTypeRequired');
      isValid = false;
    }

    // Validar headers
    if (headers) {
      headers.forEach((header, index) => {
        if (!header.key?.trim()) {
          newErrors.headers[index] = {
            ...newErrors.headers[index],
            key: t('chatbots.options.api.errors.keyRequired')
          };
          isValid = false;
        }
        if (!header.value?.trim()) {
          newErrors.headers[index] = {
            ...newErrors.headers[index],
            value: t('chatbots.options.api.errors.valueRequired')
          };
          isValid = false;
        }
      });

      // Validación especial para Content-Type en form_url
      if (body_type === 'form_url'
          && !headers.some((h) => h.key === 'Content-Type')) {
        newErrors.headers.global = t('chatbots.options.api.errors.contentTypeHeaderRequired');
        isValid = false;
      }
    }

    // Validar campos del body (content)
    if (body) {
      body.forEach((item, index) => {
        if (!item.key?.trim()) {
          newErrors.content[index] = {
            ...newErrors.content[index],
            key: t('chatbots.options.api.errors.keyRequired')
          };
          isValid = false;
        }
        if (!item.value?.trim()) {
          newErrors.content[index] = {
            ...newErrors.content[index],
            value: t('chatbots.options.api.errors.valueRequired')
          };
          isValid = false;
        }
      });
    }

    if (!isValid) {
      setErrors(newErrors);
    }
    return isValid;
  };

  const isValidFormData = () => {
    const newErrors = {
      ...defaultOption,
      option: "",
      interactive: "",
      options_list_error: "",
      options_list: [],
      file_error: "",
      actions_error: "",
      jump_to_error: "",
      api_url: "",
      method: "",
      body_type: "",
      headers: {},
      content: {}
    };

    let isValid = isValidCommonFields(newErrors);
    const { option_type, fake_option } = chatbotOption;

    if (fake_option) {
      isValid = isValidChatBotActions(newErrors, isValid);
    } else {
      if (option_type === CHATBOT_OPTIONS_TYPE.MESSAGE) isValid = isValidMessageOption(newErrors, isValid);
      if (option_type === CHATBOT_OPTIONS_TYPE.MENU) isValid = isValidMenuOption(newErrors, isValid);
      if (option_type === CHATBOT_OPTIONS_TYPE.QUESTION) isValid = isValidQuestionOption(newErrors, isValid);
      if (
        [CHATBOT_OPTIONS_TYPE.MENU, CHATBOT_OPTIONS_TYPE.QUESTION, CHATBOT_OPTIONS_TYPE.MESSAGE].includes(
          option_type
        )
      ) {
        isValid = isValidChatBotActions(newErrors, isValid);
      }
      if (option_type === CHATBOT_OPTIONS_TYPE.JUMP_TO) isValid = isValidJumpToStep(newErrors, isValid);
      if (option_type === CHATBOT_OPTIONS_TYPE.API) isValid = isValidApiOption(newErrors, isValid);

      if (option_type === CHATBOT_OPTIONS_TYPE.ACTIVE_MIA) isValid = true;
      if (option_type === CHATBOT_OPTIONS_TYPE.RESOLVE_CHAT) isValid = true;
    }

    setErrors(newErrors);

    return isValid;
  };

  const buildQuestionOptionData = (chatbot_option) => {
    chatbot_option = buildChatbotOptionInteractiveData(chatbot_option);

    chatbot_option = buildOptionsSublistData(chatbot_option);

    chatbot_option.options_list = buildOptionList();
    chatbot_option.use_api = chatbotOption.use_api;

    return chatbot_option;
  };

  const buildMenuOptionData = (chatbot_option) => {
    chatbot_option = buildChatbotOptionInteractiveData(chatbot_option);

    chatbot_option.options_list = buildOptionList();

    return chatbot_option;
  };

  const buildChatbotOptionInteractiveData = (chatbot_option) => {
    if (chatbotOption.interactive === "list") {
      const {
        list_title, button_title, selection_title, options_list
      } = chatbotOption;

      chatbot_option = {
        ...chatbot_option,
        list_title,
        button_title,
        selection_title,
        options_list
      };
    }

    if (chatbotOption.use_api) {
      chatbot_option.chat_bot_option_form_attributes = {
        ...chatbotOption.form_options,
        id: chatbotOption.form_options.id || null
      };
    }

    return chatbot_option;
  };

  const buildMessageOptionData = (chatbot_option) => {
    const {
      message_type,
      selectedFile,
      answer,
      attached_file,
      originalAttachedFile
    } = chatbotOption;

    chatbot_option = {
      ...chatbot_option,
      message_type,
      answer: message_type === "audio" ? "" : answer
    };

    if (message_type === "text" && !isEmpty(originalAttachedFile)) {
      chatbot_option = {
        ...chatbot_option,
        file_deleted: true
      };
    }

    if (selectedFile) {
      chatbot_option = {
        ...chatbot_option,
        file: selectedFile
      };
    }

    return chatbot_option;
  };

  const buildChatbotActionsData = (chatbot_option) => {
    if (!chatbotOption.has_step_actions) {
      chatbotOption.skip_option = false;
      forEach(chatbotOption.chat_bot_actions, (action) => {
        if (action.id) {
          chatbot_option.chat_bot_actions_attributes.push({
            id: action.id,
            _destroy: true
          });
        }
      });
    } else {
      forEach(chatbotOption.chat_bot_actions, (action) => {
        let newAction = {
          id: action.id,
          action_type: action.action_type,
          _destroy: !!action._destroy
        };

        switch (action.action_type) {
          case "add_tag":
            newAction = {
              ...newAction,
              tag_ids: action.selected_tags.map((i) => i.value)
            };
            break;
          case "assign_agent":
            newAction = {
              ...newAction,
              retailer_user_id: action.retailer_user_id
            };
            break;
          case "get_out_bot":
            newAction = { ...newAction, exit_message: action.exit_message };
            break;
          case "save_on_db":
            newAction = { ...newAction, target_field: action.target_field };
            break;
          case "assign_team":
            newAction = {
              ...newAction,
              team_assignment_id: action.team_assignment_id
            };
            break;
          case "create_deal":
            newAction = {
              ...newAction,
              funnel_step_id: action.funnel_step_id,
              funnel_id: action.funnel_id
            };
            break;
          case "ctx_notify":
            newAction = {
              ...newAction,
              goal_index: action.goal_index,
              tracker: action.tracker,
              tracker_value: action.tracker_value
            };
            break;
          default:
            break;
        }

        chatbot_option.chat_bot_actions_attributes.push(newAction);
      });
    }

    return chatbot_option;
  };

  const buildOptionsSublistData = (chatbot_option) => {
    if (chatbotOption.answer_type != "unique") {
      forEach(chatbotOption.option_sub_lists, (sub_option) => {
        if (sub_option.id) {
          chatbot_option.option_sub_lists_attributes.push({
            id: sub_option.id,
            _destroy: true
          });
        }
      });
    } else {
      forEach(chatbotOption.option_sub_lists, (sub_option) => {
        const newOption = {
          id: sub_option.id,
          value_to_show: sub_option.value_to_show,
          _destroy: !!sub_option._destroy
        };

        chatbot_option.option_sub_lists_attributes.push(newOption);
      });
    }

    return chatbot_option;
  };

  const buildResponseOptionsList = (res) => {
    const optionsList = res.chatbot_option?.options_list || [];

    return orderBy(optionsList, ["position"], ["asc"]).map((subList) => ({
      ...subList,
      option_deleted: false
    }));
  };

  const buildResponseChatbotOption = (res) => {
    let newChatbotOption = { ...res.chatbot_option };

    const { option_type, message_type } = chatbotOption;

    if (option_type === CHATBOT_OPTIONS_TYPE.MESSAGE && message_type === "text") {
      newChatbotOption = {
        ...newChatbotOption,
        attached_file: null,
        selectedFile: null,
        originalAttachedFile: null
      };
    }

    return newChatbotOption;
  };

  const buildJumToStepData = (chatbot_option) => {
    if (!chatbotOption.jump_to_option) {
      return;
    }

    const newAction = {
      id:
        chatbotOption.chat_bot_actions.length > 0
          ? chatbotOption.chat_bot_actions[0].id
          : null,
      action_type: "jump_to_option",
      jump_option_id: chatbotOption.jump_to_option,
      _destroy: false
    };

    chatbot_option.chat_bot_actions_attributes.push([newAction]);
  };

  const buildApiOptionData = (chatbot_option) => {
    const {
      id,
      api_url,
      method,
      headers,
      body,
      body_type,
      variables
    } = chatbotOption?.api_options || {};
    return {
      ...chatbot_option,
      options_list: buildOptionList(),
      chat_bot_option_api_attributes: {
        id,
        api_url,
        method,
        headers,
        body,
        body_type,
        variables
      }
    };
  };

  const buildOptionList = () => {
    const newOptionsList = [];
    forEach(chatbotOption.options_list, (item) => {
      const {
        id,
        text,
        node_id,
        source,
        target,
        option_deleted,
        fake_option,
        option_type
      } = item;

      let newItem = {
        id, text, option_deleted, fake_option
      };
      if (!isEmpty(option_type)) {
        newItem = { ...newItem, option_type };
      }

      if (!item.id) {
        newItem = {
          ...newItem, node_id, source, target
        };
      }

      newOptionsList.push(newItem);
    });

    return newOptionsList;
  };

  const handleSaveOption = () => {
    if (!isValidFormData()) return;

    const {
      option_type,
      text,
      answer,
      skip_option,
      answer_type,
      interactive,
      fake_option,
      max_attempts,
      failure_text,
      fail_path_active
    } = chatbotOption;

    let chatbot_option = {
      text,
      answer,
      option_type,
      skip_option,
      answer_type,
      interactive,
      max_attempts,
      failure_text,
      fail_path_active,
      list_title: null,
      button_title: null,
      selection_title: null,
      message_type: null,
      options_list: [],
      chat_bot_actions_attributes: [],
      option_sub_lists_attributes: []
    };

    if (!chatbotOption.id) {
      chatbot_option = {
        ...chatbot_option,
        node_id: paramsForNewNode.node_id,
        source: paramsForNewNode.source,
        target: paramsForNewNode.target,
        parent_id: paramsForNewNode.option?.id
      };
    }

    const optionsListRemoved = filter(
      chatbotOption.options_list,
      (opt) => opt.id && opt.option_deleted
    );

    if (fake_option) {
      chatbot_option = buildChatbotActionsData(chatbot_option);
    } else {
      if (option_type === CHATBOT_OPTIONS_TYPE.MESSAGE) chatbot_option = buildMessageOptionData(chatbot_option);
      if (option_type === CHATBOT_OPTIONS_TYPE.MENU) chatbot_option = buildMenuOptionData(chatbot_option);
      if (option_type === CHATBOT_OPTIONS_TYPE.QUESTION) chatbot_option = buildQuestionOptionData(chatbot_option);
      if (
        [CHATBOT_OPTIONS_TYPE.QUESTION, CHATBOT_OPTIONS_TYPE.MENU, CHATBOT_OPTIONS_TYPE.MESSAGE].includes(
          option_type
        )
      ) {
        chatbot_option = buildChatbotActionsData(chatbot_option);
      }
      if (option_type === CHATBOT_OPTIONS_TYPE.JUMP_TO) {
        buildJumToStepData(chatbot_option);
      }
      if (option_type === CHATBOT_OPTIONS_TYPE.API) {
        chatbot_option = buildApiOptionData(chatbot_option);
      }
    }

    setSubmitting(true);
    const formData = fileUtils.objectToFormData({ chatbot_option });
    formData.append("id", chatbotOption.id);

    ChatbotOptionModel.save(id, formData)
      .then((res) => {
        const optionsList = buildResponseOptionsList(res);
        const newChatbotOption = buildResponseChatbotOption(res);

        const chatbotOptionUpdated = {
          ...newChatbotOption,
          options_list: optionsList
        };

        setSubmitting(false);
        saveNodeOption(chatbotOptionUpdated, optionsListRemoved);
      })
      .catch((error) => {
        setSubmitting(false);
        showtoast(error.message);
      });
  };

  return (
    <RightModal>
      <div className="mercately-modal">
        {(!chatbotOption.option_type || chatbotOption.option_type === CHATBOT_OPTIONS_TYPE.ACTIVE_MIA) ? (
          <ModalHeaderComponent
            title={t('chatbot.config.modal.add_step')}
            toggleModal={cancelOption}
          />
        ) : (
          <>
            {chatbotOption.fake_option && (
              <ModalHeaderComponent
                title={chatbotOption.text}
                toggleModal={cancelOption}
                centeredTitle={false}
              />
            )}

            {!chatbotOption.fake_option && (
              <StepHeader
                chatbotOption={chatbotOption}
                setChatbotOption={setChatbotOption}
                cancelOption={cancelOption}
                errors={errors}
              />
            )}
          </>
        )}
        <ModalBodyComponent>
          <OptionBodyComponent
            chatbot={chatbot}
            chatbotOption={chatbotOption}
            displayOptionTypes={displayOptionTypes}
            errors={errors}
            setChatbotOption={setChatbotOption}
            handleOptionClick={handleOptionClick}
            setDisplayOptionTypes={setDisplayOptionTypes}
            setErrors={setErrors}
            handleActiveMiaOptionClick={handleActiveMiaOptionClick}
            handleCancelOption={handleCancelOption}
          />
        </ModalBodyComponent>
        {(chatbotOption.option_type === CHATBOT_OPTIONS_TYPE.ACTIVE_MIA ||
           chatbotOption.option_type === CHATBOT_OPTIONS_TYPE.RESOLVE_CHAT) ? (
          <ModalFooterOnlyCancelComponent
            onCancel={cancelOption}
          />
        ) : (
          <ModalFooterComponent
            saving={submitting}
            onCancel={cancelOption}
            onSave={handleSaveOption}
          />
        )}
      </div>
    </RightModal>
  );
};

export default NodeOptionComponent;
