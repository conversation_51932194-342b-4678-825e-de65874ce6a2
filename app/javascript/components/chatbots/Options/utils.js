import i18next from 'i18next';

import { CHATBOT_OPTIONS_TYPE } from "../../../constants/AppConstants";

export const uuid = () => new Date().getTime().toString(36) + Math.random().toString(36).slice(2);

export const hasChildren = (nodes = [], nodeId = '') => {
  const children = nodes.filter((node) => node.source === nodeId && node.target !== nodeId);
  return children.length > 0;
};

export const iconMapping = {
  [CHATBOT_OPTIONS_TYPE.QUESTION]: { icon: 'mi-question-mark-circle-outline', text: i18next.t('chatbot.config.options.question') },
  [CHATBOT_OPTIONS_TYPE.MENU]: { icon: 'mi-menu-fill', text: i18next.t('chatbot.config.options.menu') },
  [CHATBOT_OPTIONS_TYPE.MESSAGE]: { icon: 'mi-message-square-outline', text: i18next.t('chatbot.config.options.message') },
  [CHATBOT_OPTIONS_TYPE.JUMP_TO]: { icon: 'mi-link-2-outline', text: i18next.t('chatbot.config.options.go_to') },
  [CHATBOT_OPTIONS_TYPE.ACTIVE_MIA]: { icon: 'mi-outline-robot-one', text: i18next.t('chatbot.config.options.active_mia') },
  [CHATBOT_OPTIONS_TYPE.API]: { icon: 'mi-outline-robot-one', text: i18next.t('chatbot.config.options.api') },
  [CHATBOT_OPTIONS_TYPE.RESOLVE_CHAT]: { icon: 'mi-message-check', text: i18next.t('chatbot.config.options.resolve_chat_caption') }
};
