import React from "react";
import { useTranslation } from 'react-i18next';
import { iconMapping } from "../utils";

const NodeContent = ({
  nodeId,
  nodeData,
  editNode,
  previewBot,
  removeNode
}) => {
  const { t } = useTranslation();

  const renderIconWithText = () => {
    const option = iconMapping[nodeData.option?.option_type];

    if (option) {
      return (
        <>
          <i className={option.icon + ' mr-5'} />
          <span>{option.text}</span>
        </>
      );
    }

    return (<></>);
  };

  return (
    <div className="chatbot-node-container">
      <div className="node-header">
        <div className="d-flex flex-column">
          <span className="text-gray-dark fw-bold fz-9">{renderIconWithText()}</span>
          <span className="text-gray-dark fz-8">{nodeData.option?.text}</span>
        </div>
        <div className="d-flex justify-content-between">
          {!['jump_to', 'active_mia', 'api', 'resolve_chat'].includes(nodeData.option?.option_type) && (
            <div className="icon-action mr-3" onClick={previewBot}>
              <div className="tooltip-top">
                <i className="mi-eye-fill text-secondary fz-10" />
                <span className="tooltiptext--black bg-dark tooltiptext tooltip-stats-view">
                  {t('chatbot.config.node_content.preview')}
                </span>
              </div>
            </div>
          )}

          {!['active_mia', 'resolve_chat'].includes(nodeData.option?.option_type) && (
            <div className="icon-action mr-3" onClick={editNode}>
              <div className="tooltip-top">
                <i className="mi-edit-outline text-secondary fz-10" />
                <span className="tooltiptext--black bg-dark tooltiptext tooltip-stats-view">
                  {t('chatbot.config.node_content.edit')}
                </span>
              </div>
            </div>
          )}

          <div className="icon-action" onClick={removeNode}>
            <div className="tooltip-top">
              <i className="mi-trash-outline text-danger fz-10"></i>
              <span className="tooltiptext--black bg-dark tooltiptext tooltip-stats-view">
                {t('chatbot.config.node_content.delete')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NodeContent;
