import React, { memo, useState } from 'react';
import { Handle, Position, useReactFlow } from 'reactflow';
import cx from 'classnames';
import { hasChildren } from '../utils';

import PlusIconComponent from './PlusIconComponent';

import * as styles from '../styles.module.css';
import NodeContent from '../Contents/NodeContent';

const CustomNode = ({
  id, data, sourcePosition, targetPosition
}) => {
  // id -> Es el id del node
  const [isDropzoneActive, setDropzoneActive] = useState(false);

  const { getNodes } = useReactFlow();

  const onDrop = () => {
    setDropzoneActive(false);
  };

  const onDragOver = (evt) => {
    evt.preventDefault();
  };

  const onDragEnter = () => {
    setDropzoneActive(true);
  };

  const onDragLeave = () => {
    setDropzoneActive(false);
  };

  const onClick = (e) => {
    e.stopPropagation();
    data.addNode(id, data);
  };

  const handleEditNode = (e) => {
    e.stopPropagation();
    data.editNode(id);
  };

  const handlePreviewBot = (e) => {
    e.stopPropagation();
    data.previewBot(id);
  };

  const handleRemoveNode = () => {
    data.removeNode(id, data);
  };

  const className = cx(styles.node, { [styles.nodeDropzone]: isDropzoneActive });

  const displayPlusIcon = () => {
    if (['jump_to', 'active_mia', 'resolve_chat'].includes(data.option.option_type)) return false;

    const nodes = getNodes();

    return !hasChildren(nodes, id);
  };

  return (
    <>
      <div
        className={className}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onDragEnter={onDragEnter}
        onDragLeave={onDragLeave}
      >
        <Handle className={styles.handle} type="target" position={targetPosition || Position.Top} />
        <Handle className={styles.handle} type="source" position={sourcePosition || Position.Bottom} />
        <NodeContent
          nodeId={id}
          nodeData={data}
          editNode={handleEditNode}
          previewBot={handlePreviewBot}
          removeNode={handleRemoveNode}
        />
      </div>

      {displayPlusIcon() && (
        <PlusIconComponent onClick={onClick} />
      )}
    </>
  );
};

export default memo(CustomNode);
