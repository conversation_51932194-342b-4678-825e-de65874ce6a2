import React, { useEffect, useState } from "react";
import { useTranslation } from 'react-i18next';

import QuestionStep from "./QuestionStep";
import MenuOption from "./MenuOption";
import MessageOption from "./MessageOption";
import JumpToStep from "./Steps/JumpToStep";
import MenuOptionStep from "./Steps/MenuOptionStep";
import ResolveChatStep from "./Steps/ResolveChatStep";
import { CHATBOT_OPTIONS_TYPE } from '../../../constants/AppConstants';
import ChatbotOptionModel from '../ChatbotOptionModel';
import NoActiveMia from "./Steps/NoActiveMia";
import ApiIcon from "../../icons/ApiIcon";
import ApiOption from "./ApiOption";
import ResolveChatIcon from "../../icons/ResolveChatIcon";

const OptionBodyComponent = ({
  chatbot,
  chatbotOption,
  setChatbotOption,
  handleOptionClick,
  displayOptionTypes,
  setDisplayOptionTypes,
  errors,
  setErrors,
  handleActiveMiaOptionClick,
  handleCancelOption
}) => {
  const { t } = useTranslation();

  const [customerColumns, setCustomerColumns] = useState([]);

  useEffect(() => {
    loadCustomerColumns();
  }, []);

  const loadCustomerColumns = () => {
    ChatbotOptionModel.getCustomerColumns().then((res) => {
      setCustomerColumns(res.fields);
    });
  };

  const getClassSelected = (value) => {
    if (chatbotOption.option_type === value) return 'selected';

    return '';
  };

  return (
    <div className="container-fluid-no-padding">
      {displayOptionTypes && (
        <div className="row col-md-12 px-0">
          <div className="col-md-12">
            <label className="col-form-label fz-16">{t('chatbot.config.modal.select_step_type')}</label>
          </div>

          <div className={`d-flex col-md-12 mt-10 option-message-type ${getClassSelected(CHATBOT_OPTIONS_TYPE.MESSAGE)}`} onClick={() => handleOptionClick(CHATBOT_OPTIONS_TYPE.MESSAGE)}>
            <div className="d-flex">
              <i className="mi-message-square-outline fz-20" aria-hidden="true"></i>
            </div>

            <div className="d-flex flex-column ml-5">
              <span className="text-gray-dark fw-bold fz-14">{t('chatbot.config.options.message')}</span>
              <span className="all-day-text fz-12 t-justify">{t('chatbot.config.options.message_description')}</span>
            </div>
          </div>

          <div className={`d-flex col-md-12 mt-20 option-message-type ${getClassSelected(CHATBOT_OPTIONS_TYPE.MENU)}`} onClick={() => handleOptionClick(CHATBOT_OPTIONS_TYPE.MENU)}>
            <div className="d-flex">
              <i className="mi-menu-fill fz-20" aria-hidden="true"></i>
            </div>

            <div className="d-flex flex-column ml-5">
              <span className="text-gray-dark fw-bold fz-14">{t('chatbot.config.options.menu')}</span>
              <span className="all-day-text fz-12 t-justify">{t('chatbot.config.options.menu_description')}</span>
            </div>
          </div>

          <div className={`d-flex col-md-12 mt-20 option-message-type ${getClassSelected(CHATBOT_OPTIONS_TYPE.QUESTION)}`} onClick={() => handleOptionClick(CHATBOT_OPTIONS_TYPE.QUESTION)}>
            <div className="d-flex">
              <i className="mi-question-mark-circle-outline fz-20" aria-hidden="true"></i>
            </div>

            <div className="d-flex flex-column ml-5">
              <span className="text-gray-dark fw-bold fz-14">{t('chatbot.config.options.question')}</span>
              <span className="all-day-text fz-12 t-justify">{t('chatbot.config.options.question_description')}</span>
            </div>
          </div>

          <div className={`d-flex col-md-12 mt-20 option-message-type ${getClassSelected(CHATBOT_OPTIONS_TYPE.ACTIVE_MIA)}`} onClick={() => handleActiveMiaOptionClick(CHATBOT_OPTIONS_TYPE.ACTIVE_MIA)}>
            <div className="d-flex">
              <i className="mi-outline-robot-one fz-20" aria-hidden="true"></i>
            </div>

            <div className="d-flex flex-column ml-5">
              <span className="text-gray-dark fw-bold fz-14">{t('chatbot.config.options.active_mia')}</span>
              <span className="all-day-text fz-12 t-justify">{t('chatbot.config.options.active_mia_description')}</span>
            </div>
          </div>

          <div className={`d-flex col-md-12 mt-20 option-message-type ${getClassSelected(CHATBOT_OPTIONS_TYPE.JUMP_TO)}`} onClick={() => handleOptionClick(CHATBOT_OPTIONS_TYPE.JUMP_TO)}>
            <div className="d-flex">
              <i className="mi-link-2-outline fz-20" aria-hidden="true"></i>
            </div>

            <div className="d-flex flex-column ml-5">
              <span className="text-gray-dark fw-bold fz-14">{t('chatbot.config.options.go_to')}</span>
              <span className="all-day-text fz-12 t-justify">{t('chatbot.config.options.go_to_description')}</span>
            </div>
          </div>

          <div className={`d-flex col-md-12 mt-20 option-message-type ${getClassSelected(CHATBOT_OPTIONS_TYPE.RESOLVE_CHAT)}`} onClick={() => handleActiveMiaOptionClick(CHATBOT_OPTIONS_TYPE.RESOLVE_CHAT)}>
            <div className="d-flex">
              <i className="mi-message-check fz-20" aria-hidden="true" />
            </div>

            <div className="d-flex flex-column ml-5">
              <span className="text-gray-dark fw-bold fz-14">{t('chatbot.config.options.resolve_chat')}</span>
              <span className="all-day-text fz-12 t-justify">{t('chatbot.config.options.resolve_chat_description')}</span>
            </div>
          </div>
          
          <div className={`d-flex col-md-12 mt-20 option-message-type ${getClassSelected(CHATBOT_OPTIONS_TYPE.API)}`} onClick={() => handleOptionClick(CHATBOT_OPTIONS_TYPE.API)}>
            <div className="d-flex">
              <i className="fz-20" aria-hidden="true">
                <ApiIcon iconName="check-circle-outline" />
              </i>
            </div>

            <div className="d-flex flex-column ml-5">
              <span className="text-gray-dark fw-bold fz-14">{t('chatbot.config.options.api')}</span>
              <span className="all-day-text fz-12 t-justify">{t('chatbot.config.options.api_description')}</span>
            </div>
          </div>

          <div className="col-md-12 px-0">
            <span className="funnel-input-error">{errors.option_type}</span>
          </div>
        </div>
      )}

      {!displayOptionTypes && (
        <>
          {!chatbotOption.fake_option && (
            <>
              {chatbotOption.option_type === CHATBOT_OPTIONS_TYPE.QUESTION && (
                <QuestionStep
                  chatbotOption={chatbotOption}
                  setChatbotOption={setChatbotOption}
                  handleChangeOptionType={() => setDisplayOptionTypes(true)}
                  errors={errors}
                  customerColumns={customerColumns}
                  chatbot={chatbot}
                />
              )}

              {chatbotOption.option_type === CHATBOT_OPTIONS_TYPE.MENU && (
                <MenuOption
                  chatbotOption={chatbotOption}
                  setChatbotOption={setChatbotOption}
                  handleChangeOptionType={() => setDisplayOptionTypes(true)}
                  errors={errors}
                  customerColumns={customerColumns}
                  chatbot={chatbot}
                />
              )}

              {chatbotOption.option_type === CHATBOT_OPTIONS_TYPE.MESSAGE && (
                <MessageOption
                  chatbot={chatbot}
                  chatbotOption={chatbotOption}
                  setChatbotOption={setChatbotOption}
                  errors={errors}
                  setErrors={setErrors}
                  customerColumns={customerColumns}
                />
              )}

              {chatbotOption.option_type === CHATBOT_OPTIONS_TYPE.API && (
                <ApiOption
                  chatbot={chatbot}
                  chatbotOption={chatbotOption}
                  setChatbotOption={setChatbotOption}
                  errors={errors}
                  setErrors={setErrors}
                  customerColumns={customerColumns}
                />
              )}

              {chatbotOption.option_type === CHATBOT_OPTIONS_TYPE.ACTIVE_MIA && (
                <NoActiveMia
                  handleCancelOption={handleCancelOption}
                />
              )}

              {chatbotOption.option_type === CHATBOT_OPTIONS_TYPE.JUMP_TO && (
                <JumpToStep
                  chatbot={chatbot}
                  chatbotOption={chatbotOption}
                  setChatbotOption={setChatbotOption}
                  errors={errors}
                  setErrors={setErrors}
                  customerColumns={customerColumns}
                />
              )}
            </>
          )}

          {chatbotOption.fake_option && (
            <MenuOptionStep
              chatbotOption={chatbotOption}
              setChatbotOption={setChatbotOption}
              customerColumns={customerColumns}
              errors={errors}
            />
          )}
        </>
      )}
    </div>
  );
};

export default OptionBodyComponent;
