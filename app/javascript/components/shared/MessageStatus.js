import React from 'react';
import { useTranslation } from 'react-i18next';

import MessageDateTime from './MessageDateTime';
import MessageStatusIcon from './MessageStatusIcon';
import SenderData from './SenderData';

const MessageStatus = ({
  chatType,
  handleMessageEvents,
  message,
  dateFormat,
  containerClassName = ''
}) => {
  const { t } = useTranslation();

  const shouldShowIcon = () => {
    if (chatType === "whatsapp"
      && message.direction === 'outbound'
      && handleMessageEvents === true) {
      return true;
    }
    return (message.sent_by_retailer === true && (message.date_read || message.error_message));
  };

  return (
    <>
      <div className={`text-right message-status ${containerClassName}`}>
        <MessageDateTime
          chatType={chatType}
          message={message}
          dateFormat={dateFormat}
        />
        {shouldShowIcon() && (
          <MessageStatusIcon
            chatType={chatType}
            message={message}
          />
        )}
        {message.sent_by_mia && (
          <div class="d-block fs-10">
            <span class="mt-3">{t('chatbot_ai.message_status')}</span>
            <i className="mi-outline-robot-one fz-14 pl-3" aria-hidden="true"></i>
          </div>
        )}

      </div>
      {message.sender_full_name && (
        <SenderData
          senderFullName={message.sender_full_name}
          isNote={message.note}
        />
      )}
    </>
  );
};

export default MessageStatus;
