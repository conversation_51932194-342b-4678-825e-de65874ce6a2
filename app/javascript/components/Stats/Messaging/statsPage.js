/* eslint-disable import/no-unresolved */
import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { <PERSON>hn<PERSON>, Bar } from 'react-chartjs-2';
import { useDispatch, useSelector } from 'react-redux';

import moment from 'moment';
import {
  forEach,
  filter,
  isEmpty,
  cloneDeep
} from 'lodash';

import { LineChart, ColumnChart } from 'react-chartkick';
import 'chartkick/chart.js';
import Big from 'big.js';
import { DateRange } from "react-date-range";
import { es } from "date-fns/locale";

import MessagesByPlatformModel from "./MessagesByPlatformModel";
import waIcon from 'images/new_design/wa.png';
import msmIcon from 'images/new_design/msm.png';
import igIcon from 'images/new_design/ig.png';
import mlIcon from 'images/new_design/ml.png';

import Progressbar from '../../shared/ProgressBar';
import {
  fetchMessagesByPlatform,
  fetchUsageByPlatform,
  fetchAverageResponseTimes,
  fetchResponseTimesByHour,
  fetchMostUsedTags,
  fetchSentMessagesBy,
  fetchMessagesByHours,
  fetchAgentChats,
  fetchNewAndRecurringClients
} from '../../../actions/stats';

import { fetchAgents } from '../../../actions/agents';

import PlatformPercentage from "../PlatformPercentage";
import AvatarName from "../AvatarName";
import PlatformMessageCounter from "../PlatformMessageCounter";
import Tag from '../Tag';
import PaginatedItems from '../../common/PaginatedItems';
import timeUtils from '../../../util/timeUtils';

ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const rangeStartDate = moment().subtract(1, 'months').toDate();
const rangeEndDate = moment().toDate();

const platforms = [
  { value: null, label: 'Todas las plataformas' },
  { value: 0, label: 'WhatsApp' },
  { value: 1, label: 'Messenger' },
  { value: 2, label: 'Instagram' },
  { value: 3, label: 'Mercado Libre' }
];

const agentPerformancePlatforms = [
  { value: null, label: 'Todas las plataformas' },
  { value: 0, label: 'WhatsApp' },
  { value: 1, label: 'Messenger' },
  { value: 2, label: 'Instagram' }
];

const defaultDoughnutData = {
  datasets: [
    {
      data: [0, 0, 0, 0]
    }
  ]
};

const VBoptions = {
  plugins: {
    legend: {
      display: false
    },
    title: {
      display: false
    },
    tooltip: {
      callbacks: {
        label(tooltipItem) {
          return `${tooltipItem.formattedValue} %`;
        }
      }
    }
  },
  scales: {
    x: {
      grid: {
        display: false
      },
      title: {
        display: false
      }
    },
    y: {
      display: false
    }
  },
  maintainAspectRatio: false
};

const defaultMessagesByHour = [
  ["0 - 1 a.m.", 0], ["1 - 2 a.m.", 0], ["2 - 3 a.m.", 0],
  ["3 - 4 a.m.", 0], ["4 - 5 a.m.", 0], ["5 - 6 a.m.", 0],
  ["6 - 7 a.m.", 0], ["7 - 8 a.m.", 0], ["8 - 9 a.m.", 0],
  ["9 - 10 a.m.", 0], ["10 - 11 a.m.", 0], ["11 a.m. - 12 m.", 0],
  ["12 m. - 1 p.m.", 0], ["1 - 2 p.m.", 0], ["2 - 3 p.m.", 0],
  ["3 - 4 p.m.", 0], ["4 - 5 p.m.", 0], ["5 - 6 p.m.", 0],
  ["6 - 7 p.m.", 0], ["7 - 8 p.m.", 0], ["8 - 9 p.m.", 0],
  ["9 - 10 p.m.", 0], ["10 - 11 p.m.", 0], ["11 - 12 p.m.", 0]
];

const StatsPageComponent = () => {
  const dispatch = useDispatch();
  const {
    messagesByPlatform,
    usageByPlatform,
    averageResponseTimes,
    responseTimesByHour,
    mostUsedTags,
    newAndRecurringClientsData,
    agentChats,
    setMessagesBy,
    messagesByHour,
    responseTimesRule
  } = useSelector((reduxState) => reduxState.statsReducer);

  const { agents } = useSelector((reduxState) => reduxState.agentsReducer);

  const [showDateRange, setShowDateRange] = useState(false);
  const [startDate, setStartDate] = useState(rangeStartDate);
  const [endDate, setEndDate] = useState(rangeEndDate);
  const [dateFilter, setDateFilter] = useState({
    startDate: moment(rangeStartDate).format('YYYY-MM-DD'),
    endDate: moment(rangeEndDate).format('YYYY-MM-DD')
  });
  const [initialStartDate, setInitialStartDate] = useState(rangeStartDate);
  const [initialEndDate, setInitialEndDate] = useState(rangeEndDate);
  const [agentsOptions, setAgentsOptions] = useState([]);
  const [agentSelected0, setAgentSelected0] = useState();
  const [agentSelected1, setAgentSelected1] = useState();
  const [agentSelected3, setAgentSelected3] = useState();
  const [dataMessagesByPlatform, setDataMessagesByPlatform] = useState([]);
  const [averageResponseTimesFormated, setAverageResponseTimesFormated] = useState({ labels: [], datasets: [] });
  const [responseTimesByHourFormatted, setResponseTimesByHourFormatted] = useState({ labels: [], datasets: [] });
  const [newAndRecurringClients, setNewAndRecurringClients] = useState({
    newCustomers: 0,
    newCustomersPercentage: 0,
    recurringCustomers: 0,
    recurringCustomersPercentage: 0,
    totalCustomers: 0
  });
  const [selectedPlatform2, setSelectedPlatform2] = useState(platforms[0]);
  const [selectedPlatform3, setSelectedPlatform3] = useState(agentPerformancePlatforms[0]);
  const [selectedPlatformForMessages, setSelectedPlatformForMessages] = useState(platforms[0]);
  const [doughnutData, setDoughnutData] = useState(defaultDoughnutData);
  const [averageResponseTimesText, setAverageResponseTimesText] = useState('');
  const [responseTimesByHourText, setResponseTimesByHourText] = useState('');
  const [selectedAverageResponseTimePlatform, setSelectedAverageResponseTimePlatform] = useState(agentPerformancePlatforms[0]);
  const [selectedResponseTimeByHourPlatform, setSelectedResponseTimeByHourPlatform] = useState(agentPerformancePlatforms[0]);
  const [messagesByHourData, setMessagesByHourData] = useState(defaultMessagesByHour);

  const formatAgents = () => {
    const data = [{ value: null, label: 'Todos los agentes' }];
    forEach(agents, (row) => {
      data.push({ value: row.id, label: `${row.first_name} ${row.last_name}` });
    });
    setAgentsOptions(data);
    setAgentSelected1(data[0]);
    setAgentSelected3(data[0]);
  };

  const fetchStatistics = (paramStartDate, paramEndDate) => {
    dispatch(fetchMessagesByPlatform(paramStartDate, paramEndDate));
    dispatch(fetchUsageByPlatform(paramStartDate, paramEndDate));
    dispatch(fetchAverageResponseTimes(paramStartDate, paramEndDate, null, null));
    dispatch(fetchResponseTimesByHour(paramStartDate, paramEndDate, null, null));
    dispatch(fetchMostUsedTags(paramStartDate, paramEndDate));
    dispatch(fetchNewAndRecurringClients(paramStartDate, paramEndDate, null));
    dispatch(fetchAgentChats(paramStartDate, paramEndDate, null));
    dispatch(fetchSentMessagesBy(paramStartDate, paramEndDate, null));
    dispatch(fetchMessagesByHours(paramStartDate, paramEndDate, null));
  };

  const applySearch = () => {
    setStartDate(initialStartDate);
    setEndDate(initialEndDate);
    setDateFilter({
      startDate: moment(initialStartDate).format('YYYY-MM-DD'),
      endDate: moment(initialEndDate).format('YYYY-MM-DD')
    });
    setAgentSelected1(agentsOptions[0]);
    setAgentSelected3(agentsOptions[0]);
    setSelectedPlatform2(platforms[0]);
    setSelectedPlatformForMessages(platforms[0]);
    setShowDateRange(false);

    const paramStartDate = moment(initialStartDate).format('YYYY-MM-DD');
    const paramEndDate = moment(initialEndDate).format('YYYY-MM-DD');

    fetchStatistics(paramStartDate, paramEndDate);
  };

  useEffect(() => {
    fetchStatistics(dateFilter.startDate, dateFilter.endDate);

    return () => dispatch({ type: 'RESET_TEMPORAL_STATE' });
  }, []);

  const cancelSearch = () => {
    setInitialStartDate(startDate);
    setInitialEndDate(endDate);
    setShowDateRange(false);
  };

  useEffect(() => {
    formatAgents();
  }, [agents]);

  const onClickOut = (e) => {
    if (document.getElementById('date-range-stats') && !document.getElementById('date-range-stats').contains(e.target) && showDateRange) {
      cancelSearch();
    }
  };

  useEffect(() => {
    window.addEventListener('click', onClickOut);
    return () => {
      window.removeEventListener('click', onClickOut);
    };
  }, [showDateRange]);

  useEffect(() => {
    dispatch(fetchAgents());
  }, []);

  useEffect(() => {
    if (isEmpty(usageByPlatform)) {
      setDoughnutData(defaultDoughnutData);
    } else {
      setDoughnutData({
        datasets: [
          {
            data: [
              usageByPlatform.percentage_total_ws_messages,
              usageByPlatform.percentage_total_msn_messages,
              usageByPlatform.percentage_total_ig_messages,
              usageByPlatform.percentage_total_ml_messages
            ],
            backgroundColor: [
              '#20B038',
              '#625BFF',
              '#F95D0E',
              '#FFE800'
            ],
            borderColor: [
              '#20B038',
              '#625BFF',
              '#F95D0E',
              '#FFE800'
            ],
            borderWidth: 1
          }
        ]
      });
    }
  }, [usageByPlatform]);

  const fillData = (platformData) => {
    const data = {};
    forEach(platformData, (row) => {
      data[`"${row.date}"`] = row.amount;
    });

    return data;
  };

  const formatMessagesByPlatformData = () => {
    const data = [
      { "name": "Whatsapp", "data": fillData(messagesByPlatform.ws.data) },
      { "name": "Messenger", "data": fillData(messagesByPlatform.msn.data) },
      { "name": "Instagram", "data": fillData(messagesByPlatform.ig.data) },
      { "name": "Mercado Libre", "data": fillData(messagesByPlatform.ml.data) }
    ];

    setDataMessagesByPlatform(data);
  };

  useEffect(() => {
    if (messagesByPlatform) {
      formatMessagesByPlatformData();
    }
  }, [messagesByPlatform]);

  const manageRangeDate = ({ selection }) => {
    setInitialStartDate(selection.startDate);
    setInitialEndDate(selection.endDate);
  };

  const parseAverageResponseTimes = () => {
    const responseTimes = parseResponseTimesData(averageResponseTimes);

    setAverageResponseTimesFormated({
      labels: ['0 - 1 hr', '1 - 8 hr', '8 - 24 hr', '> 24 hr'],
      datasets: [
        {
          data: [responseTimes[0], responseTimes[1], responseTimes[2], responseTimes[3]],
          backgroundColor: '#782F79'
        }
      ]
    });

    setAverageResponseTimesText(timeUtils.secondsToHMS(responseTimes[4]));
  };

  const parseResponseTimesByHour = () => {
    const responseTimes = parseResponseTimesData(responseTimesByHour);

    setResponseTimesByHourFormatted({
      labels: ['0 - 1 hr', '1 - 8 hr', '8 - 24 hr', '> 24 hr'],
      datasets: [
        {
          data: [responseTimes[0], responseTimes[1], responseTimes[2], responseTimes[3]],
          backgroundColor: '#782F79'
        }
      ]
    });

    setResponseTimesByHourText(timeUtils.secondsToHMS(responseTimes[4]));
  };

  const parseResponseTimesData = (_averageResponseTimes) => {
    const range1 = filter(_averageResponseTimes, (row) => Number(row.conversation_time_average) >= 0 && Number(row.conversation_time_average) <= 3600);
    const range2 = filter(_averageResponseTimes, (row) => Number(row.conversation_time_average) > 3600 && Number(row.conversation_time_average) <= 28800);
    const range3 = filter(_averageResponseTimes, (row) => Number(row.conversation_time_average) > 28800 && Number(row.conversation_time_average) <= 86400);
    const range4 = filter(_averageResponseTimes, (row) => Number(row.conversation_time_average) > 86400);

    let totalTime = 0;
    let totalRange1 = 0;
    let totalRange2 = 0;
    let totalRange3 = 0;
    let totalRange4 = 0;

    forEach(_averageResponseTimes, (row) => {
      totalTime += Number(row.conversation_time_average);
    });

    if (!isEmpty(_averageResponseTimes)) {
      totalTime = Number(Big(totalTime).div(_averageResponseTimes.length).round(2));
      totalRange1 = Number(Big(range1.length).times(100).div(_averageResponseTimes.length).round(2));
      totalRange2 = Number(Big(range2.length).times(100).div(_averageResponseTimes.length).round(2));
      totalRange3 = Number(Big(range3.length).times(100).div(_averageResponseTimes.length).round(2));
      totalRange4 = Number(Big(range4.length).times(100).div(_averageResponseTimes.length).round(2));
    }

    return [totalRange1, totalRange2, totalRange3, totalRange4, totalTime];
  };

  useEffect(() => {
    parseAverageResponseTimes();
  }, [averageResponseTimes]);

  useEffect(() => {
    parseResponseTimesByHour();
  }, [responseTimesByHour]);

  const getAverageResponseTimes = (data) => {
    setAgentSelected1(data);
    dispatch(fetchAverageResponseTimes(dateFilter.startDate, dateFilter.endDate, data.value, selectedAverageResponseTimePlatform.value));
  };

  const getAverageResponseTimeByHour = (data) => {
    setAgentSelected0(data);
    dispatch(fetchResponseTimesByHour(dateFilter.startDate, dateFilter.endDate, data.value, selectedResponseTimeByHourPlatform.value));
  };

  const getAverageResponseTimesByPlatform = (data) => {
    setSelectedAverageResponseTimePlatform(data);
    dispatch(fetchAverageResponseTimes(dateFilter.startDate, dateFilter.endDate, agentSelected1.value, data.value));
  };

  const getResponseTimesByHourByPlatform = (data) => {
    setSelectedResponseTimeByHourPlatform(data);
    dispatch(fetchResponseTimesByHour(dateFilter.startDate, dateFilter.endDate, agentSelected0.value, data.value));
  };
  const getAgentChats = (data) => {
    setSelectedPlatform3(data);
    dispatch(fetchAgentChats(dateFilter.startDate, dateFilter.endDate, data.value));
  };

  const getMessagesByHour = (data) => {
    setSelectedPlatformForMessages(data);
    dispatch(fetchMessagesByHours(dateFilter.startDate, dateFilter.endDate, data.value));
  };

  useEffect(() => {
    const formattedData = {
      newCustomers: 0,
      newCustomersPercentage: 0,
      recurringCustomers: 0,
      recurringCustomersPercentage: 0,
      totalCustomers: 0
    };

    if (!_.isEmpty(newAndRecurringClientsData)) {
      formattedData.newCustomers += newAndRecurringClientsData.new_customers;
      formattedData.newCustomersPercentage += newAndRecurringClientsData.new_customers_percentage;
      formattedData.recurringCustomers += newAndRecurringClientsData.recurring_customers;
      formattedData.recurringCustomersPercentage += newAndRecurringClientsData.recurring_customers_percentage;
      formattedData.totalCustomers += newAndRecurringClientsData.total_customers;
    }

    setNewAndRecurringClients(formattedData);
  }, [newAndRecurringClientsData]);

  const getNewAndRecurringClients = (data) => {
    setAgentSelected3(data);
    dispatch(fetchNewAndRecurringClients(dateFilter.startDate, dateFilter.endDate, data.value));
  };
  const getSentMessagesBy = (data) => {
    setSelectedPlatform2(data);
    dispatch(fetchSentMessagesBy(dateFilter.startDate, dateFilter.endDate, data.value));
  };

  const handleExport = () => {
    const params = { start_date: initialStartDate, end_date: initialEndDate };

    MessagesByPlatformModel.exportMessagesByPlatform(params)
      .then((res) => {
        const { message } = res;
        showtoast(message);
      }).catch((error) => {
      if (error.message) {
        showtoast(error.message);
      } else {
        showtoast("Ocurrió un error inesperado.");
      }
    });
  };

  useEffect(() => {
    const data = cloneDeep(defaultMessagesByHour);
    let hourData;
    let localHour;

    forEach(messagesByHour, (rowMessage) => {
      forEach(rowMessage.message_data, (message) => {
        localHour = moment().utc().set('hour', message.hour).local()
          .locale('es')
          .format('H');
        hourData = data[localHour];
        data[localHour] = [hourData[0], hourData[1] + message.amount_messages];
      });
    });
    setMessagesByHourData(data);
  }, [messagesByHour]);

  return (
    <div className="container-fluid-no-padding mt-5">
      <div className="row">
        <div className="col-12 d-flex beetwen-flex">
          <div>
            <h1 className="page__title">Estadísticas de mensajería</h1>
            <span className="current-page-stats">
              Datos interesantes que tienes en cada plataforma de mensajería
            </span>
          </div>
          <div className="beetwen-flex">
            <div className="p-relative px-8">
              <div className="stats-date-range-button flex-center-xy" onClick={() => setShowDateRange(!showDateRange)}>
                {moment(initialStartDate).format('DD/MM/YYYY')}
                {' - '}
                {moment(initialEndDate).format('DD/MM/YYYY')}
                <i className="fas fa-chevron-down" />
              </div>
              {showDateRange && (
                <div className="stats-date-range bg-white tags" id="date-range-stats">
                  <DateRange
                    locale={es}
                    ranges={[
                      {
                        startDate: initialStartDate,
                        endDate: initialEndDate,
                        key: 'selection'
                      }
                    ]}
                    onChange={manageRangeDate}
                    moveRangeOnFirstSelection={false}
                    showPreview={false}
                  />
                  <button type="button" className="btn text-danger" onClick={cancelSearch}>Cancelar</button>
                  <button type="button" className="btn text-blue" onClick={applySearch}>Aplicar</button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="col-12 box-container border-gray mt-24">
        <div className="row box-container p-30">
          <div className="d-flex col-md-12 beetwen-flex p-relative">
            <h5 className="form-container_sub-title ml-0">Mensajes por plataformas</h5>

            <div className="text-center ml-16">
              <button type="button" className="blue-button my-24px my-md-0 fz-16" onClick={handleExport}>Descargar</button>
            </div>
          </div>

          <div className="row col-md-12">
            <div className="col-lg-4 col-md-4 flex-column flex-center-xy p-12">
              <div className="stats-total-message-container">
                <p>Mensajes en total</p>
                <p>{(messagesByPlatform.ws.total_messages + messagesByPlatform.msn.total_messages + messagesByPlatform.ig.total_messages + messagesByPlatform.ml.total_messages)}</p>
              </div>
              <div className="stats-card-row-values w-100">
                <span className="stats-card-label pb-8">Recibidos</span>
                <span className="stats-card-label pb-8 pr-50">
                  {(messagesByPlatform.ws.total_inbound + messagesByPlatform.msn.total_inbound + messagesByPlatform.ig.total_inbound + messagesByPlatform.ml.total_inbound)}
                </span>
              </div>
              <div className="stats-card-row-values w-100">
                <span className="stats-card-label pb-0">Enviados</span>
                <span className="stats-card-label pb-8 pr-50">
                  {(messagesByPlatform.ws.total_outbound + messagesByPlatform.msn.total_outbound + messagesByPlatform.ig.total_outbound + messagesByPlatform.ml.total_outbound)}
                </span>
              </div>
            </div>
            <div className="col-lg-8 col-md-8 mt-30">
              <LineChart colors={["#20B038", "#625BFF", "#F95D0E", "#FFE800"]} data={dataMessagesByPlatform} />
            </div>
          </div>

          <div className="row col-md-12 mt-20 stats-platform-counters">
            <PlatformMessageCounter
              icon={waIcon}
              className="stats-border-right"
              total={messagesByPlatform.ws.total_messages}
              inbound={messagesByPlatform.ws.total_inbound}
              outbound={messagesByPlatform.ws.total_outbound}
            />
            <PlatformMessageCounter
              className="stats-border-right"
              icon={msmIcon}
              total={messagesByPlatform.msn.total_messages}
              inbound={messagesByPlatform.msn.total_inbound}
              outbound={messagesByPlatform.msn.total_outbound}
            />
            <PlatformMessageCounter
              className="stats-border-right"
              icon={igIcon}
              total={messagesByPlatform.ig.total_messages}
              inbound={messagesByPlatform.ig.total_inbound}
              outbound={messagesByPlatform.ig.total_outbound}
            />
            <PlatformMessageCounter
              icon={mlIcon}
              total={messagesByPlatform.ml.total_messages}
              inbound={messagesByPlatform.ml.total_inbound}
              outbound={messagesByPlatform.ml.total_outbound}
            />
          </div>
        </div>
      </div>

      <div className="row mt-24 col-md-12 pr-0 pl-0">
        <div className="col-md-6 pr-12 stats-card">
          <div className="col-md-12 box-container pt-30 pr-30 pb-30 pl-30">
            <h5 className="form-container_sub-title ml-0 stats-card-title">Tiempo promedio de respuesta en horario laboral</h5>
            {responseTimesRule ? (
              <>
                <div className="mt-15 text-center">
                  <div className="d-flex justify-content-center mb-20">
                    <Select
                      options={agentsOptions}
                      value={agentSelected0}
                      className="stats-selector col-md-5 pl-0"
                      classNamePrefix="stats-selector"
                      onChange={getAverageResponseTimeByHour}
                      placeholder="Todos los agentes"
                    />

                    <Select
                      options={agentPerformancePlatforms}
                      value={selectedResponseTimeByHourPlatform}
                      className="stats-selector col-md-5 pl-0"
                      classNamePrefix="stats-selector"
                      onChange={getResponseTimesByHourByPlatform}
                      placeholder="Todas las plataformas"
                    />
                  </div>
                  <div className="stats-chart-bar-value">{responseTimesByHourText}</div>
                  <span className="stats-chart-bar-label">Tiempo promedio de respuesta en horario laboral</span>
                </div>
                <div className="mt-20 flex-center-xy">
                  <div style={{ width: 240 }}>
                    <Bar
                      options={VBoptions}
                      data={responseTimesByHourFormatted}
                      width={240}
                      height={200}
                    />
                  </div>
                </div>
              </>
            ) : (
              <div className="mt-15 text-center">
                <p className="fs-16">
                  No tienes activa esta funcionalidad, puedes activarla
                  {' '}
                  <a href={`/retailers/${ENV.SLUG}/business_rules`}> aquí</a>
                </p>
              </div>
            )}
          </div>
        </div>
        <div className="col-md-6 pl-12 stats-card">
          <div className="col-md-12 box-container pt-30 pr-30 pb-30 pl-30">
            <h5 className="form-container_sub-title ml-0 stats-card-title">Tiempo promedio de respuesta</h5>
            <div className="mt-15 text-center">
              <div className="d-flex justify-content-center mb-20">
                <Select
                  options={agentsOptions}
                  value={agentSelected1}
                  className="stats-selector col-md-5 pl-0"
                  classNamePrefix="stats-selector"
                  onChange={getAverageResponseTimes}
                  placeholder="Todos los agentes"
                />

                <Select
                  options={agentPerformancePlatforms}
                  value={selectedAverageResponseTimePlatform}
                  className="stats-selector col-md-5 pl-0"
                  classNamePrefix="stats-selector"
                  onChange={getAverageResponseTimesByPlatform}
                  placeholder="Todas las plataformas"
                />
              </div>
              <div className="stats-chart-bar-value">{averageResponseTimesText}</div>
              <span className="stats-chart-bar-label">Tiempo promedio de respuesta</span>
            </div>
            <div className="mt-20 flex-center-xy">
              <div style={{ width: 240 }}>
                <Bar
                  options={VBoptions}
                  data={averageResponseTimesFormated}
                  width={240}
                  height={200}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="row mt-24 col-md-12 pr-0 pl-0">
        <div className="col-md-6 pr-12 stats-card">
          <div className="col-md-12 box-container p-30 mh-vh-30">
            <h5 className="form-container_sub-title ml-0 stats-card-title">Etiquetas más utilizadas</h5>
            {mostUsedTags.length > 5
              ? (<PaginatedItems items={mostUsedTags} itemsPerPage={5} />)
              : mostUsedTags?.map((item) => (
                <Tag key={`most-used-tag-${item.tag_id}`} tag={item} />
              ))}
          </div>
        </div>

        <div className="col-md-6 pl-12 stats-card">
          <div className="col-md-12 box-container pt-30 pr-30 pb-30 pl-30">
            <div className="d-flex col-md-12 beetwen-flex p-relative">
              <h5 className="form-container_sub-title ml-0">Mensajes enviados por</h5>
              <div className="col-md-4 col-sm-6 px-0">
                <Select
                  options={platforms}
                  value={selectedPlatform2}
                  className="stats-selector"
                  classNamePrefix="stats-selector"
                  onChange={getSentMessagesBy}
                  placeholder="Todas las plataformas"
                />
              </div>
            </div>

            <table className="table table-borderless stats-table">
              <thead>
                <tr>
                  <th scope="col">Agente</th>
                  <th scope="col" className="text-center">Total</th>
                </tr>
              </thead>
              <tbody>
                {setMessagesBy?.map((agentStat) => (
                  <tr key={agentStat.retailer_user_id}>
                    <td>
                      <AvatarName firstName={agentStat.first_name} lastName={agentStat.last_name} />
                    </td>
                    <td className="text-right">{agentStat.total_messages}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div className="row mt-24 col-md-12 pr-0 pl-0">
        <div className="col-md-12 pr-12 stats-card">
          <div className="col-md-12 box-container p-30 mh-vh-30">
            <div className="d-flex col-md-12 beetwen-flex p-relative">
              <div className="col-md-8 col-sm-6 px-0">
                <h5 className="form-container_sub-title ml-0">Estado de conversaciones  por agente en tiempo real</h5>
                <span className="subtitle">Estos son los chats con los cuales ha habido interacción en el rango de fecha seleccionado</span>
              </div>
              <div className="col-md-4 col-sm-6 px-0">
                <Select
                  options={agentPerformancePlatforms}
                  value={selectedPlatform3}
                  className="stats-selector"
                  classNamePrefix="stats-selector"
                  onChange={getAgentChats}
                  placeholder="Todas las plataformas"
                />
              </div>
            </div>

            <table className="table table-borderless stats-table">
              <thead>
                <tr className="text-right">
                  <th className="text-left" scope="col">Agente</th>
                  <th scope="col">Total</th>
                  <th scope="col">Pendientes</th>
                  <th scope="col">Resueltos</th>
                </tr>
              </thead>
              <tbody>
                {agentChats?.map((agentChat, index) => (
                  <tr key={`agent-chat-${index.toString()}`}>
                    <td>
                      <AvatarName firstName={agentChat.first_name} lastName={agentChat.last_name} />
                    </td>
                    <td className="text-right">{agentChat.total_chats}</td>
                    <td className="text-right">{agentChat.amount_chat_in_process}</td>
                    <td className="text-right">{agentChat.amount_chat_resolved}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

      </div>

      <div className="row mt-24 col-md-12 pr-0 pl-0">

        <div className="col-md-6 pr-12 stats-card">
          <div className="col-md-12 box-container pt-30 pr-30 pb-30 pl-30">
            <div className="d-flex col-md-12 beetwen-flex p-relative">
              <h5 className="form-container_sub-title ml-0 stats-card-title">Clientes nuevos VS recurrentes con conversaciones</h5>
              <div className="col-md-4 col-sm-6 px-0">
                <Select
                  options={agentsOptions}
                  value={agentSelected3}
                  className="stats-selector"
                  classNamePrefix="stats-selector"
                  onChange={getNewAndRecurringClients}
                  placeholder="Todos los agentes"
                />
              </div>
            </div>

            <div className="col-md-12 pl-0 pr-0 pt-30">
              <div className="stats-card-row-values">
                <span className="stats-card-label">Clientes nuevos</span>
                <span className="stats-card-label">{newAndRecurringClients.newCustomers}</span>
              </div>
              <Progressbar bgcolor="#3CAAE1" progress={newAndRecurringClients.newCustomersPercentage || 0} height={32} />
              <div className="stats-card-row-values">
                <span className="stats-card-label">Clientes recurrentes</span>
                <span className="stats-card-label">{newAndRecurringClients.recurringCustomers}</span>
              </div>
              <Progressbar bgcolor="#3CAAE1" progress={newAndRecurringClients.recurringCustomersPercentage || 0} height={32} />
            </div>
          </div>
        </div>

        <div className="col-md-6 pl-12 stats-card">
          <div className="col-md-12 box-container p-30">
            <h5 className="form-container_sub-title ml-0 stats-card-title">Uso por plataforma</h5>
            <div className="mt-20">
              <Doughnut
                data={doughnutData}
                width={200}
                height={200}
                options={{
                  maintainAspectRatio: false,
                  cutout: '70%'
                }}
              />
            </div>

            <div className="mt-20 text-center">
              <div className="flex-center-xy">
                <PlatformPercentage className="mr-24" percentage={usageByPlatform.percentage_total_ws_messages} name="WhatsApp" color="#20B038" />
                <PlatformPercentage className="ml-24" percentage={usageByPlatform.percentage_total_msn_messages} name="Messenger" color="#625BFF" />
              </div>
              <div className="flex-center-xy mt-24">
                <PlatformPercentage className="mr-24" percentage={usageByPlatform.percentage_total_ig_messages} name="Instagram" color="#F95D0E" />
                <PlatformPercentage className="ml-24" percentage={usageByPlatform.percentage_total_ml_messages} name="Mercado Libre" color="#FFE800" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="row box-container mt-24 p-30">
        <div className="d-flex col-md-12 beetwen-flex p-relative">
          <h5 className="form-container_sub-title ml-0">Horarios de recepción de mensajes</h5>
          <div className="col-md-2 col-sm-6 px-0">
            <Select
              options={agentPerformancePlatforms}
              value={selectedPlatformForMessages}
              className="stats-selector pl-5"
              classNamePrefix="stats-selector"
              onChange={getMessagesByHour}
              placeholder="Todas las plataformas"
            />
          </div>
        </div>

        <div className="row col-md-12 mt-30">
          <ColumnChart data={messagesByHourData} />
        </div>
      </div>
    </div>
  );
};

export default StatsPageComponent;
