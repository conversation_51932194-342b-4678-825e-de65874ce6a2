import React, { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, Link } from 'react-router';
import {
  find, forEach, isEmpty, throttle
} from "lodash";
import { forkJoin } from 'rxjs';

// eslint-disable-next-line import/no-unresolved
import Loader from 'images/dashboard/loader.jpg';
import DealIcon from 'images/chargebee_plans/percentage_icon.svg';

import PlansModel from './PlansModel';
import { formatMoney } from "../../util/numberUtils";

import { getRetailerInfo } from "../../actions/retailerUsersActions";
import Addon from "./Addon";
import Charge from "./Charge";
import PaymentMethods from "./PaymentMethods";
import httpService from "../../services/httpService";
import PaymentMethodModel from '../PaymentMethods/PaymentMethodModel';
import { PlansDataService } from "./PlansDataService";
import PaymentPlanAlert from "../shared/PaymentPlanAlert";
import { addToDataLayer } from '../../util/gtm';
import { fetchCurrentRetailerUser } from '../../actions/actions';
import { CHARGEBEE_SEAT_ADDON_IDS, CHARGEBEE_GROWTH_ADDON_IDS } from "../../constants/AppConstants";
import PaymentChangeAlert from "./PaymentPlans/PaymentChangeAlert";

const csrfToken = document.querySelector('[name=csrf-token]').content;

const PaymentPlanDetails = () => {
  const { id: urlId } = useParams();
  const [id, setId] = useState(urlId);

  const dispatch = useDispatch();

  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);
  const { currentRetailerUser } = useSelector((reduxState) => reduxState.mainReducer);

  const [allPlans, setAllPlans] = useState([]);
  const [addons, setAddons] = useState([]);
  const [charges, setCharges] = useState([]);

  const [yearlyChecked, setYearlyChecked] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState({ price: 0 });
  const [totalCost, setTotalCost] = useState(0);
  const [setupIntent, setSetupIntent] = useState(null);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [subscriptionItems, setSubscriptionItems] = useState({});
  const [acceptTermsAndContitions, setAcceptTermsAndContitions] = useState(false);
  const [entitlements, setEntitlements] = useState([]);
  const [currentPlan, setCurrentPlan] = useState(null);
  const [newPlan, setNewPlan] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processPayment, setProcessPayment] = useState(false);
  const [totalCharges, setTotalCharges] = useState(0);
  const [estimate, setEstimate] = useState({});
  const [changedGrowthTool, setChangedGrowthTool] = useState(false);

  const {
    credit_note_estimates: creditNoteEstimates,
    unbilled_charge_estimates: unbilledChargeEstimates
  } = estimate;
  const activeTeamAgents = retailer_info.active_team_agents || 0;
  const searchParams = new URLSearchParams(window.location.search);
  const newPlanValue = searchParams.get('newPlan');

  const loadPaymentMethods = () => {
    PaymentMethodModel.get().then((res) => {
      setPaymentMethods(res.payment_methods.map((item) => ({ ...item, payment_payload: JSON.parse(item.payment_payload) })));
    });
  };

  const hasPlan = !isEmpty(currentPlan);

  const textButton = () => {
    if ((newPlan || hasChangedSeats || changedGrowthTool) && sumEstimates() > 0) return 'Pagar y actualizar plan';
    if (!hasPlan) return 'Pagar';
    if (currentPlan.status == 'cancelled') return 'Reactivar';

    return 'Actualizar';
  };

  const getPeriodicity = (periodicity) => {
    switch (periodicity) {
      case 'monthly':
        return 1;
      case 'quarterly':
        return 3;
      case 'yearly':
        return 12;
      default:
        return 3;
    }
  };

  const handleChangeToYearlyPlan = () => {
    if (selectedPlan?.item_id) {
      const isYearlyChecked = !yearlyChecked;
      if (isYearlyChecked) {
        allPlans.forEach(({ period, items }) => {
          if (period === 'yearly') {
            const newPlan = items.find((item) => item.item_id === selectedPlan.item_id);
            if (newPlan) {
              setId(newPlan.id);
            }
          }
        });
      } else if (urlId !== id) {
        setId(urlId);
      }

      setYearlyChecked(isYearlyChecked);
    }
  };

  useEffect(() => {
    dispatch(getRetailerInfo());
    dispatch(fetchCurrentRetailerUser());
    loadinitialData();

    return () => dispatch({ type: "RESET_TEMPORAL_STATE" });
  }, []);

  useEffect(() => {
    setSubscriptionItems(subscriptionData());
    calculateCost();
  }, [addons]);

  useEffect(() => {
    if (isEmpty(allPlans)) return;

    const planId = newPlanValue || id;
    forEach(allPlans, (allPlan) => {
      forEach(allPlan.items, (item) => {
        if (item.id == planId) {
          setSelectedPlan({ ...item, period: allPlan.period });
          loadAddons(allPlan.period);
          loadCharges();
          loadPlanEntitlements(item.item_id, item.tier_id);
          calculateCost();
        }
      });
    });
    setLoading(false);
  }, [allPlans, id]);

  const debouncedEstimate = useCallback(
    throttle((params) => {
      setProcessPayment(true);
      PlansModel.estimateUpdateSubscription(params).then((data) => {
        setEstimate(data.estimate);
      }).finally(() => setProcessPayment(false));
    }, 3000),
    []
  );

  useEffect(() => {
    if ((newPlan || hasChangedSeats || changedGrowthTool) && hasPlan && currentPlan && selectedPlan.id) {
      const currentPlanAddons = currentPlan.addons?.map((addon)=>{
        return {...addon, quantity: 0, selected: false}
      });

      const params = {
        new_subscription_item_id: selectedPlan.id,
        addons: [...addons, ...currentPlanAddons]
      };

      debouncedEstimate(params);
    }
  }, [selectedPlan, addons, newPlan]);

  const findPlanWithPeriod = (plans, planId) => {
    for (const plan of plans) {
      const foundItem = plan.items.find((item) => item.id === planId);
      if (foundItem) {
        return { ...foundItem, period: plan.period };
      }
    }
    return null;
  };

  const loadinitialData = () => {
    const promises$ = [
      PlansModel.getSubscriptionData(),
      PlansModel.get(),
      PlansModel.getSetupIntent(csrfToken),
      PaymentMethodModel.get()
    ];

    forkJoin(promises$).subscribe((res) => {
      const allPlansResponse = res[1].plans;

      if (newPlanValue) setNewPlan(findPlanWithPeriod(allPlansResponse, newPlanValue));

      const currentChargebeePlan = findPlanWithPeriod(allPlansResponse, res[0].plan.chargebee_plan_id);

      setCurrentPlan({ ...currentChargebeePlan, ...res[0].plan });
      setAllPlans(allPlansResponse);
      setSetupIntent(res[2].data);
      setPaymentMethods(res[3].payment_methods.map((item) => ({ ...item, payment_payload: JSON.parse(item.payment_payload) })));
    }, (error) => {
      console.log(error, '---------------------------error');
    });
    setOriginalCostToAddons();
  };

  const setOriginalCostToAddons = () => {
    const newAddons = addons.map((addon) => ({ ...addon, totalAdded: 0, originalPrice: addon.totalPrice }));

    setAddons(newAddons);
  };

  const addonsCost = (woBought = false) => {
    let cost = 0;
    addons.forEach((addon) => {
      if (addon.selected) {
        if (woBought) {
          cost += addon.woBoughtTotalPrice;
        } else {
          cost += addon.totalPrice;
        }
      }
    });
    return cost || 0;
  };

  const calculateCost = () => {
    let cost = addonsCost();
    cost += selectedPlan.price;
    setTotalCost(cost);
  };

  const subscriptionData = () => {
    if (!retailer_info) return;

    return {
      retailer: {
        id: retailer_info.id
      },
      subscription: {
        id: selectedPlan.id,
        addons,
        charges
      }
    };
  };

  const periodicityLabel = (period, large = false) => {
    switch (period) {
      case 'monthly':
        if (large) return 'Mensual';
        return 'Mes';
      case 'quarterly':
        if (large) return 'Trimestral';
        return 'Trimestre';
      case 'yearly':
        if (large) return 'Anual';
        return 'Año';
      default:
        return '';
    }
  };

  const dateOfRenewal = (period) => {
    const estimateRenewal = estimate?.subscription_estimate?.next_billing_at;
    if (estimateRenewal) {
      return moment.unix(estimateRenewal).format('DD/MM/YYYY');
    }
    if (currentPlan && !isEmpty(currentPlan) && currentPlan.status == 'active') {
      return currentPlan.next_billing_at;
    }
    const date = new Date();
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    switch (period) {
      case 'monthly':
        return `${day}/${month + 1}/${year}`;
      case 'quarterly':
        return `${day}/${month + 3}/${year}`;
      case 'yearly':
        return `${day}/${month}/${year + 1}`;
      default:
        return '';
    }
  };

  const matchAddon = (addon) => {
    const addonsToUse = hasPlan ? currentPlan.addons : addons;
    const fieldToFind = hasPlan ? 'item_price_id' : 'item_id';
    const valueToFind = hasPlan ? 'id' : 'item_id';
    const quantityField = hasPlan ? 'quantity' : 'itemsQuantity';
    const addonIn = find(addonsToUse, { [fieldToFind]: addon[valueToFind] });

    if (!addonIn) return addon;

    if (isEmpty(addon.tiers)) {
      return { ...addon, selected: true, itemsQuantity: addonIn[quantityField] };
    }

    if (!isEmpty(addon.tiers)) {
      return { ...addon, selected: true, selectedTier: find(addon.tiers, { price: addonIn.unit_price }) };
    }

    return addon;
  };

  const matchCharge = (charge) => {
    const findCharge = find(charges, { id: charge.id });
    return findCharge || charge;
  };

  const loadCharges = () => {
    PlansModel.getCharges().then((res) => {
      let newCharges = res.charges;
      if (!hasPlan) {
        newCharges = newCharges.map((addon) => matchCharge(addon));
      }

      setCharges(newCharges);
    });
  };

  const setBoughtAddons = (newAddons) => {
    let { addons: currentPlanAddons } = currentPlan;
    if (isEmpty(currentPlanAddons)) return newAddons;

    currentPlanAddons = currentPlanAddons.map((currentAddon) => {
      if (CHARGEBEE_SEAT_ADDON_IDS.includes(currentAddon.item_price_id))
        return { ...currentAddon, item_id: "Extra-Agent" };
      else if (CHARGEBEE_GROWTH_ADDON_IDS.includes(currentAddon.item_price_id))
        return { ...currentAddon, item_id: "Growth-Tools" };
      else
        return currentAddon;
    });

    return newAddons.map((addon) => {
      const foundCurrentAddon = currentPlanAddons.find((currentAddon) => currentAddon.item_id === addon.item_id);

      if (foundCurrentAddon) {
        const boughtQuantity = foundCurrentAddon.quantity;
        return {
          ...addon, boughtQuantity, selected: true, itemsQuantity: boughtQuantity
        };
      }
      return { ...addon };
    });
  };

  const loadAddons = (period) => {
    setAddons([]);
    PlansModel.getAddons({ period }).then((res) => {
      const newAddons = res.addons.map((addon) => matchAddon(addon));

      setAddons(setBoughtAddons(newAddons));
    });
  };

  const loadPlanEntitlements = (planId, tierId) => {
    PlansModel.getEntitlements({ plan_id: planId, tier_id: tierId }).then((res) => {
      setEntitlements(res.entitlements);
    });
  };

  const handleSelectAddon = (addon, options = {}) => {
    const currentAddon = addons.find((a) => a.id === addon.id);

    if (currentAddon.initialPrice === null && currentAddon.selected) {
      currentAddon.initialPrice = addon.totalPrice;
    }

    setChangedGrowthTool(currentAddon.pricing_model === 'flat_fee' && currentAddon.selected != options.checked);

    currentAddon.selected = true;
    if (currentAddon.pricing_model === 'tiered') {
      currentAddon.selectedTier = options.tier || {};
      currentAddon.totalPrice = options.tier?.price || 0;
      if (isEmpty(currentAddon.selectedTier)) currentAddon.selected = false;
    } else if (currentAddon.pricing_model === 'per_unit') {
      if (options.value === 0) currentAddon.selected = false;
      currentAddon.itemsQuantity = options.value;
      currentAddon.totalPrice = addon.price * options.value;
      currentAddon.woBoughtTotalPrice = addon.price * (options.value - (addon.boughtQuantity || 0));
    } else if (currentAddon.pricing_model === 'flat_fee') {
      currentAddon.totalPrice = addon.price;

      if (!options.checked) {
        currentAddon.totalPrice = 0;
        currentAddon.selected = options.checked;
      }
    }

    if (currentAddon.totalPrice > currentAddon.initialPrice) {
      const amount = currentAddon.totalPrice - currentAddon.initialPrice;
      currentAddon.totalAdded = prorateCost(amount);
    } else {
      currentAddon.totalAdded = 0;
    }

    const newAddons = addons.map((a) => {
      if (a.id === addon.id) return { ...currentAddon };
      return a;
    });
    setAddons(newAddons);
  };

  const handleSelectCharge = (charge, options) => {
    const currentCharge = charges.find((c) => c.id === charge.id);

    if (currentCharge.pricing_model === 'flat_fee') {
      currentCharge.selected = options.selected;
      currentCharge.totalPrice = charge.price;
      setTotalCharges(totalCharges + charge.price);

      if (!options.selected) {
        currentCharge.totalPrice = 0;
        setTotalCharges(totalCharges - charge.price);
      }
    } else if (currentCharge.pricing_model === 'per_unit') {
      if (options.quantity === 0) {
        currentCharge.selected = false;
        currentCharge.totalPrice = 0;
        setTotalCharges(totalCharges - charge.price);
      } else {
        currentCharge.selected = true;
        currentCharge.totalPrice = charge.price * options.quantity;
        setTotalCharges(totalCharges + charge.price);
      }
    }

    const newCharges = charges.map((c) => {
      if (c.id === charge.id) return { ...currentCharge };
      return c;
    });

    setCharges(newCharges);
  };

  const totalSelectedAgents = () => {
    if (isEmpty(addons) || isEmpty(entitlements)) return 0;

    return addons.reduce((total, addon) => (addon.selected && addon.item_id == 'Extra-Agent' ? total + addon.itemsQuantity : total), 0);
  };

  const includedAgents = () => {
    const element = find(entitlements, { feature_id: "agents-included" });
    return element?.value ?? 0;
  };

  const totalAgents = () => {
    if (isEmpty(addons) || isEmpty(entitlements)) return 0;

    return Number(includedAgents()) + totalSelectedAgents();
  };

  const totalBoughtAgents = () => {
    if (isEmpty(addons)) return 0;

    const extraAgentsAddon = addons.find((addon) => addon.item_id === 'Extra-Agent');
    return Number(extraAgentsAddon?.boughtQuantity) || 0;
  };

  const mapDataFromItemId = (item) => {
    if (item.selected) {
      switch (item.item_id) {
        case 'Extra-Agent':
          return {
            event: 'AdditionalUsersPayment',
            monto: item.totalPrice / 100,
            usuarios: item.itemsQuantity
          };
        case 'Hazme-mi-chatbot':
          return {
            event: 'CustomChatbotPayment',
            monto: item.price / 100
          };
        case 'Mercately-Setup-3-hours':
          return {
            event: 'MercatelySetupPayment',
            monto: item.price / 100
          };
        default:
          return null;
      }
    }

    return null;
  };

  const addAddonsAndCharges = () => {
    const allCharges = charges.concat(addons).map(mapDataFromItemId).filter((item) => !!item);
    allCharges.forEach((data) => {
      addToDataLayer(data);
    });
  };

  const createSubscription = (checkPaymentMethods = true) => {
    if (!acceptTermsAndContitions && !retailer_info.accept_terms_and_conditions) {
      alert('Debes aceptar los términos y condiciones');
      return;
    }
    if (totalAgents() < activeTeamAgents) {
      alert('Los usuarios activos es mayor a usuarios a contratar, desactiva usuarios para continuar');
      return;
    }
    if (checkPaymentMethods && paymentMethods.length === 0) {
      PlansDataService.setSubject({ addCardAndPay: true });
      return;
    }
    setProcessPayment(true);
    const params = subscriptionInfo();
    const paymentValue = String(subtotal());

    const gtmEventPayment = {
      event: 'paymentSuccess',
      paymentAmount: paymentValue,
      periodo: selectedPlan.period
    };

    const gtmEventSubscription = {
      event: 'subscriptionPayment',
      subscriptionPayment: paymentValue,
      periodo: selectedPlan.period
    };

    if (currentPlan?.status == 'cancelled' && !newPlan) {
      PlansModel.reactivateSubscription(params).then((data) => {
        location.href = data.redirect_to;
        if (data.message) showtoast(data.message);
      }).catch((error) => {
        if (error.message) {
          showtoast(error.message);
        } else {
          showtoast('Error al reactivar la suscripción');
        }
        setProcessPayment(false);
      });
      return;
    }

    if (!currentPlan || isEmpty(currentPlan)) {
      httpService.post('/api/v1/plans/create_subscription', params).then((data) => {
        addAddonsAndCharges();
        addToDataLayer(gtmEventPayment);
        addToDataLayer(gtmEventSubscription);
        location.href = data.redirect_to;
        if (data.message) showtoast(data.message);
      }).catch((error) => {
        if (error.message) {
          showtoast(error.message);
        } else {
          showtoast('Error al crear la suscripción');
        }
        setProcessPayment(false);
      });
    } else {
      params.replace_items_list = true;
      httpService.put('/api/v1/plans/update_subscription', params).then((data) => {
        addAddonsAndCharges();
        addToDataLayer(gtmEventPayment);
        addToDataLayer(gtmEventSubscription);
        location.href = data.redirect_to;
        if (data.message) showtoast(data.message);
      }).catch((error) => {
        if (error.message) {
          showtoast(error.message);
        } else {
          showtoast('Error al actualizar la suscripción');
        }
        setProcessPayment(false);
      });
    }
  };

  const subscriptionInfo = () => ({
    accept_terms_and_conditions: acceptTermsAndContitions,
    subscription: {
      id: selectedPlan.id,
      addons,
      charges,
      replace_items: !isEmpty(currentPlan)
    }
  });

  const handleAcceptTermsAndContitions = () => {
    setAcceptTermsAndContitions(!acceptTermsAndContitions);
  };

  const currentPlanDates = () => {
    const startDate = new Date(currentPlan.current_term_start);
    const endDate = new Date(currentPlan.current_term_end);
    const currentDate = new Date();
    return {
      startDate,
      endDate,
      currentDate
    };
  };

  const prorateCost = (amount) => {
    const { startDate, endDate, currentDate } = currentPlanDates();

    const amountToPay = calculateProrate(startDate, endDate, currentDate, amount);

    return amountToPay;
  };

  const calculateProrate = (startDate, endDate, currentDate, totalCost) => {
    const totalDays = (endDate - startDate) / (1000 * 60 * 60 * 24);
    const daysToPay = ((endDate - currentDate) / (1000 * 60 * 60 * 24)) + 1;
    const proratedAmount = (totalCost / totalDays) * daysToPay;

    return proratedAmount;
  };

  const selectedCharges = charges.filter((charge) => charge.selected);

  const backToPlansUrl = () => {
    if (!currentPlan || isEmpty(currentPlan)) {
      return `/retailers/${ENV.SLUG}/plans`;
    }

    return `/retailers/${ENV.SLUG}/pricing`;
  };

  const checkAcquired = (charge) => {
    if (retailer_info.make_my_chatbot) {
      if (charge.id === 'Hazme-mi-chatbot-USD') {
        return true;
      }
    }
    if (retailer_info.mercately_setup) {
      if (charge.id === 'Mercately-Setup-3-hours-USD') {
        return true;
      }
    }
    return false;
  };

  const currentPlanSeats = totalBoughtAgents() + Number(includedAgents());

  const hasChangedSeats = currentPlanSeats !== totalAgents();

  const sumEstimates = () => {
    if (!newPlan && !hasChangedSeats && !changedGrowthTool) return 0;

    const unBilledCharges = unbilledChargeEstimates?.reduce((sum, currentObject) => sum + (currentObject.amount || 0), 0) || 0;
    const credits = creditNoteEstimates?.reduce((sum, currentObject) => sum + (currentObject.total || 0), 0) || 0;
    const total = (unBilledCharges - credits) / 100;
    return total > 0 ? total : 0;
  };

  const subtotal = () => {
    if (newPlan || hasPlan) return sumEstimates() + (totalCharges / 100);

    return (totalCost + totalCharges) / 100 || 0;
  };

  const renderSubtotal = () => {
    const subtotalValue = subtotal();
    return `$ ${formatMoney(subtotalValue)} usd`;
  };

  const isUpgradePlan = () => {
    if ((!newPlan && !hasChangedSeats && !changedGrowthTool) || sumEstimates() === 0) return false;

    return !isEmpty(unbilledChargeEstimates) || !isEmpty(creditNoteEstimates);
  };

  const showAddonsAndCharges = () => !(
    hasPlan
      && (retailer_info?.plan_status === "inactive"
        || currentPlan?.status == "cancelled")
  );

  const chargesToShow = () => {
    if (isEmpty(charges)) return [];

    const plan = selectedPlan || currentPlan
    if (!plan?.has_ai) {
      return charges.filter((charge) => charge.item_id !== 'mia-configuration');
    }

    return charges;
  }

  return (
    <div className="ml-sm-60 no-left-margin-xs d-flex screen-height">
      <div className="w-100 p-40">
        <PaymentPlanAlert createSubscription={createSubscription} submitting={processPayment} />
        <div className="col-md-12">
          <a className="text-gray-dark cursor-pointer text-decoration-none fz-14" href={backToPlansUrl()} method="GET">
            <i className="mi-arrow-ios-back-outline" />
            Atrás
          </a>
        </div>

        <div className="mt-20 col-md-12">
          <h1 className="page__title">Configura y adquiere tu plan</h1>
        </div>
        {loading && (
          <div>
            <div className="chat_loader mt-100">
              <img src={Loader} alt="" />
            </div>
          </div>
        )}
        {!loading && (
          <div className="d-flex flex-row mt-20">
            <div className="col-md-7">
              <div className="col-md-12 px-0">
                <div className="plan-container p-32">
                  <div className="mb-16 d-flex align-items-center justify-content-start">
                    <span className="fz-22 ff-poppins-semibold">
                      Plan
                      {' '}
                      {selectedPlan.name}
                    </span>
                    <Link
                      className="blue-bordered-button border-5 btn fz-12 ml-12 px-4 py-0 text-center"
                      to={`/retailers/${ENV.SLUG}/plans${hasPlan ? '?changePlan=true' : ''}`}
                    >
                      Cambiar Plan
                    </Link>
                  </div>
                  <span className="text-light-gray fz-14">{selectedPlan.item_description}</span>
                  <div className="d-flex flex-row mt-10">
                    <span className="fz-22 semi-bold text-gray-dark mr-2">
                      $
                      {formatMoney((selectedPlan.price) / 100 || 0)}
                    </span>
                    <span className="mt-9 fz-14 text-gray-dark">
                      usd/
                      {periodicityLabel((selectedPlan.period), true)}
                    </span>
                  </div>
                  {(!hasPlan && (selectedPlan.period !== 'yearly' || yearlyChecked)) && (
                    <div className="d-flex">
                      <div className="align-items-center bg-light-cian border-12 d-flex p-14 mt-16 fz-14">
                        <img src={DealIcon} alt="Cambia al plan anual y ahorra hasta 20%" className="mr-8" />
                        Cambia al plan anual y ahorra hasta 20%
                        <label className="switch medium top-0 ml-16">
                          <input type="checkbox" checked={yearlyChecked} onChange={handleChangeToYearlyPlan} />
                          <span className="slider round" />
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {addons.map((addon) => (
                <Addon
                  key={`${addon.id}`}
                  addon={addon}
                  handleSelectAddon={handleSelectAddon}
                  entitlements={entitlements}
                  disabledInput={!showAddonsAndCharges()}
                  activeTeamAgents={activeTeamAgents}
                  showExtraAgentAlert={totalAgents() < activeTeamAgents}
                  periodicity={getPeriodicity(selectedPlan.period)}
                />
              ))}

              <h2 className="fz-22 ff-poppins-semibold my-24">
                Obtén apoyo adicional
              </h2>

              {charges.length > 0
                && (
                <div>
                    {chargesToShow().map((charge) => (
                      <div className="plan-container p-32 mb-24">
                        <Charge
                          key={`${charge.id}`}
                          charge={charge}
                          handleSelectCharge={handleSelectCharge}
                          acquired={charge.acquired || checkAcquired(charge)}
                          disabledInput={!showAddonsAndCharges()}
                        />
                      </div>
                    ))}
                </div>
                )}
            </div>

            <div className="col-md-5">
              <div className="payment-method-container mt-n3 mb-15">
                <PaymentMethods
                  setupIntent={setupIntent}
                  retailerInfo={retailer_info}
                  paymentMethods={paymentMethods}
                  setPaymentMethods={setPaymentMethods}
                  loadPaymentMethods={loadPaymentMethods}
                  loadSubscriptionData={subscriptionInfo}
                  createSubscription={createSubscription}
                />
              </div>

              <div className="plan-container p-32">

                <div>
                  <span className="fz-22 ff-poppins-semibold">Detalles de facturación</span>
                  <hr />
                  <div>
                    <p className="semi-bold fz-14">
                      Ciclo de facturación:
                      {' '}
                      <span className="ff-poppins-semibold">
                        {periodicityLabel(selectedPlan.period, true)}
                      </span>
                    </p>
                  </div>
                  <hr />
                </div>

                <span className="semi-bold text-blue fz-18 mb-16">
                  Pagos
                  {' '}
                  {periodicityLabel(selectedPlan.period, true)}
                  es
                </span>

                <div className="d-flex justify-content-between align-items-center fz-12">
                  <div className="d-flex flex-column">
                    <span className="ff-poppins-semibold text-gray-dark">
                      Plan
                      {' '}
                      {selectedPlan.external_name}
                      {' '}
                      {periodicityLabel(selectedPlan.period, true)}
                    </span>
                  </div>
                  <span className="ff-poppins-semibold text-gray-dark">
                    $
                    {formatMoney(selectedPlan.price / 100 || 0)}
                    {' '}
                    usd
                  </span>
                </div>
                <span className="fz-12 text-light-gray">
                  $
                  {formatMoney(selectedPlan.price / 100 / getPeriodicity(selectedPlan.period) || 0)}
                  {' '}
                  usd
                  {' '}
                  x
                  {' '}
                  {getPeriodicity(selectedPlan.period)}
                  {' '}
                  meses
                </span>

                {addons.map((addon) => {
                  if (addon.selected) {
                    return (
                      <div key={`addon-${addon.id}`} className="mt-15">
                        <div className="d-flex justify-content-between align-items-center fz-12">
                          <div className="d-flex flex-column">
                            <span className="ff-poppins-semibold text-gray-dark">{addon.external_name}</span>
                          </div>
                          <span className="ff-poppins-semibold text-gray-dark">
                            {`$${formatMoney((addon.totalPrice / 100) || 0)} usd`}
                          </span>
                        </div>
                        <span className="fz-12 text-light-gray d-block">
                          $
                          {formatMoney((addon.price / getPeriodicity(selectedPlan.period) / 100) || 0)}
                          {' '}
                          usd
                          {' '}
                          x
                          {addon.pricing_model === 'per_unit' && (
                            <>
                            {' '}
                            {addon.itemsQuantity}
                            {` agente${addon.itemsQuantity > 1 ? 's' : ''} `}
                            x
                            </>
                          )}
                          {' '}
                          {getPeriodicity(selectedPlan.period)}
                          {' '}
                          meses
                        </span>
                      </div>
                    );
                  }
                  return null;
                })}

                <div className="d-flex justify-content-between fz-14 mt-15">
                  <span className="ff-poppins-semibold text-gray-dark">
                    Pago total
                    {' '}
                    {periodicityLabel(selectedPlan.period, true)}
                  </span>
                  <span className="ff-poppins-semibold text-gray-dark">
                    {`$${formatMoney(totalCost / 100 || 0)} usd`}
                  </span>
                </div>

                <div>
                  <p className="m-0 text-light-gray fz-12">
                    Fecha de renovación:
                    {' '}
                    {dateOfRenewal(selectedPlan.period)}
                  </p>
                  <hr />
                </div>

                <div>
                  {selectedCharges.length > 0 && (
                    <span className="semi-bold text-blue fz-18 mb-16">
                      Pagos no recurrentes
                    </span>
                  )}
                  {selectedCharges.map((charge) => (
                    <>
                      <div className="d-flex justify-content-between fz-12 mt-10" key={`charge-${charge.id}`}>
                        <span className="ff-poppins-semibold text-gray-dark">{charge.external_name}</span>
                        <span className="ff-poppins-semibold text-gray-dark">
                          {`$${formatMoney(charge.totalPrice / 100 || 0)} usd`}
                        </span>
                      </div>
                      <div>
                        <p className="m-0 text-light-gray fz-12">
                          1 pago
                        </p>
                      </div>
                    </>
                  ))}
                  {selectedCharges.length > 0 && <hr />}
                </div>

                {currentPlan
                  && (
                  <>
                    {addons.some((addon) => addon.totalAdded > 0)
                      && <span className="bold-400 text-gray-dark mt-10">Cargos por actualizaciones</span>}
                    {addons.map((addon) => {
                      if (addon.selected && addon.totalAdded > 0) {
                        return (
                          <div className="d-flex justify-content-between fz-14" key={`addon-${addon.id}`}>
                            <span className="text-light-gray">{addon.external_name}</span>
                            <span className="text-light-gray">
                              {`$${formatMoney(addon.totalAdded / 100 || 0)} usd`}
                            </span>
                          </div>
                        );
                      }
                    })}
                  </>
                  )}

                {
                  isUpgradePlan() && (
                    <>
                      <span className="semi-bold text-blue fz-18">
                        Actualización de pago
                      </span>
                      {
                        unbilledChargeEstimates?.map((unbilled) => (
                          <div className="d-flex justify-content-between align-items-center fz-12 mt-15">
                            <div className="d-flex flex-column">
                              <span className="ff-poppins-semibold text-gray-dark">
                                {
                                  unbilled.entity_type === "plan_item_price"
                                    ? `Plan ${newPlan.external_name} ${periodicityLabel(newPlan.period, true)}`
                                    : unbilled.description
                                  }
                              </span>
                            </div>
                            <span className="ff-poppins-semibold text-gray-dark">
                              {`$${formatMoney((unbilled?.amount || 0) / 100)} usd`}
                            </span>
                          </div>
                        ))
                      }
                      {
                        creditNoteEstimates?.map((credit) => (
                          <div className="mt-15">
                            <div className="d-flex justify-content-between align-items-center fz-12">
                              <div className="d-flex flex-column">
                                <span className="ff-poppins-semibold text-gray-dark">
                                  Créditos factura
                                  {' '}
                                  {credit.reference_invoice_id}
                                </span>
                              </div>
                              <span className="ff-poppins-semibold text-gray-dark">
                                -
                                {' '}
                                {`$${formatMoney((credit?.total || 0) / 100)} usd`}
                              </span>
                            </div>
                          </div>
                        ))
                      }
                      <div>
                        <hr />
                      </div>
                    </>
                  )
                }

                <div className="d-flex justify-content-between">
                  <span className="semi-bold fz-18 text-gray-dark">Subtotal</span>
                  <span className="semi-bold fz-16 text-gray-dark">
                    {renderSubtotal()}
                  </span>
                </div>
                <div className="d-flex justify-content-between fz-14 mt-4">
                  <span className="semi-bold text-gray-dark">Impuestos</span>
                  <span className="semi-bold fz-16 text-gray-dark">
                    {`$ ${formatMoney(0)} usd`}
                  </span>
                </div>

                <div>
                  <hr />
                  <div className="d-flex justify-content-between">
                    <span className="ff-poppins-semibold fz-18 text-gray-dark">Total a pagar hoy</span>
                    <span className="ff-poppins-semibold fz-16 text-gray-dark">
                      {renderSubtotal()}
                    </span>
                  </div>
                </div>

                {
                  (hasPlan && (newPlan || hasChangedSeats || changedGrowthTool)) && (
                    <PaymentChangeAlert
                      totalCost={totalCost}
                      currentPlanName={`${currentPlan.external_name} ${periodicityLabel(currentPlan.period, true)}`}
                      selectedPlanName={`${selectedPlan.external_name} ${periodicityLabel(selectedPlan.period, true)}`}
                      dateOfRenewal={dateOfRenewal()}
                      totalSelectedAgents={totalSelectedAgents()}
                      isUpgradePlan={sumEstimates() > 0}
                      currentPlanSeats={currentPlan?.bought_agents ? currentPlan?.bought_agents : currentPlanSeats}
                      totalAgents={totalAgents()}
                    />
                  )
                }

                <p className="text-light-gray mt-10 fz-12 text-justify">
                  Aceptas que tu membresía de Mercately continúe activa, y
                  hasta que la canceles, te cobremos el cargo
                  {' '}
                  {periodicityLabel(selectedPlan.period, true)?.toLowerCase()}
                  {' '}
                  actualizado. Puedes cancelar la membresía en cualquier momento
                  para evitar cargos en el futuro.
                </p>

                {!retailer_info.accept_terms_and_conditions && (
                  <div className="d-flex fz-14 mt-10">
                    <input type="checkbox" checked={acceptTermsAndContitions || retailer_info.accept_terms_and_conditions} onChange={handleAcceptTermsAndContitions} />
                    <span className="text-light-gray ml-8">Aceptar términos y condiciones</span>
                  </div>
                )}

                <div className="mt-20">
                  <button
                    type="button"
                    className="btn blue-button btn-lg btn-block mx-0"
                    disabled={processPayment}
                    onClick={createSubscription}
                  >
                    {processPayment && <span className="pr-5"><i className="fas fa-spinner"></i></span>}
                    {textButton()}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentPlanDetails;
