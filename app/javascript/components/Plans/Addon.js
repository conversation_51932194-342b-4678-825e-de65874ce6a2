import React, { useEffect, useState } from "react";

import Select from 'react-select';
import { find, isEmpty } from "lodash";
import UserIcon from 'images/chargebee_plans/addon_user.png';
import { formatMoney } from "../../util/numberUtils";
import customStyles from '../../util/selectStyles';

const Addon = ({
  addon = {},
  handleSelectAddon,
  entitlements,
  disabledInput = false,
  showExtraAgentAlert,
  activeTeamAgents,
  periodicity
}) => {
  const [tiers, setTiers] = useState([]);
  const [selectedTier, setSelectedTier] = useState(null);
  const [selectedValue, setSelectedValue] = useState(0);
  const [agentsIncluded, setAgentsIncluded] = useState(null);

  useEffect(() => {
    formatTiers(addon.tiers);

    if (addon.pricing_model === 'tiered') setSelectedTier(addon.selectedTier || null);
    if (addon.pricing_model === 'per_unit') setSelectedValue(addon.itemsQuantity || 0);
    if (addon.pricing_model === 'flat_fee') handleSelectAddon(addon, { checked: addon.selected || false });
  }, []);

  useEffect(() => {
    if (!addon || isEmpty(addon) || isEmpty(entitlements)) return;

    if (addon.item_id === 'Extra-Agent') {
      const element = find(entitlements, { feature_id: "agents-included" });
      setAgentsIncluded(element);
    }
  }, [addon, entitlements]);

  useEffect(() => {
    if (!addon || isEmpty(addon)) return;

    if (addon.pricing_model === 'per_unit') handleSelectAddon(addon, { value: selectedValue });
  }, [selectedValue]);

  useEffect(() => {
    if (!selectedTier || isEmpty(selectedTier)) return;

    handleSelectAddon(addon, { tier: selectedTier });
  }, [selectedTier]);

  const handleSelectedTier = (option) => {
    setSelectedTier(option.tier);
  };

  const buildTierLabelOption = (tier) => {
    let label = '';
    if (tier.starting_unit && tier.ending_unit) label = `${tier.starting_unit} - ${tier.ending_unit}`;
    if (tier.starting_unit && !tier.ending_unit) label = `> ${tier.starting_unit}`;

    return `${label}, precio: $${formatMoney(tier.price / 100 || 0)}`;
  };

  const formatTiers = (tiersToFormat) => {
    setTiers(tiersToFormat.map((tier) => ({
      value: tier.price,
      label: buildTierLabelOption(tier),
      tier
    })));
  };

  const handleIncrement = () => {
    setSelectedValue(selectedValue + 1);
  };

  const handleDecrement = () => {
    if (selectedValue === 0) return;

    setSelectedValue(selectedValue - 1);
  };

  const selectFlatFeeAddon = (e) => {
    const { target } = e;
    const { checked } = target;
    handleSelectAddon(addon, { checked });
  };

  const showSeatsAlert = () => addon.item_id === 'Extra-Agent' && showExtraAgentAlert;

  switch (addon.pricing_model) {
    case 'flat_fee':
      return (
        <div className="col-md-12 px-0 mt-20 border-24">
          <div className="plan-container p-32">
            <div className="d-flex justify-content-between align-items-center">
              <h2 className="fz-22 ff-poppins-semibold mb-10">{addon.metadata?.title || addon.external_name}</h2>
              <label className="switch">
              {!disabledInput && (
                <>
                  <input type="checkbox" checked={addon.selected} onChange={selectFlatFeeAddon} />
                  <span className="slider round" />
                </>
              )}
              </label>
            </div>
            <div className="d-flex flex-row">
              <span className="fz-22 text-gray-dark font-weight-bold"></span>
              <span className="mt-9 fz-14 text-light-gray">
                $
                {formatMoney((addon.price / 100) || 0)} usd
              </span>
            </div>
          </div>
        </div>
      );
    case 'tiered':
      return (
        <div className="col-md-12 px-0 mt-20 border-24">
          <div className="plan-container p-32">
            <div className="d-flex justify-content-between align-items-center">
              <span className="fz-22 text-gray-dark font-weight-bold mb-10">{addon.metadata?.title || addon.external_name}</span>
            </div>
            <Select
              placeholder="Elige una opción..."
              options={tiers}
              onChange={handleSelectedTier}
              value={tiers.find((el) => el.value === selectedTier?.price)}
              styles={customStyles}
            />
          </div>
        </div>
      );
    case 'per_unit':
      return (
        <div className="col-md-12 px-0 mt-20 border-24">
          <div className="plan-container p-32">
            <h2 className="fz-22 ff-poppins-semibold mb-16">{addon.metadata?.title || addon.external_name}</h2>

            {agentsIncluded && (
              <div className="d-flex">
                <div className="align-items-center border-12 d-flex px-8 py-2 warm-status fz-14">
                  <i className="mi-person-fill mr-4"></i>
                  {agentsIncluded.value}
                  {' '}
                  usuarios incluidos con el plan
                </div>
              </div>
            )}

            {
              !disabledInput && (
                <div className="row mt-16">
                  <div className="align-items-center col-9 d-flex">
                    <img src={UserIcon} alt="Agrega un usuario" className="w-66 mr-8" />
                    <div className="contacts-included">
                      <p className="mt-9 fz-14">
                        Agrega otro usuario por $
                        {formatMoney((addon.price / periodicity / 100) || 0)} usd mensuales
                      </p>
                    </div>
                  </div>
                  <div className="col-3">
                    <div className="d-flex align-items-center">
                      <button
                        type="button"
                        onClick={handleDecrement}
                        className="btn fz-22 mx-0 p-0"
                      >
                        <i className="mi-circle-minus text-blue"></i>
                      </button>
                      <input
                        type="text"
                        className="bg-grey border-5 form-control fz-20 mx-8 p-6 no-border text-center"
                        value={selectedValue}
                        readOnly
                      />
                      <button
                        type="button"
                        onClick={handleIncrement}
                        className="btn fz-22 mx-0 p-0"
                      >
                        <i className="mi-circle-plus text-blue"></i>
                      </button>
                    </div>
                  </div>
                </div>
              )
            }
            {agentsIncluded && (
            <>
              <span className="mt-9 fz-14 ff-poppins-semibold">
                Total usuarios:
                {' '}
                {Number(agentsIncluded.value) + selectedValue}
              </span>
            </>
            )}

            {
              showSeatsAlert() && (
                <p className="p-10 mt-16 warm-status fz-14 border-12">
                  Actualmente tienes {activeTeamAgents} usuarios activos. Recuerda desactivar tus usuarios para actualizar tu plan
                </p>
              )
            }

          </div>
        </div>
      );
    default:
      return null;
  }
};

export default Addon;
