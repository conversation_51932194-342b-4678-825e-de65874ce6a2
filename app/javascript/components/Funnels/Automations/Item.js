import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from "react-router-dom";
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { find } from 'lodash';

import BoxMenu from './BoxMenu';

import EditIcon from 'images/edit.svg';
import TrashIcon from '../../icons/TrashIcon';
import ManageAutomation from './ManageAutomation';

import { removeItem } from "../../../services/RxJSBoardService";

const getBorderColor = (isDragging, item) =>
  isDragging ? item.bg_color : 'transparent';

const getBgColor = (item) => {
  if (item.kind == 'send_ws_message') {
    return '#00b9ae26';
  };
  return '#2a449426';
};

const Container = styled.div`
  border-radius: ${8}px;
  border: 2px solid transparent;
  border-color: ${(props) => getBorderColor(props.isDragging, props.item)};
  background-color: ${(props) => getBgColor(props.item)};
  box-shadow: ${({ isDragging }) => (isDragging ? `2px 2px 1px #A5ADBA` : 'none')};
  box-sizing: border-box;
  padding: 8px;
  min-height: 40px;
  display: flex;
  position: relative;
`;

// Previously this extended React.Component
// That was a good thing, because using React.PureComponent can hide
// issues with the selectors. However, moving it over does can considerable
// performance improvements when reordering big lists (400ms => 200ms)
// Need to be super sure we are not relying on PureComponent here for
// things we should be doing in the selector as we do not know if consumers
// will be using PureComponent

function Item(props) {
  const location = useLocation();
  const { t } = useTranslation("Funnels");
  const searchParams = new URLSearchParams(location.search);
  const funnelId = searchParams.get("funnel_id");
  const { columnInfo, columnId, item, isDragging, provided, isClone } = props;

  const { funnels } = useSelector((reduxState) => reduxState.funnelsReducer);

  const [showMenu, setShowMenu] = useState(false);
  const [openedManageAutomation, setOpenedManageAutomation] = useState(false);
  const [additionalInfo, setAdditionalInfo] = useState({});

  useEffect(() => {
    if (funnelId === item.web_id) {
      setOpenedManageAutomation(true);
    }

    buildAdditionalInfo();
  }, [item]);

  const buildAdditionalInfo = () => {
    let newInfo = {};

    switch (item.kind) {
      case 'send_ws_message':
        newInfo = {
          name: t('dealAutomation.strategy.firstStrategy'),
          icon: 'mi-ws-writing',
          icon_color: '#00B9AE',
          description: t('dealAutomation.strategy.description')
        };
        break;
      default:
        newInfo = {
          name: t('dealAutomation.strategy.secondStrategy'),
          icon: 'mi-bag-outline',
          icon_color: '#2A4494',
          description: t('dealAutomation.strategy.description')
        };
    };

    setAdditionalInfo(newInfo);
  };

  const handleOpenDelete = () => {
    const destroy = confirm(t('dealAutomation.deleteConfirmation'));
    if (destroy) {
      removeItem(item);
    };
  };

  const toggleModal = () => {
    setOpenedManageAutomation(!openedManageAutomation);
  };

  const getFrequencyText = () => {
    let text = '';

    if (item.sending_frequency == 'interval') {
      if (Number(item.time_interval) > 0) {
        if (item.time_interval == 1) {
          text = t('dealAutomation.timeFrequency.daily');
        } else {
          text = t('dealAutomation.timeFrequency.intervalDaily', { time_interval: item.time_interval, s: 's' });
        }
      } else {
        if (item.interval_day > 0) {
          text = t('dealAutomation.timeFrequency.intervalDaily', { time_interval: item.interval_day, s: item.interval_day > 1 ? 's' : '' });
          if (item.interval_hour && item.interval_hour != '00:00') {
            text += t('dealAutomation.timeFrequency.intervalDailyHour', { interval_hour: item.interval_hour });
          }
        } else {
          text = t('dealAutomation.timeFrequency.intervalHour', { interval_hour: item.interval_hour });
        }
      }
    } else {
      switch (item.time_interval_programmed) {
        case 'diary':
          text = t('dealAutomation.timeFrequency.daily');
          break;
        case 'once_a_week':
          text = t('dealAutomation.timeFrequency.weekly');
          break;
        case 'once_a_month':
          text = t('dealAutomation.timeFrequency.monthly');
          break;
      }

      if (item.interval_hour) {
        text += t('dealAutomation.timeFrequency.programmedIntervalHour', { interval_hour: item.interval_hour });
      }
    }

    return text;
  };

  const getMoveDealText = () => {
    let text = '';
    const funnel = find(funnels, { id: item.to_funnel_id });  
    if (funnel) {
      const step = find(funnel.funnel_steps, { id: item.to_funnel_step_id });

      text = `${funnel.name} -> ${step?.name || t('dealAutomation.deletedStep')}`;
    }
    return text;
  };

  return (
    <Container
      isDragging={isDragging}
      isClone={isClone}
      item={item}
      ref={provided.innerRef}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
    >
      <>
        <div className="w-100 p-relative" onClick={() => setShowMenu(false)}>
          <div className="d-flex mt-5">
            <i className={`${additionalInfo.icon} mr-8 fz-18`} style={{ color: additionalInfo.icon_color }} />
            <span className="fz-14 font-600 text-gray-dark">{additionalInfo.name}</span>
          </div>

          <div className="d-flex flex-column fz-14 text-gray-dark mt-10">
            <span className="mt-10">{getFrequencyText()}</span>
            <span className="mt-10">{additionalInfo.description}</span>
            {item.kind == 'move_deal_between_steps' && (<span className="mt-10 wrap-text">{getMoveDealText()}</span>)}
          </div>
          {showMenu && (
            <BoxMenu customStyles={{ top: '35px', right: '0px' }}>
              <div
                className="cursor-pointer px-18 py-8 border-8 tw-flex tw-items-center"
                onClick={() => setOpenedManageAutomation(true)}>
                <img src={EditIcon} width="12" height="14" />
                <span className="fs-14 ml-10">{t('dealAutomation.editTitle')}</span>
              </div>
              <div
                className="cursor-pointer px-18 py-8 border-8 tw-flex tw-items-center"
                onClick={handleOpenDelete}>
                <TrashIcon />
                <span className="fs-14 ml-10">{t('dealAutomation.deleteTitle')}</span>
              </div>
            </BoxMenu>
          )}
        </div>
        <button className="btn-card-setting p-absolute"
          onClick={() => setShowMenu(true)}
          onBlur={() => setTimeout(() => { setShowMenu(false) }, 250)}
          style={{ zIndex: 1, right: '8px' }}>
          <i className="mi-more-horizontal-outline" />
        </button>
      </>
      {openedManageAutomation && (
        <ManageAutomation
          selectedAutomation={item}
          columnInfo={columnInfo}
          toggleModal={toggleModal} />
      )}
    </Container>
  );
}

export default React.memo(Item);
