/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable no-param-reassign */
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Select from 'react-select';
import {
  cloneDeep, forEach, isEmpty, find
} from "lodash";
import DatePicker from "react-datepicker";
import moment from 'moment';
import validator from 'validator';

import ShowMoreText from 'react-show-more-text';
import EditAction from "./EditAction";

import { fetchCustomerFields, fetchNotes } from '../../../actions/actions';

import { DOCUMENT_TYPES } from "../../../constants/AppConstants";
import selectStyles from '../../../util/selectStyles';
import { customerFullName } from "../../../util/utils";
import { visiblePhoneNumber } from "../../../util/phoneNumber";
import { getCurrentRetailerUserInfo } from "../../../actions/retailerUsersActions";
import httpService from "../../../services/httpService";
import Tags from './Tags';
import Notes from './Notes';
import CustomerEvents from './CustomerEvents';
import CustomerAddresses from "./CustomerAddresses";
import { getCountryOptions, filterOption, customCountryFormatOptionLabel } from '../../../services/countryService';
import ConversationTopics from "./ConversationTopics";

const csrfToken = document.querySelector('[name=csrf-token]').content;
let loadCustomFields = false;
let isEditingEnabled = false;
const countryOptions = getCountryOptions();

const CustomerDetails = ({
  customerSelected,
  platformOptions,
  currentCustomerFbPost
}) => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getCurrentRetailerUserInfo());
  }, []);

  const { currentRetailerUser } = useSelector((state) => state.retailerUsersReducer);
  const {
    customer,
    customFields,
    customerFields
  } = useSelector((reduxState) => reduxState.mainReducer);

  const [viewMore, setViewMore] = useState(false);
  const [customerData, setCustomerData] = useState({});
  const [customFieldsGrouped, setCustomFieldsGrouped] = useState([]);
  const [edit, setEdit] = useState(false);
  const [loader, setLoader] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(false);
  const [sections, setSections] = useState({});

  const [errors, setErrors] = useState({ first_name: '', last_name: '' });

  useEffect(() => {
    const statusListener = (data) => {
      if (data.status === true && isEditingEnabled && data.customer_id === customerSelected.id) {
        dispatch(fetchCustomerFields(data.customer_id));
      }
    };

    if (socket) {
      socket.on('update_customer_chat_info', statusListener);
    }

    return () => {
      if (socket) {
        socket.off('update_customer_chat_info', statusListener);
      }
    };
  }, []);

  useEffect(() => {
    loadCustomFields = false;
    setEdit(false);
    setViewMore(false);
    setCustomerData({
      first_name: customerSelected.first_name || '',
      last_name: customerSelected.last_name || '',
      whatsapp_name: customerSelected.whatsapp_name || '',
      country_id: customerSelected.country_id || '',
      phone: customerSelected.phone || '',
      email: customerSelected.email || '',
      notes: customerSelected.notes || '',
      emoji_flag: customerSelected.emoji_flag
    });

    dispatch({ type: "SET_CUSTOMER_DEALS", data: [] });
    resetSections();
  }, [customerSelected]);

  useEffect(() => {
    if (!customer || isEmpty(customer)) return;
    setCustomerData({
      first_name: customer.first_name || '',
      last_name: customer.last_name || '',
      country_id: customer.country_id || '',
      phone: customer.phone || '',
      email: customer.email || '',
      notes: customer.notes || '',
      id_type: customer.id_type,
      id_number: customer.id_number || '',
      emoji_flag: customer.emoji_flag
    });
  }, [customer]);

  useEffect(() => {
    const clonedCustomFileds = cloneDeep(customFields);
    let customerFieldIn;
    forEach(clonedCustomFileds, (field) => {
      field.answer = field.field_type === 'boolean' ? false : '';
      field.id_saved = null;
      customerFieldIn = find(customerFields, { customer_related_field_id: field.id });

      if (customerFieldIn) {
        field.id_saved = customerFieldIn.id;
        switch (customerFieldIn.customer_related_field.field_type) {
          case 'boolean':
            field.answer = (customerFieldIn.data === 't');
            break;
          case 'date':
            if (customerFieldIn.data) {
              const newDate = customerFieldIn.data.split('/');
              field.answer = new Date(`${newDate[1]}/${newDate[2]}/${newDate[0]}`);
            } else {
              field.answer = '';
            }
            break;
          default:
            field.answer = customerFieldIn.data;
        }
      }

      if (field.field_type === 'list') {
        field.list_options = field.list_options.map((option) => ({ value: option.key, label: option.value }));
      }
    });

    setCustomFieldsGrouped(clonedCustomFileds);
  }, [customFields, customerFields]);

  const handleInputChange = (e) => {
    e.preventDefault();
    const input = e.target;
    const { name, value } = input;

    setCustomerData((prevState) => ({
      ...prevState,
      [name]: value
    }));
  };

  useEffect(() => {
    if (!sections.noteOpened) return;

    let chatType;

    switch (platformOptions.platform) {
      case 'whatsapp':
        chatType = 'whatsapp_chats';
        break;
      case 'facebook_comments':
        chatType = 'facebook_comments';
        break;
      case 'instagram_comments':
        chatType = 'instagram_comments';
        break;
      default:
        chatType = 'facebook_chats';
    }

    dispatch(fetchNotes(customer.id, chatType, currentCustomerFbPost));
  }, [sections.noteOpened]);

  const resetSections = () => {
    setSections({
      tagOpened: false,
      noteOpened: false,
      customerEventsOpened: false,
      addressesOpened: false,
      conversationTopicsOpened: false
    });
    setEditingCustomer(false);
  };

  const handleCustomerSection = () => {
    setEditingCustomer(!editingCustomer);
  };

  const handleAction = () => {
    if (!loadCustomFields) {
      loadCustomFields = true;
      dispatch(fetchCustomerFields(customerSelected.id));
    }

    if (edit) {
      saveData();
      handleCustomerSection();
    } else {
      isEditingEnabled = true;
      dispatch(fetchCustomerFields(customerSelected.id));
      setEdit(!edit);
      handleCustomerSection();
    }
  };

  const toggleViewMore = () => {
    if (!loadCustomFields) {
      loadCustomFields = true;
      dispatch(fetchCustomerFields(customerSelected.id));
    }

    setViewMore((prevState) => (!prevState));
  };

  const handleSelectForCustomer = ({ value }, { name }) => {
    setCustomerData((prevState) => ({
      ...prevState,
      [name]: value
    }));
  };

  const setValues = (fieldName, value) => {
    const clonedCustomFieldsGrouped = cloneDeep(customFieldsGrouped);
    const fieldIn = find(clonedCustomFieldsGrouped, { identifier: fieldName });

    if (fieldIn) {
      fieldIn.answer = value;
      setCustomFieldsGrouped(clonedCustomFieldsGrouped);
    }
  };

  const renderIconClass = (opened) => {
    const iconClass = opened ? 'fa-chevron-up' : 'fa-chevron-down';
    return iconClass;
  };

  const handleSections = (section) => {
    const newSections = cloneDeep(sections);
    Object.keys(newSections).forEach((sec) => {
      if (sec === `${section}Opened`) {
        newSections[sec] = !newSections[sec];
      } else {
        newSections[sec] = false;
      }
    });

    setSections(newSections);
  };

  const handleInputChangeCustomField = (e, field) => {
    const { target } = e;
    let value = target.type === 'checkbox' ? target.checked : target.value;
    const { name } = target;

    if (field.field_type === 'integer') {
      value = value.replace(/\D/g, '');
    }

    if (field.field_type === 'float') {
      const regex = /([0-9]*[\.|\,]{0,1}[0-9]{0,2})/s;
      [value] = value.match(regex);
    }

    setValues(name, value);
  };

  const onChangeDateCustomField = (date, field) => {
    setValues(field.identifier, date);
  };

  const handleCustomFieldSelect = (option, field) => {
    setValues(field.identifier, option.value);
  };

  const printCustomField = (field) => {
    switch (field.field_type) {
      case 'string':
      case 'integer':
      case 'float':
        return (
          <div className="figma-field mb-8">
            <label htmlFor={`custom-field-${field.identifier}`}>{field.name}</label>
            <input
              type="text"
              id={`custom-field-${field.identifier}`}
              name={field.identifier}
              className="mercately-input"
              value={field.answer}
              onChange={(e) => handleInputChangeCustomField(e, field)}
            />
          </div>
        );
      case 'date':
        return (
          <div className="figma-field mb-8">
            <label htmlFor={`custom-field-${field.identifier}`}>{field.name}</label>
            <DatePicker
              selected={field.answer}
              onChange={(date) => onChangeDateCustomField(date, field)}
              locale="es"
              className="custom-input"
              yearDropdownItemNumber={5}
              dateFormat="yyyy-MM-dd"
              showYearDropdown
              name={field.identifier}
            />
          </div>
        );
      case 'boolean': {
        return (
          <div className="mb-8">
            <label className="switch">
              <input
                type="checkbox"
                checked={field.answer}
                onChange={(e) => (handleInputChangeCustomField(e, field))}
                name={field.identifier}
              />
              <span className="slider round" />
            </label>
            <span className="text-gray-light fz-14">{field.name}</span>
          </div>
        );
      }
      case 'list':
        return (
          <div className="figma-field mb-8">
            <label htmlFor={`custom-field-${field.identifier}`}>{field.name}</label>
            <Select
              placeholder="Selecciona"
              className="select-tag"
              classNamePrefix="select-tag"
              styles={selectStyles}
              isSearchable
              options={field.list_options}
              value={[field.list_options.find((el) => el.value === field.answer)]}
              onChange={(option) => handleCustomFieldSelect(option, field)}
              components={{
                IndicatorSeparator: () => null
              }}
            />
          </div>
        );
      case 'default':
      default:
        return null;
    }
  };

  const isValidForm = () => {
    let isValid = true;
    const newErrors = { email: '' };

    if (customerData.email && !validator.isEmail(customerData.email)) {
      isValid = false;
      newErrors.email = 'Correo inválido';
    }
    if (customerData.phone && !customerData.country_id && !customer.ws_active) {
      isValid = false;
      newErrors.country_id = 'Seleccionar un país';
    }

    setErrors(newErrors);

    return isValid;
  };

  const saveData = () => {
    if (!isValidForm()) return;

    setLoader(true);
    resetSections();
    const formData = cloneDeep(customerData);
    formData.customer_related_data_attributes = [];
    forEach(customFieldsGrouped, (field) => {
      const customer_related_data = {
        customer_related_field_id: field.id
      };

      switch (field.field_type) {
        case 'date':
          customer_related_data.data = field.answer ? moment(field.answer).format("YYYY-MM-DD") : '';
          break;
        default:
          customer_related_data.data = field.answer;
      }

      if (field.id_saved) customer_related_data.id = field.id_saved;

      formData.customer_related_data_attributes.push(customer_related_data);
    });

    updateCustomer(formData);
  };
  const updateCustomer = async (params) => {
    const endPoint = `/api/v1/customers/${customer.id}`;

    httpService.put(endPoint, params, csrfToken)
      .then((res) => {
        const {
          first_name,
          last_name,
          phone,
          country_id,
          emoji_flag
        } = res?.customer || {};

        const newCustomerData = {
          first_name,
          last_name,
          phone,
          country_id,
          emoji_flag
        };

        const data = { customer: newCustomerData };

        setCustomerData((prevState) => ({
          ...prevState,
          ...newCustomerData
        }));

        dispatch({ type: 'SET_CUSTOMER_UPDATED_FOR_CHATS', data });
        setEdit(false);
        setLoader(false);
        isEditingEnabled = false;
      })
      .catch((err) => {
        const messages_values = Object.values(err.message)
          .flat()
          .join(' ');
        if (messages_values.includes("Customer related field ya está en uso")) {
          showtoast("Algo salió mal, por favor recarga la página e intenta de nuevo");
        } else {
          showtoast(messages_values);
        }

        setLoader(false);
      });
  };

  const formatAnswer = (field) => {
    let answer = '';

    switch (field.field_type) {
      case 'boolean':
        answer = field.answer ? 'Si' : 'No';
        break;
      case 'date':
        if (field.answer) {
          answer = moment(field.answer).format("YYYY-MM-DD");
        }
        break;
      default:
        answer = field.answer;
    }

    return answer;
  };

  const iconClass = viewMore ? 'fa-chevron-up' : 'fa-chevron-down';

  return (
    <div>
      <div className="mt-5 d-flex justify-content-between">
        <label className="fz-14 fw-bold ff-semi-bold">Detalles del cliente</label>
        <EditAction toggleAction={handleAction} edit={edit} />
      </div>
      {loader && <div className="text-center mt-10"><i className="fa fa-spinner fa-spin" /></div>}
      {!edit && (
        <>
          <div>
            <span className="mr-10 text-blue fz-12">Nombres:</span>
            <span className="cursor-pointer text-gray-light fz-14">
              {customerFullName(customerData, currentRetailerUser)}
            </span>
          </div>

          <div>
            <span className="mr-10 text-blue fz-12">Teléfono:</span>
            <span className="cursor-pointer text-gray-light fz-14">
              {customerData.emoji_flag}
              {' '}
              {visiblePhoneNumber(customerData.phone, currentRetailerUser)}
            </span>
          </div>

          <div>
            <span className="mr-10 text-blue fz-12">Email:</span>
            <span className="cursor-pointer text-gray-light fz-14 text-break">{customerData.email}</span>
          </div>

          <div>
            <span className="mr-10 text-blue fz-12">Notas:</span>
            <span className="cursor-pointer text-gray-light fz-14 text-break pre-wrap">
              <ShowMoreText
                more="Ver más"
                less="Ver menos"
                expanded={false}
              >
                {customerData.notes}
              </ShowMoreText>
            </span>
          </div>

          {viewMore && (
            <>
              <div>
                <span className="mr-10 text-blue fz-12">Identificación:</span>
                <span className="cursor-pointer text-gray-light fz-14 text-break">{customerData.id_number}</span>
              </div>

              {customFieldsGrouped.map((field, index) => (
                <div key={`customer-custom-field-view-${index}`}>
                  <span className="mr-10 text-blue fz-12">
                    {field.name}
                    :
                  </span>
                  <span className="cursor-pointer text-gray-light fz-14 text-break">{formatAnswer(field)}</span>
                </div>
              ))}
            </>
          )}

          <div className="text-center mt-15">
            <span className="cursor-pointer fz-14 text-blue" onClick={toggleViewMore}>
              Ver
              {' '}
              {viewMore ? 'menos' : 'mas'}
              {' '}
              {<i className={`fa ${iconClass}`} aria-hidden="true"></i>}
            </span>
          </div>
        </>
      )}

      {edit && (
        <>
          <div className="mt-10">
            <div className="figma-field mb-8">
              <label htmlFor="first_name">Nombres</label>
              <span className="ml-10 error-msg fz-12">{errors.first_name}</span>
              <input
                id="first_name"
                name="first_name"
                className="mercately-input"
                value={customerData.first_name}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div>
            <div className="figma-field mb-8">
              <label htmlFor="last_name">Apellidos</label>
              <span className="ml-10 error-msg fz-12">{errors.last_name}</span>
              <input
                id="last_name"
                name="last_name"
                className="mercately-input"
                value={customerData.last_name}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {customer && !customer.ws_active && (
            <>
              <div>
                <div className="figma-field mb-8">
                  <label htmlFor="country">País</label>
                  <span className="ml-10 error-msg fz-12">{errors.country_id}</span>
                  <Select
                    filterOption={filterOption}
                    options={countryOptions}
                    formatOptionLabel={customCountryFormatOptionLabel}
                    placeholder=""
                    className="select-tag"
                    classNamePrefix="select-tag"
                    styles={selectStyles}
                    onChange={handleSelectForCustomer}
                    value={[countryOptions.find((el) => el.value === customerData.country_id)]}
                    name="country_id"
                    components={{
                      IndicatorSeparator: () => null
                    }}
                  />
                </div>
              </div>
              <div>
                <div className="figma-field mb-8">
                  <label htmlFor="phone">Teléfono</label>
                  <span className="ml-10 error-msg fz-12">{errors.phone}</span>
                  <input
                    id="phone"
                    name="phone"
                    className="mercately-input"
                    value={visiblePhoneNumber(customerData.phone, currentRetailerUser)}
                    disabled={currentRetailerUser.agent && !currentRetailerUser.see_phone_numbers}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </>
          )}

          <div>
            <div className="figma-field mb-8">
              <label htmlFor="email">Correo eléctronico</label>
              <span className="ml-10 error-msg fz-12">{errors.email}</span>
              <input
                id="email"
                name="email"
                className="mercately-input"
                value={customerData.email}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div>
            <div className="figma-field mb-8">
              <label htmlFor="id_type">Tipo documento</label>
              <Select
                options={DOCUMENT_TYPES}
                placeholder="Selecciona"
                className="select-tag"
                classNamePrefix="select-tag"
                styles={selectStyles}
                onChange={handleSelectForCustomer}
                name="id_type"
                value={[DOCUMENT_TYPES.find((el) => el.value === customerData.id_type)]}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </div>
          </div>

          <div>
            <div className="figma-field mb-8">
              <label htmlFor="id_number">Identificación</label>
              <input
                id="id_number"
                name="id_number"
                className="mercately-input"
                value={customerData.id_number}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div>
            <div className="figma-field mb-8">
              <label htmlFor="email">Notas</label>
              <span className="ml-10 error-msg fz-12">{errors.notes}</span>
              <textarea
                id="notes"
                name="notes"
                className="mercately-input"
                value={customerData.notes}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {customFieldsGrouped.map((field, index) => (
            <div key={`customer-cunstom-field-${index}`}>
              {printCustomField(field, index)}
            </div>
          ))}

          <div>
            <button type="button" onClick={saveData} className="blue-button-chat text-decoration-none fz-14 w-100 bg-light-cian">
              <span className="text-blue fz-14 bold-font">Guardar</span>
            </button>
          </div>
        </>
      )}
      {!editingCustomer && (
      <>
        <div className="divider-line p-relative mt-10"></div>

        <Tags handleSections={handleSections} />

        <div className="divider-line p-relative"></div>

        <ConversationTopics
          customerId={customerSelected?.id}
          sections={sections}
          handleSections={handleSections}
          renderIconClass={renderIconClass}
          channel={platformOptions.platform}
        />

        <div className="divider-line p-relative"></div>

        <CustomerAddresses
          customer={customerSelected}
          sections={sections}
          handleSections={handleSections}
          renderIconClass={renderIconClass}
        />

        <div className="divider-line p-relative"></div>

        <div className="mt-5 d-flex justify-content-between cursor-pointer mt-10" onClick={() => handleSections('note')}>
          <label className="fz-14 fw-bold ff-semi-bold">Notas</label>
          <i className={`fa ${renderIconClass(sections.noteOpened)} fz-12`} aria-hidden="true"></i>
        </div>

        {sections.noteOpened && (
        <Notes handleSections={handleSections} platformOptions={platformOptions} />
        )}

        <div className="divider-line p-relative"></div>

        <div className="mt-5 d-flex justify-content-between cursor-pointer mt-10" onClick={() => handleSections('customerEvents')}>
          <label className="fz-14 fw-bold ff-semi-bold">Actividades</label>
          <i className={`fa ${renderIconClass(sections.customerEventsOpened)} fz-12`} aria-hidden="true"></i>
        </div>

        {sections.customerEventsOpened && (
          <CustomerEvents platformOptions={platformOptions} />
        )}
      </>
      )}
    </div>
  );
};

export default CustomerDetails;
