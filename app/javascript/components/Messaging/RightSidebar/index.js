import React, { useEffect } from "react";

import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from 'react-i18next';
import CustomerDetails from "./CustomerDetails";
import Orders from "./Orders";
import Integrations from "./Integrations";
import Deals from "./Deals";
import CustomerCard from "./CustomerCard";
import TabBar from "./TabBar";
import ScheduledMessagesMC from "../../../presentation/components/ScheduledMessagesMC";
import { fetchCustomerDealsCount } from '../../../actions/funnels';

const RightSidebar = ({
  customerSelected,
  platformOptions,
  isDealModalOpen,
  setIsDealModalOpen,
  isOrderModalOpen,
  setIsOrderModalOpen,
  currentCustomerFbPost,
  showDeals = true,
  showOrders = true,
  customClass = ''
}) => {
  const {
    customer,
    customerDealsCount,
    ordersCount
  } = useSelector((reduxState) => reduxState.mainReducer);
  const { currentRetailerUser } = useSelector((reduxState) => reduxState.retailerUsersReducer);
  const dispatch = useDispatch();
  const { t } = useTranslation('Inbox');

  useEffect(() => {
    dispatch(fetchCustomerDealsCount(customerSelected.id));
  },[customerSelected]);

  return (
    !customerSelected.is_group && (
      <div className={`chat-details-container gray-border-left ${customClass}`}>
        <CustomerCard
          customer={customer}
          currentRetailerUser={currentRetailerUser}
          dealsNumber={customerDealsCount}
          ordersNumber={ordersCount}
        />
        <TabBar
          tabs={[
            {
              tooltip: t('rightSideBar.title.customers'),
              header: <i className="mi-people-outline fs-24" />,
              panel: (
                <CustomerDetails
                  customerSelected={customerSelected}
                  currentCustomerFbPost={currentCustomerFbPost}
                  platformOptions={platformOptions}
                />
              )
            },
            {
              tooltip: t('rightSideBar.title.orders'),
              disabled: !showOrders,
              header: <i className="mi-shopping-bag-outline fs-24" />,
              panel: (
                <Orders
                  isOrderModalOpen={isOrderModalOpen}
                  setIsOrderModalOpen={setIsOrderModalOpen}
                  customer={customer}
                  platform={platformOptions.platform}
                />
              ),
              panelProps: { mountOnEnter: true, unmountOnExit: true }
            },
            {
              tooltip: t('rightSideBar.title.funnel'),
              disabled: !showDeals || !['whatsapp', 'messenger', 'instagram'].includes(platformOptions.platform),
              header: <i className="mi-funnel-outline fs-24" />,
              panel: (
                <Deals
                  platformOptions={platformOptions}
                  isDealModalOpen={isDealModalOpen}
                  setIsDealModalOpen={setIsDealModalOpen}
                />
              )
            },
            {
              tooltip: t('rightSideBar.title.automations'),
              disabled: platformOptions.platform !== "whatsapp",
              header: <i className="mi-bot fs-24" />,
              panel: <ScheduledMessagesMC platformOptions={platformOptions} />
            },
            {
              tooltip: t('rightSideBar.title.integrations'),
              header: <i className="mi-stats fs-24" />,
              panel: <Integrations platformOptions={platformOptions} />
            }
          ]}
        />
      </div>
    )
  );
};

export default RightSidebar;
