import {
  ERASE_DEAL,
  LOAD_DATA_FAILURE,
  ADD_DEALS_TO_COLUMN
} from "../actionTypes";
import FunnelModel from '../components/Funnels/FunnelModel';

import { getJwtCookie } from "../util/jwtCookie";
import CustomerModel from "../components/Customers/CustomerModel";
import { serializeParams } from "../util/urlUtils";

/* Customers */
export const fetchFunnelSteps = (params, webId) => {
  let endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/funnels/${webId}?`;
  let funnelFilter = '';

  if (params) {
    const queryString = serializeParams(params);
    funnelFilter += queryString;
    endpoint += queryString;
  }

  return async (dispatch) => {
    dispatch({ type: 'GET_FUNNELS_START' });
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "GET",
      credentials: "same-origin",
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: "GET_FUNNELS", data });
          dispatch({ type: 'INCREMENT_DATA_VERSION', data })
          dispatch({ type: "SET_FUNNEL_FILTERS", funnelFilter });
        },
        (err) => {
          dispatch({ type: "LOAD_DATA_FAILURE", err });
        }
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
      }).finally(() => {
        dispatch({ type: 'END_FETCHING_FUNNELS' });
        dispatch({ type: 'GET_FUNNELS_END' });
      });
  };
};

export const fetchExportedFunnels = (page = 1) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/exported_funnels?page=${page}`;

  return async (dispatch) => {
    dispatch({ type: 'GET_EXPORTED_FUNNELS_START' });
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "GET",
      credentials: "same-origin",
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: "GET_EXPORTED_FUNNELS", data });
          dispatch({ type: 'GET_EXPORTED_FUNNELS_END' });
        },
        (err) => {
          dispatch({ type: "LOAD_DATA_FAILURE", err });
          dispatch({ type: 'GET_EXPORTED_FUNNELS_END' });
        }
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
        dispatch({ type: 'GET_EXPORTED_FUNNELS_END' });
      });
  };
};

export const exportFunnel = (params, all = false) => {
  let endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/funnels/export_funnel?all_funnels=${all}`;

  if (params && typeof params === 'object' && !all) {
    const queryString = serializeParams(params);
    endpoint += `&${queryString}`;
  }

  return async (dispatch) => {
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "POST",
      credentials: 'same-origin',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*'
      }
    })
      .then((res) => res.json())
      .then(
        () => showtoast('Recibirás un correo cuando la exportación esté lista, si no lo recibes, podrás encontrar un link en la pestaña de exportaciones. El tiempo de espera puede variar, desde pocos minutos hasta varias horas, dependiendo del tamaño.'),
        (err) => dispatch({ type: 'LOAD_DATA_FAILURE', err })
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
      });
  };
};

export const updateFunnelStepDeal = (body) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/deals/update_deal`;
  return async (dispatch) => {
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "POST",
      credentials: 'same-origin',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*'
      },
      body: JSON.stringify({ deal: body })
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: 'CHANGE_DEAL_COLUMN', data })
          dispatch({ type: 'INCREMENT_DATA_VERSION', data })
        },
        (err) => dispatch({ type: 'LOAD_DATA_FAILURE', err })
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
      });
  };
};

export const updateFunnelStep = (body) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/funnels/${body.funnelId}/funnel_steps/update_funnel_step`;
  return async (dispatch) => {
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "POST",
      credentials: 'same-origin',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*'
      },
      body: JSON.stringify({ funnel_step: body })
    })
      .then((res) => res.json())
      .then(
        (data) => dispatch({ type: 'SET_COLUMNS', columns: body }),
        (err) => dispatch({ type: 'LOAD_DATA_FAILURE', err })
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
      });
  };
};

export const createNewDeal = (body, column) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/deals/create_deal`;
  return async (dispatch) => {
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "POST",
      credentials: 'same-origin',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*'
      },
      body: JSON.stringify({ deal: body })
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: 'SET_NEW_DEAL', data, column })
          dispatch({ type: 'INCREMENT_DATA_VERSION', data })
        },
        (err) => dispatch({ type: 'LOAD_DATA_FAILURE', err })
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
      });
  };
};

export const createNewCustomerAndDeal = (body, column, el) => async (dispatch) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/deals/create_deal`;
  const token = await getJwtCookie();
  CustomerModel.createFromRetailer({ customer: body.customer })
    .then((customerResponse) => {
      body.deal.customer_id = customerResponse.customer.id;

      fetch(endpoint, {
        method: "POST",
        credentials: 'same-origin',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*'
        },
        body: JSON.stringify(body)
      })
        .then((res) => res.json())
        .then(
          (data) => {
            dispatch({ type: 'SET_NEW_DEAL', data, column })
            dispatch({ type: 'INCREMENT_DATA_VERSION', data })
          },
          (err) => dispatch({ type: 'LOAD_DATA_FAILURE', err })
        ).catch((error) => {
          if (error.response) {
            alert(error.response);
          } else {
            alert("An unexpected error occurred.");
          }
        });
    })
    .catch((error) => {
      if (error.message) {
        alert(error.message);
        el.disabled = false;
      } else {
        alert("An unexpected error occurred.");
        el.disabled = false;
      }
    });
};

export const deleteStep = (column, funnel) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/funnels/${funnel.web_id}/funnel_steps/${column}`;
  return async (dispatch) => {
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "DELETE",
      credentials: "same-origin",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "X-Access-Level": "read-write"
      }
    })
      .then((res) => res.json())
      .then((data) => {
        dispatch({ type: "ERASE_DEAL_STEP", data, column });
        dispatch({ type: 'INCREMENT_DATA_VERSION', data })
      })
      .catch((err) => {
        dispatch({ type: "LOAD_DATA_FAILURE", err });
      });
  };
};

export const clearFunnels = () => {
  const data = {};
  return (dispatch) => dispatch({ type: "CLEAR_FUNNELS", data });
};

export const clearNewDeal = () => {
  const data = {};
  return (dispatch) => dispatch({ type: "CLEAR_NEW_DEAL", data });
};

export const clearNewStep = () => {
  const data = {};
  return (dispatch) => dispatch({ type: "CLEAR_NEW_STEP", data });
};

export const deleteDeal = (dealId, column) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/deals/${dealId}`;
  return async (dispatch) => {
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "DELETE",
      credentials: "same-origin",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "X-Access-Level": "read-write"
      }
    })
      .then((res) => res.json())
      .then((data) => {
        dispatch({
          type: "ERASE_DEAL", data, dealId, column
        });
        dispatch({ type: 'INCREMENT_DATA_VERSION', data })
      })
      .catch((err) => {
        dispatch({ type: "LOAD_DATA_FAILURE", err });
      });
  };
};

export const loadMoreDeals = (column, page, offset = 0, params) => {
  let endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/deals?page=${page}&column_id=${column}&offset=${offset}`;

  if (params) {
    const queryString = serializeParams(params);
    endpoint += `&${queryString}`;
  }

  return async (dispatch) => {
    dispatch({ type: 'START_FETCHING_FUNNELS' });
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "GET",
      credentials: "same-origin",
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => dispatch({ type: ADD_DEALS_TO_COLUMN, data, column }),
        (err) => dispatch({ type: "LOAD_DATA_FAILURE", err })
      ).catch((err) => {
        dispatch({ type: LOAD_DATA_FAILURE, err });
      }).finally(() => {
        dispatch({ type: 'END_FETCHING_FUNNELS' });
        dispatch({ type: 'GET_FUNNELS_END' });
      });
  };
};

export const fetchCustomerDeals = (customerId) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/deals/customer_deals/${customerId}`;
  return async (dispatch) => {
    dispatch({ type: "START_SET_CUSTOMER_DEALS" });
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "GET",
      credentials: "same-origin",
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: "END_SET_CUSTOMER_DEALS" });
          dispatch({ type: "SET_CUSTOMER_DEALS", data });
        },
        (err) => {
          dispatch({ type: "LOAD_DATA_FAILURE", err });
          dispatch({ type: "END_SET_CUSTOMER_DEALS" });
        }
      ).catch(
        (err) => {
          dispatch({ type: "LOAD_DATA_FAILURE", err });
          dispatch({ type: "END_SET_CUSTOMER_DEALS" });
        }
      );
  };
};

export const fetchCustomerDealsCount = (customerId) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/deals/customer_deals_count/${customerId}`;
  return async (dispatch) => {
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "GET",
      credentials: "same-origin",
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: "SET_CUSTOMER_DEALS_COUNT", data: data.customer_deals_count });
        }
      ).catch((error) => {
        dispatch({ type: "LOAD_DATA_FAILURE", error });
      });
  };
};

export const deleteSimpleDeal = (dealId) => {
  const endpoint = `${ENV.DEAL_ENGINE_BASE_URL}/deals/${dealId}`;
  return async (dispatch) => {
    const token = await getJwtCookie();
    fetch(endpoint, {
      method: "DELETE",
      credentials: "same-origin",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "X-Access-Level": "read-write"
      }
    })
      .then((res) => res.json())
      .then((data) => {
        dispatch({ type: "ERASE_SIMPLE_DEAL", data });
      })
      .catch((err) => {
        dispatch({ type: LOAD_DATA_FAILURE, err });
      });
  };
};

export const fetchOnlyFunnelSteps = () => {
  const endpoint = `/api/v1/funnels/only_steps`;

  return (dispatch) => {
    fetch(endpoint, {
      method: "GET",
      credentials: "same-origin",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => dispatch({ type: "GET_ONLY_FUNNELS_STEPS", data }),
        (err) => dispatch({ type: "LOAD_DATA_FAILURE", err })
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
      });
  };
};

export const getFunnels = (params) => (dispatch) => {
  FunnelModel.get(params)
    .then((data) => dispatch({ type: 'SET_FUNNELS', data }))
    .catch((err) => {
      dispatch({ type: "LOAD_DATA_FAILURE", err });
    });
};
