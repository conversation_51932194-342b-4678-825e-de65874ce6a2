module Stats
  class ChatsByAgentQuery
    def initialize(retailer, start_date, end_date, platform = nil)
      @retailer = retailer
      @start_date = start_date
      @end_date = end_date
      @platform = platform
    end

    def call
      custom_where = case @platform
                     when '0'
                       ' AND customers.ws_active = TRUE'
                     when '1'
                       ' AND customers.pstype = 0'
                     when '2'
                       ' AND customers.pstype = 1'
                     else
                       ''
                     end

      status_in_process_expr, status_resolved_expr = case @platform
      when '1'
        ["status_chat_messenger <= 2", "status_chat_messenger = 3"]
      when '2'
        ["status_chat_instagram <= 2", "status_chat_instagram = 3"]
      when '0'
        ["status_chat <= 2", "status_chat = 3"]
      else
        [
          "((status_chat <= 2 AND customers.ws_active = TRUE) OR (status_chat_messenger <= 2 AND customers.pstype = 0) OR (status_chat_instagram <= 2 AND customers.pstype = 1))",
          "((status_chat = 3 AND customers.ws_active = TRUE) OR (status_chat_messenger = 3 AND customers.pstype = 0) OR (status_chat_instagram = 3 AND customers.pstype = 1))"
        ]
      end

      sql_agent_chats = <<-SQL
        SELECT users.first_name, users.last_name, tbl_result_1.*,
              (tbl_result_1.amount_chat_in_process + tbl_result_1.amount_chat_resolved) total_chats
        FROM (
          SELECT agent_customers.retailer_user_id,
                 SUM(CASE WHEN #{status_in_process_expr} THEN 1 ELSE 0 END) amount_chat_in_process,
                 SUM(CASE WHEN #{status_resolved_expr} THEN 1 ELSE 0 END) amount_chat_resolved
          FROM agent_customers
          INNER JOIN retailer_users ON retailer_users.id = agent_customers.retailer_user_id
          INNER JOIN customers ON customers.id = agent_customers.customer_id
          WHERE retailer_users.retailer_id = :retailer_id
          AND (customers.last_chat_interaction BETWEEN :start_date AND :end_date OR
               customers.last_msn_interaction BETWEEN :start_date AND :end_date OR
               customers.last_ig_interaction BETWEEN :start_date AND :end_date)
          #{custom_where}
          GROUP BY agent_customers.retailer_user_id
          ) tbl_result_1
        INNER JOIN retailer_users ON retailer_users.id = tbl_result_1.retailer_user_id
        INNER JOIN users ON users.id = retailer_users.user_id
      SQL

      ActiveRecord::Base.connection.exec_query(
        ApplicationRecord.sanitize_sql([
          sql_agent_chats, {
            retailer_id: @retailer.id,
            start_date: @start_date,
            end_date: @end_date
          }
        ])
      )
    end
  end
end
