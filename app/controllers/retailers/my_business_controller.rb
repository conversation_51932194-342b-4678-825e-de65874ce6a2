class Retailers::MyBusinessController < RetailersController
  include MyBusinessConcern
  include JwtCookie

  layout 'chats/chat'

  before_action :set_full_week_schedule, only: [:schedules]

  def edit
  end

  def update_general_information
    old_slug = current_retailer.slug

    if current_retailer.update(retailer_params)
      set_jwt_cookie(current_user)
      redirect_page(old_slug)
    else
      redirect_back fallback_location: edit_business_info_path(current_retailer.slug),
                    notice: current_retailer.errors.full_messages
    end
  end

  def locations
    @addresses = current_retailer.retailer_addresses
    @new_address = RetailerAddress.new
  end

  def create_address
    @address = current_retailer.retailer_addresses.new(retailer_address_params)
    if @address.save
      redirect_to business_locations_path(current_retailer), notice: t('retailer.profile.my_business.edit.success')
    else
      flash[:notice] = current_retailer.errors.full_messages
      render action: :locations, notice: @address.errors.full_messages
    end
  end

  def update_address
    if @address.update(retailer_address_params)
      redirect_to business_locations_path(current_retailer), notice: t('retailer.profile.my_business.edit.success')
    else
      flash[:notice] = @address.errors.full_messages
      render action: :locations, notice: @address.errors.full_messages
    end
  end

  def destroy_address
    if @address.destroy
      redirect_to business_locations_path(current_retailer), notice: t('retailer.profile.my_business.delete.success')
    else
      flash[:notice] = @address.errors.full_messages
      render action: :locations, notice: @address.errors.full_messages
    end
  end

  def schedules
  end

  def shipping
  end

  def redirect_page(old_slug)
    notice = t('retailer.profile.my_business.edit.success')
    if old_slug == current_retailer.slug
      redirect_back fallback_location: retailers_my_account_path, notice: notice
    else
      redirect_to edit_business_info_path(current_retailer.slug), notice: notice
    end
  end

  def domain
  end

  def set_domain
    render action: :domain, notice: 'Debe llenar todos los campos' && return unless valid_domain_params?

    if Retailer::DomainSetter.call(
      retailer: current_retailer,
      domain: domain_params[:domain],
      certificate: domain_params[:certificate_file],
      certificate_key: domain_params[:key_file]
    )
      redirect_to business_domain_path(current_retailer), notice: t('retailer.profile.my_business.edit.domain.success')
    else
      flash[:notice] = current_retailer.errors.full_messages
      render action: :domain, notice: current_retailer.errors.full_messages
    end
  end

  private

    def set_full_week_schedule
      @exists = false
      @schedules = current_retailer.retailer_schedules
      # Si no existen horarios para el retailer, se crean horarios por defecto
      if @schedules.empty?
        # Se crea horario para los 7 días de la semana con el horaio de 8 a 17 por defecto
        7.times do |n|
          @schedules.new(weekday: n, active: false, opening_time: '8', closing_time: '17')
        end
      else
        @exists = true
      end
      @schedules
    end
end
