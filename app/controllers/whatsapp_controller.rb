class WhatsappController < ApplicationController
  protect_from_forgery with: :null_session, only: :save_message
  respond_to :json

  def receive_whatsapp_api
    return render_ok if message_params.try(:[], :payload).try(:[], :type) == 'sandbox-start'
    return render_ok unless message_params.try(:[], :payload).try(:[], :type).present? ||
                            message_params[:type] == 'billing-event'

    event = message_params[:payload][:type]

    # Get the retailer by its gupshup_src_name, which is the gupshup app name
    retailer = Retailer.find_by(gupshup_src_name: message_params[:app])

    unless retailer
      message = { message: "Whatsapp app not found: #{message_params}" }
      handle_send_error(message)
      return render_ok
    end

    if event == 'failed'
      handle_send_error(message_params[:payload])
      Whatsapp::Utils::FailedMessageProcessor.call(params: message_params)
      return render_ok
    end

    event_handler = Whatsapp::Inbound::Event.new(retailer, nil, 'gupshup')
    event_handler.process_event!(message_params)

    render_ok
  rescue StandardError => e
    Rails.logger.error(e)
    Rails.logger.info(e.backtrace.slice(0, 10))
  end

  def receive_whatsapp_qr
    return render status: :ok, json: '' if params.blank? || params[:whatsapp].blank? ||
                                           message_params[:message].try(:[], :type) == 'poll'

    # Se busca el retailer por su qr_phone_id, que es el phone_id con qr
    retailer = Retailer.find_by(qr_phone_id: message_params[:phone_id])

    unless retailer
      message = { message: "Phone id not found: #{message_params}" }
      handle_send_error(message)
      return render status: :not_found, json: message
    end

    whatsapp_params = merge_message_params(message_params, params)

    event_handler = Whatsapp::Inbound::Event.new(retailer, nil, 'qr')
    event_handler.process_event!(whatsapp_params)

    render_ok
  end

  def receive_whatsapp_api_code
    Whatsapp::Integration::Configure.call(params)
    redirect_to root_path, notice: 'Integración exitosa' # rubocop:disable Rails/I18nLocaleTexts
  rescue StandardError => e
    Rails.logger.error(e)
    redirect_to root_path, notice: 'Integración fallida' # rubocop:disable Rails/I18nLocaleTexts
  end

  private

    def message_params
      params.require(:whatsapp)
        .permit(
          :app,
          :timestamp,
          :version,
          :type,
          :direction,
          :product_id,
          :phone_id,
          :msgId,
          :chatId,
          :status,
          :conversation,
          :conversation_name,
          :receiver,
          :code,
          participants: [:id, :name, :phone],
          payload: {},
          message: {},
          user: {},
          quoted: {},
          data: [{}],
          referral: {}
        )
    end

    def merge_message_params(whatsapp_params, params)
      if params[:type] != 'message'
        whatsapp_params.merge!(
          message: params[:message],
          data: params[:data]
        )
      end

      whatsapp_params
    end

    def handle_send_error(response_message)
      Rails.logger.error(response_message)
      SlackError.send_error(response_message, '', true)
    end

    def render_ok
      render status: :ok, json: ''
    end
end
