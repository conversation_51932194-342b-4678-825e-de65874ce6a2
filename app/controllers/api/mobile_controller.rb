module Api
  class MobileController < Retailers::Api::V1::ApiController
    def check_access
      disable_access_by_authorization
    end

    private

      def disable_access_by_authorization
        validate_headers
        find_retailer_user

        return record_not_found unless @current_retailer_user
        return render_unauthorized unless @current_retailer_user.api_session_token == request.get_header('HTTP_TOKEN')
        return response_generate_token unless @current_retailer_user.api_session_expiration > Time.now

        @current_retailer = @current_retailer_user.retailer
      rescue StandardError => e
        Rails.logger.error e
        SlackError.send_error(e)
      end

      def response_generate_token
        token = @current_retailer_user.generate_api_token!

        response.headers['Authorization'] = token
        response.headers['Device'] = @current_retailer_user.api_session_device
        set_response(
          401,
          'Token Expirado, se ha generado uno nuevo',
          {
            token: token
          }.to_json
        ) and return
      end

      def validate_headers
        return render_forbidden if request.get_header('HTTP_EMAIL').blank?
        return render_forbidden if request.get_header('HTTP_DEVICE').blank?

        render_unauthorized if request.get_header('HTTP_TOKEN').blank?
      end

      def find_retailer_user
        @current_retailer_user = User.find_by(
          email: request.get_header('HTTP_EMAIL'),
          api_session_device: request.get_header('HTTP_DEVICE')
        )&.current_mobile_retailer_user
      end
  end
end
