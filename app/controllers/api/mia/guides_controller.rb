# frozen_string_literal: true

class Api::Mia::GuidesController < Api::ApiController
  include CurrentRetailer

  before_action :mia_integrated_with_guides

  def index
    result = guide_client.list
    render status: result.status, json: result.body
  end

  def show
    result = guide_client.retrieve(params[:id])
    render status: result.status, json: result.body
  end

  def create
    result = guide_client.create(create_guide_params)
    render status: result.status, json: result.body
  end

  def update
    result = guide_client.update(params[:id], guide_params)
    render status: result.status, json: result.body
  end

  def destroy
    result = guide_client.delete(params[:id])
    render status: result.status, json: result.body
  end

  def optimize
    result = guide_client.optimize(optimize_guide_params)
    render status: result.status, json: result.body
  rescue Net::ReadTimeout
    render status: :internal_server_error, json: { message: 'Something went wrong' }
  end

  def scrape
    result = guide_client.scrape(scrape_params)
    render status: result.status, json: result.body
  end

  private

    def guide_params
      params.permit(
        :id,
        :name,
        :guide_type,
        :qa_pairs,
        :instructions,
        :specific_instructions,
        :topics,
        :files,
        :url,
        :urls,
        urls_id: [],
        topics: [],
        files: [],
        urls: [],
        qa_pairs: [:question, :answer, :instruction_type, { files: [] }],
        specific_instructions: [:instruction, :instruction_id, :instruction_type, { files: [] }]
      )
    end

    def optimize_guide_params
      params.permit(:instructions)
    end

    def scrape_params
      params.permit(:url)
    end

    def create_guide_params
      guide_params.merge(
        created_by: {
          first_name: current_retailer_user.first_name,
          last_name: current_retailer_user.last_name,
          full_name: current_retailer_user.full_name,
          user_id: current_retailer_user.id
        }.to_json
      )
    end

    def mia_integrated_with_guides
      return if current_retailer.mia_integrated?('guides')

      render status: :internal_server_error, json: 'El servicio no esta integrado'
    end

    def guide_client
      @guide_client ||= MercatelyMiaApi::Guide.new(
        api_key: mia_integration.openai_key,
        secret_key: mia_integration.public_key,
        base_url: mia_integration.service_url,
        retailer_id: mia_integration.retailer_id
      )
    end

    def mia_integration
      @mia_integration ||= current_retailer.mia_integration('guides')
    end
end
