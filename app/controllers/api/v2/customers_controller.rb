# Api::V2::CustomersController handles customer-related actions such as listing customers,
# exporting customer data, and providing a paginated list of exported customer records.
#
# Inherits from:
# - Api::ApiController: Base API controller for shared functionality.
#
# Includes:
# - CurrentRetailer: Provides access to the current retailer context.
# - CustomersFilterConcern: Handles customer filtering logic.
#
# Endpoints:
# - `index`: Lists customers with filtering and pagination support.
# - `exported_customers`: Lists previously exported customer records.
# - `export`: Triggers an asynchronous export job for customer data.

class Api::V2::CustomersController < Api::ApiController
  include CurrentRetailer
  include CustomersFilterConcern

  # GET /api/v2/customers
  #
  # Lists customers for the current retailer, applying filters via a Ransack query and
  # paginating the results with Pagy. Customers are serialized using the `CustomerSerializer`.
  #
  # @return [JSON] Paginated list of customers with metadata.
  def index
    filter = build_ransack_query
    pagy, customers = pagy(filter.result, items: 25)

    render status: :ok, json: {
      customers: ActiveModelSerializers::SerializableResource.new(
        customers, each_serializer: Api::V2::CustomerSerializer,
        current_retailer_user: current_retailer_user),
      pages: pagy.pages
    }
  end

  # GET /api/v2/customers/exported_customers
  #
  # Retrieves a paginated list of customer exports ordered by descending creation date.
  # Exported customers are serialized using the `ExportedCustomerSerializer`.
  #
  # @return [JSON] Paginated list of exported customer records with metadata.
  def exported_customers
    export_customers = current_retailer.customer_exports.order(id: :desc)
    pagy, export_customers = pagy(export_customers, items: 25)

    render status: :ok, json: {
      export_customers: ActiveModelSerializers::SerializableResource.new(
        export_customers, each_serializer: Api::V2::ExportedCustomerSerializer
      ),
      pages: pagy.pages
    }
  end

  # POST /api/v2/customers/export
  #
  # Triggers the `ExportCustomersJob` asynchronously to export customer data for the
  # current retailer. An email notification will be sent when the export is ready.
  #
  # @return [JSON] Confirmation message that the export is being processed.
  def export
    Customers::ExportCustomersJob.perform_later(current_retailer_user.id, export_params)
    render status: :ok, json: { message: t('views.customers.export.notification') }
  end

  private

    # Strong parameters for customer export filtering. Allows for filtering customers by
    # various criteria such as name, phone, email, agents, tags, funnel stages, and events.
    #
    # @return [ActionController::Parameters] Permitted export parameters.
    def export_params
      params.permit(
        :type,
        q: [
          :first_name_or_last_name_or_phone_or_email_or_whatsapp_name_cont,
          :s,
          :agent_id,
          :contact_group_id,
          :created_at,
          :last_chat_interaction,
          :customer_events_customer_event_category_id_eq,
          :customer_events_customer_event_subcategory_id_eq,
          {
            funnel_steps_id_in: [],
            customer_tags_tag_id_in: []
          }
        ]
      )
    end
end
