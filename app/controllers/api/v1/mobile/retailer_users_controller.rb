class Api::V1::Mobile::RetailerUsersController < Api::MobileController
  include JwtCookie

  before_action :set_retailer

  def set_app_version
    if params[:app_version]
      @user.update(app_version: params[:app_version])
      @user.update(mobile_type: params[:mobile_type]) if params[:mobile_type]
      render status: :ok, json: {}
    else
      render status: :forbidden, json: { error: 'App version no enviada' }
    end
  end

  def toggle_active
    status = !@user.active

    if @user.update(active: status)
      @user.agent_teams.update(active: status)
      render status: :ok, json: { status: status }
    else
      render status: :forbidden, json: { error: I18n.t('retailer_user.status.failed') }
    end
  end

  def destroy
    if @user.delete_agent
      render status: :ok, json: { message: 'Usuario eliminado con éxito.' }
    else
      render status: :unprocessable_entity, json: {
        message: 'Error eliminando usuario',
        errors: @user.errors.full_messages.join(', ')
      }
    end
  end

  def get_jwt_cookie_for_mobile
    set_jwt_cookie(@user.user)
    render json: { jwt_token: cookies[:jwt_token] }, status: :ok
  end

  private

    def set_retailer
      @user = User.find_by(email: request.headers['email'] || create_params[:email])&.current_retailer_user
      return record_not_found unless @user

      @retailer = @user.retailer
    end
end
