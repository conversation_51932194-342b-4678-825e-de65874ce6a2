class Api::V1::Academy::AcademyCategoriesController < ApplicationController
  rescue_from StandardError, with: :handle_exception

  # GET /academy_categories
  def index
    categories = if params[:module_name].present?
                   search = params[:module_name].downcase
                   AcademyCategory.where(
                     'LOWER(module_name) = :exact OR ' \
                     'LOWER(module_name) LIKE :start OR ' \
                     'LOWER(module_name) LIKE :middle OR ' \
                     'LOWER(module_name) LIKE :end',
                     exact: search,
                     start: "#{search};%",
                     middle: "%;#{search};%",
                     end: "%;#{search}"
                   )
                 else
                   []
                 end
    render json: categories, status: :ok
  end

  private

    def handle_exception(exception)
      Rails.logger.error("Error: #{exception.message}")
      Rails.logger.error(exception.backtrace.join("\n"))
      render json: { error: 'An internal error occurred.' }, status: :internal_server_error
    end
end
