class Api::V1::Shops::RetailersController < Api::V1::Shops::ApiShopsController
  include ShopOrderConcern

  def send_order_notification
    send_notification
    render status: :ok, json: { message: 'Notificación enviada con exito' }
  end

  def save_order_data
    save_shop_order
    render status: :ok, json: { message: 'Orden guardada con exito' }
  end

  def mia_integration
    mia_integration = current_retailer.mia_integration(params[:mia_integration_type])

    if mia_integration
      render json: serialize_mia_integration(mia_integration), status: :ok
    else
      render json: { message: 'No se encontró la integración' }, status: :not_found
    end
  end

  private

    def serialize_mia_integration(mia_integration)
      ActiveModelSerializers::SerializableResource.new(
        mia_integration,
        serializer: Api::V1::Shops::MiaIntegrationSerializer
      ).as_json
    end

    def send_notification
      send_email_notification
      send_push_notification
    end

    def send_email_notification
      emails = RetailerUser.active(@retailer.id).select('users.email, retailer_users.user_id').map(&:email)

      emails.each do |email|
        OrderMailer.sale_notification(@retailer.id, email, params.to_json).deliver_later
      end
    end

    def send_push_notification
      emails = RetailerUser.active(@retailer.id).select('users.email, retailer_users.user_id').map(&:email)
      OneSignalPushNotification.send_sales_push(emails, params, @retailer.hide_product_prices)
    end

    def save_shop_order
      create_shop_order(params)
    end
end
