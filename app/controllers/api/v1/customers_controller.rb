class Api::V1::CustomersController < Api::ApiController # rubocop:disable Metrics/ClassLength
  # TODO: RENAME TO FACEBOOKCHATSCONTROLLER
  include CurrentRetailer
  include ActionView::Helpers::TextHelper
  include CustomerListFiltersConcern
  include FacebookCustomerListConcern
  include SerializableObjectsConcern
  include FindableCustomerByConditionsConcern
  include ReadIgMessagesConcern

  before_action :sanitize_params, only: [:update]
  before_action :set_klass, only: %i[create_message send_img set_message_as_read]
  # rubocop:disable Rails/LexicallyScopedActionFilter
  before_action :set_customer, except: [
    :index,
    :create,
    :edit,
    :create_from_direct_message,
    :create_from_retailer,
    :update_from_retailer,
    :set_message_as_read,
    :fast_answers_for_messenger,
    :fast_answers_for_instagram,
    :fast_answers_for_fb_comments,
    :fast_answers_for_ig_comments,
    :search_customers,
    :search_assigned_customers,
    :custom_fields,
    :only_info,
    :customer_details,
    :customer_summary_info
  ]
  # rubocop:enable Rails/LexicallyScopedActionFilter
  before_action :set_customer_by_web_id, only: [:edit, :update_from_retailer, :only_info]

  def index
    @platform = params[:platform].presence || 'messenger'

    Rails.logger.info("Plataforma seleccionada: #{@platform}")

    begin
      customers = if current_retailer_user.can_list_all_chats?
                    Rails.logger.info('Usuario puede listar todos los chats')
                    current_retailer.customers.facebook_customers.active.with_chat_interaction.where(pstype: @platform)
                  elsif current_retailer_user.managed_agents.present? || current_retailer_user.agent?
                    Rails.logger.info('Usuario puede listar clientes asignados o es un agente')
                    current_retailer_user.customers.facebook_customers
                      .active.with_chat_interaction
                      .where(pstype: @platform)
                  end

      Rails.logger.info("Cantidad de customers obtenidos: #{customers&.count}")

      @customers = customer_list(customers)
      Rails.logger.info("Customer list generada: #{@customers.to_sql}") # Log the SQL query

      @customers = @customers&.offset(params[:offset].to_i)
      Rails.logger.info("Customers paginados: #{@customers&.to_sql}") # Log the SQL query after pagination

      total_pages = @pagy&.pages
      Rails.logger.info("Total de páginas: #{total_pages}")

      render status: :ok, json: {
        customers: serialized_customers,
        agents: serialize(current_retailer_user.filter_agents, Api::V1::AgentSerializer),
        agent_list: serialize(current_retailer.team_agents, Api::V1::AgentSerializer),
        storage_id: current_retailer_user.storage_id,
        filter_tags: serialize(current_retailer.tags, Api::V1::TagSerializer),
        total_customers: total_pages
      }
    rescue StandardError => e
      Rails.logger.error("Error en el método index: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      render status: :internal_server_error, json: { error: 'Internal Server Error', message: e.message }
    end
  end

  def customer_details
    customer = current_retailer.customers.find_by(web_id: params[:id])

    if customer
      related_fields = current_retailer.customer_related_fields.map do |customer_related_field|
        {
          id: customer_related_field.id,
          web_id: customer_related_field.web_id,
          name: customer_related_field.name,
          field_type: customer_related_field.field_type,
          list_options: customer_related_field.list_options,
          value: CustomerRelatedDatum.find_by(customer: customer, customer_related_field: customer_related_field)&.data
        }
      end

      customer_data = {
        web_id: customer.web_id,
        country_id: customer.country_id,
        emoji_flag: customer.emoji_flag,
        email: customer.email,
        phone: customer.phone,
        first_name: customer.first_name,
        last_name: customer.last_name,
        id_number: customer.id_number,
        id_type: customer.id_type,
        tags: customer.tags,
        addresses: customer.customer_addresses,
        related_fields: related_fields
      }

      render status: :ok, json: { customer: customer_data }
    else
      render status: :not_found, json: { error: 'Cliente no encontrado' }
    end
  end

  def customer_summary_info
    # T* Necesario
    customer = current_retailer.customers.find_by(web_id: params[:id])
    customer_events = customer.customer_events
      .includes(:customer_event_category, :customer_event_subcategory) # TODO: Revisar esto
      .order(id: :desc)
    shops_customer = customer.web_id_in_shops.present? ? Shops::CustomerSync.new(customer).show : {}
    orders = (shops_customer['orders'] || []).reverse

    # T* SummaryInfo
    created_at = customer.created_at
      .in_time_zone(current_retailer.timezone || 'America/Guayaquil')
      .strftime('%d-%m-%Y %H:%M')
    ultima_actividad = customer.customer_events
      .order(created_at: :desc)
      .first
      &.created_at
      &.in_time_zone(current_retailer.timezone || 'America/Guayaquil')
      &.strftime('%d-%m-%Y %H:%M')
    total_orders = orders.size

    formatted_orders = orders.map do |order|
      {
        web_id: order['web_id'],
        id: order['id'],
        created_at: DateTime.parse(order['created_at'])
          .in_time_zone(current_retailer.timezone || 'America/Guayaquil')
          .strftime('%d-%m-%Y %H:%M'),
        status: order['status'],
        total: order['total'],
        payment_method: order['payment_transaction'] || nil, # TODO: Debe implementarse en shops
        sales_channel: order['sales_channel'] || {} # TODO: Debe implementarse en shops
      }
    end

    formatted_activity = customer_events.map do |event|
      {
        name: event.customer_event_subcategory.name,
        action: event.action,
        agent: event.agent_name,
        created_at: event.created_at
          .in_time_zone(current_retailer.timezone || 'America/Guayaquil')
          .strftime('%d-%m-%Y %H:%M')
      }
    end

    formatted_deals = customer.deals.map do |deal|
      {
        id: deal.id,
        status: deal.lead_status,
        agent: deal.agent_name,
        funnel_name: deal.funnel_step.funnel.name,
        funnel_step_name: deal.funnel_step.name,
        total: deal.funnel_step.total,
        created_at: deal.created_at
          .in_time_zone(current_retailer.timezone || 'America/Guayaquil')
          .strftime('%d-%m-%Y %H:%M')
      }
    end

    render status: :ok, json: {
      customer_name: customer.full_names,
      summary_info: {
        created_at: created_at,
        last_activity: ultima_actividad,
        total_orders: total_orders
      },
      orders: formatted_orders,
      activities: formatted_activity,
      deals: formatted_deals
    }
  end

  def show
    @platform = params[:service] unless params[:service].in?(%w[facebook_comments instagram_comments])
    @platform = 'messenger' if @platform.blank? || @platform == 'facebook'

    read_messages! if @customer.instagram? && !params[:service].in?(%w[facebook_comments instagram_comments])
    @customer.country_id = @customer.country_id_to_use if @customer.country_id.blank?
    @customer.platform_to_use = @platform

    render status: :ok, json: {
      customer: @customer.as_json(methods: [
                                    :active_bot,
                                    :allow_start_bots,
                                    :emoji_flag,
                                    :tags,
                                    :assigned_agent,
                                    :assigned_agent_mobile,
                                    :recent_inbound_fb_date,
                                    :phone_without_country_code,
                                    :country_id_to_use,
                                    :channels,
                                    :customer_mia_chatbots
                                  ]),
      hubspot_integrated: @customer.retailer.hubspot_integrated?,
      reminders: serialize_reminders(@customer.reminders.includes(:retailer_user).order(created_at: :desc)),
      tags: current_retailer.available_customer_tags(@customer.id),
      orders_count: @customer.shop_orders.size
    }
  end

  def edit
    render status: :ok, json: {
      customer: @customer.as_json(methods: [
                                    :emoji_flag,
                                    :tags,
                                    :agent_customer,
                                    :assigned_agent_mobile,
                                    :recent_inbound_fb_date,
                                    :phone_without_country_code,
                                    :country_id_to_use,
                                    :customer_addresses,
                                    :customer_related_data,
                                    :customer_mia_chatbots
                                  ])
    }
  end

  def create
    if @customer.new_record?
      @customer.created_in = 'Creado desde el API interna'.freeze
      @customer.save
    else
      @customer.update(customer_params)
    end
  end

  def create_from_direct_message
    @customer = find_or_initialize_customer

    if @customer.new_record?
      create_customer
    else
      handle_existing_customer
    end
  rescue StandardError => e
    Rails.logger.error("Error en create_from_direct_message: #{e.message}")
    render status: :unprocessable_entity, json: { message: 'No se puede crear el cliente', error: e.message }
  end

  def create_from_retailer
    @customer = Customer.new(customer_params)
    @customer.retailer_id = current_retailer.id
    @customer.created_in = 'Creación de customer desde retailer'.freeze
    @customer.agent_event = current_retailer_user

    if @customer.save
      NotificationService.new(current_retailer, @customer).notify_new_customer

      render status: :ok, json: { customer: @customer, message: 'Cliente creado con éxito' }
    else
      render status: :bad_request, json: {
        message: @customer.errors['base'].join(', ')
      }
    end
  end

  def update_from_retailer
    if @customer.update(customer_params)
      render status: :ok, json: { customer: @customer, message: 'Cliente actualizado con éxito' }
    else
      render status: :bad_request, json: {
        message: @customer.errors['base'].join(', ')
      }
    end
  end

  def update_phone_number
    if @customer.update(customer_params)
      Shopify::IntegrationMessageService.new.retry_message_by_customer(@customer.id)
      render status: :ok, json: { customer: @customer, message: I18n.t('activerecord.models.customer.update_success') }
    else
      render status: :bad_request, json: {
        message: @customer.errors['base'].join(', ')
      }
    end
  end

  def update
    @customer.agent_event = current_retailer_user
    if @customer.update(customer_params)
      notifiable_update(@customer.reload).call
      render status: :ok, json: {
        customer: @customer.as_json(methods: [
                                      :emoji_flag, :tags,
                                      :assigned_agent,
                                      :assigned_agent_mobile,
                                      :recent_inbound_fb_date,
                                      :phone_without_country_code,
                                      :customer_mia_chatbots
                                    ]),
        tags: current_retailer.available_customer_tags(@customer.id)
      }
    else
      render status: :bad_request, json: {
        customer: @customer.as_json(methods: [
                                      :active_bot,
                                      :allow_start_bots,
                                      :emoji_flag, :tags,
                                      :assigned_agent,
                                      :assigned_agent_mobile,
                                      :phone_without_country_code,
                                      :customer_mia_chatbots
                                    ]),
        errors: @customer.errors,
        tags: current_retailer.available_customer_tags(@customer.id)
      }
    end
  end

  def messages
    read_messages!
    render status: :ok, json: {
      messages: serialize_facebook_messages.to_a.reverse,
      agents: @agents,
      storage_id: current_retailer_user.storage_id,
      agent_list: current_retailer.team_agents,
      total_pages: @pagy.pages,
      customer_id: @customer.id,
      recent_inbound_message_date: @customer.recent_inbound_fb_date,
      filter_tags: current_retailer.tags
    }
  end

  def create_message
    message = @klass.new(
      customer: @customer,
      sender_uid: current_retailer_user.uid,
      id_client: @customer.psid,
      facebook_retailer: current_retailer.facebook_retailer,
      text: params[:message],
      sent_from_mercately: true,
      sent_by_retailer: true,
      retailer_user: current_retailer_user,
      file_type: params[:type],
      message_identifier: params[:message_identifier],
      note: params[:note],
      order_note: params[:order_note].present?,
      order_summary: params[:order_summary].presence
    )

    return unless message.save

    update_customer_labels

    render status: :ok, json: { message: }
  end

  def send_img
    if params[:file_data].present?
      file_data = params[:file_data].tempfile.path
      file_content_type = params[:file_data].content_type
      s3_response = Uploaders::S3Aws.new(current_retailer.id)
        .upload_file(params[:file_data], platform: params[:platform])
      url = s3_response['secure_url']
      filename = s3_response['original_filename']
    else
      url = params[:url] || params[:file_url]
    end

    filename ||= params[:file_name]
    message = @klass.new(
      customer: @customer,
      sender_uid: current_retailer_user.uid,
      id_client: @customer.psid,
      file_data:,
      facebook_retailer: current_retailer.facebook_retailer,
      sent_from_mercately: true,
      sent_by_retailer: true,
      filename:,
      retailer_user: current_retailer_user,
      file_type: params[:type],
      message_identifier: params[:message_identifier],
      file_content_type:,
      attached_file: params[:file_data]
    )

    message.assign_attributes(file_url: url, url:, file_data: nil) if url.present?

    render status: :ok, json: { message: } if message.save
  end

  def set_message_as_read
    facebook_helper = FacebookNotificationHelper
    @message = @klass.find(params[:id])
    @message.update_column(:date_read, Time.now)
    facebook_service.send_read_action(@message.customer.psid, 'mark_seen') if params[:platform] != 'instagram'
    customer = @message.customer
    unread_count = customer.unread_messenger_messages.positive? || customer.unread_instagram_messages.positive?
    if params[:platform] == 'instagram'
      customer.custom_update_columns(unread_instagram_messages: 0, unread_instagram_chat: false)
    else
      customer.custom_update_columns(unread_messenger_messages: 0, unread_messenger_chat: false)
    end
    mark_unread_flag(customer) if customer.pstype.present? && unread_count
    agents = customer.agent ? [customer.agent] : current_retailer.retailer_users.all_customers.to_a

    facebook_helper.broadcast_data(
      current_retailer,
      agents,
      nil,
      customer.agent_customer,
      customer,
      params[:platform]
    )
    render status: :ok, json: { message: @message }
  end

  def accept_opt_in
    if @customer.whatsapp_opt_in
      render status: :ok, json: { customer_id: @customer.id }
    else
      render status: :bad_request, json: { error: 'Error al aceptar opt-in de este cliente, intente nuevamente' }
    end
  end

  def selectable_tags
    tags = current_retailer.available_customer_tags(params[:id]) || []
    render status: :ok, json: { tags: }
  end

  def add_customer_tag
    @customer.customer_tags.create(tag_id: params[:tag_id])
    send_notification(params[:chat_service])

    render status: :ok, json: { customer: @customer.as_json(methods: [:emoji_flag, :tags]), tags:
                                current_retailer.available_customer_tags(@customer.id) }
  end

  def remove_customer_tag
    @customer.customer_tags.find_by(tag_id: params[:tag_id])&.destroy
    send_notification(params[:chat_service])

    render status: :ok, json: { customer: @customer.as_json(methods: [:emoji_flag, :tags]), tags:
                                current_retailer.available_customer_tags(@customer.id) }
  end

  def add_tag
    tag_params = params[:tag].strip
    tag = current_retailer.tags.find_tag(tag_params).first
    tag = current_retailer.tags.create(tag: tag_params) if tag.blank?
    @customer.customer_tags.create(tag_id: tag.id) if tag.present?
    send_notification(params[:chat_service])

    render status: :ok, json: { customer: @customer.as_json(methods: [:emoji_flag, :tags]), tags:
                                current_retailer.available_customer_tags(@customer.id),
                                filter_tags: current_retailer.tags }
  end

  def add_tags
    customer_tags = []

    params[:customer][:tag_ids].each do |tag|
      customer_tag = @customer.customer_tags.find_by(tag_id: tag)

      customer_tag = @customer.customer_tags.new(tag_id: tag) if customer_tag.blank?
      customer_tags << customer_tag
    end

    @customer.customer_tags = customer_tags

    if @customer.save
      Notifications::Mobile::Events.broadcast(@customer, @customer.tags, 'tags')
      render status: :ok, json: {
        customer: @customer.as_json(methods: [
                                      :emoji_flag,
                                      :tags,
                                      :assigned_agent,
                                      :assigned_agent_mobile,
                                      :recent_inbound_fb_date,
                                      :phone_without_country_code,
                                      :customer_mia_chatbots
                                    ]),
        tags: current_retailer.available_customer_tags(@customer.id)
      }
    else
      render status: :bad_request, json: {
        customer: @customer.as_json(methods: [
                                      :emoji_flag,
                                      :tags,
                                      :assigned_agent,
                                      :assigned_agent_mobile,
                                      :phone_without_country_code,
                                      :customer_mia_chatbots
                                    ]),
        errors: @customer.errors,
        tags: current_retailer.available_customer_tags(@customer.id)
      }
    end
  end

  def toggle_chat_bot
    @customer.agent_event = current_retailer_user
    platform = params[:chat_service].presence || 'whatsapp'
    platform = 'messenger' if platform == 'facebook'
    if @customer.policy.active_bot_for?(platform:)
      Customer::ChatBotDeactivator.call(@customer, platform:)
    else
      @customer.toggle_allow_start_bots!(params[:chat_service])
    end
    send_notification(params[:chat_service])

    render status: :ok, json: {
      customer: @customer.as_json(methods: [
                                    :active_bot,
                                    :allow_start_bots,
                                    :emoji_flag,
                                    :tags,
                                    :assigned_agent,
                                    :assigned_agent_mobile,
                                    :recent_inbound_fb_date,
                                    :phone_without_country_code,
                                    :customer_mia_chatbots
                                  ]),
      tags: current_retailer.available_customer_tags(@customer.id)
    }
  end

  def send_bulk_files
    facebook_service.send_bulk_files(@customer, current_retailer_user, params)

    render status: :ok, json: { message: 'Mensaje enviado' }
  end

  def send_multiple_answers
    facebook_service.send_multiple_answers(@customer, current_retailer_user, params)

    render status: :ok, json: { message: 'Mensaje enviado' }
  end

  def search_customers
    customers = current_retailer.customers

    customers = if params[:text].blank?
                  customers.limit(25).order(created_at: :desc)
                else
                  customers.by_search_text(params[:text])
                end

    # Modificamos la lógica de includes para evitar eager loading innecesario
    if params[:attributes]
      attributes_to_select = params[:attributes].split(',')
      customers = customers.select(attributes_to_select)
    elsif params[:serialized].blank?
      # Solo incluimos country_price si realmente se necesita
      customers = customers.select(:id, :phone, :first_name, :last_name, :email)
    end

    # Serializa solo si se solicita
    customers = serialize(customers, Api::V1::CustomerSerializer) if params[:serialized].present?

    render json: { customers: }
  end

  def search_assigned_customers
    customers = if current_retailer_user.agent?
                  current_retailer_user.customers
                else
                  current_retailer.customers
                end
    customers = customers.where.not(phone: [nil, '']).order(id: :desc)

    customers = if params[:text].blank?
                  customers.limit(params[:limit] || 300)
                else
                  customers.by_search_text(params[:text])
                end

    customers = serialize(customers, Api::V1::CustomerSerializer) if params[:serialized].present?

    render json: { customers: }
  end

  def custom_fields
    custom_fields = current_retailer
      .customer_related_fields
      .select(
        :id,
        :retailer_id,
        :name,
        :identifier,
        :field_type,
        :list_options
      ).as_json
    render status: :ok,
           json: {
             custom_fields:
           }
  end

  def toggle_block_user
    @customer.block_user
    send_notification('whatsapp')

    render status: :ok, json: {
      customer: @customer.as_json(methods: [
                                    :emoji_flag,
                                    :tags,
                                    :assigned_agent,
                                    :assigned_agent_mobile,
                                    :recent_inbound_fb_date,
                                    :phone_without_country_code,
                                    :customer_mia_chatbots
                                  ]),
      tags: current_retailer.available_customer_tags(@customer.id)
    }
  end

  # PUT /customers/:id/toggle_mia_chatbot
  def toggle_mia_chatbot
    if customer_mia_chatbot.update(active: !customer_mia_chatbot.active)
      customer_mia_chatbot.active && context_platform_service.call(@customer)

      render status: :ok, json: {
        customer: @customer.as_json(methods: [
                                      :emoji_flag,
                                      :tags,
                                      :assigned_agent,
                                      :phone_without_country_code,
                                      :country_id_to_use,
                                      :channels,
                                      :customer_mia_chatbots
                                    ]),
        message: 'Mia chatbot state updated successfully'
      }
    else
      render status: :unprocessable_entity, json: {
        error: 'Failed to update mia chatbot state',
        customer: @customer.as_json(methods: [
                                      :emoji_flag,
                                      :tags,
                                      :assigned_agent,
                                      :phone_without_country_code,
                                      :country_id_to_use,
                                      :channels,
                                      :customer_mia_chatbots
                                    ])
      }
    end
  end

  def only_info
    render status: :ok, json: {
      customer: @customer.as_json(methods: [
                                    :emoji_flag,
                                    :tags,
                                    :assigned_agent,
                                    :phone_without_country_code,
                                    :country_id_to_use,
                                    :channels,
                                    :customer_mia_chatbots
                                  ]),
      orders_count: @customer.shop_orders.size
    }
  end

  private

    # Use callbacks to share common setup or constraints between actions.
    def set_customer
      @customer = current_retailer.customers.find(params[:customer_id] || params[:id])
    end

    def set_customer_by_web_id
      @customer = current_retailer.customers.find_by(web_id: params[:id])
    end

    def sanitize_params
      params[:customer].each_pair do |param|
        next unless params[:customer][param.first]
        next if param.first == 'customer_related_data_attributes'
        next if param.first == 'notes' # Don't remove \n
        next if params[:customer][param.first].in? [true, false]

        params[:customer][param.first] = strip_tags(params[:customer][param.first]).squish
      end
    end

    def facebook_service
      @facebook_service ||= Facebook::Messages.new(
        current_retailer.facebook_retailer,
        @customer&.pstype || params[:platform] || 'messenger'
      )
    end

    def send_notification(chat_service)
      NotificationService.new(current_retailer, @customer).send_notification({ chat_service: })
    end

    def set_klass
      @klass = params[:platform] == 'instagram' ? InstagramMessage : FacebookMessage
    end

    def serialize_facebook_messages
      ActiveModelSerializers::SerializableResource.new(
        @messages,
        each_serializer: FacebookMessageSerializer
      ).as_json
    end

    def customer_params
      params.require(:customer).permit(
        :first_name,
        :last_name,
        :email,
        :phone,
        :id_type,
        :id_number,
        :address,
        :city,
        :state,
        :zip_code,
        :country_id,
        :notes,
        :hs_active,
        :id_in_shops,
        :web_id_in_shops,
        :send_for_opt_in,
        tag_ids: [],
        customer_addresses_attributes: [
          :address, :description, :city, :state, :country_id, :zip_code, :latitude, :longitude, :main
        ],
        customer_related_data_attributes: [
          :id,
          :customer_related_field_id,
          :data
        ],
        agent_customer_attributes: [
          :id,
          :retailer_user_id,
          :team_assignment_id,
          :_destroy
        ]
      ).tap do |param|
        agent_attributes = param[:agent_customer_attributes]
        agent_id = agent_attributes&.dig(:id)
        retailer_user_id = agent_attributes&.dig(:retailer_user_id)

        if agent_id.present?
          if retailer_user_id.blank?
            agent_attributes[:_destroy] = true
          elsif retailer_user_id.to_i != @customer.agent_customer.retailer_user_id
            agent_attributes[:team_assignment_id] = nil
          end
        end
      end
    end

    def agents
      if current_retailer_user.admin? ||
         current_retailer_user.supervisor?
        current_retailer.team_agents
      else
        [current_retailer_user]
      end
    end

    def serialize_reminders(reminders)
      ActiveModelSerializers::SerializableResource.new(
        reminders,
        each_serializer: Api::V1::ReminderSerializer
      ).as_json
    end

    def find_or_initialize_customer
      if params[:customer][:phone].present? && params[:customer][:country_id].present?
        phone = Customers::PhoneFormat.format_phone_number(params[:customer][:phone], params[:customer][:country_id])
        phones_to_check = PhoneService.phones_to_check(phone.gsub(/\s+/, ''))

        customer = current_retailer.customers.where(phone: phones_to_check).first

        return customer if customer.present?
      end
      current_retailer.customers.new(customer_params)
    end

    def create_customer
      @customer.agent_event = current_retailer_user
      @customer.assign_attributes(
        created_in: 'Creado desde el API interna para un chat directo',
        agent: current_retailer_user
      )

      if @customer.save
        render status: :ok, json: { customer: @customer.as_json }
      else
        render status: :unprocessable_entity,
               json: { message: 'No se puede crear el cliente', errors: @customer.errors.full_messages }
      end
    end

    def handle_existing_customer
      if unauthorized_access?
        render status: :unprocessable_entity, json: { message: 'Este cliente no lo tienes asignado' }
      elsif assigned_to_other_agent?
        render status: :unprocessable_entity, json: { message: 'Este cliente ya está asignado a otro agente' }
      else
        render status: :ok, json: { customer: @customer.as_json }
      end
    end

    def unauthorized_access?
      current_retailer_user.agent? && current_retailer_user.only_assigned? && @customer.agent != current_retailer_user
    end

    def assigned_to_other_agent?
      current_retailer_user.agent? && @customer.agent.present? && @customer.agent != current_retailer_user
    end

    def serialized_customers
      # Removemos el includes innecesario de :retailer y solo incluimos lo necesario
      ActiveModelSerializers::SerializableResource.new(
        @customers&.includes(:tags, agent: :user) || [],
        each_serializer: Api::V1::FacebookChatSerializer,
        platform: @platform
      ).as_json
    end

    def customer_mia_chatbot
      @customer_mia_chatbot ||= CustomerMiaChatbot.find_or_create_by(platform: customer_platform,
                                                                     customer_id: @customer.id)
    end

    def customer_platform
      params[:platform] || 'whatsapp'
    end

    def context_platform_service
      "Mia::Chatbot::Context::#{customer_platform.capitalize}::ContextProcessor".constantize
    end

    def update_customer_labels
      updates = {}
      updates[:human_help] = false if customer_mia_chatbot.human_help?
      updates[:buy_intention] = false if customer_mia_chatbot.buy_intention?
      customer_mia_chatbot.update!(updates) unless updates.empty?
    end

    def notifiable_update(customer)
      @notifiable_update ||= Customer::NotifiableUpdate.new(customer)
    end
end
