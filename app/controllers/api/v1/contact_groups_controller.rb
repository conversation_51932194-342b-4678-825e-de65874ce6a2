# frozen_string_literal: true

class Api::V1::ContactGroupsController < Api::ApiController
  include CurrentRetailer
  include SerializableObjectsConcern

  before_action :set_contact_group, only: %i[destroy list_customers]

  def index
    groups = current_retailer.contact_groups.not_archived.includes(:retailer_user)
      .order(created_at: :desc)

    pagy_obj, groups = pagy(groups)

    render status: :ok, json: {
      groups: serialize(groups, Api::V1::ContactGroupSerializer),
      total_pages: pagy_obj.pages
    }
  end

  # POST /api/v1/contact_groups
  def create
    contact_group = current_retailer.contact_groups.where('lower(name) = ?', params[:name]&.downcase)
    if contact_group.present?
      render status: :unprocessable_entity, json: {
        message: 'No fue posible crear la lista de clientes. Ya existe una lista con ese nombre.'
      }
      return
    end
    if ContactGroups::CreateGroupJob.perform_later(
      contact_group_params[:name],
      current_retailer_user.id,
      params[:start_date], params[:end_date],
      params[:agents], params[:tags],
      params[:funnels], params[:condition],
      params[:category], params[:subcategory],
      params[:unique_tags]
    )
      render status: :ok, json: {
        message: 'Creación de lista de clientes en proceso. Recibirá un correo cuando haya culminado.'
      }
    else
      render status: :unprocessable_entity, json: {
        message: 'No fue posible crear la lista de clientes. Por favor intente nuevamente.'
      }
    end
  end

  # DELETE /api/v1/contact_groups
  def destroy
    if @contact_group.campaigns.exists?
      @contact_group.archived!

      render status: :ok, json: {
        message: 'Lista de clientes eliminada con éxito.',
        group: {
          id: @contact_group.id,
          web_id: @contact_group.web_id
        }
      }
    elsif @contact_group.destroy
      render status: :ok, json: {
        message: 'Lista de clientes eliminada con éxito.',
        group: {
          id: @contact_group.id,
          web_id: @contact_group.web_id
        }
      }
    else
      render status: :unprocessable_entity, json: {
        message: @contact_group.errors,
        group: {
          id: @contact_group.id,
          web_id: @contact_group.web_id
        }
      }
    end
  end

  def selected_customers
    size = customers_by_params.count

    render status: :ok, json: { total_customers: size }
  end

  def preview_customers
    customers = customers_by_params.order(first_name: :asc, last_name: :asc).includes(:agent, :tags)
    pagy_obj, customers = pagy(customers)

    customer_ids = customers.map(&:id).uniq.compact
    funnel_steps_data = fetch_funnel_steps(customer_ids)

    render status: :ok, json: {
      customers: serialize(customers, Api::V1::ContactGroupMemberSerializer, funnel_steps_data: funnel_steps_data),
      total_pages: pagy_obj.pages
    }
  end

  def validate_customer_list_name
    valid = !current_retailer.contact_groups.by_name(params[:name]).not_archived.exists?

    render status: :ok, json: { valid: valid }
  end

  # GET /api/v1/contact_groups/search
  def search
    query = params[:query].to_s.strip
    page = (params[:page] || 1).to_i
    per_page = (params[:per_page] || 25).to_i

    groups = fetch_filtered_groups(query)
    groups = groups.order(created_at: :desc)
    pagy_obj, groups = pagy(groups, page: page, items: per_page)

    render_search_results(groups, pagy_obj)
  rescue StandardError => e
    log_search_error(e)
    render status: :internal_server_error, json: { error: e.message }
  end

  def list_customers
    customers = @contact_group.customers.order(first_name: :asc, last_name: :asc).includes(:agent, :tags)
    pagy_obj, customers = pagy(customers)

    customer_ids = customers.pluck(:id).uniq.compact
    funnel_steps_data = fetch_funnel_steps(customer_ids)

    render status: :ok, json: {
      customers: serialize(customers, Api::V1::ContactGroupMemberSerializer, funnel_steps_data: funnel_steps_data),
      total_pages: pagy_obj.pages
    }
  end

  private

    def set_contact_group
      @contact_group = ContactGroup.find_by(web_id: params[:id]) || not_found
    end

    def not_found
      raise ActionController::RoutingError, 'Not Found'
    end

    def customers_by_params
      filtered_customers.customers(
        current_retailer, start_date_param, end_date_param, params[:agents], params[:tags],
        params[:funnels], params[:condition], params[:category], params[:subcategory],
        params[:unique_tags]
      )
    end

    def filtered_customers
      Customers::FilteredCustomers.new
    end

    def contact_group_params
      params.require(:contact_group).permit(
        :name
      )
    end

    def start_date_param
      return if params[:start_date].blank?

      @start_date_param ||= ActiveSupport::TimeZone[timezone].parse("#{params[:start_date]&.tr('/', '-')} 00:00:00")
    end

    def end_date_param
      return if params[:end_date].blank?

      @end_date_param ||= ActiveSupport::TimeZone[timezone].parse("#{params[:end_date]&.tr('/', '-')} 23:59:59")
    end

    def timezone
      @timezone ||= current_retailer.timezone.presence || 'America/Guayaquil'
    end

    def fetch_funnel_steps(customer_ids)
      return [] if customer_ids.empty?

      token = JwtService.encode({ issuer: ENV.fetch('JWT_ISSUER', 'mercately').freeze })

      headers = { 'Authorization' => "Bearer #{token}" }

      url = "#{ENV.fetch('DEAL_ENGINE_BASE_URL', nil)}/by_contact_groups"
      body = { retailer_id: current_retailer.id, customer_ids: customer_ids }.to_json
      connection = Connection.prepare_connection(url)

      begin
        response = Connection.post_request(connection, body, headers)
        if response.success?
          JSON.parse(response.body)['funnel_steps']
        else
          Rails.logger.error("Failed to obtain funnel steps data for customer ids #{customer_ids}")
          []
        end
      rescue Faraday::ConnectionFailed => e
        Rails.logger.error("Failed to connect to the server: #{e.message} obtaining funnel steps data")
        []
      end
    end

    # Métodos para la acción search
    def fetch_filtered_groups(query)
      groups = current_retailer.contact_groups.not_archived.includes(:retailer_user)
      return groups if query.blank?

      apply_search_filters(groups, query)
    end

    def apply_search_filters(groups, query)
      # Dividir la consulta en términos individuales para búsqueda más flexible
      search_terms = query.downcase.split(/\s+/).compact_blank

      if search_terms.any?
        apply_multi_term_search(groups, search_terms)
      else
        # Si no hay términos válidos después de dividir, usar la consulta original
        groups.where('lower(name) LIKE ?', "%#{query.downcase}%")
      end
    end

    def apply_multi_term_search(groups, search_terms)
      # Construir condiciones para cada término de búsqueda
      conditions = search_terms.map do |term|
        sanitized_term = ActiveRecord::Base.sanitize_sql_like(term)
        "lower(name) LIKE '%#{sanitized_term}%'"
      end

      # Combinar condiciones con AND para que todos los términos deban coincidir
      groups.where(conditions.join(' AND '))
    end

    def render_search_results(groups, pagy_obj)
      serialized_groups = ActiveModelSerializers::SerializableResource.new(
        groups,
        each_serializer: Api::V1::ContactGroupSerializer
      ).as_json

      response = {
        groups: serialized_groups,
        total_pages: pagy_obj.pages
      }

      render status: :ok, json: response
    end

    def log_search_error(error)
      Rails.logger.error("Error in search: #{error.message}")
      Rails.logger.error(error.backtrace.join("\n"))
    end
end
