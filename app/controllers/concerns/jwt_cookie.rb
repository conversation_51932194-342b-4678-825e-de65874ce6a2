module JwtCookie
  extend ActiveSupport::Concern

  JWT_ISSUER = ENV.fetch('JWT_ISSUER', 'mercately').freeze

  private

    def set_jwt_cookie(user, retailer_id = nil)
      retailer_user = if current_retailer_user&.user_id == user.id
                        current_retailer_user
                      else
                        RetailerUser.find_by(user_id: user.id, retailer_id: retailer_id)
                      end

      return unless retailer_user&.retailer

      retailer = retailer_user.retailer

      token = JwtService.encode({
        user_id: user.id,
        retailer_id: retailer.id,
        retailer_user_id: retailer_user.id,
        currency_symbol: retailer.currency_symbol,
        timezone: retailer.timezone,
        email: user.email,
        issuer: JWT_ISSUER
      })

      cookies[:jwt_token] = {
        value: token,
        secure: Rails.env.production?,
        same_site: :strict
      }
    end
end
