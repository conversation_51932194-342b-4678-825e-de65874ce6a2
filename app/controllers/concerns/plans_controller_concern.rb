module PlansControllerConcern
  extend ActiveSupport::Concern

  def format_plans
    plans = []

    PricingTier.where(priceable_type: 'Plan').active.select(:billing_cycle).distinct.each do |pt|
      plans << {
        period: pt.billing_cycle,
        items: PricingTier.active.where(billing_cycle: pt.billing_cycle,
                                        priceable_type: 'Plan').order(:price).includes(:priceable).map do |tier|
          serialize_plan(tier)
        end
      }
    end

    plans
  end

  def format_chargebee_plans(plans, chargebee = Chargebee::Api.new)
    format_plans = []
    periods.each do |period|
      format_plans << { period: period[:name], items: plans_by_period(plans, period, chargebee) }
    end
    format_plans
  end

  def format_addons(payment_plan, addons)
    addons.map { |tier| serialize_addon(tier, payment_plan) }
  end

  def format_chargebee_addons(addons, chargebee = Chargebee::Api.new)
    format_addons = []
    addons.each do |addon|
      format_addons << serialize_chargebee_addon(addon, chargebee)
    end
    format_addons
  end

  def format_tiers(tiers)
    format_tiers = []
    return format_tiers if tiers.blank?

    tiers.each do |tier|
      format_tiers << { starting_unit: tier.starting_unit, ending_unit: tier.ending_unit, price: tier.price.to_f }
    end

    format_tiers
  end

  def format_entitlements(tier_id)
    entitlements = PricingTier.find(tier_id).entitlements

    entitlements.map { |en| serialize_entitlement(en) }
  end

  def periods
    [
      { name: 'monthly', period_unit: 'month', period: 1 },
      { name: 'quarterly', period_unit: 'month', period: 3 },
      { name: 'yearly', period_unit: 'year', period: 1 }
    ]
  end

  def match_charges(retailer, params)
    charges = params[:subscription][:charges].select { |charge| charge[:selected] }
    return if charges.blank?

    payment_plan = retailer.payment_plan
    charges.each do |charge|
      id = charge[:id]
      payment_plan.allowed_charges.each do |allowed_charge|
        next unless allowed_charge[:id] == id

        payment_plan.send("#{allowed_charge[:internal_column]}=", true)
      end
    end

    payment_plan.save
  end

  def set_payment_start_date(subscription, payment_plan)
    return if already_have_payments?(payment_plan)

    begin
      created_at_date = Time.at(subscription.created_at)
    rescue StandardError
      created_at_date = Time.now
    end

    retailer = payment_plan.retailer
    retailer.update(first_payment_date: created_at_date) if retailer.first_payment_date.blank?

    return if payment_plan.payment_start_date.present?

    payment_plan.update(payment_start_date: created_at_date)
  end

  def save_acquired_addons(retailer, params)
    payment_plan = retailer.payment_plan

    plans_utils.save_addons(payment_plan, params[:subscription][:addons])
    plans_utils.save_charges(payment_plan, params[:subscription][:charges])
  end

  def calculate_estimates(payment_plan, params)
    unbilled_charge_estimates = []
    credit_note_estimates = []
    subscription_estimate = {}
    new_tier = PricingTier.find_by(identifier: params[:new_subscription_item_id])
    current_tier = payment_plan.pricing_tier

    if new_tier.id != current_tier.id
      if new_tier.month_interval == current_tier.month_interval
        return plans_utils.upgrade_to_same_cycle(payment_plan, new_tier, current_tier, params)
      else
        return plans_utils.upgrade_to_different_cycle(payment_plan, new_tier, current_tier, params)
      end
    else
      unbilled_charge_estimates = plans_utils.addon_estimates(payment_plan, params, unbilled_charge_estimates)
    end

    {
      unbilled_charge_estimates: unbilled_charge_estimates,
      credit_note_estimates: credit_note_estimates,
      subscription_estimate: subscription_estimate
    }
  end

  private

    def chargebee
      Chargebee::Api.new
    end

    def already_have_payments?(payment_plan)
      retailer = payment_plan.retailer
      payment_plan.chargebee_subscription_id.present? || valid_paymentez_transaction?(retailer) ||
        valid_stripe_transaction?(retailer)
    end

    def valid_paymentez_transaction?(retailer)
      retailer.paymentez_transactions.where(status: 'success').count > 1
    end

    def valid_stripe_transaction?(retailer)
      retailer.stripe_transactions.success.count > 1
    end

    def plans_by_period(plans, period, chargebee = Chargebee::Api.new)
      plans_by_period = []
      plans.each do |plan|
        plans_by_period << serialize_chargebee_plan(plan, chargebee) if plan.period == period[:period] &&
                                                                        plan.period_unit == period[:period_unit]
      end

      plans_by_period
    end

    def serialize_chargebee_plan(plan, chargebee = Chargebee::Api.new)
      {
        id: plan.id,
        item_id: plan.item_id,
        price: plan.price,
        name: plan.name,
        external_name: plan.external_name,
        currency_code: plan.currency_code,
        description: plan.description,
        metadata: chargebee.item_metadata(plan.item_id),
        item_description: chargebee.item_description(plan.item_id)
      }
    end

    def serialize_plan(tier)
      plan = tier.priceable

      {
        id: tier.identifier,
        plan_id: plan.id,
        item_id: plan.identifier,
        price: tier.price.to_f || 0,
        name: plan.name,
        external_name: plan.name,
        currency_code: 'USD',
        description: plan.description,
        metadata: { key_values: JSON.parse(plan.metadata) },
        item_description: plan.description,
        tier_id: tier.id,
        has_ai: tier.contains_ai?
      }
    end

    def serialize_addon(tier, payment_plan)
      addon = tier.priceable

      {
        id: tier.identifier,
        item_id: addon.identifier,
        price: tier.price.to_f || 0,
        name: addon.name,
        external_name: addon.name,
        currency_code: 'USD',
        description: addon.description,
        pricing_model: tier.pricing_model,
        tiers: [],
        item_description: addon.description,
        tier_id: tier.id,
        acquired: tier.acquired?(payment_plan)
      }
    end

    def serialize_chargebee_addon(addon, chargebee = Chargebee::Api.new)
      {
        id: addon.id, price: addon.price, name: addon.name, item_id: addon.item_id,
        external_name: addon.external_name, currency_code: addon.currency_code,
        description: addon.description, pricing_model: addon.pricing_model,
        tiers: format_tiers(addon.tiers), metadata: chargebee.item_metadata(addon.item_id),
        item_description: chargebee.item_description(addon.item_id)
      }
    end

    def serialize_entitlement(entitlement)
      {
        id: entitlement.id,
        feature_id: entitlement.identifier,
        value: entitlement.quantity.to_i
      }
    end

    def plans_utils
      @plans_utils ||= Plans::Utils.new
    end
end
