module MyBusinessConcern
  extend ActiveSupport::Concern

  included do
    before_action :find_address, only: [:update_address, :destroy_address]
  end

  private

    def find_address
      @address = current_retailer.retailer_addresses.find_by_web_id!(params[:id] || params[:web_id])
    end

    def retailer_params
      params.require(:retailer).permit(
        :avatar,
        :background,
        :name,
        :catalog_slug,
        :country_code,
        :description,
        :retailer_number,
        :timezone,
        :currency,
        :facebook_url,
        :instagram_url,
        :twitter_url,
        :tiktok_url,
        :tax_amount,
        :shop_main_color,
        :active_shop,
        :shop_active_send_order,
        :shop_active_store_pickup,
        :hide_product_prices,
        :rotate_images_in_catalog,
        :terms_conditions_content,
        retailer_schedules_attributes: schedule_fields
      )
    end

    def retailer_address_params
      params.require(:retailer_address).permit(
        :name,
        :country_id,
        :state,
        :city,
        :address,
        :web_id
      )
    end

    def domain_params
      params.require(:retailer).permit(:domain, :certificate_file, :key_file)
    end

    def schedule_fields
      [
        :id,
        :weekday,
        :opening_time,
        :closing_time,
        :active,
        :web_id
      ]
    end

    def valid_domain_params?
      domain_params[:domain].present? && domain_params[:certificate_file].present? && domain_params[:key_file].present?
    end
end
