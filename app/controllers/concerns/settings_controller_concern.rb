# frozen_string_literal: true

# rubocop:disable Rails/ModuleLength
module SettingsControllerConcern
  include ActiveSupport::Concern

  private

    def set_user
      @user = current_retailer.retailer_users.find_by(id: params['user'])&.user
    end

    def set_retailer_user
      @user = current_retailer.retailer_users.find_by(id: params['user'])
    end

    def invitation_params
      params.require(:retailer_user)
        .permit(
          :first_name,
          :last_name,
          :email,
          :retailer_admin,
          :retailer_supervisor,
          :only_assigned,
          :allow_import,
          :allow_export,
          :allow_bots_edit,
          :allow_edit_orders,
          :not_ask_terms,
          :see_phone_numbers,
          :phone,
          :role_id,
          managed_agents: [],
          team_retailer_users_attributes: [
            :id,
            :team_id,
            :_destroy
          ]
        )
    end

    def render_unprocessable_entity(message)
      render status: :unprocessable_entity, json: { message: message }
    end

    def invite_to_workspace(user, invitation_params)
      return if user.retailers.find_by(id: current_retailer.id)

      params = {
        phone: invitation_params[:phone] || '',
        current: false,
        retailer_id: current_retailer.id
      }
      if roles_active?
        params.merge!(role_id: invitation_params[:role_id])
      else
        params.merge!(old_retailer_user_params(invitation_params))
      end

      new_retailer_user = user.retailer_users.new(params)
      new_retailer_user.save
      new_retailer_user
    end

    def render_success_invitation(new_retailer_user)
      render status: :ok, json: {
        user: ::Api::V1::StaffSerializer.new(new_retailer_user),
        message: 'Usuario invitado con éxito.'
      }
    end

    def devise_invitation_responses(user)
      if user&.errors.present?
        render status: :unprocessable_entity, json: { user: user, message: user.errors.full_messages.join(', ') }
      elsif user&.persisted?
        user.update_column(:invitation_sent_at, Time.now.utc) if RetailerUserMailer.invitation(user).deliver_now
        render_success_invitation(user.current_retailer_user)
      else
        render_error_invitation(user)
      end
    end

    def render_error_invitation(user)
      error = user.errors.full_messages.join(', ')

      if error.blank?
        user.retailer_users.each do |ru|
          error = ru.errors&.full_messages&.join(', ')
        end
      end

      render status: :internal_server_error, json: { user: user, message: error }
    end

    def mercately_invitation_responses(user)
      if user.nil?
        render_taken_email(user)
      elsif user.persisted?
        render_success_invitation(user)
      else
        render status: :unprocessable_entity, json: { message: user.errors.full_messages.join(', ') }
      end
    end

    def render_taken_email(user)
      render status: :unprocessable_entity, json: { user: user, message: 'Correo ya está asignado a alguien más' }
    end

    def build_retailer_user(invitation_params)
      params = {
        current: true,
        phone: invitation_params[:phone],
        retailer_id: current_retailer.id,
        team_retailer_users_attributes: invitation_params[:team_retailer_users_attributes] || []
      }
      return params.merge(role_id: invitation_params[:role_id]) if roles_active?

      params.merge(old_retailer_user_params(invitation_params))
    end

    def old_retailer_user_params(invitation_params)
      %i[
        retailer_admin retailer_supervisor only_assigned allow_import allow_export allow_bots_edit allow_edit_orders see_phone_numbers
      ].index_with { |key| invitation_params[key] || false }
    end

    def roles_active?
      ENV.fetch('ROLES_ACTIVE', false).present?
    end
end
# rubocop:enable Rails/ModuleLength
