.message .main-message-container {
  display: inline-block;
  vertical-align: top;
  max-width: 75%;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  padding: 12px;

  &.no-background {
    background: unset !important;
  }

  &.media-container {
    padding: 0;
    width: 300px;
    min-width: auto !important;
  }

  .video-message {
    background: $whatsapp-caption-background;
    border-radius: 14px;
    
    .media-caption {
      border-bottom-left-radius: 14px !important;
      border-bottom-right-radius: 14px !important;
    }
  }

  .video-content {
    position: relative;
    border-radius: 9px;
    overflow: hidden;
    border: 5px solid $whatsapp-caption-background;

    video {
      max-height: 200px;
    }
  }

  .audio-content {
    position: relative;
  }

  .media-content {
    position: relative;
    height: 300px;
    border: 5px solid #F1F0F0;
    border-radius: 14px;
  }

  .error-media-content {
    background: #F1F0F0;
  }

  .media-caption {
    background: #F1F0F0;
    border-radius: 0 0 20px 20px;
    padding: 6px 14px 6px 14px;
    color: initial;

    &.media-caption-template {
      background: unset;
      color: unset;
    }
  }

  .img-holder {
    width: 300px;

    .media-caption {
      border-bottom-left-radius: 14px !important;
      border-bottom-right-radius: 14px !important;
    }
  }

  .img-holder:has(.media-caption) {
    .media-content {
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
      background: $whatsapp-caption-background;
    }
  }

  .preview-holder {
    width: 300px;
  }

  .msg__img {
    object-fit: cover;
    width: 100%;
    height: 100%;
    max-width: unset !important;
    max-height: unset !important;
    border-radius: 9px;
  }

  %media-message-status {
    position: absolute;
    right: 4px;
    bottom: 0;
  }

  .img-video-message-status {
    @extend %media-message-status;
    text-shadow: 0 0 4px rgba(0, 0, 0, 1);

    span {
      color: $white;
    }
  }

  .audio-message-status {
    text-align: right;

    span,
    i,
    small {
      color: #646464;
    }
  }

  span.inbound-media {
    color: #FFF;
  }

  &.audio-background {
    background: #F1F3F4 !important;
  }
}

@media only screen and (max-width: 1280px) {
  .message .main-message-container {
    max-width: 50%;
  }
}

.message-by-retailer {
  background: #3CAAE1;
  color: #FFF;
  overflow-wrap: break-word;
  word-wrap: break-word;

  .img-holder {
    text-align: right;
  }

  &.read-message {
    background-color: #00A652 !important;
  }

  i.checks-mark {
    font-size: 0.7em;

    &.stroke {
      color: #3d8bfc;
      text-shadow:
        -1px -1px 0 #fff,
        1px -1px 0 #fff,
        -1px 1px 0 #fff,
        1px 1px 0 #fff;
    }

    &.full {
      color: #fff;
      text-shadow: none;
    }
  }

  a {
    color: #FFF;
  }

  .caption {
    background: #3d8bfc !important;
    border-radius: 20px;
    padding: 6px 14px 6px 14px;
  }

  &.error-message {
    background-color: #ff0000ad !important;

    &.audio-background {
      background-color: #ff0000ad !important;
      color: #fff;
    }

    .media-content {
      border-color: #ff0000ad !important;
    }

    .media-caption {
      background-color: #ff0000ad !important;
      color: #fff;
    }
  }

  .media-caption {
    a {
      color: initial;
    }
  }
}

div.message-content {
  ul {
    list-style: disc;
  }
  ul, ol {
    margin: 5px;
    li {
      margin-left: 15px;
    }
  }
  ol {
    list-style: auto;
  }
}

.message-by-customer {
  background: $light-grey;
  min-width: 35%;
  overflow-wrap: break-word;
  word-wrap: break-word;

  .caption {
    background: $light-grey-secondary;
    border-radius: 20px;
    padding: 6px 14px 6px 14px;
  }

  .img-holder {
    text-align: left;
  }
}
.ws-bg {
  background-color: $whatsapp-background;
}

.chat-messages-whatsapp {
  @media screen and (max-height: 768px) {
    .text-pre-line {
      font-size: 12px !important;
    }
  }

  .chat__box {
    background: $whatsapp-background !important;
  }

  .message {
    color: $whatsapp-text-color !important;
    a {
      color: inherit !important;
    }
  }

  .img-holder {
    background: $white !important;
    border-radius: 14px;
  }

  .message-by-retailer {
    background: $whatsapp-retailer-background;
    .replied-message {
      background: $whatsapp-secondary !important;
      opacity: 0.7;
      border-radius: 4px !important;
      padding: 6px 0px 6px 0px !important;
    }
    &.error-message {
      color: #FFFFFF !important;
      .message-status {
        color: #FFFFFF;
      }
    }
  }

  .message-by-customer {
    background: $white !important;
    .replied-message {
      background: $light-grey-tertiary !important;
      opacity: 0.7;
      border-radius: 4px !important;
      padding: 6px 0px 6px 0px !important;
    }
  }

  .message-by-customer, .message-by-retailer {
    color: $whatsapp-text-color !important;
  }

  .main-message-container {
    -webkit-box-shadow: 0px 1px 4px 0px $message-container-color;
    -moz-box-shadow: 0px 1px 4px 0px $message-container-color;
    box-shadow: 0px 1px 4px 0px $message-container-color;
  }

  .main-message-container:has(.img-holder, .video-content) {
    border-radius: 14px;
  }

  .media-content {
    border-color: $whatsapp-caption-background !important;
  }

  .media-caption {
    background: $whatsapp-caption-background !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    border-radius: 0 !important;
  }

  .document-message:has(.media-caption) {
    .media-caption {
      background: unset !important;
      padding: 0;
    }
  }

  .note-message {
    background-color: $note-message-background-color !important;
    color: $black !important;
  }

  .note-message-xs {
    margin-bottom: -25px;
    padding-right: 90px !important;
  }

  .note-message-btn {
    margin-left: 880px;
  }

  .limit-note {
    position: absolute;
    right: 25px;
    bottom: 56px;
    font-size: 12px;
    color: grey;
    text-align: right;
  }

  &.shadow {
    box-shadow: 0px 4px 8px 0px #00000014;
  }

  .message-actions {
    background-color: white;
    border-radius: 9999px; /* pill shape */
    border: 1px solid #ddd;
    padding: 3px 6px;
    gap: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  }
}

.mia-message {
  background-color: #FFFFFF !important;
}

.messaging-container {
  .note-message {
    background-color: $note-message-background-color;
    color: $black;
    -webkit-box-shadow: 0px 1px 4px 0px $message-container-color;
    -moz-box-shadow: 0px 1px 4px 0px $message-container-color;
    box-shadow: 0px 1px 4px 0px $message-container-color;

    .alert-warning {
      color: $warning-color-secondary;
      background-color: inherit;
      border-color: inherit;
      border: 0;
      padding: 0;
    }
  }

  .mia-internal-message {
    background-color: #F8F5FF;
    border-radius: 0.75rem;
    padding: 0.75rem;
    margin: 0.25rem;

    .alert{
      padding: 0.25rem 1.5rem 0.25rem 0.5rem;
      background-color: white;
      border-radius: 0.75rem;
      margin-bottom: 0;
      color: #767B7D;
      font-size: 0.625rem;
      line-height: 0.875rem;
      font-weight: 600;
      font-family: 'Poppins-SemiBold';
    }

    .text{
      font-size: 12px;
      line-height: 16px;
      align-content: center;

      span.message-key {
        font-family: 'Poppins-Bold' !important;
      }
    }

    .d-flex {
      display: flex;
      align-items: center;
      padding: 0.5rem 0;
      gap: 0.625rem;

      img {
        width: auto;
        height: auto;
        max-width: 40px;
        max-height: 40px;
        object-fit: contain;
      }
    }
  }
}

.chat-messages-whatsapp, .note-message {
  .message-status {
    color: $text-grey;
  }

  .img-video-message-status {
    .message-status {
      color: $white;
    }
  }
}

.media-content-ia {
  position: relative;
  border: none;
  border-radius: 14px 14px 0 0;
  background: white;
  padding: 8px;
  height: auto;
}

.media-caption-ia {
  background: white;
  padding: 8px 12px;
  border-radius: 0 0 14px 14px;
  font-size: 14px;
  line-height: 1.4;
}
