.register-background-left {
  background: #EBF7FC;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  position: relative;

  .text-overlay-left {
    margin: 0;
    color: #3C4348;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;

    .text-heading {
      font-family: 'Poppins-Bold';
      font-size: 24px;
      line-height: 34px;
      padding-left: 32px;
    }

    .text-descriptions {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-left: 32px;
      padding-right: 8px;

      .description-item {
        display: flex;
        align-items: center;
        width: 100%;
        margin-top: 24px;

        img {
          position: relative;
          max-height: 24px !important;
          max-width: 24px !important;
          margin-right: 8px;
        }

        span {
          font-weight: 400;
          font-size: 16px;
          line-height: 26px;
        }
      }

      .applications {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 16px;

        img {
          position: relative;
          max-height: 45.99px !important;
          max-width: 45.99px !important;
          margin-right: 16px;
          background-color: #fff;
          padding: 6px;
          border-radius: 5px;
        }

        img:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .bottom-image {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;

    img {
      max-width: 80%;
      height: auto;
    }
  }
}


.center-div {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;

  form {
    width: 70%;
  }

  .link-text {
    color: #808191;
    font-size: 14px;
    line-height: 24px;

    a {
      color: #3CAAE1;
    }
  }

  .profile-picture {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}

.start-div {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  height: 100vh;

  form {
    width: 70%;

    .title-input {
      font-family: 'Poppins-SemiBold';
      font-size: 16px;
      line-height: 26px;
      font-weight: 600;
    }
  }
}

.register-container {
  background-color: #FFFFFF;
  height: 100vh;
  overflow-y: auto;

  .btn-submit {
    background-color: #3CAAE1;
    color: #FFFFFF;
    border-radius: 12px;
    border: none;
    padding: 10px 20px;
    letter-spacing: 0.06em;
    outline: none;
    min-height: 48px; // Ensure minimum touch target size
    
    // Increase touch target on mobile
    @media screen and (max-width: 767px) {
      min-height: 48px;
      padding: 12px 20px;
    }
  }

  .btn-disabled {
    background-color: #E4E4E4;
    color: #808191;
    border-radius: 12px;
    border: none;
    padding: 10px 20px;
    letter-spacing: 0.06em;
    outline: none;
    min-height: 48px; // Ensure minimum touch target size
    
    // Increase touch target on mobile
    @media screen and (max-width: 767px) {
      min-height: 48px;
      padding: 12px 20px;
    }
  }

  .greetings-container {
    width: 70%;
    margin-top: 16px;
    margin-bottom: 24px;

    .greetings {
      font-size: 22px;
      font-family: 'Poppins-Bold';
      width: 70%;
    }
  }

  .auxiliar-text {
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
    color: #808191;
    width: 70%;
  }

  .pass-container {
    display: flex;
    width: 100%;
    align-items: center;
    background-color: #F7F8FD;
    
    .password-input {
      background: transparent;
      border: none;
      outline: none;
      font-size: 14px;
      padding: 8px 0;
      height: 25px;
      width: 100%;
      color: #3C4348;
      
      // Match other inputs styling
      &::placeholder {
        color: #808191;
        font-size: 14px;
      }
      
      // Increase touch target on mobile
      @media screen and (max-width: 767px) {
        height: 32px;
        padding: 8px 0;
      }
    }

    .eye-icon {
      min-width: 24px;
      min-height: 24px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #808191;
      flex-shrink: 0;
      margin-left: 8px;
      
      &:hover {
        color: #3CAAE1;
      }
      
      // Ensure adequate touch target on mobile
      @media screen and (max-width: 767px) {
        min-width: 32px;
        min-height: 32px;
        width: 32px;
        height: 32px;
      }
    }
  }

  .phone-container {
    .country {
      width: 20%;
      margin-right: 2%;
      
      // Increase touch target on mobile
      @media screen and (max-width: 767px) {
        min-height: 48px;
      }
    }

    .phone {
      width: 78%;
      
      // Increase touch target on mobile
      @media screen and (max-width: 767px) {
        min-height: 48px;
      }
    }
  }

  .welcome-row {
    width: 70%;
    margin-bottom: 20px;

    .welcome-image-container {
      background: #F7F8FD;
      border-radius: 12px;
      padding: 10px;
      margin-right: 20px;
      max-width: 60px;

      img {
        width: 60px;
        height: 40px;
      }
    }

    .welcome-text-container {
      color: #808191;
      font-size: 15px;
      line-height: 25px;
    }
  }

  .welcome-text-final {
    font-size: 14;
    line-height: 24px;
    color: #3CAAE1;
    width: 70%;

    span {
      font-family: 'Poppins-Bold';
    }
  }

  .begin-container {
    width: 70%;
    
    a {
      text-decoration: none;
    }
  }

  .register-back-link {
    font-size: 16px;
    line-height: 26px;
    color: #3C4348;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    min-height: 48px; // Ensure minimum touch target size
    padding: 8px 12px;

    :hover {
      text-decoration: none;
    }

    span {
      margin-left: 15px;
    }
  }
}

.bar-container {
  margin-top: 28px;
  margin-bottom: 20px;
  width: 70%;

  .bar {
    border: 2px solid;
    border-radius: 8px;
    width: calc(100%/3 - 2%);

    &.active {
      border-color: #3CAAE1;
    }

    &.inactive {
      border-color: #EBF7FC;
    }
  }

  .separation {
    margin-left: 3%;
  }
}

.check-mercately-container {
  display: flex;
  margin-bottom: 1%;

  .check-mercately-column {
    flex: 1;

    .check-mercately {
      box-sizing: border-box;
      border: 1px solid #E4E4E4;
      border-radius: 8px;
      height: 64px;
      color: #3C4348;
      display: flex;
      position: relative;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .label-center {
        text-align: center;
        margin-left: 10px;
        font-size: 14px;
        line-height: 24px;
        color: #3C4348;
        width: 100%;
        margin-bottom: 0px !important;
      }

      .check-mercately-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;

        img {
          margin-left: 10px;
          width: 48px;
          height: 48px;
        }

        .label-left {
          margin-left: 10px;
          font-size: 14px;
          line-height: 24px;
          color: #3C4348;
          margin-bottom: 0px !important;
        }
      }
    }

    .check-crm {
      box-sizing: border-box;
      border: 1px solid #E4E4E4;
      border-radius: 8px;
      height: 112px;
      color: #3C4348;
      display: flex;
      position: relative;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .check-crm-platform {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;

        img {
          width: 48px;
          height: 48px;
        }

        label {
          font-size: 14px;
          line-height: 24px;
          color: #3C4348;
        }

        .platform-woo{
          width: 48px;
          height: 32.53px;
        }

        .platform-nube{
          width: 40px;
          height: 30px;
        }

        .platform-odoo{
          width: 40px;
          height: 40px;
        }

        .platform-magento{
          width: 44.45px;
          height: 48px;
        }
      }
    }
  }
}

.checkmark-container {
  position: absolute;
  display: flex;
  justify-content: center;
  left: auto;
  right: 0;
  z-index: 1;
  height: 100%;
  width: 24px;
  padding: 3px;
  top: 10px;
  right: 10px;

  .checkmark {
    display: none;
    width: 24px;
    height: 24px;
  }
}

.selected-border {
  border: 2px solid #3CAAE1 !important;
}

.selected-option::before {
  content: '•';
  // color: #007bff;
  margin-right: 8px;
}

._dropdown {
  &__button {
    background-color: transparent;
    display: inline-block;
    padding: 8px 12px;
    border: 0;
    text-decoration: none;
    color: #3C4348 !important;
    cursor: pointer;
  }

  &__menu {
    position: absolute;
    border: 1px solid #CCC;
    border-radius: 16px;
    padding: 0;
    margin: 2px 0 0 0;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.1);
    background-color: #FFF;
    list-style-type: none;
    right: 0;
    z-index: 1000;
    width: 100%;
    display: none; // Oculto por defecto
    opacity: 0;
    transition: opacity 0.3s ease;

    &.visible {
      display: block; // Mostrar cuando está visible
      opacity: 1;
    }

    li {
      padding: 10px 20px;
      cursor: pointer;

      &:hover {
        background-color: #f6f6f6;
        border-radius: 16px;
      }

      a {
        display: block;
        text-decoration: none;
        color: #808191 !important;
      }

      &.divider {
        margin: 0;
        padding: 0;
        border-bottom: 1px solid #cccccc;
      }
    }
  }
}

.hidden-select {
  display: none !important;
}

@media screen and (max-width: 767px) {
  .register-background-left {
    display: none;
  }

  .center-div {
    form {
      width: 80%;
    }
  }

  .register-container {
    .greetings-container {
      width: 80%;

      .greetings {
        width: 80%;
      }
    }

    .auxiliar-text {
      width: 80%;
    }

    .welcome-row {
      width: 80%;
    }

    .welcome-text-final {
      width: 80%;
    }

    .begin-container {
      width: 80%;
    }

    .phone-container {
      .country {
        width: 40%;
      }
  
      .phone {
        width: 58%;
      }
    }
  }

  .bar-container {
    width: 80%;
  }
}
