// Optimized registration.js for better performance
// Use modern JavaScript features and minimize DOM manipulation

// Wrap in DOMContentLoaded for better performance
document.addEventListener('DOMContentLoaded', function() {
  // Cache DOM elements
  const passwordInput = document.getElementById('password-input');
  const eyeIcon = document.getElementById('eye-icon');
  
  // Only initialize if elements exist
  if (passwordInput && eyeIcon) {
    eyeIcon.addEventListener('click', togglePassword);
  }
  
  // Initialize UTM tracking
  initUTMTracking();
});

// Optimized password toggle function
function togglePassword() {
  const passwordInput = document.getElementById('password-input');
  const eyeIcon = document.getElementById('eye-icon');
  
  if (!passwordInput || !eyeIcon) return;
  
  if (passwordInput.type === "password") {
    // Show password -> show open eye (no slash)
    passwordInput.type = 'text';
    eyeIcon.classList.remove('fa-eye-slash');
    eyeIcon.classList.add('fa-eye');
  } else {
    // Hide password -> show closed eye (with slash)
    passwordInput.type = 'password';
    eyeIcon.classList.remove('fa-eye');
    eyeIcon.classList.add('fa-eye-slash');
  }
}

// Initialize UTM tracking with performance optimization
function initUTMTracking() {
  // Use requestIdleCallback for better performance
  if (window.requestIdleCallback) {
    window.requestIdleCallback(processUTMParams);
  } else {
    // Fallback for browsers that don't support requestIdleCallback
    setTimeout(processUTMParams, 100);
  }
}

function processUTMParams() {
  const params = new URLSearchParams(window.location.search);
  const utmKeys = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];

  utmKeys.forEach(key => {
    const urlValue = params.get(key);
    if (urlValue && !localStorage.getItem(key)) {
      localStorage.setItem(key, urlValue);
    }
  });
}

// Preload critical images for better LCP
function preloadCriticalImages() {
  const criticalImages = [
    'Checkmark-circle-2-fill.png',
    'logo-mercately-h.png'
  ];
  
  criticalImages.forEach(imagePath => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = '/assets/' + imagePath;
    document.head.appendChild(link);
  });
}

// Initialize preloading
preloadCriticalImages();
