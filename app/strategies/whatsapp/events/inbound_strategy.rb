# frozen_string_literal: true

module Whatsapp
  module Events
    class InboundStrategy < Strategy
      attr_reader :retailer, :params, :type, :from, :customer, :qr_params

      def initialize(retailer:, params:, from: 'gupshup', qr_params: nil)
        @retailer = retailer
        @params = params
        @from = from
        @qr_params = qr_params
      end

      def call
        return if retailer.blank? || params.blank?

        process
      end

      private

        def process
          # Find or Store the client
          @customer = find_or_save_customer
          return if customer.blank? || customer.blocked?

          return log_unsupported_message if unsupported_message?

          gwm = handle_message_type
          return if gwm.blank?

          # Broadcast to the proper chat
          Notifications::Web::Messages.new(gwm).broadcast!
        rescue StandardError => e
          handle_error(e)
        end

        def find_or_save_customer
          if params.try(:[], 'from_group')
            save_group
          else
            save_customer
          end
        end

        def handle_message_type
          if params.dig(:payload, :type) == 'reaction'
            handle_reaction_message
          else
            create_new_message
          end
        end

        def handle_reaction_message
          inbound = GupshupWhatsappMessage.find_by(
            'whatsapp_message_id = :id', id: params.dig(:payload, :payload, :id)
          )
          return if inbound.blank?

          inbound.update(reaction: params.dig(:payload, :payload, :emoji))
          inbound
        end

        def create_new_message
          payload = Whatsapp::Utils::InboundPayloadBuilder.call(
            retailer: retailer,
            customer: customer,
            params: params,
            from: from
          )
          # Store in our database the incoming text message
          gwm = GupshupWhatsappMessage.create!(payload)
          Rails.logger.info("New inbound message #{gwm.id} from #{from}")
          gwm
        end

        def handle_error(error)
          Rails.logger.error(error)
          Rails.logger.info(error.backtrace.slice(0, 10))
          SlackError.send_error(error)
          false
        end

        def save_customer
          Whatsapp::Helpers::Customers.save_customer(
            retailer,
            params.merge(direction: 'inbound'),
            from
          )
        end

        def save_group
          Whatsapp::Helpers::Groups.save_group(
            retailer,
            (qr_params || params).merge(direction: 'inbound'),
            from
          )
        end

        def unsupported_message?
          params.dig('payload', 'type') == 'unsupported'
        end

        def log_unsupported_message
          message_type = params.dig('payload', 'type')
          code = params.dig('payload', 'payload', 'code')
          reason = params.dig('payload', 'payload', 'reason')

          Rails.logger.info "Unsupported inbound message type '#{message_type}' - Code: #{code}, Reason: #{reason}"
          nil
        end
    end
  end
end
