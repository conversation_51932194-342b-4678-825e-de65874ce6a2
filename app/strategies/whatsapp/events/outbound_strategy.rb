# frozen_string_literal: true

module Whatsapp
  module Events
    class OutboundStrategy < Strategy
      attr_reader :retailer, :params, :type, :from, :customer, :qr_params

      def initialize(retailer:, params:, from: 'gupshup', qr_params: nil)
        @retailer = retailer
        @params = params
        @from = from
        @qr_params = qr_params
      end

      def call
        return unless valid_inputs?

        process
      end

      private

        def valid_inputs?
          retailer.present? && params.present?
        end

        def process
          @customer = save_customer
          return if customer.blank?
          return log_unsupported_message if unsupported_message?

          gwm = handle_message_type
          return unless gwm

          broadcast_message(gwm)
        rescue StandardError => e
          handle_error(e)
        end

        def handle_message_type
          if reaction_message?
            handle_reaction_message
          else
            create_outbound_message
          end
        end

        def reaction_message?
          params.dig(:payload, :type) == 'reaction'
        end

        def handle_reaction_message
          inbound = find_original_message
          return unless inbound

          update_message_reaction(inbound)
          inbound
        end

        def find_original_message
          GupshupWhatsappMessage.find_by(
            'whatsapp_message_id = ?',
            params.dig(:payload, :payload, :id)
          )
        end

        def update_message_reaction(message)
          emoji = params.dig(:payload, :payload, :emoji)
          message.update(reaction: emoji)
        end

        def create_outbound_message
          payload = build_message_payload
          gwm = GupshupWhatsappMessage.create!(payload)

          log_message_creation(gwm)
          gwm
        end

        def build_message_payload
          Whatsapp::Utils::OutboundPayloadBuilder.call(
            retailer: retailer,
            customer: customer,
            params: params,
            from: from
          )
        end

        def log_message_creation(gwm)
          Rails.logger.info("New outbound message #{gwm.id} from #{from}")
        end

        def broadcast_message(gwm)
          Notifications::Web::Messages.new(gwm).broadcast!
        end

        def handle_error(error)
          Rails.logger.error(error)
          Rails.logger.info(error.backtrace.slice(0, 10))
          SlackError.send_error(error)
          false
        end

        def save_customer
          Whatsapp::Helpers::Customers.save_customer(
            retailer,
            params.merge(direction: 'outbound'),
            from
          )
        end

        def unsupported_message?
          params.dig('payload', 'type') == 'unsupported'
        end

        def log_unsupported_message
          message_type = params.dig('payload', 'type')
          code = params.dig('payload', 'payload', 'code')
          reason = params.dig('payload', 'payload', 'reason')

          Rails.logger.info "Unsupported outbound message type '#{message_type}' - Code: #{code}, Reason: #{reason}"
          nil
        end
    end
  end
end
