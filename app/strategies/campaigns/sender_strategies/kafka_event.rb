# Require the constants file explicitly
module Campaigns
  module SenderStrategies
    # Sends messages by publishing an event to <PERSON><PERSON><PERSON>.
    #
    # This strategy publishes a campaign_started_event to <PERSON><PERSON><PERSON>, which will trigger
    # the asynchronous processing of the campaign by Kafka consumers.
    #
    # @example
    #   strategy = Campaigns::SenderStrategies::KafkaEvent.new
    #   success = strategy.call(campaign: campaign)
    #
    # @return [Boolean] True if the event was published successfully, false otherwise.
    # rubocop:disable Metrics/ClassLength
    class KafkaEvent < Base
      attr_reader :logger_service

      TOPIC = 'mercately_campaign_events'.freeze

      # Initializes the EventSenderStrategy.
      #
      # @param logger [Logger] Logger instance (default: Rails.logger).
      def initialize(logger_service: Rails.logger)
        @logger_service = logger_service
      end

      private

      # Construye la configuración de seguridad usando variables de ambiente
      def build_security_config
        {
          'security.protocol': ENV.fetch('KAFKA_SECURITY_PROTOCOL', 'SASL_SSL'),
          'sasl.mechanisms': ENV.fetch('KAFKA_SASL_MECHANISM', 'SCRAM-SHA-256'),
          'sasl.username': ENV.fetch('KAFKA_USERNAME', 'karafka'),
          'sasl.password': ENV.fetch('KAFKA_PASSWORD', ''),
          'ssl.ca.location': find_ca_certificate
        }
      end

      # Encuentra un certificado CA válido
      def find_ca_certificate
        default_cert = ENV.fetch('KAFKA_CA_CERT', '/home/<USER>/public_html/shared/config/ca-certificate.crt')

        return default_cert if File.exist?(default_cert)

        Rails.logger.error("❌ [KAFKA-SENDER] El certificado CA no existe en la ruta: #{default_cert}")

        # Intentar buscar el certificado en otras ubicaciones comunes
        alternative_paths = [
          '/etc/ssl/certs/ca-certificates.crt',
          '/etc/pki/tls/certs/ca-bundle.crt',
          '/etc/ssl/ca-bundle.pem',
          '/etc/pki/tls/cacert.pem',
          '/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem'
        ]

        alternative_cert = alternative_paths.find { |path| File.exist?(path) }
        if alternative_cert
          Rails.logger.info("🔄 [KAFKA-SENDER] Usando certificado CA alternativo: #{alternative_cert}")
          alternative_cert
        else
          Rails.logger.error('❌ [KAFKA-SENDER] No se encontró ningún certificado CA alternativo')
          default_cert # Devolver el default aunque no exista para que falle explícitamente
        end
      end

      # Valida que un payload sea JSON válido
      def validate_json_payload(payload)
        JSON.parse(payload.to_json)
        Rails.logger.info("✅ [KAFKA-SENDER] Payload JSON válido")
        true
      rescue JSON::ParserError => e
        Rails.logger.error("❌ [KAFKA-SENDER] El payload no es JSON válido: #{e.message}")
        false
      end

      # Configura un productor manualmente cuando la configuración está vacía
      def configure_manual_producer
        Rails.logger.error('❌ [KAFKA-SENDER] Configurando manualmente la configuración de seguridad')

        security_config = build_security_config

        # Verificar que el certificado CA exista
        unless File.exist?(security_config[:'ssl.ca.location'])
          Rails.logger.error('❌ [KAFKA-SENDER] No se encontró certificado CA válido')
          return nil
        end

        # Configurar el productor con override
        producer = Mercately::Kafka::Producer.new(config_override: security_config)
        Rails.logger.info("🔄 [KAFKA-SENDER] Configuración aplicada via config_override: #{security_config.keys.join(', ')}")
        producer
      end

      public

      # Publishes a campaign_started_event to Kafka.
      #
      # @param campaign [Campaign] The campaign instance.
      # @return [Boolean] True if the event was published successfully, false otherwise.
      def call(campaign:)
        Rails.logger.info("🚀 [KAFKA-SENDER] Preparando envío de campaña: #{campaign.id} a Kafka")

        # Verificar si la gema mercately-kafka está disponible
        unless defined?(Mercately::Kafka::ProducerPool)
          Rails.logger.error('❌ [KAFKA-SENDER] La gema mercately-kafka no está disponible')
          return false
        end

        # Verificar la configuración del productor
        Rails.logger.info('🔍 [KAFKA-SENDER] Verificando configuración de Mercately::Kafka...')
        Rails.logger.info("🔍 [KAFKA-SENDER] Mercately::Kafka definido: #{defined?(Mercately::Kafka)}")
        Rails.logger.info('🔍 [KAFKA-SENDER] Mercately::Kafka.configuration definido: ' \
                          "#{Mercately::Kafka.respond_to?(:configuration)}")

        producer_config = Mercately::Kafka.configuration.producer_config
        Rails.logger.info("🔍 [KAFKA-SENDER] producer_config class: #{producer_config.class}")
        Rails.logger.info("🔍 [KAFKA-SENDER] producer_config size: #{producer_config.size}")
        Rails.logger.info("🔍 [KAFKA-SENDER] producer_config keys: #{producer_config.keys.inspect}")

        # Si la configuración está vacía, forzar recarga
        if producer_config.empty?
          Rails.logger.warn('⚠️ [KAFKA-SENDER] Configuración vacía, forzando recarga...')
          load Rails.root.join('config/initializers/mercately_kafka.rb')
          producer_config = Mercately::Kafka.configuration.producer_config
          Rails.logger.info('🔄 [KAFKA-SENDER] Después de recarga - ' \
                            "size: #{producer_config.size}, keys: #{producer_config.keys.inspect}")
        end

        producer = nil

        if producer_config.empty?
          producer = configure_manual_producer
          return false unless producer
        else
          Rails.logger.info("✅ [KAFKA-SENDER] Configuración del productor: #{producer_config.keys.join(', ')}")
          # Usar el pool si la configuración está disponible
          producer = Mercately::Kafka::ProducerPool.get_producer

          # Verificar si el productor está en estado de error
          if producer.respond_to?(:error_state?) && producer.error_state?
            Rails.logger.error("❌ [KAFKA-SENDER] Productor obtenido del pool está en estado de error")
            return false
          end
        end

        # Construir payload del evento de inicio de campaña
        event_payload = {
          event_type: 'campaign_started_event',
          campaign_id: campaign.id,
          retailer_id: campaign.retailer_id,
          campaign_web_id: campaign.web_id,
          estimated_recipients: campaign.customers.count,
          timestamp: Time.current.to_i
        }

        Rails.logger.info("🔄 [KAFKA-SENDER] Payload del evento: #{event_payload.to_json}")

        # El productor ya está configurado arriba
        Rails.logger.info('🔄 [KAFKA-SENDER] Usando productor configurado')

        begin
          # Verificar si el payload es válido JSON
          return false unless validate_json_payload(event_payload)

          # Verificar que el productor esté disponible
          unless producer
            Rails.logger.error("❌ [KAFKA-SENDER] Productor no disponible para campaña #{campaign.id}")
            return false
          end

          # Enviar evento de inicio de campaña con reintentos
          Rails.logger.info('🔄 [KAFKA-SENDER] Enviando evento a topic: ' \
                            "#{Campaigns::SenderStrategies::KafkaEvent::TOPIC}")

          retry_count = 0
          max_retries = 3

          begin
            result = producer.produce(
              topic: Campaigns::SenderStrategies::KafkaEvent::TOPIC,
              payload: event_payload.to_json,
              key: campaign.id.to_s # Clave de partición basada en ID de campaña
            )

            Rails.logger.info("🔄 [KAFKA-SENDER] Resultado de la producción: #{result.inspect}")

            # Verificar que el resultado sea válido
            if result.nil?
              Rails.logger.error("❌ [KAFKA-SENDER] Resultado de producción es nil para campaña #{campaign.id}")
              return false
            end

            Rails.logger.info('✅ [KAFKA-SENDER] Mensaje enviado a Kafka (entrega asíncrona)')
            Rails.logger.info('✅ [KAFKA-SENDER] Evento de inicio enviado a Kafka exitosamente ' \
                              "para campaña #{campaign.id}")

            # Marcar la campaña como enviada por Kafka (después del envío exitoso, sin posibilidad de fallback)
            campaign.update!(sender_strategy: :kafka)
            Rails.logger.info("✅ [KAFKA-SENDER] Campaña #{campaign.id} marcada como sender_strategy: kafka")

            true
          rescue StandardError => e
            retry_count += 1
            Rails.logger.warn('⚠️ [KAFKA-SENDER] Intento ' \
                              "#{retry_count}/#{max_retries} falló para campaña #{campaign.id}: #{e.message}")

            if retry_count < max_retries
              sleep(0.5 * retry_count) # Backoff exponencial
              retry
            else
              Rails.logger.error("❌ [KAFKA-SENDER] Todos los reintentos fallaron para campaña #{campaign.id}")
              raise e
            end
          end
        rescue StandardError => e
          Rails.logger.error("❌ [KAFKA-SENDER] Error al enviar evento a Kafka - Campaña: #{campaign.id}: #{e.message}")
          Rails.logger.error("❌ [KAFKA-SENDER] Backtrace: #{e.backtrace.join("\n")}")
          false
        ensure
          # Solo devolver al pool si vino del pool (no si fue creado manualmente)
          if producer_config.present?
            Mercately::Kafka::ProducerPool.release_producer(producer)
            Rails.logger.info('🔄 [KAFKA-SENDER] Productor devuelto al pool')
          else
            Rails.logger.info('🔄 [KAFKA-SENDER] Productor manual no requiere devolución al pool')
          end
        end
      end


    end
    # rubocop:enable Metrics/ClassLength
  end
end
