module ValidatableWhatsappMessageConcern
  extend ActiveSupport::Concern

  class_methods do
    def content_type(message)
      message.type
    end

    def payload_present?(message)
      message.message_payload.present?
    end

    def text?(message)
      %w[text quick_reply list list_reply button_reply].include?(content_type(message))
    end

    def media?(message)
      %w[image audio video file sticker].include?(content_type(message)) || message.has_referral_media?
    end

    def file?(message)
      content_type(message) == 'file'
    end

    def location?(message)
      content_type(message) == 'location'
    end

    def contact?(message)
      content_type(message) == 'contact'
    end

    def inbound?(message)
      message.direction == 'inbound'
    end

    def outbound?(message)
      message.direction == 'outbound'
    end

    def replied?(message)
      return false if message.customer_id.blank?

      payload = message.message_payload
      payload.try(:[], 'payload').try(:[], 'context').try(:[], 'id').present? ||
        payload.try(:[], 'payload').try(:[], 'context').try(:[], 'gsId').present? ||
        payload.try(:[], 'context').try(:[], 'msgId').present?
    end

    def payload_attribute(type)
      case type
      when 'list_reply', 'button_reply'
        'title'
      else
        'text'
      end
    end

    def payload_message(message, type)
      case type
      when 'list'
        message['body'] || ''
      when 'quick_reply'
        message['content'].try(:[], 'text') || message['payload'].try(:[], 'payload').try(:[], 'text') || ''
      else
        message['payload'].try(:[], 'payload').try(:[], payload_attribute(type)) || message['text']
      end
    end

    def options_list(message)
      return {} if message.direction == 'inbound'

      case message.type
      when 'list', 'quick_reply'
        message.message_payload
      else
        {}
      end
    end

    def include_mentions?(message)
      return false unless message.present?

      message.include? '@'
    end

    def replace_mentions(message, participants, retailer)
      return message unless retailer.qr_integrated? && participants.any? && message.present?

      participants.each do |keyword|
        phone = keyword.split('@').first
        original_mention = "@#{phone}"
        message = replace_mention(message, original_mention, phone, retailer) if message.include?(original_mention)
      end
      message
    end

    def replace_mention(text, mention, phone, retailer)
      if phone == retailer.qr_phone_number
        text.gsub!(mention, "@#{retailer.name}")
      else
        customer = retailer.customers.find_by_phone("+#{phone}")
        text.gsub!(mention, "@#{customer.full_names}") if customer.present?
      end
      text
    end

    def build_replied(message)
      context = message.message_payload.try(:[], 'payload').try(:[], 'context')
      return unless context.try(:[], 'type').present?

      GupshupWhatsappMessage.new(
        retailer_id: message.retailer_id,
        customer_id: message.customer_id,
        whatsapp_message_id: message.whatsapp_message_id,
        gupshup_message_id: message.gupshup_message_id,
        status: message.status,
        direction: message.direction,
        message_payload: build_payload(context),
        created_at: Time.current
      )
    end

    def build_payload(context)
      type = context.try(:[], 'type')

      {
        'payload': {
          'id': context.try(:[], 'id'),
          'type': get_message_type(type),
          'payload': format_message_content(context, type)
        }
      }
    end

    def format_message_content(context, type)
      case type
      when 'text'
        { 'text': context.try(:[], 'text') }
      when 'image', 'document', 'audio', 'ptt', 'video', 'sticker'
        {
          'url': context.try(:[], 'url'),
          'contentType': context.try(:[], 'mime'),
          'caption': context.try(:[], 'caption'),
          'name': context.try(:[], 'filename')
        }
      when 'order'
        format_order(context)
      when 'product'
        format_product(context)
      else
        {}
      end
    end

    def no_search_gs_id?(replied, gs_id, is_state)
      replied.present? || gs_id.blank? || is_state
    end

    def get_message_type(type)
      case type
      when 'document'
        'file'
      when 'ptt'
        'audio'
      else
        type
      end
    end

    def get_referral_url(message)
      if message.retailer&.connect_qr_mercately?
        message.referral_media_id
      elsif message.referral_media_id.present?
        "https://filemanager.gupshup.io/fm/wamedia/#{message.retailer.gupshup_src_name&.strip}/#{message.referral_media_id}"
      else
        message.referral_image_or_video_url
      end
    end

    def format_order(context)
      {
        subtotal: context['subtotal'],
        total: context['total'],
        currency: context['currency'],
        products: format_products(context['products'])
      }
    end

    def format_products(products)
      products.map do |product|
        {
          price: product['price'],
          name: product['name'],
          quantity: product['quantity'],
          url: product['url']
        }
      end
    end

    def format_product(context)
      {
        name: context['name'],
        description: context['description'],
        currency: context['currency'],
        price: context['price'],
        url: context['url'],
        text: context['text']
      }
    end
  end
end
