class KarixCustomerSerializer < ActiveModel::Serializer
  has_many :tags, serializer: Api::V1::TagChatSerializer

  attributes :id, :first_name, :last_name, :email, :phone, :address, :city, :state, :zip_code,
             :id_number, :whatsapp_name, :unread_whatsapp_chat, :whatsapp_opt_in, :active_bot, :allow_start_bots,
             :status_chat, :has_deals, :unread_whatsapp_message?, :recent_inbound_message_date, :assigned_agent,
             :last_whatsapp_message, :recent_message_date, :handle_message_events?, :unread_whatsapp_messages,
             :last_messages, :id_in_shops, :your_turn_label, :show_your_turn_label,
             :last_chat_interaction, :last_msn_interaction, :last_ig_interaction, :is_group, :number_to_use, :web_id,
             :customer_mia_chatbots

  delegate :recent_inbound_message_date, to: :object

  def status_chat
    object.multiplatform_decorator.status_chat_for('whatsapp')
  end

  def active_bot
    object.policy.active_bot_for?(platform: :whatsapp)
  end

  def recent_message_date
    object.last_chat_interaction
  end

  def show_your_turn_label
    object.last_whatsapp_message_direction == 'inbound' &&
      !object.whatsapp_resolved? && object.your_turn_label
  end
end
