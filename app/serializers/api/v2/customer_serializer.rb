module Api::V2
  class CustomerSerializer < ActiveModel::Serializer
    attributes :id, :web_id, :full_names, :whatsapp_name, :country_id, :phone, :emoji_flag, :retailer_user_id,
               :whatsapp, :meta, :messenger, :instagram, :whatsapp_opt_in, :email, :created_at

    def whatsapp
      object.whatsapp?
    end

    def meta
      object.meta?
    end

    def messenger
      object.messenger?
    end

    def instagram
      object.instagram?
    end

    def phone
      return object.phone unless current_retailer_user&.agent? && !current_retailer_user&.see_phone_numbers

      object.phone&.slice(0, 7)&.ljust(object.phone&.length, '*')
    end

    private

    def current_retailer_user
      instance_options[:current_retailer_user]
    end
  end
end
