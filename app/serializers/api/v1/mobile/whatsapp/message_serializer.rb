module Api::V1::Mobile::Whatsapp
  class MessageSerializer
    include JSONAPI::Serializer
    include ValidatableWhatsappMessageConcern

    set_type :gupshup_whatsapp_message
    set_id :id

    attributes :id, :customer_id, :status, :direction, :channel, :message_type, :message_identifier,
               :created_time, :replied_message, :sender_full_name, :note, :order_note, :order_summary, :options_list,
               :reaction, :internal_url

    attribute :content_type do |gwm|
      message = gwm.message_payload
      next '' if message.blank?

      with_referral_media = gwm.has_referral_media?
      type = content_type(gwm)
      next 'list' if type == 'list'
      next 'quick_reply' if type == 'quick_reply' && gwm.direction == 'outbound'
      next 'text' if text?(gwm) && !with_referral_media
      next 'media' if media?(gwm)
      next 'location' if type == 'location'
      next 'contact' if type == 'contact'
      next 'order' if type == 'order'
      next 'product' if type == 'product'
    end

    attribute :content_text, if: proc { |record| payload_present?(record) && text?(record) } do |gwm|
      message = gwm.message_payload
      msg = payload_message(message, gwm.type)
      msg += " #{message['payload'].try(:[], 'referral').try(:[], 'source_url')}" if gwm.has_referral?
      msg
    end

    attribute :content_media_url, if: proc { |record| payload_present?(record) && media?(record) } do |gwm|
      next '' if gwm.direction == 'inbound' && gwm.retailer.gupshup_integrated? && gwm.created_at <= 6.days.ago

      message = gwm.message_payload
      url = if gwm.has_referral_media?
              get_referral_url(gwm)
            else
              message&.dig('originalUrl') ||
                message&.dig('payload', 'payload', 'url') ||
                message&.dig('url') || ''
            end
      url&.gsub('http:', 'https:') || ''
    end

    attribute :content_media_caption, if: proc { |record| payload_present?(record) && media?(record) } do |gwm|
      message = gwm.message_payload
      caption = if gwm.has_referral_media?
                  "#{message['payload'].try(:[], 'payload').try(:[], 'text') || message['text']} " \
                    "#{message['payload'].try(:[], 'referral').try(:[], 'source_url')}"
                elsif media?(gwm)
                  message.try(:[], 'caption') ||
                    message.try(:[], 'payload').try(:[], 'payload').try(:[], 'caption')
                end
      caption || ''
    end

    attribute :content_media_type, if: proc { |record| payload_present?(record) && media?(record) } do |gwm|
      with_media = gwm.has_referral_media?
      type = content_type(gwm)
      next '' if type == 'text' && !with_media
      next gwm.referral_type_media if with_media
      next 'document' if type == 'file'

      type
    end

    attribute :content_location_longitude, if: proc { |record|
                                                 payload_present?(record) && location?(record)
                                               } do |gwm|
      message = gwm.message_payload
      gwm.direction == 'inbound' ? message['payload']['payload']['longitude'] : message['longitude']
    end

    attribute :content_location_latitude, if: proc { |record|
                                                payload_present?(record) && location?(record)
                                              } do |gwm|
      message = gwm.message_payload
      gwm.direction == 'inbound' ? message['payload']['payload']['latitude'] : message['latitude']
    end

    attribute :content_location_label, if: proc { |record| payload_present?(record) && location?(record) } do |gwm|
      message = gwm.message_payload
      message['name']
    end

    attribute :content_location_address, if: proc { |record|
                                               payload_present?(record) && location?(record)
                                             } do |gwm|
      message = gwm.message_payload
      message['address']
    end

    attribute :status do |gwm|
      gwm.status.to_s
    end

    attribute :message_type do |_gwm|
      'conversation'
    end

    attribute :created_time, &:created_at

    attribute :contacts_information, if: proc { |record| payload_present?(record) && contact?(record) } do |gwm|
      message = gwm.message_payload
      info = []
      contacts = message.try(:[], 'payload').try(:[], 'payload').try(:[], 'contacts') || []

      contacts.each do |c|
        next if c.blank?

        info << {
          names: c['name'],
          phones: c['phones'],
          emails: c['emails'],
          addresses: c['addresses'],
          org: c['org']
        }
      end

      info
    end

    attribute :replied_message, if: proc { |record|
      record.message_payload.try(:[], 'payload').try(:[], 'context').try(:[], 'id')
    } do |gwm|
      next unless gwm.replied_deep.to_i < 1

      message = gwm.message_payload
      id = message.try(:[], 'payload').try(:[], 'context').try(:[], 'id')
      gs_id = message.try(:[], 'payload').try(:[], 'context').try(:[], 'gsId')
      next unless id.present? || gs_id.present?

      customer = Customer.find_by(id: gwm.customer_id)
      next if customer.blank?

      replied = customer.gupshup_whatsapp_messages.find_by(whatsapp_message_id: id)
      replied = customer.gupshup_whatsapp_messages.find_by(gupshup_message_id: gs_id) unless replied || gs_id.blank?

      next if replied.blank?

      replied.replied_deep = gwm.replied_deep.to_i + 1

      # TODO: Change serializer and pluck attributes
      JSON.parse(
        GupshupWhatsappMessageSerializer.new(
          replied
        ).serializable_hash.to_json
      )
    end

    attribute :filename, if: proc { |record| payload_present?(record) && media?(record) } do |gwm|
      message = gwm.message_payload
      next message.try(:[], 'filename') || message.try(:[], 'payload').try(:[], 'payload').try(:[], 'filename') ||
        message.try(:[], 'payload').try(:[], 'payload').try(:[], 'name')
    end

    attribute :error_message do |gwm|
      ::Whatsapp::Translate.get_error_message(gwm)
    end

    attribute :sender_full_name do |gwm|
      next if gwm.direction == 'inbound' || gwm.retailer_user_id.blank?

      full_name = "#{gwm.sender_first_name} #{gwm.sender_last_name}".strip
      full_name.presence || gwm.sender_email
    end

    attribute :inbound_sender_full_name, if: proc { |record| inbound?(record) }, &:inbound_sender_full_name

    attribute :inbound_sender_number, if: proc { |record| inbound?(record) }, &:inbound_sender_phone

    attribute :options_list do |gwm|
      options_list(gwm)
    end

    attribute :from_group, &:from_group

    attribute :content_ws_order do |gwm|
      type = content_type(gwm)
      if type == 'order'
        message = gwm.message_payload
        message.try(:[], 'payload').try(:[], 'payload')
      end
    end

    attribute :content_ws_product do |gwm|
      type = content_type(gwm)
      if type == 'product'
        message = gwm.message_payload
        message.try(:[], 'payload').try(:[], 'payload')
      end
    end

    attribute :account_uid do |gwm|
      gwm.gupshup_message_id || gwm.message_identifier
    end

    attribute :channel do |_gwm|
      'whatsapp'
    end

    attribute :reaction do |gwm|
      gwm&.reaction
    end
  end
end
