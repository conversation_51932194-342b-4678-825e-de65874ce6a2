module Api::V1::Mobile::Whatsapp
  class UnifiedChatSerializer < ::Api::V1::Mobile::UnifiedWhatsappChatSerializer
    attributes :phone_without_country_code, :email, :id_type, :notes, :id_number, :id_in_shops,
               :whatsapp_opt_in, :address, :city, :state, :zip_code, :country_id, :allow_mia_chatbot

    delegate :phone_without_country_code, to: :object

    def allow_mia_chatbot
      platform = instance_options[:platform].to_s.downcase
      object.customer_mia_chatbots&.any? do |chatbot|
        chatbot.platform.to_s.downcase == platform && chatbot.active
      end
    end
  end
end
