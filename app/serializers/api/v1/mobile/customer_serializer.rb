# frozen_string_literal: true

module Api::V1::Mobile
  class CustomerSerializer < ActiveModel::Serializer
    attributes :id, :first_name, :last_name, :email, :whatsapp_opt_in, :phone, :phone_with_country_code, :open_chat?,
               :is_group, :number_to_use, :retailer_user_id

    def phone
      object.phone_without_country_code
    end

    def phone_with_country_code
      object.phone
    end

    def open_chat?
      object.is_chat_open?
    end
  end
end
