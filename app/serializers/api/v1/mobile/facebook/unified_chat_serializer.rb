module Api::V1::Mobile::Facebook
  class UnifiedChatSerializer < ::Api::V1::Mobile::UnifiedFacebookChatSerializer
    attributes :phone_without_country_code, :phone, :country_id, :id_type, :notes, :id_number, :id_in_shops,
               :whatsapp_name, :whatsapp_opt_in, :recent_inbound_message_date, :is_group,
               :address, :city, :state, :zip_code, :blocked, :allow_mia_chatbot

    delegate :phone_without_country_code, to: :object

    def recent_inbound_message_date
      object.multiplatform_decorator.recent_inbound_message_date_for(@instance_options[:platform])
    end

    def allow_mia_chatbot
      platform = instance_options[:platform].to_s.downcase
      object.customer_mia_chatbots&.any? do |chatbot|
        chatbot.platform.to_s.downcase == platform && chatbot.active
      end
    end
  end
end
