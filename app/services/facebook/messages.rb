require 'mime/types'
require 'open-uri'

module Facebook
  # rubocop:disable Metrics/ClassLength
  class Messages
    def initialize(facebook_retailer, type = 'messenger')
      @type = type
      @facebook_retailer = facebook_retailer
      if @type == 'instagram'
        @klass = InstagramMessage
        @uid = @facebook_retailer&.ig_page_uid
        @access_token = @facebook_retailer&.ig_access_token
      else
        @klass = FacebookMessage
        @uid = @facebook_retailer&.uid
        @access_token = @facebook_retailer&.access_token
      end
    end

    # rubocop:disable Metrics/CyclomaticComplexity
    # rubocop:disable Metrics/PerceivedComplexity
    def save(message_data)
      customer = Facebook::Customers.new(@facebook_retailer, @type).import(message_data['sender']['id'])
      urls_with_file_types = file_type_url(message_data)
      urls_with_file_types.each_with_index do |url_with_file_type, index|
        url, file_type = url_with_file_type
        url, file_type = get_fallback_media_url(url) if file_type == 'fallback'
        file_name = File.basename(URI.parse(url)&.path) if url.present?
        file_type = sniff_mime_type(url) if valid_file_type?(file_type)
        mid = if index.positive?
                message_data['message']['mid'].dup.concat("___#{index}")
              else
                message_data['message']['mid']
              end
        @klass.create(
          customer: customer,
          facebook_retailer: @facebook_retailer,
          sender_uid: message_data['sender']['id'],
          id_client: message_data['sender']['id'],
          text: message_data['message']&.[]('text'),
          file_type: file_type,
          url: url,
          reply_to: message_data['message']&.[]('reply_to')&.[]('mid'),
          filename: file_name,
          mid: mid
        )
      end
    rescue StandardError => e
      Rails.logger.error("Error saving Facebook/Instagram message: #{e.message}")
      Rails.logger.info(e.backtrace.slice(0, 15))
    end
    # rubocop:enable Metrics/CyclomaticComplexity
    # rubocop:enable Metrics/PerceivedComplexity

    def valid_file_type?(file_type)
      (@type == 'instagram' && file_type.in?(%w[story_mention share reply_to reply_ads])) ||
        (@type == 'messenger' && file_type.in?(%w[reply_ads]))
    end

    def import_delivered(message_id, psid)
      url = message_url(message_id)
      conn = Connection.prepare_connection(url)
      response = Connection.get_request(conn)
      save_delivered(response, psid) if response
    end

    def save_delivered(response, psid)
      customer = Customer.find_by(psid: psid, retailer_id: @facebook_retailer.retailer_id)
      attachment = response['attachments']&.[]('data')&.[](0)
      file_type = attachment&.[]('mime_type')
      file_name = attachment&.[]('name')
      attachment_url = grab_url(response, file_type)
      message = @klass.create_with(
        customer: customer,
        facebook_retailer: @facebook_retailer,
        sender_uid: @uid,
        id_client: psid,
        file_type: file_type,
        url: attachment_url,
        text: response['message'],
        filename: file_name
      ).find_or_create_by(facebook_retailer_id: @facebook_retailer.id, mid: response['id'])
      return unless attachment_url && message.url.nil?

      message.update(
        url: attachment_url,
        file_type: file_type,
        filename: message.filename.presence || file_name
      )
    end

    def send_message(to, message, payload = {}, comment_id = nil)
      url = send_message_url
      conn = Connection.prepare_connection(url)
      response = Connection.post_request(conn, prepare_message(to, message, payload, comment_id))
      parsed_response = JSON.parse(response.body)
      if parsed_response.blank?
        SlackError.send_error(
          "send_message_url: #{send_message_url}, " \
          "Customer FB id: #{to}, " \
          "parsed_response: #{parsed_response}, " \
          "response: #{response.body}, " \
          "prepare_message: #{prepare_message(to, message, payload, comment_id)}"
        )
      end
      if parsed_response['message_id'].blank?
        SlackError.send_error("Customer FB id: #{to}, facebook_response: #{parsed_response}")
      end
      parsed_response
    end

    def send_attachment(*params)
      to, file_data, filename, url, file_type, file_content_type, payload, comment_id = params

      conn = Faraday.new(send_message_url) do |f|
        f.request :multipart
        f.request :json
        f.request :url_encoded
        f.adapter :net_http
        f.response :logger, Logger.new($stdout), bodies: true
      end
      response = Connection.post_form_request(
        conn,
        prepare_attachment(to, file_data, filename, url, file_type, file_content_type, payload, comment_id)
      )
      parse_resp = JSON.parse(response.body)
      if parse_resp.blank?
        SlackError.send_error(
          "send_message_url: #{send_message_url}, " \
          "Customer FB id: #{to}, " \
          "parse_resp: #{parse_resp}, " \
          "response: #{response.body}, " \
          "prepare_attachment: #{prepare_attachment(to, file_data, filename, url, file_type, file_content_type,
                                                    payload, comment_id)}"
        )
      end

      if parse_resp['message_id'].blank?
        SlackError.send_error("Customer FB id: #{to}, facebook_attachment_response: #{response.body}")
      end

      parse_resp
    end

    def mark_read(psid)
      customer = Customer.find_by(psid: psid, retailer_id: @facebook_retailer.retailer_id)
      return if customer.blank?

      messages = customer.message_records.where.not(note: true).retailer_unread.order(:id)
      last_message = messages.last
      return if last_message.blank?

      read_date = Time.zone.now
      messages.update_all(date_read: read_date)

      facebook_helper = FacebookNotificationHelper
      retailer = @facebook_retailer.retailer
      last_message.date_read = read_date
      agents = customer.agent.present? ? [customer.agent] : retailer.retailer_users.all_customers.to_a
      facebook_helper.broadcast_data(retailer, agents, last_message, customer.agent_customer, nil, @type)
    end

    def send_read_action(to, action)
      url = send_message_url
      conn = Connection.prepare_connection(url)
      Connection.post_request(conn, prepare_action(to, action))
    end

    def send_bulk_files(customer, retailer_user, params)
      Facebook::Api.new(@facebook_retailer, retailer_user, @type).send_bulk_files(customer, params)
    end

    def send_multiple_answers(customer, retailer_user, params)
      Facebook::Api.new(@facebook_retailer, retailer_user, @type).send_multiple_answers(customer, params)
    end

    def save_postback_interaction(message_data)
      customer = Facebook::Customers.new(@facebook_retailer).import(message_data['sender']['id'])
      # Handle border case
      # In postback include the title of the button and text of the message
      text = message_data.dig('postback', 'title')

      @klass.create(
        customer: customer,
        facebook_retailer: @facebook_retailer,
        sender_uid: message_data['sender']['id'],
        id_client: message_data['sender']['id'],
        text: text
      )
    end

    private

      def sniff_mime_type(url)
        uri = URI.parse(url)
        response = Net::HTTP.get_response(uri)
        response.content_type[0, 5]
      rescue Errno::ENOENT
        'unknown'
      end

      def add_human_agent_tag(psid, body)
        last_message_date = Customer.find_by(psid: psid, retailer_id: @facebook_retailer.retailer_id)
          .facebook_messages.inbound.last&.created_at
        return body unless @type == 'instagram' || (last_message_date && last_message_date <= 24.hours.ago)

        body.merge!(messaging_type: 'MESSAGE_TAG', tag: 'HUMAN_AGENT')
      end

      def prepare_message(to, message, payload, comment_id)
        body = define_recipient(to, comment_id, 'plain')

        body = if payload.try(:[], 'list').present?
                 prepare_interactive_message(body, payload)
               else
                 prepare_plain_message(body, message)
               end
        body = add_human_agent_tag(to, body)
        body.to_json
      end

      def prepare_plain_message(body, message)
        body.merge!({
          message: {
            text: message
          }
        })
      end

      # En el caso de IG esto solo funciona en el app mobile,
      # si se envía desde la web se recibirá solo el mensaje de texto.
      def prepare_interactive_message(body, payload)
        return prepare_plain_message(body, payload['message']) if payload.blank?

        buttons = payload['list']
        return if buttons.blank?

        if buttons.count < 4 && @type == 'messenger'
          add_message_buttons(body, payload['message'], buttons)
        else
          add_message_quick_replies(body, payload['message'], buttons)
        end
      end

      def add_message_buttons(body, message, buttons)
        buttons_body = buttons.map do |button|
          {
            type: 'postback',
            title: button['title'],
            payload: button['postbackText']
          }
        end
        body.merge!({
          message: {
            attachment: {
              type: 'template',
              payload: {
                template_type: 'button',
                text: message.presence,
                buttons: buttons_body
              }
            }
          }
        })
      end

      def add_message_quick_replies(body, message, quick_replies)
        quick_replies_body = quick_replies.map do |quick_reply|
          {
            content_type: 'text',
            title: quick_reply['title'],
            payload: quick_reply['postbackText']
          }
        end
        body.merge!({
          message: {
            text: message.presence || 'aa',
            quick_replies: quick_replies_body
          }
        })
      end

      def prepare_attachment(*params)
        to, file_path, filename, url, file_type, file_content_type, payload, comment_id = params

        body = if file_path.present?
                 content_type = file_content_type.presence || MIME::Types.type_for(file_path).first.content_type
                 type = check_content_type(content_type)

                 {
                   recipient: define_recipient(to, comment_id, 'file_data'),
                   message: JSON.dump(attachment: {
                     type: type,
                     payload: {}
                   }),
                   filedata: Faraday::UploadIO.new(file_path, content_type, filename)
                 }
               else
                 {
                   recipient: define_recipient(to, comment_id, 'file_url'),
                   message: {
                     attachment: {
                       type: file_type,
                       payload: {
                         url: url
                       }
                     }
                   }
                 }
               end
        body[:message].merge!(build_attachment_list(payload['list'])) if payload['list'].present?
        add_human_agent_tag(to, body)
      end

      def build_attachment_list(quick_replies)
        { quick_replies: JSON.dump(
          quick_replies.map do |quick_reply|
            {
              content_type: 'text',
              title: quick_reply['title'],
              payload: quick_reply['postbackText']
            }
          end
        ) }
      end

      def send_message_url
        params = {
          access_token: @access_token
        }
        "https://graph.facebook.com/v19.0/me/messages?#{params.to_query}"
      end

      def message_url(message_id)
        params = {
          fields: 'message,attachments,created_time,to,from',
          access_token: @access_token
        }
        "https://graph.facebook.com/#{message_id}?#{params.to_query}"
      end

      def fallback_url(url)
        post_id = CGI.parse(URI.parse(url).query)['story_fbid'][0]
        params = {
          fields: 'message,child_attachments,attachments{media,subattachments}',
          access_token: @access_token
        }
        "https://graph.facebook.com/v19.0/#{@uid}_#{post_id}?#{params.to_query}"
      rescue StandardError
        nil
      end

      def check_content_type(content_type)
        return if content_type.blank?
        return 'image' if content_type.include?('image/')
        return 'audio' if content_type.include?('audio/')
        return 'video' if content_type.include?('video/')

        'file' if right_file_format?(content_type)
      end

      # rubocop:disable Metrics/CyclomaticComplexity
      # rubocop:disable Metrics/PerceivedComplexity
      def grab_url(response, content_type)
        type = check_content_type(content_type)
        return if type.blank?

        attachment = response['attachments']&.[]('data')&.[](0)
        return attachment&.[]('image_data')&.[]('url') if type == 'image'
        return attachment&.[]('video_data')&.[]('url') if type == 'video'

        attachment&.[]('file_url')
      end
      # rubocop:enable Metrics/CyclomaticComplexity
      # rubocop:enable Metrics/PerceivedComplexity

      def prepare_action(to, action)
        body = {
          recipient: {
            id: to
          },
          sender_action: action
        }
        body = add_human_agent_tag(to, body)
        body.to_json
      end

      # rubocop:disable Metrics/CyclomaticComplexity
      # rubocop:disable Metrics/PerceivedComplexity
      # rubocop:disable Style/SafeNavigationChainLength
      def file_type_url(message_data)
        if @type == 'instagram' && message_data['message']&.[]('reply_to')&.[]('story')&.[]('url')
          file_type = 'reply_to'
        end
        return [[message_data['message']&.[]('reply_to')&.[]('story')&.[]('url'), file_type]] if file_type == 'reply_to'

        # Check for referral ads_context_data with video_url or photo_url
        referral_ads = message_data['message']&.dig('referral', 'ads_context_data')
        if referral_ads.present?
          if referral_ads['video_url'].present?
            return [[referral_ads['video_url'], 'reply_ads']]
          elsif referral_ads['photo_url'].present?
            return [[referral_ads['photo_url'], 'reply_ads']]
          end
        end

        return [[]] unless message_data['message']&.[]('attachments')

        message_data['message']&.[]('attachments')&.map do |attachment|
          [
            attachment&.[]('url') || attachment&.[]('payload')&.[]('url'),
            attachment[:type]
          ]
        end
      end
      # rubocop:enable Metrics/CyclomaticComplexity
      # rubocop:enable Metrics/PerceivedComplexity
      # rubocop:enable Style/SafeNavigationChainLength

      def get_fallback_media_url(url)
        post_url = fallback_url(url)
        return [url, 'fallback'] if post_url.nil?

        response = JSON.parse(Faraday.get(post_url).body)
        response['attachments']['data'].map do |attachment|
          video_url = attachment['media']['source']
          if video_url
            [video_url, 'video']
          else
            [attachment['media']['image']['src'], 'image']
          end
        end
      rescue StandardError
        [url, 'fallback']
      end

      def right_file_format?(content_type)
        [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel'
        ].include?(content_type)
      end

      def define_recipient(to, comment_id, from)
        case from
        when 'plain'
          if comment_id
            {
              recipient: {
                comment_id: comment_id
              }
            }
          else
            {
              recipient: {
                id: to
              }
            }
          end
        when 'file_data'
          if comment_id
            JSON.dump(comment_id: comment_id)
          else
            JSON.dump(id: to)
          end
        when 'file_url'
          if comment_id
            {
              comment_id: comment_id
            }
          else
            {
              id: to
            }
          end
        end
      end
  end
  # rubocop:enable Metrics/ClassLength
end
