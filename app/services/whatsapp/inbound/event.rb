module Whatsapp
  module Inbound
    class Event < Base
      def process_event!(params)
        gsw_type = determine_gsw_type(params)
        qr_params = params if gsw_type == 'inbound'

        log_params(params, gsw_type)

        select_executor.call(
          gsw_type,
          retailer: @retailer,
          params: params,
          from: @from,
          qr_params: qr_params
        )
      end

      private

        def determine_gsw_type(params)
          return 'message-outbound' if outbound_message?(params)

          params['type']
        end

        def outbound_message?(params)
          params['type'] == 'message' && params['direction'] == 'outbound'
        end

        def log_params(params, gsw_type)
          Rails.logger.info '*' * 100
          Rails.logger.info "PARAMS: #{params}"
          Rails.logger.info "DIRECTION: #{gsw_type}"
          Rails.logger.info '*' * 100
        end

        def select_executor
          @from == 'gupshup' ? Strategies::Whatsapp::Events::ApiExecutor : Strategies::Whatsapp::Events::QrExecutor
        end
    end
  end
end
