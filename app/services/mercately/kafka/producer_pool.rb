module Mercately
  module <PERSON>f<PERSON>
    # Pool of Kafka producers.
    #
    # This class manages a pool of Kafka producers to avoid creating a new producer
    # for each message. It provides methods to get a producer from the pool and
    # release it back to the pool when done.
    class ProducerPool
      # Maximum number of producers in the pool
      MAX_POOL_SIZE = ENV.fetch('KAFKA_PRODUCER_POOL_SIZE', '5').to_i

      # Maximum idle time for a producer (in seconds)
      MAX_IDLE_TIME = ENV.fetch('KAFKA_PRODUCER_MAX_IDLE_TIME', '300').to_i

      class << self
        # Cache para la configuración del productor (evita leer certificados repetidamente)
        attr_accessor :cached_config, :config_cache_time, :ssl_cert_content, :ssl_cert_path

        # Gets a producer from the pool.
        #
        # @param _retry_options [Hash] The retry options for the producer (ignored for now).
        # @return [Mercately::Kafka::Producer] A producer from the pool.
        def get_producer(_retry_options = {})
          initialize_pool

          @pool_mutex.synchronize do
            # Limpiar productores expirados
            cleanup_expired_producers

            # Buscar un productor disponible
            available_producer = find_available_producer

            if available_producer
              mark_producer_as_used(available_producer)
              Rails.logger.debug('🔄 Reutilizando productor existente del pool')
              return available_producer[:producer]
            end

            # Si no hay productores disponibles y el pool no está lleno, crear uno nuevo
            if @pool.size < MAX_POOL_SIZE
              create_new_producer
            else
              # Si el pool está lleno, esperar por un productor disponible o usar el más antiguo
              Rails.logger.warn("⚠️ Pool de productores lleno (#{MAX_POOL_SIZE}), reutilizando el más antiguo")
              oldest_producer = @pool.min_by { |p| p[:last_used] }
              mark_producer_as_used(oldest_producer)
              oldest_producer[:producer]
            end
          end
        end

        # Releases a producer back to the pool.
        #
        # @param producer [Mercately::Kafka::Producer] The producer to release.
        # @return [void]
        def release_producer(producer)
          return unless producer

          @pool_mutex.synchronize do
            # Encontrar el productor en el pool y marcarlo como disponible
            pool_entry = @pool.find { |p| p[:producer] == producer }
            if pool_entry
              pool_entry[:in_use] = false
              pool_entry[:last_used] = Time.current
              Rails.logger.debug('🔄 Productor liberado y marcado como disponible en el pool')
            else
              Rails.logger.debug('🔄 Productor liberado (no estaba en el pool)')
            end
          end
        end

        # Closes all producers in the pool.
        #
        # @return [void]
        def close_all
          return unless @pool_mutex

          @pool_mutex.synchronize do
            # Si no hay pool, inicializarlo como array vacío para evitar errores
            @pool ||= []

            Rails.logger.info("🔄 Cerrando #{@pool.size} productores en el pool")
            @pool.each do |pool_entry|
              producer = pool_entry[:producer]
              next if producer.respond_to?(:error_state?) && producer.error_state?

              producer.shutdown if producer.respond_to?(:shutdown)
            rescue StandardError => e
              Rails.logger.warn("⚠️ Error al cerrar productor Kafka: #{e.message}")
            end
            @pool.clear

            # Limpiar cache de configuración
            clear_cache

            Rails.logger.info('✅ Pool de productores cerrado')
          end
        end

        # Limpia el cache de configuración
        def clear_cache
          @cached_config = nil
          @config_cache_time = nil
          @valid_cert_cache = nil
          @fd_error_count = 0
          Rails.logger.debug('🧹 Cache de configuración limpiado')
        end

        # Verifica si hay demasiados errores de file descriptors
        def too_many_file_descriptor_errors?
          (@fd_error_count || 0) >= 3
        end

        # Incrementa el contador de errores de file descriptors
        def increment_fd_error_count
          @fd_error_count = (@fd_error_count || 0) + 1
          Rails.logger.warn("⚠️ Error de file descriptor ##{@fd_error_count}")
        end

        private

          # Inicializa el pool si no existe
          def initialize_pool
            @pool ||= []
            @pool_mutex ||= Mutex.new
          end

          # Busca un productor disponible en el pool
          def find_available_producer
            @pool.find { |p| !p[:in_use] && !producer_expired?(p) && !producer_in_error?(p) }
          end

          # Marca un productor como en uso
          def mark_producer_as_used(pool_entry)
            pool_entry[:in_use] = true
            pool_entry[:last_used] = Time.current
          end

          # Verifica si un productor ha expirado
          def producer_expired?(pool_entry)
            Time.current - pool_entry[:last_used] > MAX_IDLE_TIME
          end

          # Verifica si un productor está en estado de error
          def producer_in_error?(pool_entry)
            producer = pool_entry[:producer]
            producer.respond_to?(:error_state?) && producer.error_state?
          end

          # Limpia productores expirados del pool
          def cleanup_expired_producers
            expired_producers = @pool.select { |p| producer_expired?(p) || producer_in_error?(p) }

            expired_producers.each do |pool_entry|
              producer = pool_entry[:producer]
              producer.shutdown if producer.respond_to?(:shutdown) && !producer_in_error?(pool_entry)
            rescue StandardError => e
              Rails.logger.warn("⚠️ Error al cerrar productor expirado: #{e.message}")
            end

            @pool.reject! { |p| producer_expired?(p) || producer_in_error?(p) }

            return unless expired_producers.any?

            Rails.logger.info("🧹 Limpiados #{expired_producers.size} productores expirados/erróneos del pool")
          end

          # Crea un nuevo productor y lo agrega al pool
          def create_new_producer
            # Si ya hay demasiados errores de file descriptors, no crear más productores
            if too_many_file_descriptor_errors?
              Rails.logger.warn('⚠️ Demasiados errores de file descriptors, usando productor dummy')
              return create_error_producer('Too many file descriptor errors')
            end

            begin
              producer = build_producer

              pool_entry = {
                producer: producer,
                created_at: Time.current,
                last_used: Time.current,
                in_use: true
              }

              @pool << pool_entry
              Rails.logger.info("✅ Nuevo productor creado y agregado al pool (#{@pool.size}/#{MAX_POOL_SIZE})")
              producer
            rescue StandardError => e
              Rails.logger.error("❌ Error al crear productor Kafka: #{e.message}")
              Rails.logger.error("❌ Backtrace: #{e.backtrace.first(5).join("\n")}")

              # Incrementar contador de errores de file descriptors
              increment_fd_error_count if e.message.include?('Too many open files')

              # Crear un productor dummy que falle de manera controlada
              create_error_producer(e.message)
            end
          end

          # Construye un nuevo productor con la configuración adecuada
          def build_producer
            # Usar la configuración base sin modificar la global
            config = build_producer_config

            Rails.logger.info("📝 Configuración del productor: #{config.keys.join(', ')}")

            # Crear el productor usando la configuración local
            Mercately::Kafka::Producer.new(config_override: config)
          end

          # Construye la configuración del productor con cache
          def build_producer_config
            # Cache la configuración por 5 minutos para evitar leer certificados repetidamente
            cache_duration = 300 # 5 minutos
            current_time = Time.current

            if @cached_config && @config_cache_time &&
               (current_time - @config_cache_time) < cache_duration
              Rails.logger.debug('🔄 Usando configuración cacheada del productor')
              return @cached_config.dup
            end

            Rails.logger.info('🔧 Construyendo nueva configuración del productor')
            config = Mercately::Kafka.configuration.producer_config.dup

            # Sobrescribir bootstrap.servers si está configurado
            if ENV['KAFKA_BOOTSTRAP_SERVERS'].present?
              config['bootstrap.servers'] = ENV['KAFKA_BOOTSTRAP_SERVERS']
              Rails.logger.info("🔧 Usando bootstrap.servers: #{ENV['KAFKA_BOOTSTRAP_SERVERS']}")
            end

            # Configuración de seguridad para entornos que la requieren
            config.merge!(build_security_config) if requires_security_config?

            # Cachear la configuración
            @cached_config = config.dup
            @config_cache_time = current_time
            Rails.logger.info('✅ Configuración del productor cacheada')

            config
          end

          # Verifica si se requiere configuración de seguridad
          def requires_security_config?
            Rails.env.production? || ENV['KAFKA_SECURITY_PROTOCOL'].present?
          end

          # Construye la configuración de seguridad
          def build_security_config
            security_config = {
              'security.protocol': ENV.fetch('KAFKA_SECURITY_PROTOCOL', 'SASL_SSL'),
              'sasl.mechanisms': ENV.fetch('KAFKA_SASL_MECHANISM', 'SCRAM-SHA-256'),
              'sasl.username': ENV.fetch('KAFKA_USERNAME', 'karafka'),
              'sasl.password': ENV.fetch('KAFKA_PASSWORD', '')
            }

            # Configurar certificado SSL
            cert_path = find_valid_certificate
            return raise 'Certificado SSL requerido pero no encontrado' unless cert_path

            security_config[:'ssl.ca.location'] = cert_path
            Rails.logger.info("🔒 Usando certificado SSL: #{cert_path}")

            security_config
          end

          # Encuentra un certificado SSL válido (con cache)
          def find_valid_certificate
            # Cache del certificado válido para evitar verificaciones repetidas
            @valid_cert_cache ||= begin
              cert_path = ENV.fetch('KAFKA_CA_CERT', '/home/<USER>/public_html/shared/config/ca-certificate.crt')

              if valid_certificate?(cert_path)
                Rails.logger.info("✅ Certificado principal válido: #{cert_path}")
                cert_path
              else
                Rails.logger.warn('⚠️ Certificado principal no válido, buscando alternativo')
                find_system_certificate
              end
            end

            @valid_cert_cache
          end

          # Verifica si un certificado es válido
          def valid_certificate?(cert_path)
            return false unless File.exist?(cert_path)

            cert_content = File.read(cert_path)
            cert_content.include?('BEGIN CERTIFICATE')
          rescue StandardError => e
            Rails.logger.warn("⚠️ Error al verificar certificado #{cert_path}: #{e.message}")
            false
          end

          # Encuentra un certificado del sistema que funcione
          def find_system_certificate
            alternative_paths = [
              '/etc/ssl/certs/ca-certificates.crt',
              '/etc/pki/tls/certs/ca-bundle.crt',
              '/etc/ssl/ca-bundle.pem',
              '/etc/pki/tls/cacert.pem',
              '/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem',
              '/home/<USER>/public_html/current/config/ca-certificate.crt',
              '/home/<USER>/public_html/shared/config/ca-certificate.crt'
            ]

            alternative_paths.find { |path| valid_certificate?(path) }
          end

          # Crea un productor dummy que falla de manera controlada
          def create_error_producer(error_message)
            Rails.logger.error("🚨 Creando productor dummy debido a error: #{error_message}")
            Rails.logger.error('🚨 Razón: No se pudo crear productor real de Kafka')
            Rails.logger.error('🚨 Impacto: Los mensajes no se enviarán hasta resolver el problema')

            dummy_producer = Object.new
            dummy_producer.instance_variable_set(:@error_state, true)
            dummy_producer.instance_variable_set(:@error_message, error_message)

            def dummy_producer.produce(*args)
              Rails.logger.error('🚨 DUMMY PRODUCER: Intento de envío bloqueado')
              Rails.logger.error("🚨 DUMMY PRODUCER: Error original: #{@error_message}")
              Rails.logger.error("🚨 DUMMY PRODUCER: Argumentos del mensaje: #{args.inspect}")
              Rails.logger.error('🚨 DUMMY PRODUCER: Acción requerida: Revisar configuración de Kafka')
              nil # Retornar nil para indicar fallo
            end

            def dummy_producer.shutdown
              Rails.logger.info('🚨 DUMMY PRODUCER: Shutdown llamado (no-op)')
            end

            def dummy_producer.error_state?
              @error_state
            end

            # Agregar al pool como productor en error
            pool_entry = {
              producer: dummy_producer,
              created_at: Time.current,
              last_used: Time.current,
              in_use: true
            }

            @pool << pool_entry
            Rails.logger.error("🚨 Productor dummy agregado al pool. Pool size: #{@pool.size}")
            dummy_producer
          end
      end
    end
  end
end
