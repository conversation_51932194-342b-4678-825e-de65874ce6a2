module ChatBots::V2
  # rubocop:disable all
  class ProcessChatbot
    attr_accessor :api_options

    def initialize(customer, chat_bot, origin_instance, text, media)
      @customer = customer
      @retailer = customer.retailer
      @chat_bot = chat_bot
      @origin_instance = origin_instance
      @text = text
      @media = media
      @finish_bot = true
      @platform = ::Message::Platform.call(message_klass: @origin_instance.class)
    end

    # Se encarga de el manejo de comenzar un chatbot o continuar con las opciones
    # si es que ya hay un chatbot activo.
    def manage_chat_bot
      # Si no es un mensaje de texto, se toma el multimedia y se procesa
      # Si es de Formulario sin sublista, se acepta y continua
      # Si es de Formulario con sublista, se toma como intento fallido
      # Si es de Decision, se toma como intento fallido
      if @media
        process_media
        return
      end

      # En listas y botones, si la respuesta no pertenece a la opcion actual, se ignora
      return if not_current_option?

      @text = @text[0] if @text.instance_of?(Array)
      text = @text.strip
      # Si el chat ya tiene un bot activo, operamos sobre la opcion actual del chat.
      # Sino, buscamos si hay un bot que haga match con lo que escribio la persona,
      # o si hay alguno que se active con cualquier interaccion.
      if @customer.policy.active_bot_for?(platform: @platform)
        # Opcion actual que tiene el chat
        @current_option = ChatBots::CurrentOptionFinder.call(customer: @customer, platform: @platform)

        @customer_active_bot = @customer.active_bots.find_by(platform: @platform)

        if @customer_active_bot&.is_preactivated?
          @selected = @current_option
          @customer_active_bot.update!(is_preactivated: false)
          execute_step
          return
        end

        case @current_option.option_type
        when 'decision'
          # Buscamos la opcion que haga match con lo que escribio la persona
          @selected = match_option(text)
        when 'form'
          if @current_option.answer_type_location?
            manage_failed_attempts
            return false
          end
          @selected = get_form_child(text)
          return if @selected == false

          @input_option = true
        when 'api'
          @selected = @current_option.first_active_option
        when 'resolve_chat'
          mark_chat_as_resolved!
          finish_bot!
          return
        else
          return
        end

        # Si la opcion no es encontrada y la opcion actual es de tipo Decision
        # retornamos y se envia el mensaje de fallo o se repiten las opciones.
        # Si es de tipo Formulario no retorna, ya que puede que no tenga opciones
        # hijas y termina la ejecucion en ella.
        return unless @customer.policy.active_bot_for?(platform: @platform) && (@selected || @input_option)

        # Si la opcion seleccionada no ejecuta un endpoint, se guarda la relacion de esta (@selected)
        # y el customer/chat en cuestion. En caso de que ejecute un endpoint, no se guarda, lo hace en
        # un paso siguiente, ya que debemos ver primero si la respuesta del endpoint es exitosa para
        # poder pasar a la siguiente opcion.
        save_customer_option
      else
        # Revisamos si el chat_bot cumple las condiciones para interactuar con dicho chat
        return unless check_chat_bot_history

        # Si es encontrado, tomamos la primera opcion del chatbot, que es la raiz.
        @selected = @chat_bot.active_root
      end

      # Ejecuta las acciones del paso, ademas envia los mensajes, verifica el tipo de paso y hace
      # lo correspondiente.
      execute_step
    end

    def process_media
      # Si el chat ya tiene un bot activo, operamos sobre la opcion actual del chat.
      # Sino, buscamos si hay un bot con cualquier interaccion.
      if @customer.policy.active_bot_for?(platform: @platform)
        # Opcion actual que tiene el chat
        @current_option = ChatBots::CurrentOptionFinder.call(customer: @customer, platform: @platform)

        @customer_active_bot = @customer.active_bots.find_by(platform: @platform)

        if @customer_active_bot&.is_preactivated?
          @selected = @current_option
          @customer_active_bot.update!(is_preactivated: false)
          execute_step
          return
        end

        case @current_option.option_type
        when 'decision'
          @selected = manage_failed_attempts
          return unless @selected
        when 'form'
          @input_option = true
          return if manage_location_forms

          unless @current_option.answer_type_anything?
            @selected = manage_failed_attempts
            return unless @selected
          end
        else
          return
        end

        @selected ||= @current_option.first_active_option

        # Si la opcion seleccionada no ejecuta un endpoint, se guarda la relacion de esta (@selected)
        # y el customer/chat en cuestion. En caso de que ejecute un endpoint, no se guarda, lo hace en
        # un paso siguiente, ya que debemos ver primero si la respuesta del endpoint es exitosa para
        # poder pasar a la siguiente opcion.
        save_customer_option
      else
        # Si no se consigue retornamos y no pasa mas nada.
        return unless check_chat_bot_history

        # Si es encontrado, tomamos la primera opcion del chatbot, que es la raiz.
        @selected = @chat_bot.active_root
      end

      # Ejecuta las acciones del paso, ademas envia los mensajes, verifica el tipo de paso y hace
      # lo correspondiente.
      execute_step
    end

    def not_valid_media?
      return true unless @current_option.answer_type_anything?

      @current_option.decision?
    end

    # Se encarga de ver si el maximo de intentos fallidos ya fueron alcanzados. En ese caso, envia la
    # opcion de fallo si tiene. Si no, actualiza el customer y le incrementa los
    # intentos fallidos que lleva y envia el mensaje de intento fallido.
    def manage_failed_attempts
      # Chequea si el limite fue alcanzado. Si es asi retorna la opcion de fallo si tiene.
      # Y busca la opcion hija de la ruta de fallo para ser enviada.
      if reached_failed_attempts && @current_option.failure_option
        failure_option = @current_option.failure_option.first_active_option
        return failure_option if failure_option

        Customer::ChatBotDeactivator.call(@customer, platform: @platform)
        return
      end

      # Actualiza el customer con los intentos fallidos que lleva.
      ChatBots::FailedAttemptsUpdater.call(@customer, platform: @platform)
      # Envia el mensaje si tienen configurado en la opcion avisar que el customer ha fallado.
      return false if @current_option.failure_text.blank?

      message_service(@current_option).send_message(false, false, true)
      false
    end

    def manage_location_forms
      return false unless @current_option.answer_type_location?

      if @origin_instance.type == 'location'
        @selected = @current_option.first_active_option
        location = @origin_instance.location
        ChatBots::LocationOption::Organizer.call(
          customer: @customer,
          latitude: location['latitude'],
          longitude: location['longitude']
        )
        save_customer_option
        execute_step
        true
      else
        manage_failed_attempts
        false
      end
    end

    # Actualiza el customer/chat con la opcion que ha seleccionado, para saber en que paso va.
    def update_customer_flow
      return unless @selected.present? && !@selected.jump_to?

      # Se actualiza el customer y se resetea el contador de intentos fallidos
      ChatBots::CustomerFlowUpdater.call(@customer,
                                        selected: @selected.id,
                                        platform: @platform,
                                        keep_failed_attempts: false,
                                        save_outbound_date: @chat_bot.reminder_message.present?)
    end

    # Revisa si un bot ya fue activado o no para un customer/chat. En caso de que haya sido activado
    # ya, se estudia si el bot tiene activada la reactivacion, y se ve si se le activa nuevamente, si
    # es que ya ha transcurrido el tiempo necesario. Sino se le ha activado, se le activa y ya.
    def check_chat_bot_history
      return false if @chat_bot.blank?

      if @customer.policy.allow_start_bots_for?(platform: @platform)
        # Si se ha marcado manualmente que inicie el chatbot desde el inbox, se activa
        @customer.chat_bot_customers.create(chat_bot_id: @chat_bot.id, platform: @platform)
        true
      elsif activate_after_outbound?
        # Si está marcado para activarse después de un mensaje saliente y
        # ya ha pasado el tiempo de espera establecido se activa el bot
        @customer.chat_bot_customers.create(chat_bot_id: @chat_bot.id, platform: @platform)
        true
      elsif time_to_reactivate?
        # Si la reactivacion del bot esta activa y ya ha transcurrido el tiempo establecido desde la
        # ultima vez que el customer escribio, o el agente seteo manualmente que se le activen los bots
        # al customer, entonces el bot se le activara, de otro modo no.
        @customer.chat_bot_customers.create(chat_bot_id: @chat_bot.id, platform: @platform)
        true
      elsif activate_for_first_time?(@origin_instance.last_message)
        # Checar si es primer mensaje y es inbound, si existe un mensaje anterior no activar acá.
        # Si el customer no ha interactuado con el chatbot
        @customer.chat_bot_customers.create(chat_bot_id: @chat_bot.id, platform: @platform)
        true
      else
        false
      end
    end

    # Activa el bot por primera vez si no ha escrito antes,
    # Si no está activo que solo se active para nuevos clientes,
    # Si ha escrito antes, tiene configurado tiempo de reactivación y ha pasado el tiempo de reactivación
    def activate_for_first_time?(last_message)
      return false unless @chat_bot.only_new_customers

      last_inbound = @origin_instance.before_last_message
      return true if !@chat_bot.outbound_enabled && last_inbound.blank?
      return true if @chat_bot.outbound_enabled && last_message.blank?
      return false unless @chat_bot.reactivate_after

      # Si ha pasado el tiempo establecido desde el último mensaje recibido se le activa
      last_message_time_elapsed?(last_message)
    end

    def activate_after_outbound?
      return false unless @chat_bot.outbound_enabled
      return false if @chat_bot.reactivate_after.blank? && @origin_instance.last_outbound.present?
      return true if @chat_bot.reactivate_after.blank? && @origin_instance.last_outbound.blank?

      # Si ha pasado el tiempo establecido desde el último mensaje saliente se le activa
      outbound_stale_time_elapsed?
    end

    # Guarda las opciones que selecciona el customer.
    def save_customer_option
      return if @selected.blank?

      @customer.customer_bot_options.create(chat_bot_option_id: @selected.id, platform: @platform)
    end

    # Hace la comparacion entre lo que escribe la persona y las opciones del chatbot.
    def match_option(text)
      # TODO: Adaptar para respuestas dinámicas
      text_to_i = text.to_i
      # Solo toma las opciones hijas activas de la opcion actual.
      options = @current_option.children.active

      # Se busca por posicion. Por ejemplo si la persona envia 1, 2, 3, etc...
      option = options.find_by(position: text_to_i)
      return pick_option(option) if option.present?

      # Hace una comparacion literal entre lo que escribe el customer y las opciones. Quita las
      # tildes y demas, y hace los textos minusculos para una mejor aproximacion.
      option = options.select do |_option|
        I18n.transliterate(_option.text.downcase) == I18n.transliterate(text.downcase)
      end.first
      return pick_option(option) if option.present?

      search = I18n.transliterate(text.downcase)
      words = options.order(position: :asc).map { |op| I18n.transliterate(op.text.downcase) }

      comp = []
      words.each do |w|
        comp << similarity(search, w)
      end

      max = comp.max.to_f
      if max >= 0.5
        indexes = comp.each_index.select { |i| comp[i] == max }

        if indexes.size == 1
          option = options.find { |op| op.position == indexes[0] + 1 }
          return pick_option(option)
        end
      end

      # Al no haber ninguna opcion seleccionada, se cuenta como intento fallido.
      manage_failed_attempts
    end

    # Ejecuta las acciones de las opciones.
    # chat_bot_option = opcion a la cual se le ejecutaran las acciones
    # classification = Tipo de acciones que se ejecutaran. Valores posibles: 'default', 'success', 'failed'
    def execute_actions(chat_bot_option, classification = 'default')
      return if chat_bot_option.blank?

      # Busca las acciones a ejecutar dependiendo de la clasificacion:
      # default = acciones generales de la opcion
      # success = acciones de exito al consumir un endpoint
      # failed = acciones de fallo al consumir un endpoint
      actions = chat_bot_option.chat_bot_actions.classified(classification).order_by_action_type
      return if actions.blank?

      actions.each do |act|
        case act.action_type
        when 'add_tag'
          ChatBots::Actions::AddCustomerTag.call(@customer, act)
        when 'assign_agent'
          ChatBots::Actions::AssignCustomerAgent.call(@customer, act)
        when 'get_out_bot'
          ChatBots::Actions::ExitBot.call(@customer, chat_bot_option, @origin_instance, @platform)
          @sent_in_action = true
          @finish_bot = false
        when 'save_on_db'
          @customer = ChatBots::Actions::SaveCustomerData.call(@customer, act, @selected_value, @media, @text)
        when 'jump_to_option'
          jump_to_chat_bot_option(act, chat_bot_option)
          @finish_bot = false
        when 'assign_team'
          ChatBots::Actions::AssignCustomerTeam.call(@customer, act)
        when 'ctx_notify'
          ChatBots::Actions::NotifyGoalReached.call(@customer, act)
        when 'create_deal'
          ChatBots::Actions::CreateDeal.call(@customer, act)
        end
      end
    end

    # Hace saltar al bot a una opcion especifica para que continue desde alli.
    # Primero chequea si la opcion actual tiene contenido para mandar el mensaje.
    # De tener, envia el mensaje y luego envia el de la opcion a donde se salta.
    def jump_to_chat_bot_option(action, option)
      jump_option = action.jump_option
      return if jump_option.blank?

      # Revisamos si la opcion destino se ignora cuando tiene ya valor almacenado.
      if skip_this_option?(jump_option)
        manage_skip_option(jump_option, option)
        return
      end

      ChatBots::CustomerFlowUpdater.call(@customer,
                                        selected: jump_option.id,
                                        platform: @platform,
                                        keep_failed_attempts: false,
                                         save_outbound_date: @chat_bot.reminder_message.present?)
      @sent_in_action = true
      if jump_option.api?
        # Si la opcion destino es de tipo API, se la marca como seleccionada
        @selected = jump_option
        # y se ejecuta la opción tipo api, iniciando un nuevo flujo desde la nueva opcion.
        execute_api_step
      else
        # De lo contrario se envía el mensaje y ejecuta las acciones de la opcion destino.
        message_service(jump_option).send_message(false, false, false)
        execute_actions(jump_option) if jump_option.decision?
      end
    end

    # En caso de que el bot tenga reactivacion, calcula si ya paso el tiempo desde la ultima vez
    # que el customer escribio, para saber si volver a activar el bot o no.
    def time_to_reactivate?
      return true if @origin_instance.last_message.blank?
      return false if @chat_bot.reactivate_after.blank?

      if (!@chat_bot.outbound_enabled && last_message_time_elapsed?(@origin_instance.before_last_message)) ||
         (@chat_bot.outbound_enabled && last_message_time_elapsed?(@origin_instance.last_message))
        # Si el customer tenia un bot activo, lo desactiva, ya que paso el tiempo de reactivacion
        # y se le debe activar de nuevo desde el inicio.
        Customer::ChatBotDeactivator.call(@customer, platform: @platform)
        true
      else
        false
      end
    end

    def last_message_time_elapsed?(last_message)
      return false unless last_message.present?

      ((@origin_instance.created_at - last_message.created_at) / 3600) >= @chat_bot.reactivate_after
    end

    def outbound_stale_time_elapsed?
      return false if @chat_bot.reactivate_after.blank?

      last_message = @origin_instance.last_message
      return true if last_message.blank?

      ((@origin_instance.created_at - last_message.created_at) / 3600) >= @chat_bot.reactivate_after
    end

    # Se selecciona la opcion a la cual se le van a ejecutar las acciones.
    def option_to_execute
      return nil if @customer_active_bot&.is_preactivated? && @current_option.form?
      return @selected if @customer_active_bot&.is_preactivated? && @current_option.decision?
      # Si la actual es de tipo Formulario, se retorna esa
      return ChatBots::CurrentOptionFinder.call(customer: @customer, platform: @platform) if @input_option
      # Si es de tipo Decision, se retorna la opcion seleccionada por el customer (@selected)
      return @selected if @selected&.decision?

      nil
    end

    # Hace la comparacion entre lo que escribe la persona y la sublista de la opcion.
    def match_sub_list_items(text)
      text_to_i = text.to_i
      options = @current_option.option_sub_lists.order(position: :asc)

      # Se busca por posicion. Por ejemplo si la persona envia 1, 2, 3, etc...
      option = options.select { |opt| opt.position == text_to_i }.first
      if option.present?
        @selected_value = option.value_to_show
        return @current_option.first_active_option
      end

      # Hace una comparacion literal entre lo que escribe el customer y las opciones. Quita las
      # tildes y demas, y hace los textos minusculos para una mejor aproximacion.
      option = options.select do |opt|
        I18n.transliterate(opt.value_to_show.downcase) == I18n.transliterate(text.downcase)
      end.first

      # Toma el valor a guardar.
      if option.present?
        @selected_value = option.value_to_show
        return @current_option.first_active_option
      end

      search = I18n.transliterate(text.downcase)
      words = options.map { |op| I18n.transliterate(op.value_to_show.downcase) }
      option = get_match_option(options, search, words)

      if option.present?
        @selected_value = option.value_to_show
        return @current_option.first_active_option
      end

      # Al no haber ninguna opcion seleccionada, se cuenta como intento fallido.
      manage_failed_attempts
    end


    # Hace la comparacion entre lo que escribe la persona y las opciones de la opcion dinamica autogenerada.
    def match_dynamic_list(text)
      # extraemos el ancestro tipo api de la opcion actual
      ancestor_api = @current_option.closest_api_ancestor
      return false if ancestor_api.nil?

      # buscamos la respuesta del customer en la tabla customer_bot_responses
      response = @customer.customer_bot_responses
                          .where(chat_bot_option_id: ancestor_api.id, status: 'success')
                          .first&.response(version: 2)
      # si no hay respuesta, no se hace nada
      return false if response.blank?

      # obtenemos las opciones de la lista dinámica del formulario
      form_options = @current_option.chat_bot_option_form
      # si no hay opciones, no se hace nada
      return false if form_options.blank?

      data_source_keys = form_options.data_source.split('.')
      title_source_key = form_options.title_source.split('.').last
      value_source_key = form_options.value_source.split('.').last

      data = response.dig(*data_source_keys) || []
      # si no hay datos, no se hace nada
      return false if data.blank?

      text_to_i = text.to_i
      text_index = text_to_i - 1

      # Se busca por posicion. Por ejemplo si la persona envia 1, 2, 3, etc...
      if text_index >= 0 && text_index < data.size
        option = data[text_index]
        @selected_value = option[value_source_key]
        return @current_option.first_active_option
      end

      search = I18n.transliterate(text.downcase)
      # Hace una comparacion literal entre lo que escribe el customer y las opciones. Quita las
      # tildes y demas, y hace los textos minusculos para una mejor aproximacion.
      option = data.map do |opt|
        title = opt[title_source_key]
        value = opt[value_source_key]
        opt if [value, title].any? { |val| I18n.transliterate(val.downcase) == search }
      end.compact.first

      # Toma el valor a guardar.
      if option.present?
        @selected_value = option[value_source_key]
        return @current_option.first_active_option
      end

      title_words = data.map { |op| I18n.transliterate(op[title_source_key]) }
      option = get_match_option(data, search, title_words)

      if option.present?
        @selected_value = option[value_source_key]
        return @current_option.first_active_option
      end

      button_words = data.map.with_index { |op, index| "#{index+1}-#{@current_option.id}" }
      option = get_match_option(data, search, button_words)

      if option.present?
        @selected_value = option[value_source_key]
        return @current_option.first_active_option
      end

      # Al no haber ninguna opcion seleccionada, se cuenta como intento fallido.
      manage_failed_attempts
      false
    end

    # Setea el cuerpo a enviar en el endpoint.
    def set_body_request(action)
      params = {}

      action.data.each do |d|
        next unless d.key.present? && d.value.present?

        params[d.key] = set_value_param(d.value)
      end

      action.payload_type == 'json' ? params.to_query : params
    end

    # Define si el valor a setear en los parametros es uno tipeado por el agente, o lo tomara de
    # un campo de la tabla customers, o sera tomado de un campo dinamico del customer.
    def set_value_param(value)
      columns = Customer.column_names
      # Buscamos si hay algun campo dinamico que coincida con el valor del dato.
      related = @retailer.customer_related_fields.find_by(identifier: value)

      # Si lo hay, tomamos el valor de alli.
      if related.present?
        data = @customer.customer_related_data.find_by(customer_related_field_id: related.id)&.data.presence || ''
        related.field_type == 'integer' ? data.to_i : data
      elsif columns.include?(value)
        # Sino hay campo dinamico, pero si hay coincidencia con algun campo de la tabla customers, se toma
        @customer.send(value)
      else
        # Sino coincide ninguno de los anteriores, entonces tomamos el valor tal cual esta.
        value
      end
    end

    def parse_json(response)
      JSON.parse(response.read_body)
    rescue StandardError
      {}
    end

    # Chequea si se han alcanzado el maximo de intentos fallidos o no. De ser alcanzados, se envia
    # el mensaje avisando y se sale del chatbot. Sino, se incrementa el contador.
    def reached_failed_attempts
      return false unless @current_option.fail_path_active && @current_option.max_attempts
      return false unless @customer.multiplatform_decorator.failed_bot_attempts_for(@platform) + 1 >= @current_option.max_attempts

      true
    end

    # Chequea si enviar o no el mensaje de la opcion que ejecuta la accion de saltar a otra opcion
    # es decir, de la opcion origen, antes de que haga dicho salto, y por ende antes de que mande
    # el mensaje de la opcion a la que se salta (opcion destino).
    def send_option_on_jump?(option)
      option.decision? && (option.answer.present? || option.file.attached? ||
        option.has_additional_answers_filled?)
    end

    # Hace una recursividad para buscar la proxima opcion que no tenga en true el atributo
    # que dice que se debe saltar a la siguiente opcion si esta ya tiene informacion guardada.
    # Y de encontrarla la envia y actualiza el flujo del customer/chat.
    def manage_skip_option(option, _from_option = nil)
      if option.blank?
        Customer::ChatBotDeactivator.call(@customer, platform: @platform)
        return
      end

      if option.jump_to?
        execute_actions(option)
        return
      end

      # Si la opcion es de decision, o no esta seteada para saltar, o si esta seteada para saltar pero
      # no tiene valor, entonces entra en esa y manda el mensaje y actualiza el customer/chat.
      if option.decision? || option.skip_option == false || not_existing_option_content?(option)
        ChatBots::CustomerFlowUpdater.call(@customer,
                                           selected: option.id,
                                           platform: @platform,
                                           keep_failed_attempts: false, save_outbound_date: @chat_bot.reminder_message.present?)
        @sent_in_action = true
        message_service(option).send_message
        execute_actions(option) if option.decision?
      else
        manage_skip_option(option.first_active_option)
      end
    end

    # Chequea si el campo solicitado en la accion que se desea ignorar, tiene valor o no
    # De tener, retorna false, lo que hace que siga la recursividad buscando la siguiente
    # opcion. Si no tiene, retorna true, finalizando la recursividad.
    def not_existing_option_content?(option)
      action = option.save_data_action_complete
      return true if action.blank?

      value = action.target_field.presence || action.customer_related_field&.identifier
      bot_api(option).search_variable_value(value, 'content').blank?
    end

    # Revisa si la opcion enviada en parametros esta configurada para saltarse.
    def skip_this_option?(option)
      return false unless option

      option.form? && option.skip_option && @sent_in_action.blank?
    end

    def trigrams(text)
      return [] if text.strip.blank?

      parts = []
      padded = "  #{text} ".downcase
      padded.chars.each_cons(3) { |w| parts << w.join }

      parts
    end

    def similarity(text, compare_to)
      tri_text = trigrams(text)
      tri_compare = trigrams(compare_to)
      return 0.0 if [tri_text, tri_compare].any?(&:empty?)

      # Find number of trigrams shared between them
      same_size = (tri_text & tri_compare).size

      # Find unique total trigrams in both arrays
      all_size = (tri_text | tri_compare).size

      same_size.to_f / all_size
    end

    def get_match_option(options, search, words)
      comp = []
      words.each do |word|
        comp << similarity(search, word)
      end

      max = comp.max.to_f
      return unless max >= 0.5

      indexes = comp.each_index.select { |i| comp[i] == max }
      return unless indexes.size == 1

      options.find { |op| op.position == indexes[0] + 1 }
    end

    def not_current_option?
      current_option_id = ChatBots::CurrentOptionFinder.call(customer: @customer, platform: @platform)&.id
      @text.instance_of?(Array) && @text.try(:[], 1) && current_option_id && @text[1].to_i != current_option_id
    end

    def finish_bot!
      return unless finish_bot?

      Customer::ChatBotDeactivator.call(@customer, platform: @platform)
    end

    def finish_bot?
      return false unless @finish_bot

      if @selected&.form?
        false
      elsif @current_option&.form? && @current_option.children&.active.blank?
        true
      elsif @selected&.children&.active.blank?
        true
      else
        false
      end
    end

    def message_service(option)
      ChatBots::V2::Message.new(@customer, option, @origin_instance)
    end

    def bot_api(option)
      BotApi.new(@customer, option, platform: @platform)
    end

    def pick_option(option)
      if option.fake_option
        execute_actions(option)
        option.children.active.first
      else
        option
      end
    end

    def execute_active_mia_step(option)
      if @retailer.mia_integrated?('chatbot') && @retailer.mia_platforms.exists?(name: @platform.to_s.capitalize)
        redis.set("#{@platform.to_s}_messages_#{@customer.id}", 'hola')
        job = mia_chatbot_job.perform_later(@customer.id, 'text', nil)
        customer_message_job.update(job_id: job.provider_job_id, platform_message_id: @origin_instance.id)
        customer_mia_chatbot.update!(active: true, human_help: false)
      end

      finish_bot!
    end

    def execute_step
      # Decidimos a cual opcion le vamos a ejecutar las acciones, tomando en cuenta si es de
      # tipo Decision o Formulario. Si es de tipo Formulario, ejecutamos las acciones de la
      # opcion actual. Si es de Decision, ejecutamos la de la opcion seleccionada (@selected)
      exec_option = option_to_execute
      # Actualizamos el customer/chat con la opcion seleccionada (@selected) para saber en que
      # paso del bot esta actualmente.
      update_customer_flow

      # Si la opcion seleccionada es activar MIA
      if @selected&.active_mia?
        execute_active_mia_step(@selected)
        return
      end

      if @selected&.resolve_chat?
        mark_chat_as_resolved!
        finish_bot!
        return
      end

      # Se ejecutan las acciones de la opcion que retorna el metodo option_to_execute
      execute_actions(exec_option)

      # Si la opción seleccionada es de tipo API, se ejecuta el paso de la opción
      if @selected&.api?
        execute_api_step
        return
      end


      # Si la opcion seleccionada es de Formulario, y esta configurada para saltarse si tiene contenido,
      # y no se ha saltado a ninguna otra opcion en las acciones, entonces se chequea si tiene contenido
      # o no, para saber si mandarla o iniciar la recursividad para buscar la siguiente.
      if skip_this_option?(@selected)
        manage_skip_option(@selected)
        return
      end

      # Si la opcion seleccionada es de salto, ejecutamos dicha accion
      if @selected&.jump_to?
        execute_actions(@selected)
        return
      end

      # Mandamos el mensaje con el contenido de la opcion seleccionada (@selected)
      message_service(@selected).send_message unless @sent_in_action

      if @selected&.decision? && @current_option&.form? && !@current_option.contains_exit_action?
        execute_actions(@selected)
      end

      # Enviamos a finalizar el chatbot si es la última opción del chatbot, no tiene hijos.
      # No se termina si la opcion actual ejecuta las acciones de saltar a otra opción, o volver al inicio,
      # o volver a la opción anterior o terminar el bot
      finish_bot!
    end

    def execute_api_step(chat_bot_option = nil)
      @selected = chat_bot_option if @selected.blank? && chat_bot_option.present?
      return if @selected.blank?

      @api_options = @selected.chat_bot_option_api
      return if @api_options.blank?

      # Se ejecuta el endpoint de la opcion actual, y se guarda la respuesta en el customer
      api_response = ChatBots::ApiOption::Organizer.call(
        customer: @customer,
        api_options: @api_options,
        platform: @platform
      )

      # Si la respuesta es exitosa, se configura la variable @selected con la primera opcion
      # activa hija de la opcion actual. Si no, se configura con la primera opcion activa del camino fallido
      child = if api_response.success?
                @selected.first_active_option
              else
                @selected.failure_option.first_active_option
              end

      if child.blank?
        finish_bot!
        return
      end

      ChatBots::CustomerFlowUpdater.call(@customer,
                                         selected: child.id,
                                         platform: @platform,
                                         keep_failed_attempts: false,
                                         save_outbound_date: @chat_bot.reminder_message.present?)

      # Se ejecuta el paso de la opción siguiente a la opción actual de tipo api
      @origin_instance.send_message_step(child)
    end

    def mark_chat_as_resolved!
      Chats::StatusChatUpdater.call(
        @customer,
        status: 'resolved',
        platform: @platform
      )
      @origin_instance.block_chat_reactivation = true
    end

    # Busca la opcion siguiente si tiene match en la sublista o en caso de no tener sublista.
    # Cuando tiene sublista si hay match, retorna la hija de exito, sino cuenta el fallido.
    # Cuando ya alcanza el maximo de fallidos si lo tiene configurado, retorna la hija de fallo.
    def get_form_child(text)
      if @current_option.use_api?
        match_dynamic_list(text)
      elsif @current_option.has_sub_list?
        match_sub_list_items(text)
      else
        @current_option.first_active_option
      end
    end

    def notification_service
      Shared::AutomaticAssignments.new
    end

    private

      def redis
        @redis ||= Redis.new(url: ENV.fetch('REDIS_PROVIDER', 'redis://localhost:6379/1'))
      end

      def customer_message_job
        @customer_message_job ||= CustomerMessageJob
          .find_or_create_by(platform: @platform.to_s.downcase, customer_id: @customer.id)
      end

      def mia_chatbot_job
        "Mia::Chatbot::#{@platform.to_s.capitalize}Job".constantize
      end

      def customer_mia_chatbot
        @customer_mia_chatbot ||= @customer.customer_mia_chatbot_for(@platform.to_s)
      end
  end
end
# rubocop:enable all
