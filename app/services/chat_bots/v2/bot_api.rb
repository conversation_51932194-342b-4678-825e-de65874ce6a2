module ChatBots::V2
  # rubocop:disable Metrics/ClassLength
  class BotApi
    def initialize(customer, chat_bot_option, platform: nil)
      @customer = customer
      @retailer = customer.retailer
      @chat_bot_option = chat_bot_option
      @chat_bot = chat_bot_option&.chat_bot
      @platform = platform
    end

    def get_message(get_out, error_exit, failed_attempt)
      message = if failed_attempt
                  failed_attempt_message
                elsif get_out
                  get_out_message
                elsif error_exit
                  error_exit_message
                else
                  @chat_bot_option.answer
                end

      replace_message_variables(message)
    end

    def failed_attempt_message
      @chat_bot_option.failure_text.presence || ''
    end

    def get_out_message
      # Si no fue por intentos fallidos, busca si en la accion de salida hay un mensaje configurado.
      # De haberlo lo toma, sino toma el mensaje de salida general del bot.
      action = @chat_bot_option.chat_bot_actions.find_by(action_type: :get_out_bot)
      action&.exit_message.presence || (@chat_bot.enabled_goodbye_message ? @chat_bot.goodbye_message : '')
    end

    def error_exit_message
      # Si es por intentos fallidos, toma el mensaje de salida por intentos fallidos del bot.
      @chat_bot.error_message
    end

    # Busca las variables en el texto del mensaje, y las reemplaza con el valor real.
    def replace_message_variables(message)
      return message unless message.include?('{{')

      ocurrences = message.scan('{{').size
      api_options = @chat_bot_option.closest_api_ancestor&.chat_bot_option_api

      (1..ocurrences).each do
        text = message[/\{{.*?\}}/]
        text = text.gsub('{{', '')
        text = text.gsub('}}', '')
        message[/\{{.*?\}}/] = map_variable_value(text, api_options).to_s
      end

      message
    end

    def map_variable_value(value, api_options)
      ChatBots::Options::VariableValueMapper.call(
        variable: value, customer: @customer, platform: @platform, api_options: api_options
      )
    end

    # Define el valor a setear en la variable. Lo tomara de un campo de la tabla customers,
    # o sera tomado de un campo dinamico del customer.
    def search_variable_value(value, from = 'variable')
      value = value.strip
      # Buscamos si hay algun campo dinamico que coincida con el valor del dato.
      related_field = @retailer.customer_related_fields.find_by(identifier: value)

      # Si lo hay, tomamos el valor de alli.
      if related_field.present?
        fetch_related_field_value(related_field)
      elsif Customer.column_names.include?(value)
        # Sino hay campo dinamico, pero si hay coincidencia con algun campo de la tabla customers, se toma
        @customer.send(value).presence || ''
      else
        # Sino coincide ninguno de los anteriores, entonces tomamos el valor tal cual esta si es que viene
        # el llamado para sustituir una variable, sino, si viene de chequear el contenido, devolvemos nil.
        from == 'variable' ? value : nil
      end
    end

    # Agrega la lista de opciones a seleccionar en el mensaje pasado por parametro.
    def append_text_options(message)
      if @chat_bot_option.decision?
        menu_text_options(message)
      elsif @chat_bot_option.form?
        form_text_options(message)
      end
    end

    def menu_text_options(message)
      items_list = @chat_bot_option.children.active.order(position: :asc)
      return message unless items_list.exists?

      # Construye la lista de opciones a seleccionar tomando en cuenta la sublista de la opcion.
      message = message.present? ? "#{message}\n\n" : ''
      items_size = items_list.size - 1

      items_list.each_with_index do |item, index|
        message += "#{item.position}. #{item.text}"
        message += "\n" if index != items_size
      end

      message
    end

    # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    def form_text_options(message)
      # Si la opción usa la respuesta de un paso tipo api para generar la lista
      if @chat_bot_option.use_api?
        # obtenemos los registros necesarios para armar la lista de opciones
        form_options, api_responses = fetch_api_vars
        # Construye la lista de opciones a seleccionar tomando en cuenta la sublista de la opcion.
        message = message.present? ? "#{message}\n\n" : ''
        # Obtenemos las keys del hash de respuesta de la api para armar la lista de opciones
        data_source, title_key, = parse_form_list_keys(form_options)
        # Obtenemos el hash de respuesta de la api
        data_list = api_responses.last.response(version: 2).with_indifferent_access&.dig(*data_source) || []
        data_list_size = data_list.size - 1

        data_list.each_with_index do |item, index|
          # Obtenemos el valor a mostrar y el valor a enviar
          title = item[title_key].presence || ''

          # Agregamos la opción a la lista de opciones si existe el title y value
          if title.present?
            message += "#{index + 1}. #{title}"
            message += "\n" if index != data_list_size
          end
        end
      # Si la opcion tiene su propia sublista estática
      else
        items_list = @chat_bot_option.option_sub_lists.order(position: :asc)
        return message unless items_list.exists?

        # Construye la lista de opciones a seleccionar tomando en cuenta la sublista de la opcion.
        message = message.present? ? "#{message}\n\n" : ''
        items_size = items_list.size - 1

        items_list.each_with_index do |item, index|
          message += "#{item.position}. #{item.value_to_show}"
          message += "\n" if index != items_size
        end
      end

      message
    rescue StandardError => e
      Rails.logger.error("Error al armar la lista de opciones: #{e.message}")
      message
    end
    # rubocop:enable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity

    # Setea las opciones de botones para una opcion
    def append_buttons_options
      if @chat_bot_option.decision?
        menu_button_options
      elsif @chat_bot_option.form?
        form_button_options
      end
    end

    def menu_button_options
      options = []
      items_list = @chat_bot_option.children.active.order(position: :asc)
      return options unless items_list.exists?

      items_list.each do |item|
        options << {
          type: 'text',
          title: item.text.strip,
          postbackText: postback_text(item)
        }
      end

      options
    end

    # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    def form_button_options
      options = []
      # Si la opción usa la respuesta de un paso tipo api para generar la lista
      if @chat_bot_option.use_api?
        form_options, api_responses = fetch_api_vars
        # Obtenemos las keys del hash de respuesta de la api para armar la lista de opciones
        data_source, title_key, = parse_form_list_keys(form_options)
        # Obtenemos el hash de respuesta de la api
        data_list = api_responses.last.response(version: 2).with_indifferent_access&.dig(*data_source) || []
        # filtramos los n primeros elementos en función del tipo de respuesta
        data_list = filter_list_options(data_list)

        data_list.each_with_index do |item, index|
          title = item[title_key].presence || ''
          next if title.blank?

          # Agregamos la opción a la lista de opciones si existe el title
          options << {
            type: 'text',
            title: title.strip.to_s,
            postbackText: "#{index + 1}-#{@chat_bot_option.id}"
          }
        end
      else
        items_list = @chat_bot_option.option_sub_lists.order(position: :asc)
        return options unless items_list.exists?

        items_list.each do |item|
          options << {
            type: 'text',
            title: item.value_to_show.strip,
            postbackText: postback_text(item)
          }
        end
      end

      options
    rescue StandardError => e
      Rails.logger.error("Error al armar la lista de botones de opciones: #{e.message}")
      []
    end
    # rubocop:enable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity

    def postback_text(item)
      "#{item.position}-#{@chat_bot_option.id}"
    end

    def execute_option(origin_instance)
      if form_and_skip_option?
        manage_form_skip_option(origin_instance)
      elsif jump_to_or_message_option?
        execute_chatbot_actions(origin_instance)
      elsif active_mia_option?
        execute_active_mia_step(origin_instance)
      elsif api_option?
        execute_api_option(origin_instance)
      elsif resolve_chat_option?
        execute_resolve_chat_step(origin_instance)
      else
        process_decision_or_send_message(origin_instance)
      end
    end

    private

      def form_and_skip_option?
        @chat_bot_option.form? && @chat_bot_option.skip_option
      end

      def form_and_location_option?
        @chat_bot_option.form? && @chat_bot_option.answer_type_location?
      end

      def jump_to_or_message_option?
        @chat_bot_option.jump_to? || @chat_bot_option.message?
      end

      def active_mia_option?
        @chat_bot_option.active_mia?
      end

      def api_option?
        @chat_bot_option.api?
      end

      def resolve_chat_option?
        @chat_bot_option.resolve_chat?
      end

      def manage_form_skip_option(origin_instance)
        chatbot_processor_service(origin_instance).manage_skip_option(@chat_bot_option)
      end

      def execute_chatbot_actions(origin_instance)
        chatbot_processor_service(origin_instance).execute_actions(@chat_bot_option)
      end

      def execute_active_mia_step(origin_instance)
        chatbot_processor_service(origin_instance).execute_active_mia_step(@chat_bot_option)
      end

      def execute_resolve_chat_step(origin_instance)
        processor_service = chatbot_processor_service(origin_instance)
        processor_service.mark_chat_as_resolved!
        processor_service.finish_bot!
      end

      def process_decision_or_send_message(origin_instance)
        actions = execute_actions_if_decision(origin_instance)
        return if actions&.find { |act| act.action_type == 'get_out_bot' }.present?

        chatbots_message_service(origin_instance).send_message
      end

      def save_location_and_send_message(origin_instance)
        location = origin_instance.location
        return if location.blank?

        ChatBots::LocationOption::Organizer.call(
          customer: @customer,
          latitude: location.latitude,
          longitude: location.longitude
        )

        chatbots_message_service(origin_instance).send_message
      end

      def execute_actions_if_decision(origin_instance)
        return unless @chat_bot_option.decision?

        chatbot_processor_service(origin_instance).execute_actions(@chat_bot_option)
      end

      def execute_api_option(origin_instance)
        api_options = @chat_bot_option.chat_bot_option_api
        return if api_options.blank?

        chatbot_processor = chatbot_processor_service(origin_instance)
        chatbot_processor.api_options = api_options
        chatbot_processor.execute_api_step(@chat_bot_option)
      end

      def fetch_related_field_value(related_field)
        data = @customer.customer_related_data.find_by(customer_related_field_id: related_field.id)&.data || ''
        related_field.list? ? related_field.get_list_option_value(data) : data
      end

      def filter_list_options(list_options)
        return [] unless list_options.is_a?(Array)

        case @chat_bot_option.interactive
        when 'buttons'
          list_options.first(3)
        when 'list'
          list_options.first(10)
        else
          list_options
        end
      end

      def fetch_api_vars
        # Buscamos el ancestro tipo api más cercano a la opción
        ancestor_api = @chat_bot_option.closest_api_ancestor
        # Si no existe, no se puede generar la lista de opciones
        raise ArgumentError, 'No se encontró ancestro tipo api' if ancestor_api.blank?

        # Buscamos la respuesta de la api
        api_responses = CustomerBotResponse.where(
          chat_bot_option_id: ancestor_api.id,
          customer_id: @customer.id,
          platform: @platform
        )
        # Si no existe respuesta, no se puede generar la lista de opciones
        raise ArgumentError, 'No se encontró ancestro tipo api' if api_responses.blank?

        # Buscamos las opciones del formulario
        form_options = @chat_bot_option.chat_bot_option_form
        # Si no existen opciones, no se puede generar la lista de opciones
        raise ArgumentError, 'No se encontró configuración de api para pregunta' if form_options.blank?

        [form_options, api_responses]
      end

      def parse_form_list_keys(form_options)
        data_source = form_options.data_source.split('.')
        title_key = form_options.title_source.split('.').last
        value_key = form_options.value_source.split('.').last

        [data_source, title_key, value_key]
      end

      def chatbot_processor_service(origin_instance)
        ChatBots::V2::ProcessChatbot.new(@customer, @chat_bot, origin_instance, nil, nil)
      end

      def chatbots_message_service(origin_instance)
        ChatBots::V2::Message.new(@customer, @chat_bot_option, origin_instance)
      end
  end
  # rubocop:enable Metrics/ClassLength
end
