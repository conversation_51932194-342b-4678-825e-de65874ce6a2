# frozen_string_literal: true

module Strategies
  module Whatsapp
    module Events
      class ApiExecutor < ApplicationService
        attr_accessor :strategy, :retailer, :params, :from, :qr_params

        def initialize(strategy, retailer:, params:, from: nil, qr_params: nil)
          @strategy = strategy
          @retailer = retailer
          @params = params
          @from = from
          @qr_params = qr_params
          raise ArgumentError, 'Invalid strategy' if @strategy.blank?
        end

        def call
          return if select_strategy.blank?
          return if retailer.blank? || params.blank?

          select_strategy.call(
            retailer: retailer,
            params: params,
            from: from,
            qr_params: qr_params
          )
        end

        private

          def select_strategy
            {
              'message' => ::Whatsapp::Events::InboundStrategy,
              'message-outbound' => ::Whatsapp::Events::OutboundStrategy,
              'quick_reply' => ::Whatsapp::Events::InboundStrategy,
              'message-event' => ::Whatsapp::Events::ApiMessageStrategy,
              'billing-event' => ::Whatsapp::Events::BillingStrategy,
              'template-event' => ::Whatsapp::Events::TemplateStrategy
            }[strategy]
          end
      end
    end
  end
end
