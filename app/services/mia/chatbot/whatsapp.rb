# frozen_string_literal: true

module Mia::Chatbot
  class Whatsapp < Base
    def initialize(message)
      super
      @platform = 'whatsapp'
    end

    def call
      return if message_enqueued?
      return if customer_group?
      return unless valid_inbound_type?
      return unless active_mia_chatbot?

      Rails.logger.info('%' * 100)
      Rails.logger.info('Mia::Chatbot::Whatsapp')
      return handle_media if %w[image file audio].include?(inbound_type)

      handle_text
    end

    private

      def message_enqueued?
        customer_message_job.platform_message_id == message.id
      end

      def customer_group?
        customer.is_group
      end

      def valid_inbound_type?
        %w[text audio image file].include?(inbound_type)
      end

      def inbound_type
        @inbound_type ||= message_payload['type']
      end

      def inbound_message
        return @inbound_message if defined?(@inbound_message)

        inbound_payload = message_payload['payload']
        @inbound_message = inbound_type == 'text' ? inbound_payload['text'] : inbound_payload['url']
      end

      def last_message
        @last_message ||= customer.gupshup_whatsapp_messages.where(id: ..message.id).second_to_last
      end

      def message_payload
        @message_payload ||= message.message_payload['payload']
      end

      def handle_text
        enqueue_message(inbound_message)
        cancel_previous_job

        job = Mia::Chatbot::WhatsappJob
          .set(wait: wait_time)
          .perform_later(customer.id, 'text', get_referenced_message_id)
        save_customer_message_job(job, message.id)
      end

      def handle_media
        caption = message_payload.dig('payload', 'caption')
        file_name = message_payload.dig('payload', 'name')

        media_params = {
          url: inbound_message,
          caption:,
          file_name:
        }

        Mia::Chatbot::WhatsappJob
          .perform_later(customer.id, inbound_type, get_referenced_message_id, media_params)
      end

      def get_referenced_message_id
        message_payload.dig('context', 'id')
      end
  end
end
