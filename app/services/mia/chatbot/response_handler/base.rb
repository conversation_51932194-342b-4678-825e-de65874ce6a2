module Mia::Chatbot::ResponseHandler
  class Base < ApplicationService
    attr_reader :mia_response, :customer, :platform, :retailer

    def initialize(mia_response:, customer: nil, platform: nil)
      @mia_response = mia_response

      @customer = customer || Customer.find_by(id: mia_response[:customer_id])
      return if @customer.nil?

      @retailer = @customer.retailer
      @platform = platform || mia_response[:platform] || 'whatsapp'
    end

    def call
      return unless @customer

      handle_mia_response
    end

    private

      # Status code 200 is obtained when the AI have context of the
      # customer questions, if not, we will obtain Status code 202 and
      # will deactivate mia chatbot after sending the default message,
      # to allow the agent to continue with the conversation
      def handle_mia_response
        case mia_response.status
        when 200
          Rails.logger.info "Mia response body: #{mia_response.body}"
          process_outbound_message(mia_response.body)
          # It will validate if after 24 hours the customer still needs human help
          # to deactivate the flag if the chatbot is reactivated. The database transaction only
          # will be executed if human_help is true (Only one possible time)
          update_customer_labels(mia_response.body)
        when 202
          process_outbound_message(mia_response.body)
        else
          Rails.logger.error "There was an error trying to get Mia responses: #{mia_response.body}"
        end
      end

      def mia_flag(body)
        body['flag']&.to_sym
      end

      def update_customer_labels(mia_response)
        flag = mia_flag(mia_response)
        return if flag.nil? || flag != :message

        updates = {}
        updates[:human_help] = false if customer_mia_chatbot.human_help?
        updates[:buy_intention] = false if customer_mia_chatbot.buy_intention?
        customer_mia_chatbot.update!(updates) unless updates.empty?
      end

      def customer_mia_chatbot
        @customer_mia_chatbot ||= customer.customer_mia_chatbot_for(platform)
      end

      def process_outbound_message(mia_response)
        flag = mia_flag(mia_response)
        process_builders.fetch(flag) do
          Rails.logger.error("There was an error trying to get Mia responses: Flag '#{flag}' not supported")
          return
        end.call(mia_response)
      end

      def process_builders
        @process_builders ||= {
          message: method(:process_messages),
          buy: method(:process_buy_intention),
          help: method(:process_human_help)
        }
      end

      def process_messages(mia_response)
        messages = mia_response['response']
        messages.each_with_index do |message, index|
          if index != messages.length - 1
            process_message(message)
            next
          end

          process_message(message, mia_response['sources'])
        end
      end

      def process_message(message, sources = nil)
        if message['references'].present?
          process_message_with_references(message, sources)
        elsif message['text'].present?
          msg_response = send_text_message(message['text'])
          return if sources.blank?

          platform_message = msg_response[:message]
          save_message_mia_response_sources_data(platform_message.id, sources)
        end
      end

      def process_message_with_references(message, sources)
        references = message['references']
        text = message['text']
        message_length = calculate_message_length(message)

        if references.length == 1 && message_length < 1024
          msg_response = send_reference(references.first, text)
          return if sources.blank?

          platform_message = msg_response[:message]
          save_message_mia_response_sources_data(platform_message.id, sources)
        else
          send_multiple_messages(text, references, sources)
        end
      end

      def send_multiple_messages(text, references, sources)
        send_text_message(text) if text.present?
        references.each_with_index do |reference, index|
          msg_response = send_reference(reference)
          next if index != references.length - 1 || sources.blank?

          platform_message = msg_response[:message]
          save_message_mia_response_sources_data(platform_message.id, sources)
        end
      end

      def calculate_message_length(message)
        message['char_count'] || message['text']&.length || 0
      end

      def save_message_mia_response_sources_data(message_id, value)
        MessageMiaResponseSourcesData.create(message_id:, value:, platform:)
      end

      def process_buy_intention(mia_response)
        send_text_message(mia_response['response'])
        process_internal_message('buy')
        customer_mia_chatbot.update!(active: false, buy_intention: true)
        process_note_message({ message: mia_response['summary'], message_identifier: SecureRandom.uuid })
      end

      def process_human_help(mia_response)
        send_text_message(mia_response['response'])
        process_internal_message('human_help')
        customer_mia_chatbot.update!(active: false, human_help: true)
        process_note_message({ message: mia_response['summary'], message_identifier: SecureRandom.uuid })
      end

      def get_filename_from_url(url)
        url.split('/').last
      end

      def get_file_type(content_type)
        return 'image' if content_type.include?('image')
        return 'audio' if content_type.include?('audio')
        return 'video' if content_type.include?('video')

        'file'
      end
  end
end
