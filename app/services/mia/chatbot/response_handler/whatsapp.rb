module Mia::Chatbot::ResponseHandler
  class Whatsapp < Base
    private

      def send_text_message(text)
        whatsapp_service.send_message(type: 'text', params: { type: 'text', message: text })
      end

      def whatsapp_service
        @whatsapp_service ||= ::Whatsapp::Outbound::Msg.new(
          retailer, @customer, nil, retailer.gupshup_integrated? ? 'gupshup' : 'maytapi', true
        )
      end

      def send_reference(reference, caption = '')
        content_type = reference['content_type']
        url = reference['url']

        if content_type.start_with?('text/')
          send_text_message(url)
        else
          file_name = get_filename_from_url(url)
          params = { type: 'file', caption:, url:, content_type:, file_name: }
          whatsapp_service.send_message(type: 'file', params:)
        end
      end

      def process_internal_message(mia_flag)
        whatsapp_service.create_mia_internal_message(mia_flag)
      end

      def process_note_message(params)
        whatsapp_service.create_note(params: params, retailer_user: nil, order_note: false, sent_by_mia: true)
      end
  end
end
