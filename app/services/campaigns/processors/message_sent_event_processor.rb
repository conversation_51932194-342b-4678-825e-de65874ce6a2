module Campaigns
  module Processors
    # Procesador responsable de procesar eventos "message sent" y rastrear el progreso de campañas.
    #
    # Este procesador escucha mensajes `campaign_message_sent_event`, los procesa, y actualiza
    # el progreso de la campaña en Redis. Dispara la finalización de campaña una vez que todos 
    # los mensajes han sido procesados.
    #
    # Responsabilidades:
    # - Validar el tipo de evento y campos requeridos.
    # - Actualizar Redis con información de rastreo de mensajes.
    # - Verificar si la campaña está completada y disparar el evento de finalización.
    #
    # @example Procesando un evento de mensaje enviado
    #   processor = Campaigns::Processors::MessageSentEventProcessor.new
    #   processor.process({ "event_type" => "campaign_message_sent_event", "campaign_id" => 123 })
    class MessageSentEventProcessor
      attr_reader :logger

      # Inicializa el procesador con dependencias.
      #
      # @param logger [Logger] Instancia del logger.
      def initialize(logger: Rails.logger)
        @logger = logger
      end

      # Retorna la instancia del productor de eventos.
      #
      # @return [Object] La instancia del productor de eventos.
      def event_producer
        @event_producer ||= Mercately::Kafka::ProducerPool.get_producer
      end

      # Procesa un evento de mensaje enviado recibido desde Kafka.
      #
      # - Asegura que el tipo de evento y campos requeridos sean válidos.
      # - Rastrea el progreso del mensaje en Redis.
      # - Si todos los mensajes han sido procesados, dispara el evento de finalización de campaña.
      #
      # @param payload [Hash] El payload del evento.
      def process(payload)
        return unless valid_event?(payload)

        campaign_id = payload['campaign_id']
        message_id = payload['message_id']
        payload.fetch('status', 'sent').to_sym

        log_info("Processing message sent event for campaign #{campaign_id}, message #{message_id}")

        # Ya no verificamos si la campaña está marcada como completada en Redis
        # porque ya no estamos marcando la campaña como completada en Redis
        is_already_completed = false
        logger.warn("DIAGNOSTIC: Is campaign already marked as completed? #{is_already_completed} (always false now)")

        # Decrease pending messages count
        # Movido desde CampaignMessageConsumer para asegurar que solo se decremente
        # cuando el evento campaign_message_sent_event haya sido procesado correctamente
        redis_key = "campaign:#{campaign_id}:pending_messages"
        current_count = Redis.current.decr(redis_key).to_i
        logger.info("Decreased pending messages count for campaign #{campaign_id}: #{current_count} remaining")

        # Check if the counter is negative and correct it
        if current_count.negative?
          # Si el contador es negativo, corregirlo a cero
          logger.warn("⚠️ Pending messages count is negative (#{current_count}) for campaign #{campaign_id}, resetting to 0")
          Redis.current.set(redis_key, 0)
          current_count = 0
        end

        # Check the pending messages counter (after decrementing)
        pending_count = current_count
        logger.warn("DIAGNOSTIC: Pending messages counter: #{pending_count}")

        # Check if the campaign should be completed
        # La campaña está completa si el contador pending_messages es 0
        should_complete = pending_count.zero?
        logger.warn("DIAGNOSTIC: Should complete campaign? #{should_complete} (pending_count=#{pending_count}, is_already_completed=#{is_already_completed})")

        if should_complete && !is_already_completed
          log_info("All messages processed for campaign #{campaign_id}. Triggering completion.")

          # Use the completion service to handle campaign completion
          completion_service = Campaigns::CampaignCompletionService.new(
            campaign_id: campaign_id,
            event_producer: event_producer,
            logger: logger
          )

          logger.warn('DIAGNOSTIC: Calling completion_service.complete_if_needed')

          if completion_service.complete_if_needed
            log_success("Campaign #{campaign_id} completed successfully")
          else
            logger.warn('DIAGNOSTIC: completion_service.complete_if_needed returned false')
          end
        end
      rescue StandardError => e
        log_error('Error processing message sent event', e)
      ensure
        # Siempre devolver el productor al pool si lo obtuvimos
        if @event_producer
          Mercately::Kafka::ProducerPool.release_producer(@event_producer)
          logger.info('🔄 MESSAGE_SENT_PROCESSOR: Productor devuelto al pool')
          @event_producer = nil
        end
      end

      private

        # Validates the event payload.
        #
        # @param payload [Hash] The event payload.
        # @return [Boolean] True if the event is valid, false otherwise.
        def valid_event?(payload)
          return false unless payload.is_a?(Hash)
          return false unless payload['event_type'] == 'campaign_message_sent_event'
          return false unless payload['campaign_id']

          true
        end

        # Logs the current campaign status.
        #
        # @param campaign_id [Integer] The campaign ID.
        def log_campaign_status(campaign_id)
          # Ignoramos TrackerService por ahora
          # stats = campaign_tracker.get_stats(campaign_id)
          # log_info("Campaign status: #{stats[:sent]} sent, #{stats[:failed]} failed, #{stats[:total]} total")

          # En su lugar, usamos el contador pending_messages de Redis
          pending_count = Redis.current.get("campaign:#{campaign_id}:pending_messages").to_i
          log_info("Campaign status: pending_messages=#{pending_count}")
        end

        # Logs an error message with exception details.
        #
        # @param message [String] The error message.
        # @param exception [StandardError] The exception instance.
        def log_error(message, exception)
          logger.error("#{message}: #{exception.message}")
        end

        # Logs an info message.
        #
        # @param message [String] The info message.
        def log_info(message)
          logger.info(message)
        end

        # Logs a success message.
        #
        # @param message [String] The success message.
        def log_success(message)
          logger.info(message)
        end
    end
  end
end
