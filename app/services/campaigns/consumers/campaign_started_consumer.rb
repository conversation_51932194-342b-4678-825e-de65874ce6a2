require 'ostruct'

# Require the constants file explicitly
module Campaigns
  module Consumers
    # Consumer responsible for processing campaign start events received from Kafka.
    #
    # This consumer:
    # - Validates the event type.
    # - Retrieves the campaign from the database.
    # - Iterates over campaign customers, sending messages.
    # - Produces a "message sent" event regardless of delivery success.
    #
    # The process is designed for **scalability and testability**, ensuring separation of concerns.
    #
    # @example Processing a campaign start event
    #   consumer = Campaigns::Consumers::CampaignStartedConsumer.new
    #   consumer.process({ "event_type" => "campaign_started_event", "campaign_id" => 123 })
    class CampaignStartedConsumer
      attr_reader :logger

      # Initializes the consumer with necessary dependencies.
      #
      # @param logger [Logger] Logger instance.
      def initialize(logger: Rails.logger)
        @logger = logger
      end

      # Returns the message producer instance.
      #
      # @return [Campaigns::MessageEventProducer] The message producer instance.
      def message_producer
        @message_producer ||= Campaigns::MessageEventProducer.new
      end

      # Processes an event received from Kafka.
      #
      # - Ensures the event is valid.
      # - Fetches the campaign.
      # - Processes customers by sending messages.
      #
      # @param payload [Hash] The event payload.
      def process(payload)
        # Log the full payload for debugging
        logger.info("🔍 CAMPAIGN_STARTED_CONSUMER: Received payload: #{payload.inspect}")

        unless valid_event_type?(payload, 'campaign_started_event')
          logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Invalid event type: #{payload['event_type']}")
          return
        end

        campaign_id = payload['campaign_id']
        logger.info("🚀 CAMPAIGN_STARTED_CONSUMER: Processing campaign start event #{campaign_id}")

        # Log Redis connection status
        begin
          redis_info = Redis.current.info
          logger.info('✅ CAMPAIGN_STARTED_CONSUMER: Redis connection successful. Version: ' \
                      "#{redis_info['redis_version']}")
        rescue StandardError => e
          logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Redis connection error: #{e.message}")
        end

        campaign = fetch_campaign(campaign_id)
        unless campaign
          logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Campaign not found: #{campaign_id}")
          return
        end

        logger.info("✅ CAMPAIGN_STARTED_CONSUMER: Campaign found: #{campaign.name} (ID: #{campaign.id})")

        process_campaign_customers(campaign)
      rescue StandardError => e
        logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Error processing campaign start event: #{e.message}")
        logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Backtrace: #{e.backtrace.join("\n")}")
      end

      private

        # Validates if the event type matches the expected type.
        #
        # @param payload [Hash] The event payload.
        # @param expected_type [String] The expected event type.
        # @return [Boolean] True if the event type matches.
        def valid_event_type?(payload, expected_type)
          payload['event_type'] == expected_type
        end

        # Retrieves a campaign by ID.
        #
        # Logs an error if the campaign is not found.
        #
        # @param campaign_id [Integer] The campaign ID.
        # @return [Campaign, nil] The campaign instance or nil if not found.
        def fetch_campaign(campaign_id)
          campaign = Campaign.find_by(id: campaign_id)

          if campaign.nil?
            logger.error("Campaign with ID #{campaign_id} not found")
            return nil
          end

          # Ensure campaign has estimated_cost to avoid comparison errors
          if campaign.estimated_cost.nil?
            logger.warn("Campaign with ID #{campaign_id} has no estimated_cost, setting default value")
            campaign.update_column(:estimated_cost, 0)
          end

          campaign
        end

        # Iterates over campaign customers and processes them individually.
        #
        # Uses `find_each` for efficient batch processing.
        # Handles the case when no customers are associated with the campaign.
        #
        # @param campaign [Campaign] The campaign instance.
        def process_campaign_customers(campaign)
          logger.info("🔄 CAMPAIGN_STARTED_CONSUMER: Processing customers for campaign #{campaign.id}")

          # Verify if campaign has any customers
          begin
            customer_count = campaign.customers.count
            logger.info("🔢 CAMPAIGN_STARTED_CONSUMER: Customer count query successful: #{customer_count}")
          rescue StandardError => e
            logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Error counting customers: #{e.message}")
            logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Backtrace: #{e.backtrace.join("\n")}")
            return
          end

          if customer_count.zero?
            logger.warn("⚠️ CAMPAIGN_STARTED_CONSUMER: No customers found for campaign #{campaign.id}")
            return
          end

          logger.info("✅ CAMPAIGN_STARTED_CONSUMER: Found #{customer_count} customers for campaign #{campaign.id}")

          # Inicializar los contadores en Redis directamente
          begin
            logger.info('🔄 CAMPAIGN_STARTED_CONSUMER: Inicializando contadores en Redis...')

            # Obtener una conexión a Redis
            redis = Redis.current

            # Verificar que Redis está disponible
            redis_ping = redis.ping
            logger.info("✅ CAMPAIGN_STARTED_CONSUMER: Redis disponible, respuesta a PING: #{redis_ping}")

            # Inicializar contadores - Simplificado para depender solo de pending_messages
            redis_key_total = "campaign:#{campaign.id}:total"
            redis_key_pending = "campaign:#{campaign.id}:pending_messages"

            # Usar una transacción para inicializar los contadores
            redis.multi do |transaction|
              transaction.set(redis_key_total, customer_count)
              transaction.set(redis_key_pending, customer_count)
            end

            logger.info("✅ CAMPAIGN_STARTED_CONSUMER: Contadores inicializados en Redis para campaña #{campaign.id}")

            # Verificar que los contadores se inicializaron correctamente
            total = redis.get(redis_key_total).to_i
            pending = redis.get(redis_key_pending).to_i

            logger.info('📊 CAMPAIGN_STARTED_CONSUMER: Contadores después de inicialización:')
            logger.info("   - #{redis_key_total} = #{total}")
            logger.info("   - #{redis_key_pending} = #{pending}")

            # Verificar que los valores son correctos
            if total != customer_count || pending != customer_count
              logger.error('❌ CAMPAIGN_STARTED_CONSUMER: Los contadores no se inicializaron correctamente')
              logger.error("   - Esperado: #{customer_count}")
              logger.error("   - Total actual: #{total}")
              logger.error("   - Pendientes actual: #{pending}")
              return
            end

            logger.info('✅ CAMPAIGN_STARTED_CONSUMER: Verificación de contadores exitosa')
          rescue StandardError => e
            logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Error al inicializar contadores en Redis: #{e.message}")
            logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Backtrace: #{e.backtrace.join("\n")}")
            return
          end

          # 🔄 PRUEBA: Habilitando la producción de eventos pending para el segundo paso
          logger.info('🔄 PRUEBA: Habilitando producción de eventos campaign_message_pending_event')
          logger.info("🔄 PRUEBA: Produciendo #{customer_count} eventos pending para la campaña #{campaign.id}")

          # Producir un evento campaign_message_pending_event para cada cliente
          logger.info('🔄 CAMPAIGN_STARTED_CONSUMER: Starting to produce pending events for ' \
                      "#{customer_count} customers...")

          # Contador para seguimiento
          events_produced = 0
          errors_count = 0

          campaign.customers.find_each do |customer|
            if customer.nil?
              logger.warn("⚠️ CAMPAIGN_STARTED_CONSUMER: Nil customer found for campaign #{campaign.id}, skipping")
              next
            end

            begin
              produce_message_pending_event(campaign, customer)
              events_produced += 1

              # Log progress periodically
              if (events_produced % 10).zero? || events_produced == customer_count
                logger.info('📊 CAMPAIGN_STARTED_CONSUMER: Progress - produced ' \
                            "#{events_produced}/#{customer_count} pending events")
              end
            rescue StandardError => e
              errors_count += 1
              logger.error('❌ CAMPAIGN_STARTED_CONSUMER: Error producing pending event for customer ' \
                             "#{customer.id}: #{e.message}")
              # Don't log full backtrace for each error to avoid log flooding
            end
          end

          logger.info('✅ CAMPAIGN_STARTED_CONSUMER: Finished producing message pending events for campaign ' \
                      "#{campaign.id}")
          logger.info('📊 CAMPAIGN_STARTED_CONSUMER: Summary - Total: ' \
                      "#{customer_count}, Produced: #{events_produced}, Errors: #{errors_count}")
        end

        # Produces a "message pending" event for a customer.
        #
        # This event will be consumed by CampaignMessageConsumer to send the actual message.
        #
        # @param campaign [Campaign] The campaign instance.
        # @param customer [Customer] The customer instance.
        def produce_message_pending_event(campaign, customer)
          logger.info('🔄 CAMPAIGN_STARTED_CONSUMER: Produciendo evento campaign_message_pending_event ' \
                      "para campaña #{campaign.id}, cliente #{customer.id}")

          # Construir el payload del evento
          event_payload = {
            event_type: 'campaign_message_pending_event',
            campaign_id: campaign.id,
            customer_id: customer.id,
            timestamp: Time.current.to_i,
            retailer_id: campaign.retailer_id
          }

          logger.info("📦 CAMPAIGN_STARTED_CONSUMER: Payload del evento: #{event_payload.inspect}")

          # Obtener el productor del pool
          begin
            producer = Mercately::Kafka::ProducerPool.get_producer
            logger.info('✅ CAMPAIGN_STARTED_CONSUMER: Productor obtenido del pool correctamente')

            # Verificar que el productor sea válido
            if producer.nil?
              logger.error('❌ CAMPAIGN_STARTED_CONSUMER: El productor es nil')
              raise 'El productor es nil'
            end

            # Verificar la clase del productor
            logger.info("📊 CAMPAIGN_STARTED_CONSUMER: Clase del productor: #{producer.class}")
          rescue StandardError => e
            logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Error al obtener el productor: #{e.message}")
            logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Backtrace: #{e.backtrace.join("\n")}")
            raise # Re-raise to be caught by the caller
          end

          # Verificar el tópico
          topic = Campaigns::MessageEventProducer::TOPIC
          logger.info("📊 CAMPAIGN_STARTED_CONSUMER: Tópico a usar: #{topic}")

          # Producir el evento en el mismo topic que los otros eventos de campaña
          begin
            logger.info("🔄 CAMPAIGN_STARTED_CONSUMER: Enviando evento al tópico #{topic} con clave #{campaign.id}")

            # Convertir el payload a JSON
            json_payload = event_payload.to_json
            logger.info("📦 CAMPAIGN_STARTED_CONSUMER: JSON payload: #{json_payload}")

            # Producir el evento
            # Usar el ID de la campaña como clave para asegurar que todos los eventos
            # de la misma campaña vayan a la misma partición y se procesen en orden
            producer.produce(
              topic: topic,
              payload: json_payload,
              key: campaign.id.to_s # Usar el ID de la campaña como clave para asegurar el orden
            )

            # No esperamos confirmación de entrega - confiamos en Kafka
            logger.info('✅ CAMPAIGN_STARTED_CONSUMER: Evento producido (sin esperar confirmación)')
            logger.info('✅ CAMPAIGN_STARTED_CONSUMER: Confiando en Kafka para la entrega del mensaje')

            # Verificar que el evento se envió a la misma partición que el evento campaign_started_event
            logger.info('📊 CAMPAIGN_STARTED_CONSUMER: Verificar que el evento se envió a la partición correcta')

            # Devolver true para indicar éxito
            true
          rescue StandardError => e
            logger.error('❌ CAMPAIGN_STARTED_CONSUMER: Error al producir evento ' \
                         "campaign_message_pending_event: #{e.message}")
            logger.error("❌ CAMPAIGN_STARTED_CONSUMER: Backtrace: #{e.backtrace.join("\n")}")

            # Re-raise para que el caller pueda manejar el error
            raise
          ensure
            # Siempre devolver el productor al pool
            Mercately::Kafka::ProducerPool.release_producer(producer)
            logger.info('🔄 CAMPAIGN_STARTED_CONSUMER: Productor devuelto al pool')
          end
        end

        # Logs an error with exception details.
        #
        # @param message [String] The error message.
        # @param exception [StandardError] The raised exception.
        def log_error(message, exception)
          logger.error("#{message}: #{exception.message}")
        end
    end
  end
end
