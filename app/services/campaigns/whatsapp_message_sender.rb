# frozen_string_literal: true

module Campaigns
  # Servicio específico para envío de mensajes de WhatsApp en campañas
  # Usa el mismo flujo que el sistema sincrónico para máxima compatibilidad
  class WhatsappMessageSender
    attr_reader :campaign, :customer, :params, :logger

    # @param campaign [Campaign] Campaña a enviar
    # @param customer [Customer] Cliente destinatario
    # @param params [Hash] Parámetros del mensaje
    def initialize(campaign, customer, params = {})
      @campaign = campaign
      @customer = customer
      @params = params
      @logger = Rails.logger
    end

    # Método de clase para compatibilidad con el patrón de servicios
    #
    # @param campaign [Campaign] Campaña a enviar
    # @param customer [Customer] Cliente destinatario
    # @param params [Hash] Parámetros del mensaje
    # @return [Hash] Resultado estructurado
    def self.call(campaign, customer, params = {})
      new(campaign, customer, params).call
    end

    # Envía el mensaje de WhatsApp usando el mismo flujo que el sistema sincrónico
    #
    # @return [Hash] Resultado estructurado con success, message y error
    def call
      # Validar parámetros requeridos
      validation_result = validate_parameters
      return validation_result if validation_result

      logger.info("🔄 CAMPAIGNS: Enviando mensaje para campaña #{campaign.id}, cliente #{customer.id}")

      # Usar el mismo flujo que MessageSenderService (sistema sincrónico)
      send_gupshup_notification(build_params)
    rescue StandardError => e
      logger.error("❌ CAMPAIGNS: Error enviando mensaje: #{e.message}")
      logger.error("❌ CAMPAIGNS: Backtrace: #{e.backtrace.join('\n')}")

      # Retornar error estructurado
      {
        success: false,
        error: e.message,
        message: nil
      }
    end

    private

      # Valida que los parámetros requeridos estén presentes y no vacíos
      #
      # @return [Hash, nil] Error de validación o nil si todo está correcto
      def validate_parameters
        return validation_error('Campaign no puede ser nil') if campaign.nil?
        return validation_error('Customer no puede ser nil') if customer.nil?

        # Si están persistidos, tienen ID y son válidos
        return validation_error('Campaign debe estar guardada en la base de datos') if campaign.id.blank?
        return validation_error('Customer debe estar guardado en la base de datos') if customer.id.blank?

        nil
      end

      # Construye los parámetros para el envío (igual que MessageSenderService)
      #
      # @return [Hash] Parámetros para message sending
      def build_params
        {
          gupshup_template_id: campaign.whatsapp_template.gupshup_template_id,
          template_params: campaign.customer_content_params(customer),
          template: 'true',
          type: content_type,
          **message_params
        }
      end

      # Parámetros específicos del mensaje basados en el tipo de archivo adjunto
      #
      # @return [Hash] Message-specific parameters
      def message_params
        if campaign.file.attached?
          {
            caption: message_text,
            file_name: campaign.file.filename.to_s,
            url: campaign.file_url
          }
        else
          { message: message_text }
        end
      end

      # Texto del mensaje formateado para el cliente
      #
      # @return [String] Formatted message text
      def message_text
        campaign.customer_details_template(customer).gsub(/(\r)/, '')
      end

      # Tipo de contenido basado en archivo adjunto
      #
      # @return [String] Content type (text, image, video, or document)
      def content_type
        return 'text' unless campaign.file.attached?

        content_type = campaign.file.content_type
        return 'image' if content_type.include?('image')
        return 'video' if content_type.include?('video')

        'document'
      end

      # Tipo de recurso (file o template)
      #
      # @return [String] Resource type
      def resource_type
        campaign.file.attached? ? 'file' : 'template'
      end

      # Envía notificación vía servicio WhatsApp (igual que MessageSenderService)
      #
      # @param params [Hash] Message parameters
      # @return [Hash] Resultado estructurado
      def send_gupshup_notification(params)
        gws = Whatsapp::Outbound::Msg.new(campaign.retailer, customer)
        response = gws.send_message(type: resource_type, params: params)

        process_response(response)
      rescue StandardError => e
        logger.error("❌ CAMPAIGNS: Error en send_gupshup_notification: #{e.message}")
        {
          success: false,
          error: e.message,
          message: nil
        }
      end

      # Procesa la respuesta de la API (igual que MessageSenderService)
      #
      # @param response [Hash] API response containing message details
      # @return [Hash] Resultado estructurado
      def process_response(response)
        message = response[:message]

        unless message.instance_of?(GupshupWhatsappMessage)
          logger.error('❌ CAMPAIGNS: Respuesta no contiene GupshupWhatsappMessage válido')
          return {
            success: false,
            error: 'Respuesta inválida del servicio de WhatsApp',
            message: nil
          }
        end

        # Actualizar campaign_id (igual que MessageSenderService)
        message.update(campaign_id: campaign.id)

        logger.info("✅ CAMPAIGNS: Mensaje enviado exitosamente con ID: #{message.id}")

        {
          success: true,
          message: message,
          error: nil
        }
      rescue StandardError => e
        logger.error("❌ CAMPAIGNS: Error al procesar la respuesta de WhatsApp: #{e.message}")
        {
          success: false,
          error: "Error al procesar respuesta: #{e.message}",
          message: nil
        }
      end

      # Retorna error de validación
      #
      # @param message [String] Error message
      # @return [Hash] Structured error response
      def validation_error(message)
        logger.error("❌ CAMPAIGNS: #{message}")
        {
          success: false,
          error: message,
          message: nil
        }
      end
  end
end
