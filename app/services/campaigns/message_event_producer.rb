module Campaigns
  # Producer for campaign message events.
  #
  # This service is responsible for creating and publishing Kafka events related to message sending.
  #
  # @example Producing a message sent event
  #   producer = Campaigns::MessageEventProducer.new
  #   producer.produce_message_sent(campaign, customer, message)
  class MessageEventProducer
    # @return [Object] The Kafka producer instance.
    attr_reader :producer
    # @return [Logger] The logger instance.
    attr_reader :logger

    # Kafka topic where message events are published.
    TOPIC = 'mercately_campaign_events'.freeze

    # Event type for message sent events.
    EVENT_TYPE = 'campaign_message_sent_event'.freeze

    # Initializes a new MessageEventProducer instance.
    #
    # @param producer [Object] The Kafka producer instance (default: Mercately::Kafka::ProducerPool.get_producer).
    # @param logger [Logger] The logger instance (default: Rails.logger).
    def initialize(producer: nil, logger: Rails.logger)
      @producer = producer || Mercately::Kafka::ProducerPool.get_producer
      @logger = logger
    end

    # Produces a "message sent" event to Kaf<PERSON>.
    #
    # Steps:
    # - Constructs the event payload.
    # - Logs the event production.
    # - Publishes the event to Kafka.
    #
    # @param campaign [Campaign] The campaign instance.
    # @param customer [Customer] The customer instance.
    # @param message [Message] The message instance.
    # @return [Boolean] Returns true if the event was successfully produced; otherwise, false.
    def produce_message_sent(campaign, customer, message)
      event_payload = build_event_payload(campaign, customer, message)

      log_event_production(campaign, customer, message)

      # Enviar el mensaje usando el productor del pool
      begin
        # Producir el mensaje sin especificar partición para que Kafka decida
        result = @producer.produce(
          topic: TOPIC,
          payload: event_payload.to_json,
          key: partition_key(campaign, message)
        )

        logger.info("✅ Mensaje enviado exitosamente a Kafka: #{result.inspect}")
        true
      rescue StandardError => e
        log_error('Error producing message sent event', e)
        false
      ensure
        # Siempre devolver el productor al pool si lo obtuvimos del pool
        Mercately::Kafka::ProducerPool.release_producer(@producer) if @producer.is_a?(Rdkafka::Producer)
      end
    end

    private

      # Builds the event payload for the message sent event.
      #
      # @param campaign [Campaign] The campaign instance.
      # @param customer [Customer] The customer instance.
      # @param message [Message] The message instance.
      # @return [Hash] The structured event payload.
      def build_event_payload(campaign, customer, message)
        {
          event_type: EVENT_TYPE,
          campaign_id: campaign.id,
          customer_id: customer.id,
          message_id: message.id,
          channel: message.channel,
          status: message.status,
          timestamp: Time.current.to_i
        }
      end

      # Logs event production details.
      #
      # @param campaign [Campaign] The campaign instance.
      # @param customer [Customer] The customer instance.
      # @param message [Message] The message instance.
      def log_event_production(campaign, customer, message)
        logger.info("Producing message sent event for campaign #{campaign.id}, customer #{customer.id}, message #{message.id}")
      end

      # Logs an error message and its backtrace.
      #
      # @param message [String] The error message.
      # @param exception [StandardError] The raised exception.
      def log_error(message, exception)
        logger.error("#{message}: #{exception.message}")
        logger.error(exception.backtrace.join("\n"))
      end

      # Generates a partition key for Kafka.
      #
      # @param campaign [Campaign] The campaign instance.
      # @param message [Message] The message instance.
      # @return [String] The partition key.
      def partition_key(campaign, _message)
        # Usar solo el ID de la campaña como clave para asegurar que todos los mensajes
        # de una misma campaña vayan a la misma partición y se procesen en orden
        campaign.id.to_s
      end
  end
end
