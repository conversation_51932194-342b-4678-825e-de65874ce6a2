module Campaigns
  # Service responsible for completing a campaign.
  #
  # This service is responsible for:
  # 1. Checking if a campaign is complete (all messages have been sent)
  # 2. Producing a campaign_completed_event to Kafka
  # 3. Updating the campaign status in the database
  #
  # @example
  #   service = Campaigns::CampaignCompletionService.new(campaign_id: 123)
  #   service.complete_if_needed
  class CampaignCompletionService
    attr_reader :campaign_id, :event_producer, :logger

    # Initializes the service with the campaign ID and dependencies.
    #
    # @param campaign_id [Integer] The campaign ID.
    # @param event_producer [Object] The Kafka producer instance.
    # @param logger [Logger] The logger instance.
    def initialize(campaign_id:, event_producer: Mercately::Kafka::ProducerPool.get_producer, logger: Rails.logger)
      @campaign_id = campaign_id
      @event_producer = event_producer || Mercately::Kafka::ProducerPool.get_producer
      @logger = logger
    end

    # Completes the campaign if all messages have been sent.
    #
    # @return [Boolean] True if the campaign was completed, false otherwise.
    def complete_if_needed
      # Check if the campaign is already completed
      campaign = Campaign.find_by(id: campaign_id)
      if campaign.nil?
        logger.error("Campaign not found: #{campaign_id}")
        return false
      end

      if campaign.sent? || campaign.failed?
        logger.info("Campaign #{campaign_id} is already completed with status: #{campaign.status}")
        return false
      end

      # Check if all messages have been sent
      pending_count = Redis.current.get("campaign:#{campaign_id}:pending_messages").to_i
      if pending_count.positive?
        logger.info("Campaign #{campaign_id} still has #{pending_count} pending messages")
        return false
      end

      # Get campaign statistics
      total_messages = Redis.current.get("campaign:#{campaign_id}:total").to_i
      sent_messages = total_messages - pending_count
      failed_messages = 0 # In a real implementation, we would track failed messages

      # Produce campaign_completed_event
      event_payload = {
        event_type: 'campaign_completed_event',
        campaign_id: campaign_id,
        total_messages: total_messages,
        sent_messages: sent_messages,
        failed_messages: failed_messages,
        timestamp: Time.current.to_i
      }

      logger.info("Producing campaign_completed_event for campaign #{campaign_id}")
      logger.info("Payload: #{event_payload.inspect}")

      begin
        # Produce the event
        result = event_producer.produce(
          topic: 'mercately_campaign_events',
          payload: event_payload.to_json,
          key: campaign_id.to_s
        )

        logger.info("Campaign completed event produced successfully: #{result.inspect}")

        # Update campaign status directly
        campaign.update(status: :sent)
        # NOTE: total_messages, sent_messages, failed_messages no existen en el modelo Campaign
        # Estas estadísticas se mantienen en Redis y en los eventos de Kafka

        logger.info("Campaign #{campaign_id} marked as sent")
        true
      rescue StandardError => e
        logger.error("Error producing campaign_completed_event: #{e.message}")
        logger.error(e.backtrace.join("\n"))
        false
      ensure
        # Always return the producer to the pool if we got it from the pool
        if event_producer.is_a?(Rdkafka::Producer)
          Mercately::Kafka::ProducerPool.release_producer(event_producer)
          logger.info('Producer returned to pool')
        end
      end
    end
  end
end
