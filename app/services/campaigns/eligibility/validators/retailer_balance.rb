module Campaigns
  module Eligibility
    module Validators
      # Validates if the retailer has sufficient balance for the campaign.
      #
      # This validator checks:
      # - If the retailer has BSP Mercately connection (unlimited balance)
      # - If the retailer's WhatsApp balance is sufficient for the estimated cost
      class RetailerBalance < Base
        # Validates retailer balance for the campaign.
        #
        # @param campaign [Campaign] The campaign to validate.
        # @param result [Result] The result object to store validation outcomes.
        # @return [void]
        def validate(campaign, result)
          has_balance = retailer_has_sufficient_balance?(campaign)
          result.add_check(:has_balance, has_balance)
          log_check('has_balance', has_balance)
        rescue StandardError => e
          log_error('has_balance', e)
          result.add_check(:has_balance, false)
        end

        private

          # Checks if the retailer has sufficient balance for the campaign.
          #
          # @param campaign [Campaign] The campaign to check.
          # @return [Boolean] True if the retailer has sufficient balance.
          def retailer_has_sufficient_balance?(campaign)
            # Si el retailer tiene conexión BSP Mercately, tiene balance ilimitado
            if campaign.retailer.connect_bsp_mercately
              Rails.logger.info("🔍 Retailer #{campaign.retailer.id} tiene BSP Mercately - balance ilimitado")
              return true
            end

            # Obtener valores con fallbacks seguros
            ws_balance = campaign.retailer.ws_balance || 0.0
            estimated_cost = campaign.estimated_cost || 0.0

            Rails.logger.info("🔍 Balance check - Retailer #{campaign.retailer.id}: " \
                              "ws_balance=#{ws_balance}, estimated_cost=#{estimated_cost}")

            # Comparar balance vs costo estimado
            result = ws_balance >= estimated_cost
            Rails.logger.info("🔍 Balance sufficient: #{result}")
            result
          end
      end
    end
  end
end
