module Campaigns
  class WhatsappTemplates
    def execute
      ActiveRecord::Base.connection_pool.release_connection
      ActiveRecord::Base.connection_pool.with_connection do
        campaigns = Campaign.pending.where(send_at: 2.minutes.ago..1.minute.from_now, in_chunks: [false, nil])
        return unless campaigns.any?

        @campaign_ids = campaigns.ids
        campaigns.update_all status: :processing
      end

      Campaign.where(id: @campaign_ids).find_each.with_index do |campaign, index|
        if !campaign.retailer.connect_bsp_mercately && campaign.retailer.ws_balance < campaign.estimated_cost
          campaign.update(reason: :insufficient_balance, status: :failed)
          next
        end

        Rails.logger.info(<<~BRLOG
          ************QUEUEING CAMPAIGN************
          CAMPAIGN_ID: #{campaign.web_id}
          CAMPAIGN_INDEX: #{index}
          HOUR: #{Time.zone.now}
        BRLOG
                         )

        # Seleccionar la estrategia adecuada para esta campaña
        strategy = Campaigns::SenderStrategySelector.call(campaign: campaign)
        Rails.logger.info("🔍 Estrategia seleccionada: #{strategy.class.name} para campaña #{campaign.id}")

        # Ejecutar la estrategia seleccionada
        begin
          Rails.logger.info("🔍 Ejecutando estrategia #{strategy.class.name} para campaña #{campaign.id}")
          success = strategy.call(campaign: campaign)
          Rails.logger.info("🔍 Resultado de la estrategia: #{success.inspect} para campaña #{campaign.id}")

          if success
            Rails.logger.info("✅ Estrategia #{strategy.class.name} ejecutada exitosamente para campaña #{campaign.id}")
          else
            Rails.logger.error("❌ Error al ejecutar la estrategia #{strategy.class.name} " \
                               "para la campaña #{campaign.id}")
            Rails.logger.error('❌ Usando flujo sincrónico como fallback')
            Rails.logger.info("🔍 Ejecutando flujo sincrónico como fallback para campaña #{campaign.id}")
            Campaigns::SendCampaignJob.perform_later(campaign.id)
          end
        rescue StandardError => e
          Rails.logger.error("❌ Error al ejecutar la estrategia #{strategy.class.name} " \
                             "para la campaña #{campaign.id}: #{e.message}")
          Rails.logger.error("❌ Backtrace: #{e.backtrace.join("\n")}")
          Rails.logger.error('❌ Usando flujo sincrónico como fallback')
          Rails.logger.info("🔍 Ejecutando flujo sincrónico como fallback para campaña #{campaign.id}")
          Campaigns::SendCampaignJob.perform_later(campaign.id)
        end
      rescue StandardError => e
        Rails.logger.error("Error: Al ejecutar Job de envío de campaña API: #{e.message}")
        campaign.update(reason: :service_down, status: :failed)
        next
      end
    rescue StandardError => e
      Rails.logger.error("Error: Al ejecutar servicio de envío de campañas: #{e.message}")
    end
  end
end
