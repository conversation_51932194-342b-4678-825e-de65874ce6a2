class KarafkaManager
  class << self
    # Inicia el servidor Karafka si no está en ejecución
    # @return [Hash] Resultado de la operación
    def start
      # Verificar si este nodo debe ejecutar Karafka
      unless ENV['KAFKA_CONSUMER_ENABLED'] == 'true'
        return { success: false, message: 'Este nodo no está configurado para ejecutar Karafka' }
      end

      # Verificar si Karafka ya está en ejecución
      return { success: false, message: 'Karafka ya está en ejecución', status: status } if running?

      # Intentar adquirir el bloqueo
      unless KarafkaLock.acquire
        return { success: false, message: 'No se pudo adquirir el bloqueo para Karafka', status: status }
      end

      # Iniciar Karafka en segundo plano
      begin
        pid = spawn('bundle exec karafka server', out: Rails.root.join('log', 'karafka.log'),
                                                  err: Rails.root.join('log', 'karafka_error.log'))
        Process.detach(pid)

        Rails.logger.info("Karafka started with PID #{pid}")
        { success: true, pid: pid, message: 'Karafka iniciado correctamente' }
      rescue StandardError => e
        KarafkaLock.release
        Rails.logger.error("Error starting Karafka: #{e.message}")
        { success: false, message: "Error al iniciar Karafka: #{e.message}" }
      end
    end

    # Detiene el servidor Karafka si está en ejecución
    # @return [Hash] Resultado de la operación
    def stop
      pids = find_karafka_pids

      return { success: false, message: 'Karafka no está en ejecución' } if pids.empty?

      stopped_pids = []
      errors = []

      pids.each do |pid|
        Process.kill('TERM', pid)
        stopped_pids << pid
      rescue StandardError => e
        errors << "Error al detener proceso #{pid}: #{e.message}"
      end

      KarafkaLock.release

      if errors.empty?
        { success: true, stopped_pids: stopped_pids, message: 'Karafka detenido correctamente' }
      else
        { success: stopped_pids.any?, stopped_pids: stopped_pids, errors: errors,
          message: 'Karafka detenido con errores' }
      end
    end

    # Reinicia el servidor Karafka
    # @return [Hash] Resultado de la operación
    def restart
      stop_result = stop
      sleep(2) # Dar tiempo para que se detenga completamente
      start_result = start

      {
        success: start_result[:success],
        stop: stop_result,
        start: start_result,
        message: "Karafka reiniciado #{start_result[:success] ? 'correctamente' : 'con errores'}"
      }
    end

    # Verifica si Karafka está en ejecución
    # @return [Boolean] true si Karafka está en ejecución, false si no
    def running?
      !find_karafka_pids.empty? || KarafkaLock.locked?
    end

    # Obtiene el estado actual de Karafka
    # @return [Hash] Estado actual de Karafka
    def status
      pids = find_karafka_pids
      lock_info = KarafkaLock.info

      {
        running: pids.any? || lock_info.present?,
        pids: pids,
        lock: lock_info,
        node: {
          hostname: NodeRole.hostname,
          karafka_enabled: ENV['KAFKA_CONSUMER_ENABLED'] == 'true'
        }
      }
    end

    private

      # Encuentra los PIDs de los procesos Karafka en ejecución
      # @return [Array<Integer>] Lista de PIDs
      def find_karafka_pids
        `pgrep -f "karafka server"`.split("\n").map(&:to_i)
      end
  end
end
