module Shops
  class RetailerSync < Shops::Api
    def create(params = {})
      body = set_body
      body.merge!(params)
      response = HTTParty.post(
        "#{@shops_url}/api/v1/retailers",
        body: body,
        headers: set_headers
      )
      raise("Shops Response: #{response.code}, #{response.parsed_response}") if response.code != 201

      response.parsed_response
    end

    def update(params = {})
      body = set_body
      body.merge!(params)
      response = HTTParty.put(
        "#{@shops_url}/api/v1/retailers",
        body: body,
        headers: set_headers
      )
      raise("Shops Response: #{response.code}, #{response.parsed_response}") if response.code != 200

      response.parsed_response
    end

    private

      def set_body
        {
          retailer: retailer_data
        }
      end

      def retailer_data
        retailer_info = Retailer.find(@retailer.id)
        {
          active: retailer_info.active_shop,
          shop_main_color: retailer_info.shop_main_color,
          name: retailer_info.name,
          description: retailer_info.description,
          phone_number: retailer_phone_number(retailer_info),
          country: retailer_info.country_code,
          currency: retailer_info.currency_symbol,
          currency_code: currency_code(retailer_info),
          unique_key: retailer_info.unique_key,
          catalog_slug: retailer_info.catalog_slug,
          tax_amount: retailer_info.tax_amount,
          timezone: retailer_info.timezone,
          facebook_url: retailer_info.facebook_url,
          instagram_url: retailer_info.instagram_url,
          twitter_url: retailer_info.twitter_url,
          whatsapp_url: retailer_info.whatsapp_url,
          tiktok_url: retailer_info.tiktok_url,
          api_key: retailer_info.api_key,
          last_api_key_modified_date: retailer_info.last_api_key_modified_date,
          logo_url: logo_url(retailer_info),
          background_url: background_url(retailer_info),
          active_send_order: retailer_info.shop_active_send_order,
          active_store_pickup: retailer_info.shop_active_store_pickup,
          rotate_images_in_catalog: retailer_info.rotate_images_in_catalog,
          payphone_connected: retailer_info.payphone_connected,
          payphone_id: retailer_info.payphone_id,
          domain: retailer_info.domain,
          terms_conditions_content: retailer_info.terms_conditions_content,
          shipping_cost_method: retailer_info.shipping_cost_method,
          mia_products_sync: retailer_info.mia_products_sync,
          id_in_mercately: retailer_info.id,
          hide_product_prices: retailer_info.hide_product_prices
        }
      end

      def retailer_phone_number(retailer)
        retailer.gupshup_phone_number.presence ||
          retailer.qr_phone_number.presence ||
          retailer.phone_number
      end

      def currency_code(retailer)
        retailer.currency&.downcase.presence || 'usd'
      end

      def logo_url(retailer)
        retailer.avatar.attached? ? retailer.avatar.public_url : ''
      end

      def background_url(retailer)
        retailer.background.attached? ? retailer.background.public_url : ''
      end
  end
end
