class OneSignalPushNotification
  def initialize(emails, body, customer_id, channel, additional_data = nil)
    @emails = Array(emails).compact
    @body = body
    @customer_id = customer_id
    @channel = channel
    @additional_data = additional_data
  end

  def self.send_sales_push(emails, order, hide_prices = false)
    body = "Cliente: #{order[:customer][:full_name]}.#{hide_prices ? '' : "\nTotal: #{order[:total]}"}"
    params = {
      'app_id' => ENV['ONE_SIGNAL_APP_ID'],
      'headings' => { 'en' => '¡Tienes un nuevo pedido! 🤑' },
      'contents' => { 'en' => body },
      'data' => {
        'type' => 'navigate',
        'screen' => 'Order',
        'screenParamKey' => 'orderId',
        'screenParamValue' => order[:web_id]
      },
      'priority' => 10,
      'content_available' => true,
      'mutable_content' => true,
      'channel_for_external_user_ids' => 'push',
      'include_external_user_ids' => emails
    }
    OneSignalPushNotification.send_request(params)
  end

  def self.send_qr_disconnection_push(emails, reconnect_url)
    body = '⚠️ Parece que perdiste la conexión de WhatsApp, haz click aquí y escanea tu código QR para reconectarte.'
    params = {
      'app_id' => ENV['ONE_SIGNAL_APP_ID'],
      'headings' => { 'en' => '¡Conexión de Whatsapp!' },
      'contents' => { 'en' => body },
      'data' => {},
      'url' => reconnect_url,
      'priority' => 10,
      'content_available' => true,
      'mutable_content' => true,
      'channel_for_external_user_ids' => 'push',
      'include_external_user_ids' => emails
    }
    OneSignalPushNotification.send_request(params)
  end

  def self.send_request(params)
    return if Rails.env.test?

    uri = URI.parse('https://onesignal.com/api/v1/notifications')
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri.path,
                                  'Content-Type' => 'application/json;charset=utf-8',
                                  'Authorization' => "Basic #{ENV['ONE_SIGNAL_AUTH']}")
    request.body = params.as_json.to_json
    http.request(request)
  end

  def self.send_message(emails, title, message)
    params = {
      'app_id' => ENV['ONE_SIGNAL_APP_ID'],
      'headings' => { 'en' => message },
      'contents' => { 'en' => message },
      'data' => {
        "type": 'message',
        "title": title,
        "body": message
      },
      'priority' => 10,
      'content_available' => true,
      'mutable_content' => true,
      'channel_for_external_user_ids' => 'push',
      'include_external_user_ids' => emails
    }
    OneSignalPushNotification.send_request(params)
  end

  def self.send_template(retailer_user_emails, template_id)
    params = {
      'app_id' => ENV['ONE_SIGNAL_APP_ID'],
      'template_id' => template_id,
      'priority' => 10,
      'content_available' => true,
      'mutable_content' => true,
      'channel_for_external_user_ids' => 'push',
      'include_external_user_ids' => retailer_user_emails
    }
    OneSignalPushNotification.send_request(params)
  end

  def send_messages
    customer = Customer.find(@customer_id)
    return unless customer.present?

    data = {
      "type": 'message',
      "channel": @channel,
      "customer_id": @customer_id,
      "title": customer.notification_info,
      "body": @body,
      "retailer_id": customer.retailer_id
    }
    data.merge!(@additional_data) unless @additional_data.nil?
    params = {
      'app_id' => ENV['ONE_SIGNAL_APP_ID'],
      'headings' => { 'en' => customer.notification_info },
      'contents' => { 'en' => @body },
      'data' => data,
      'priority' => 10,
      'content_available' => true,
      'mutable_content' => true,
      'channel_for_external_user_ids' => 'push',
      'include_external_user_ids' => @emails,
      'android_channel_id' => '055762dd-3694-43fd-953f-25af3977b7d1',
      'android_group' => @customer_id.to_s
    }
    OneSignalPushNotification.send_request(params)
  end

  def self.send_financial_results_push(emails, data)
    body = "Clientes nuevos: #{data[:new_customers]}\n"
    body += "Clientes recurrentes: #{data[:recurring_customers]}\n"
    body += "Total de ventas: #{data[:total_sales]}"

    params = {
      'app_id' => ENV['ONE_SIGNAL_APP_ID'],
      'headings' => { 'en' => "Tus resultados del #{1.day.ago.strftime('%d-%m-%Y')}" },
      'contents' => { 'en' => body },
      'data' => {},
      'priority' => 10,
      'content_available' => true,
      'mutable_content' => true,
      'channel_for_external_user_ids' => 'push',
      'include_external_user_ids' => emails
    }
    OneSignalPushNotification.send_request(params)
  end
end
