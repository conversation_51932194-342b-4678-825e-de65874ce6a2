module Chat<PERSON>ot<PERSON>el<PERSON>
  def action_events_list
    exceptions = [:get, :put, :patch, :remove]

    ChatBotAction.action_events.except(*exceptions).keys.collect do |a|
      [ChatBotAction.enum_translation(:action_event, a), a]
    end
  end

  def option_types_list
    ChatBotOption.option_types.except(
      :message,
      :jump_to,
      :failure,
      :active_mia,
      :api,
      :resolve_chat
    ).keys.collect do |a|
      [ChatBotOption.enum_translation(:option_type, a), a]
    end
  end

  def payload_types_list
    exceptions = [:form]

    ChatBotAction.payload_types.except(*exceptions).keys.collect do |a|
      [ChatBotAction.enum_translation(:payload_type, a), a]
    end
  end

  def action_types_list(classification)
    except_actions = {
      default: %i[auto_generate_option repeat_endpoint_option ctx_notify],
      success: %i[exec_callback repeat_endpoint_option go_back_bot ctx_notify],
      failed: %i[exec_callback auto_generate_option ctx_notify]
    }[classification.to_sym]

    ChatBotAction.action_types.except(*except_actions).keys.collect do |a|
      [ChatBotAction.enum_translation(:action_type, a), a]
    end
  end

  def on_failed_attempts_list
    ChatBot.on_failed_attempts.keys.collect { |o| [ChatBot.enum_translation(:on_failed_attempt, o), o] }
  end

  def team_assignment_list(retailer, chat_bot)
    query = team_assignment_query(chat_bot.platforms)

    retailer.team_assignments.where(query).active_for_assignments.map { |ta| [ta.name, ta.id] }
  end

  def interactive_options
    ChatBotOption.interactives.keys.filter { |i| i != 'fake' }
      .collect { |a| [ChatBotOption.enum_translation(:interactive, a), a] }
  end

  private

    def team_assignment_query(platforms)
      return if platforms.blank?

      query = ''
      operator = ''
      platforms.each do |p|
        query += "#{operator} #{p} = true "
        operator = :or
      end
      query
    end
end
