module FacebookMessages
  extend ActiveSupport::Concern

  included do
    include AgentMessengerAssignmentConcern
    include PushNotificationable
    include CustomerTmpMessagesConcern
    include CustomerEvents::ServiceConcern
    include CustomerEvents::MessageConcern

    belongs_to :facebook_retailer
    belongs_to :customer
    belongs_to :retailer_user, optional: true

    validates :mid, uniqueness: { scope: :facebook_retailer_id }, allow_blank: true

    before_validation :nil_to_false
    before_create :set_sender_information
    before_create :attach_agent_name
    after_create :sent_by_retailer?
    after_create :assign_agent, unless: :note
    after_create :set_recent_inbound_message_date

    after_commit :upload_media_resources, if: :inbound_media?, on: :create
    after_commit :send_welcome_message, on: :create, unless: :note
    after_commit :send_inactive_message, on: :create, unless: :note
    after_commit :broadcast_to_counter_channel, on: [:create, :update]
    # Se incluye aca porque necesita ejecutarse antes del broadcast.
    include StatusChatConcern
    after_commit :reactivate_chat, on: :create, unless: :note
    after_commit :mark_unread_flag, on: :create
    after_commit :set_last_interaction, on: :create, unless: :note
    after_commit :send_facebook_message, on: :create, unless: -> { note || mia_flag.present? }

    scope :customer_unread, -> { where(date_read: nil, sent_by_retailer: false) }
    scope :retailer_unread, -> { where(date_read: nil, sent_by_retailer: true) }
    scope :inbound, -> { where(sent_by_retailer: false) }
    scope :unread, -> { where(date_read: nil) }
    scope :range_between, -> (start_date, end_date) { where(created_at: start_date..end_date) }

    delegate :retailer, to: :facebook_retailer

    attr_accessor :file_url, :file_content_type, :do_not_serialize, :attached_file
  end

  def message_info
    return text if file_type.blank?

    general_file_type = file_type.split('/').first
    case general_file_type
    when 'image'
      'Imagen'
    when 'video'
      'Video'
    when 'audio', 'voice'
      'Audio'
    when 'location'
      'Ubicación'
    when 'sticker'
      'Sticker'
    when 'fallback'
      'Publicación'
    else
      'Archivo'
    end
  end

  def sent_by_agent?
    sent_by_retailer
  end

  private

    def platform
      self.class == FacebookMessage ? 'messenger' : 'instagram'
    end

    def sent_by_retailer?
      return unless sender_uid == facebook_retailer.uid || sender_uid == facebook_retailer.instagram_uid

      update_column(:sent_by_retailer, true)
    end

    def send_facebook_message
      return unless sent_from_mercately

      pl = payload || {}
      comment = get_comment
      m = if text.present? && file_url.blank?
            facebook_service.send_message(id_client, text, pl, comment&.comment_id)
          elsif file_data.present? || file_url.present?
            facebook_service.send_attachment(
              id_client, file_data, filename, file_url, file_type, file_content_type, pl, comment&.comment_id
            )
          end
      update_scheduled_automation(m, comment)
      update_columns(mid: m['message_id'], error_message: m['error'].try(:[], 'message'))
      import_facebook_message(m['message_id']) if file_data.present? || file_url.present?
    end

    def update_scheduled_automation(response, comment)
      return unless comment

      sch_automation = case self.class.to_s
                       when 'FacebookMessage'
                         retailer.scheduled_automations.find_by(facebook_comment_id: comment&.id)
                       when 'InstagramMessage'
                         retailer.scheduled_automations.find_by(instagram_comment_id: comment&.id)
                       end

      return unless sch_automation

      sch_automation.update(status: response['error'].blank? ? 'sent' : 'failed')
    end

    def attach_agent_name
      return if !retailer.attach_agent_name? || retailer_user.nil? || text.blank?

      agent_label = retailer_user.full_name || retailer_user.email
      self.text = "#{agent_label}:\n\n#{text}"
    end

    def broadcast_to_counter_channel
      facebook_helper = FacebookNotificationHelper
      retailer = facebook_retailer.retailer
      agents = customer.agent.present? ? [customer.agent] : retailer.retailer_users.all_customers.to_a
      facebook_helper.broadcast_data(retailer, agents, self, customer.agent_customer, nil, platform)
    end

    def send_welcome_message
      return if sent_by_retailer

      retailer = facebook_retailer.retailer
      welcome_message = customer.messenger? ? retailer.messenger_welcome_message : retailer.instagram_welcome_message
      total_messages = customer.total_messenger_messages
      return unless total_messages == 1 && welcome_message && sent_by_retailer == false

      send_messenger_notification(welcome_message.message)
    end

    def send_inactive_message
      return if sent_by_retailer

      retailer = facebook_retailer.retailer
      inactive_message = customer.messenger? ? retailer.messenger_inactive_message : retailer.instagram_inactive_message
      before_last_message_msn = customer.before_last_messenger_message

      return unless inactive_message && sent_by_retailer == false && before_last_message_msn &&
                    send_message?(before_last_message_msn, inactive_message)

      send_messenger_notification(inactive_message.message)
    end

    def send_messenger_notification(message)
      self.class.create(
        customer: customer,
        sender_uid: facebook_retailer.uid,
        id_client: id_client,
        facebook_retailer: facebook_retailer,
        text: message,
        sent_from_mercately: true,
        sent_by_retailer: true,
        url: nil,
        file_type: nil,
        filename: nil
      )
    end

    def send_message?(before_last_message_msn, inactive_message)
      hours = ((created_at - before_last_message_msn.created_at) / 3600).to_i

      hours >= inactive_message.interval
    end

    def import_facebook_message(mid)
      facebook_service.import_delivered(mid, customer.psid)
    end

    def facebook_service
      Facebook::Messages.new(facebook_retailer, platform)
    end

    def set_last_interaction
      if platform == 'messenger'
        customer.update_column(:last_msn_interaction, created_at)
      else
        customer.update_column(:last_ig_interaction, created_at)
      end
    end

    def set_sender_information
      return unless retailer_user.present?

      self.sender_first_name = retailer_user.first_name
      self.sender_last_name = retailer_user.last_name
      self.sender_email = retailer_user.email
    end

    def mark_unread_flag
      return unless sent_by_retailer == false

      retailer_counter = RetailerCounterCache.fetch(retailer.id)
      return if retailer_counter.blank?

      if customer.messenger?
        retailer_user_field = :unread_messenger_chats_count
        total_field = :fb_messages
        unassigned_field = :fb_messages_na
      else
        retailer_user_field = :unread_instagram_chats_count
        total_field = :ig_messages
        unassigned_field = :ig_messages_na
      end

      customer.with_lock do
        if customer.messenger?
          customer.custom_update_columns(unread_messenger_messages: customer.unread_messenger_messages + 1)
        else
          customer.custom_update_columns(unread_instagram_messages: customer.unread_instagram_messages + 1)
        end
      end
      return unless customer.unread_messenger_messages == 1 || customer.unread_instagram_messages == 1

      agent = customer.agent
      if agent.present?
        retailer_counter.inc(total_field => 1)
        agent.update(retailer_user_field => agent.send(retailer_user_field) + 1)
      else
        retailer_counter.inc(total_field => 1, unassigned_field => 1)
      end
    rescue StandardError => e
      Rails.logger.error(e)
      SlackError.send_error(e)
    end

    def nil_to_false
      self.note = false if note.nil?
    end

    def reactivate_chat
      return if sent_by_retailer == true

      customer.platform_to_use = Message::Platform.call(message_klass: self.class.to_s)
      customer.reactivate_chat! unless block_chat_reactivation
    end

    def set_recent_inbound_message_date
      return if sent_by_retailer == true

      if customer.messenger?
        customer.update_columns(recent_inbound_msn_date: created_at)
      else
        customer.update_columns(recent_inbound_ig_date: created_at)
      end
    end

    def upload_media_resources
      UploadResourcesJob.perform_later(id, platform)
    end

    def get_comment
      if platform == 'messenger'
        return if facebook_comment_id.blank?

        facebook_comment
      else
        return if instagram_comment_id.blank?

        instagram_comment
      end
    end
end
