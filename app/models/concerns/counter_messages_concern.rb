module CounterMessagesConcern
  extend ActiveSupport::Concern

  included do
    after_commit :increase_unread_counter, on: :create
  end

  private

    def increase_unread_counter
      return if direction != 'inbound'

      retailer_counter = get_or_create_retailer_counter
      return if retailer_counter.blank?

      # Check if it's the first message BEFORE incrementing customer count
      is_first_message = customer.count_unread_messages.zero?

      update_customer_unread_count
      update_counters(retailer_counter, is_first_message)
    rescue StandardError => e
      Rails.logger.error("[CounterMessagesConcern] Error: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      SlackError.send_error(e)
    end

    def get_or_create_retailer_counter
      # Use thread-local cache to avoid multiple MongoDB queries within the same request
      RetailerCounterCache.fetch(retailer_id)
    rescue StandardError => e
      Rails.logger.warn "[CounterMessagesConcern] Cache fetch failed: #{e.message}, using direct query"
      RetailerCounter.find_by(retailer_id: retailer_id)
    end

    def update_customer_unread_count
      customer.with_lock do
        customer.custom_update_columns(count_unread_messages: customer.count_unread_messages + 1)
      end
    end

    def update_counters(retailer_counter, is_first_message)
      agent = customer.agent

      # Only increment counters if it's the first unread message (matching develop behavior)
      if is_first_message
        if agent.present?
          retailer_counter.inc(ws_messages: 1)
          agent.with_lock do
            agent.update(unread_whatsapp_chats_count: agent.unread_whatsapp_chats_count + 1)
          end
        else
          retailer_counter.inc(ws_messages: 1, ws_messages_na: 1)
        end
      end
    end
end
