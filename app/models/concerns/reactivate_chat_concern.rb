module ReactivateChatConcern
  extend ActiveSupport::Concern

  included do
    after_commit :reactivate, on: :create
  end

  private

    def reactivate
      begin
        return if note
      rescue
      end
      return if direction != 'inbound'

      customer.platform_to_use = Message::Platform.call(message_klass: self.class.to_s)
      customer.reactivate_chat! unless block_chat_reactivation
    rescue StandardError => e
      Rails.logger.error(e)
      SlackError.send_error(e)
    end
end
