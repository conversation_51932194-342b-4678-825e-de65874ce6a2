module Messages::GupshupWhatsappMessageBehavior
  extend ActiveSupport::Concern

  attr_accessor :skip_automatic, :replied_deep

  included do
    def type
      message_payload.try(:[], 'payload').try(:[], 'type') || message_payload.try(:[], 'type')
    end

    # rubocop:disable Naming/PredicateName
    def has_referral?
      message_payload['payload'].try(:[], 'referral').present?
    end

    def has_referral_media?
      return false if integration == 'qr' && !retailer.connect_qr_mercately?

      is_image_or_video?
    end
    # rubocop:enable Naming/PredicateName

    def from_group
      message_payload.try(:[], 'from_group')
    end

    def inbound_sender_full_name
      message_payload.try(:[], 'payload').try(:[], 'sender').try(:[], 'name')
    end

    def inbound_sender_phone
      message_payload.try(:[], 'payload').try(:[], 'sender').try(:[], 'phone')
    end

    def referral_type_media
      return 'image' if is_image?

      'video'
    end

    def referral_media_id
      message_payload['payload'].try(:[], 'referral').try(:[], 'image').try(:[], 'id').presence ||
        message_payload['payload'].try(:[], 'referral').try(:[], 'video').try(:[], 'id')
    end

    def referral_image_or_video_url
      message_payload['payload'].try(:[], 'referral').try(:[], 'image_url').presence ||
        message_payload['payload'].try(:[], 'referral').try(:[], 'video_url')
    end

    private

      def is_image_or_video?
        is_image? || is_video?
      end

      def is_image?
        message_payload['payload'].try(:[], 'referral').try(:[], 'image').try(:[], 'id').present? ||
          message_payload['payload'].try(:[], 'referral').try(:[], 'image_url').present?
      end

      def is_video?
        message_payload['payload'].try(:[], 'referral').try(:[], 'video').try(:[], 'id').present? ||
          message_payload['payload'].try(:[], 'referral').try(:[], 'video_url').present?
      end
  end
end
