module ChatBotSelectionConcern
  extend ActiveSupport::Concern
  # Busca si algun bot hace match con lo que envio la persona o si hay alguno con activacion
  # con cualquier interaccion
  # TODO - Se deberian pasar los metodos active_chat_bots y before_last_message por parametros
  # en lugar de llamarlos desde aqui.
  def get_current_bot
    # Si ya ha pasado el tiempo de reactivacion del chatbot que tiene activo el chat, lo reseteamos.
    # Es decir que desactivamos el chatbot actual para poder activarlo de nuevo u otro.
    # Si el chatbot actual no tiene reactivacion, no pasa nada.
    reset_chat_bot_if_needed
    if active_chat_bot_available?
      chatbot = find_active_chat_bot
      return chatbot if chatbot.present?
    end

    find_chat_bot_by_interaction || find_chat_bot_by_trigger
  end

  # Revisa si el bot que tiene activo el customer ya esta fuera de tiempo, es decir, que ya paso
  # mas del tiempo de reactivacion desde la ultima vez que el customer escribio, y se le debe
  # desactivar.
  def chat_bot_out_of_time?
    return false if preactivated_bot?

    last_message = before_last_message
    chat_bot = ChatBots::CurrentOptionFinder.call(customer: customer, platform: platform)&.chat_bot
    chat_bot && chat_bot.reactivate_after.present? && last_message &&
      (((created_at - last_message.created_at) / 3600) >= chat_bot.reactivate_after)
  end

  # Remove URLs from text
  def remove_url_from_text(text)
    text.downcase.gsub(%r{\b(?:https?|ftp)://\S+\b}, '')
  end

  def platform
    @platform ||= ::Message::Platform.call(message_klass: self.class)
  end

  def reset_chat_bot_if_needed
    Customer::ChatBotDeactivator.call(customer, platform: platform) if chat_bot_out_of_time?
  end

  def find_active_chat_bot
    ChatBots::CurrentOptionFinder.call(customer: customer, platform: platform)&.chat_bot
  end

  def active_chat_bot_available?
    customer.policy.active_bot_for?(platform: platform)
  end

  def find_chat_bot_by_interaction
    active_chat_bots.find_by(any_interaction: true) if @media_selection || @text.blank?
  end

  def find_chat_bot_by_trigger
    chat_bots = active_chat_bots
    return chat_bots.find_by(any_interaction: true) if @text.instance_of?(Array)
    return if @text.blank?

    text = remove_url_from_text(@text) # Quita las urls del texto para que no interfieran en el match.

    chat_bots.where.not(triggers: nil).select do |cb|
      # Busca entre las opciones de activacion del bot, si alguna hace match con lo que envio la persona.
      triggers = cb.triggers.map { |trigger| I18n.transliterate(trigger.downcase) }
      triggers.include?(I18n.transliterate(text.strip))
    end&.first.presence || chat_bots.find_by(any_interaction: true)
  end

  def preactivated_bot?
    active_bot = customer.active_bots.find_by(platform: platform)
    active_bot&.is_preactivated? || false
  end
end
