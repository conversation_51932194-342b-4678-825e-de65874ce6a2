class InstagramMessage < ApplicationRecord
  include MiaAutomaticAnswerConcern
  include InstagramChatBotActionConcern
  include FacebookMessages
  include FirstLoad::DateRangeable
  include FirstLoad::MessagesConcern

  belongs_to :instagram_comment, optional: true
  belongs_to :scheduled_automation, optional: true

  before_create :upload_file, if: :upload_file?

  scope :inbound_messages, -> { where(sent_by_retailer: false) }
  scope :outbound_messages, -> { where(sent_by_retailer: true) }

  enum mia_flag: { 'buy' => 0, 'human_help' => 1 }

  attr_accessor :block_chat_reactivation

  private

    def upload_file
      return if file_type != 'image'

      response = Uploaders::S3Aws.new(facebook_retailer_id)
        .upload_file(attached_file, platform: 'instagram')

      self.file_url = response['secure_url'] || response['url']
      self.url = file_url.dup if file_url.present?
      self.file_type = 'image'
      self.file_data = nil if file_url.present?
    end

    def upload_file?
      (file_data.present? || attached_file.present?) && (url.blank? || file_url.blank?)
    end

    def inbound_media?
      !sent_by_retailer && url.present? && %w[video audio image].include?(file_type)
    end
end
