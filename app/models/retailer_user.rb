# frozen_string_literal: true

# rubocop:disable Rails/HasManyOrHasOneDependent
# rubocop:disable Rails/InverseOf
# rubocop:disable Metrics/ClassLength
# rubocop:disable Naming/PredicateName
class RetailerUser < ApplicationRecord
  include Delegations::RetailerUser
  include TransliteratableEmailConcern
  include EmailableConcern
  include RetailerUserNotifiableUpdateConcern

  belongs_to :retailer
  belongs_to :user, optional: true
  belongs_to :role, optional: true

  has_one :onboarding_schedule

  has_many :a_customers, class_name: 'Customer', dependent: :destroy
  has_many :customers, lambda { |agent|
    agent_filter = agent.only_assigned? && agent.agent? ? agent.id : [agent.id, nil].concat(agent.managed_agents)
    unscope(where: :retailer_user_id).where(retailer_user_id: agent_filter, retailer_id: agent.retailer_id)
  }

  has_many :customer_fb_posts
  has_many :agent_watched_videos
  has_many :agent_notifications, dependent: :destroy
  has_many :retailer_user_notifications, class_name: 'RetailerUserNotification'
  has_many :agent_customers
  has_many :deals
  has_many :contact_groups
  has_many :agent_teams, dependent: :destroy
  has_many :team_assignments, through: :agent_teams
  has_many :retailer_average_response_times
  has_many :mobile_tokens, dependent: :destroy
  has_many :templates, dependent: :destroy
  has_many :calendar_events, dependent: :destroy
  has_many :onboarding_notifications
  has_many :notifications, through: :retailer_user_notifications
  has_many :mercately_videos, through: :agent_watched_videos
  has_many :team_retailer_users, dependent: :destroy
  has_many :teams, through: :team_retailer_users
  has_many :agent_team_changelogs, dependent: :destroy
  has_many :user_academy_statuses, dependent: :destroy
  has_many :user_academy_progresses, dependent: :destroy

  validates :user_id, uniqueness: { scope: :retailer_id }, allow_nil: true
  validate :onboarding_status_format
  validate :at_least_one_admin, if: :retailer_admin_changed_to_false?, on: :update

  before_save :set_only_assigned
  before_save :check_counters
  before_save :max_agents_limit
  before_save :set_current, if: :set_current?
  before_save :set_current_mobile, if: :set_current_mobile?
  after_update :subscribe, if: :saved_change_to_email?

  after_commit :subscribe, on: :create
  after_commit :send_registered_email, if: :retailer_admin?
  after_commit :schedule_onboarding, on: :create, if: :owner?

  scope :all_customers, -> { where(only_assigned: false) }
  scope :only_assigned_customers, -> { where(only_assigned: true) }
  scope :active, lambda { |retailer_id|
    joins(:user).where(retailer_id: retailer_id, removed_from_team: false, users: { invitation_token: nil })
  }
  scope :active_and_pending_agents, -> (retailer_id) { where(retailer_id: retailer_id, removed_from_team: false) }
  scope :active_admins, lambda { |retailer_id|
    joins(:user).where(retailer_id: retailer_id, retailer_admin: true, removed_from_team: false, users:
      { invitation_token: nil })
  }
  scope :active_agents, lambda {
    joins(:user).where(retailer_admin: false, retailer_supervisor: false, removed_from_team: false, users:
      { invitation_token: nil })
  }

  enum locale: { es: 0, en: 1, pt: 2 }
  enum mobile_type: { android: 0, ios: 1 }

  accepts_nested_attributes_for :retailer
  accepts_nested_attributes_for :user
  accepts_nested_attributes_for :agent_teams, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :team_retailer_users, allow_destroy: true

  delegate :first_name,
           :last_name,
           :email,
           :agree_terms,
           :api_session_device,
           :api_session_token,
           :api_session_expiration,
           :invitation_token, to: :user, allow_nil: true

  attr_reader :raw_invitation_token
  attr_accessor :not_ask_terms

  def self.connect_catalog?(permissions, connection_type)
    permissions.any? { |p| p['permission'] == 'catalog_management' && p['status'] == 'granted' } &&
      connection_type == 'catalog'
  end

  def self.connect_facebook_comments?(permissions, connection_type)
    permissions.any? { |p| p['permission'] == 'pages_manage_engagement' && p['status'] == 'granted' } &&
      permissions.any? { |p| p['permission'] == 'pages_read_user_content' && p['status'] == 'granted' } &&
      connection_type == 'facebook_comments'
  end

  def self.connect_instagram_comments?(permissions, connection_type)
    permissions.any? { |p| p['permission'] == 'instagram_manage_comments' && p['status'] == 'granted' } &&
      connection_type == 'instagram_comments'
  end

  def self.connect_messenger?(permissions, connection_type)
    permissions.any? { |p| p['permission'] == 'pages_manage_metadata' && p['status'] == 'granted' } &&
      connection_type == 'messenger'
  end

  def self.connect_instagram?(permissions, connection_type)
    permissions.any? { |p| p['permission'] == 'instagram_manage_messages' && p['status'] == 'granted' } &&
      connection_type == 'instagram'
  end

  def self.from_omniauth(auth, retailer_user, permissions, connection_type)
    retailer_user.update(provider: auth.provider, uid: auth.uid, facebook_access_token: auth.credentials.token)
    retailer_user.long_live_user_access_token

    retailer_user.handle_page_connection(connection_type: connection_type)
    retailer_user.handle_catalog_connection if connect_catalog?(permissions, connection_type)

    retailer_user
  end

  def self.ransackable_attributes(_auth_object = nil)
    %w[active agree_terms allow_bots_edit allow_edit_orders allow_export allow_import
       api_session_expiration app_version created_at current doppler_subscribed email facebook_access_token_expiration
       favorite_funnel_id first_name id instagram_unread invitation_accepted_at invitation_created_at invitation_limit
       invitation_sent_at invitations_count invited_by_id invited_by_type last_name
       last_sync_with_churnzero_at locale managed_agents messenger_unread ml_unread mobile_type notifications_payload
       onboarding_status only_assigned phone provider remember_created_at removed_from_team reset_password_sent_at
       retailer_admin retailer_id retailer_supervisor role_id see_phone_numbers super_admin
       total_unread_ml_count uid unread_fb_comments_count unread_ig_comments_count unread_instagram_chats_count
       unread_messenger_chats_count unread_ml_chats_count unread_ml_questions_count unread_whatsapp_chats_count
       updated_at user_id utm_campaign utm_content utm_medium utm_source utm_term whatsapp_unread]
  end

  def self.ransackable_associations(_auth_object = nil)
    %w[a_customers agent_customers agent_notifications agent_team_changelogs agent_teams
       agent_watched_videos calendar_events contact_groups customer_fb_posts customers
       deals mercately_videos mobile_tokens notifications onboarding_notifications onboarding_schedule retailer
       retailer_average_response_times retailer_user_notifications role team_assignments team_retailer_users teams
       templates user]
  end

  # TODO: mover a FacebookRetailer
  def handle_page_connection(connection_type: 'messenger')
    facebook_retailer = FacebookRetailer.find_or_create_by(retailer_id: retailer.id)
    facebook_service = Facebook::Api.new(facebook_retailer, self, connection_type, uid, facebook_access_token)
    facebook_service.update_retailer_access_token
  end

  def handle_catalog_connection
    FacebookCatalog.find_or_create_by(retailer_id: retailer.id)
  end

  def long_live_user_access_token
    # TODO: manejar errores del response de facebook
    facebook_service = Facebook::Api.new(nil, self)
    response = facebook_service.long_live_user_access_token
    self.facebook_access_token = response['access_token']
    self.facebook_access_token_expiration = Time.zone.now + response['expires_in'].seconds if response['expires_in']
    save!
  end

  def active_for_authentication?
    user.active_for_authentication? && !removed_from_team?
  end

  def inactive_message
    removed_from_team? ? 'Tu cuenta no se encuentra activa' : ''
  end

  def full_name
    "#{first_name} #{last_name}"
  end

  def mobile_info
    "#{mobile_type}/#{app_version}"
  end

  def supervisor?
    retailer_supervisor || false
  end

  def admin?
    retailer_admin || false
  end

  def superior?
    admin? || supervisor?
  end

  def agent?
    !retailer_admin && !retailer_supervisor
  end

  def owner?
    retailer.owner&.id == id
  end

  def can_edit_chatbots?
    return true if supervisor? || admin?

    allow_bots_edit || false
  end

  def customer_fb_posts_by_customers
    if only_assigned
      customer_fb_posts
    else
      ids = [id, nil].concat(managed_agents)
      CustomerFbPost.where(retailer_user_id: ids, retailer_id: retailer_id)
    end
  end

  def storage_id
    "#{id}_#{retailer_id}_#{email}"
  end

  def android?
    mobile_type == 'android' || mobile_type.nil?
  end

  def ios?
    mobile_type == 'ios'
  end

  # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
  def remove_agent?(assigned_agent)
    return false if assigned_agent&.retailer_user_id == id

    if managed_agents.any?
      !(managed_agents.include?(assigned_agent&.retailer_user_id) || assigned_agent.blank?)
    elsif !agent?
      false
    elsif only_assigned?
      assigned_agent.blank? || (assigned_agent && assigned_agent.retailer_user_id != id)
    else
      assigned_agent.present?
    end
  end
  # rubocop:enable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity

  def filter_agents
    return team_agents(managed_agents_objects.includes(:user)) if managed_agents.any?
    return retailer.team_agents.includes(:user) if admin? || supervisor?

    RetailerUser.where(id: id).includes(:user)
  end

  def subscribe
    return if Rails.env.test?

    response = doppler.subscribe
    update_column(:doppler_subscribed, true) if response[0] == 200
  end

  def unsubscribe
    return if Rails.env.test?

    response = doppler.unsubscribe
    update_column(:doppler_subscribed, false) if response[0] == 200
  end

  def has_unread_chats?
    get_unread_whatsapp_chats_count.positive? ||
      get_unread_messenger_chats_count.positive? ||
      get_unread_instagram_chats_count.positive? ||
      (get_unread_fb_comments_count + get_unread_ig_comments_count).positive?
  end

  def get_unread_fb_comments_count
    retailer_counter = RetailerCounterCache.fetch(retailer.id)
    return 0 if retailer_counter.blank?

    counter = 0
    counter += if managed_agents.any?
                 fb_comments_managed_counter_total
               elsif admin? || supervisor?
                 retailer_counter.fb_comments
               elsif only_assigned
                 unread_fb_comments_count
               else
                 retailer_counter.fb_comments_na + unread_fb_comments_count
               end

    counter
  end

  def get_unread_ig_comments_count
    retailer_counter = RetailerCounterCache.fetch(retailer.id)
    return 0 if retailer_counter.blank?

    counter = 0
    counter += if managed_agents.any?
                 ig_comments_managed_counter_total
               elsif admin? || supervisor?
                 retailer_counter.ig_comments
               elsif only_assigned
                 unread_ig_comments_count
               else
                 retailer_counter.ig_comments_na + unread_ig_comments_count
               end

    counter
  end

  def get_unread_whatsapp_chats_count
    retailer_counter = RetailerCounterCache.fetch(retailer.id)
    return 0 if retailer_counter.blank?

    counter = 0
    counter += if managed_agents.any?
                 whatsapp_managed_counter_total(retailer_counter)
               elsif admin? || supervisor?
                 retailer_counter.ws_messages
               elsif only_assigned
                 unread_whatsapp_chats_count
               else
                 retailer_counter.ws_messages_na + unread_whatsapp_chats_count
               end

    counter
  end

  def get_unread_messenger_chats_count
    retailer_counter = RetailerCounterCache.fetch(retailer.id)
    return 0 if retailer_counter.blank?

    counter = 0
    counter += if managed_agents.any?
                 messenger_managed_counter_total(retailer_counter)
               elsif admin? || supervisor?
                 retailer_counter.fb_messages
               elsif only_assigned
                 unread_messenger_chats_count
               else
                 retailer_counter.fb_messages_na + unread_messenger_chats_count
               end

    counter
  end

  def get_unread_instagram_chats_count
    retailer_counter = RetailerCounterCache.fetch(retailer.id)
    return 0 if retailer_counter.blank?

    counter = 0
    counter += if managed_agents.any?
                 instagram_managed_counter_total(retailer_counter)
               elsif admin? || supervisor?
                 retailer_counter.ig_messages
               elsif only_assigned
                 unread_instagram_chats_count
               else
                 retailer_counter.ig_messages_na + unread_instagram_chats_count
               end

    counter
  end

  def delete_agent(new_agent = nil)
    if user.retailers.count > 1
      old_user_id = user_id.dup
      update!(removed_from_team: true, user_id: nil, current: false, current_mobile: false)
      update_current(old_user_id)
      update_current_mobile(old_user_id)
    else
      user_attributes = { id: user.id, email: "#{id}-<EMAIL>", not_ask_terms: 'true' }
      update!(removed_from_team: true, user_attributes: user_attributes)
    end
    reassign_chats(new_agent)
    ChatBotAction.remove_agent(id)
    AgentTeam.remove_agent(id)
    TeamRetailerUser.remove_agent(id)
    remove_agent_from_deals_engine(id)
  end

  def remove_agent_from_deals_engine(agent_id)
    token = JwtService.encode({
      issuer: ENV.fetch('JWT_ISSUER', 'mercately').freeze
    })

    headers = {
      'Authorization' => "Bearer #{token}"
    }

    url = "#{ENV.fetch('DEAL_ENGINE_BASE_URL', nil)}/deals/remove_agent_from_deals"
    body = { deal: { retailer_user_id: agent_id } }.to_json
    connection = Connection.prepare_connection(url)

    begin
      response = Connection.put_request(connection, body, headers)
      if response.success?
        Rails.logger.info("Successfully removed relationships from deals for agent #{agent_id}")
      else
        Rails.logger.error("Failed to remove relationships from deals for agent #{agent_id}")
      end
    rescue Faraday::ConnectionFailed => e
      Rails.logger
        .error("Failed to connect to the server: #{e.message} removing relationships from deals for agent #{agent_id}")
    end
  end

  def reassign_chats(new_agent)
    ActiveRecord::Base.transaction do
      if new_agent.present?
        AgentCustomer.where(retailer_user_id: id).update_all(retailer_user_id: new_agent)
      else
        AgentCustomer.where(retailer_user_id: id).destroy_all
      end
      retailer.customers.where(retailer_user_id: id).update_all(retailer_user_id: new_agent)
    end
  rescue StandardError => e
    Rails.logger.error "Error reassigning chats from agent #{id} to agent #{new_agent}"
    Rails.logger.error e
    false
  end

  def renew_invitation_url
    return unless invitation_token

    enc, raw = Devise.token_generator.generate(self.class, :invitation_token)
    self.invitation_token = raw
    save(validate: false)
    default_url_options = Rails.application.config.default_url_options[:host]
    Rails.application.routes.url_helpers
      .accept_user_invitation_url(host: default_url_options, invitation_token: enc)
  end

  def can_list_all_chats?
    managed_agents.empty? && (admin? || supervisor?)
  end

  def managed_agents_objects
    return if managed_agents.count.zero?

    RetailerUser.where(id: managed_agents | [id])
  end

  def current!
    update!(current: true)
  end

  def current_mobile!
    update!(current_mobile: true)
  end

  def team_ids
    teams.ids
  end

  private

    def onboarding_status_format
      onboarding_status = self.onboarding_status.to_h.transform_keys(&:to_sym)

      unless %i[step skipped completed].all? { |key| onboarding_status.key?(key) }
        errors.add(:onboarding_status, 'error de validación')
      end

      unless (0..4).include?(onboarding_status[:step].to_i) &&
             [true, false].include?(ActiveModel::Type::Boolean.new.cast(onboarding_status[:skipped])) &&
             [true, false].include?(ActiveModel::Type::Boolean.new.cast(onboarding_status[:completed]))
        errors.add(:onboarding_status, 'valores invalidos')
      end
    end

    def set_only_assigned
      self.only_assigned = false unless agent?
    end

    def max_agents_limit
      return unless new_record? || (removed_from_team_changed? && removed_from_team == false)

      return unless RetailerUser.active_and_pending_agents(retailer_id).size >= (retailer.payment_plan&.max_agents || 2)

      errors.add(:base, 'Límite máximo de agentes alcanzado')
      throw(:abort)
    end

    def doppler
      @doppler ||= Doppler::AgentSync.new(self)
    end

    def schedule_onboarding
      OnboardingSchedule.create(retailer_user: self)
    end

    def check_counters
      self.unread_whatsapp_chats_count = 0 if unread_whatsapp_chats_count.negative?
      self.unread_messenger_chats_count = 0 if unread_messenger_chats_count.negative?
      self.unread_instagram_chats_count = 0 if unread_instagram_chats_count.negative?
    end

    def team_agents(agents)
      agents.joins(:user).where(removed_from_team: false, users: { invitation_token: nil })
    end

    def whatsapp_managed_counter_total(retailer_counter = nil)
      retailer_counter ||= RetailerCounterCache.fetch(retailer.id)
      retailer_counter.ws_messages_na +
        retailer.retailer_users.where(id: managed_agents).sum(:unread_whatsapp_chats_count)
    end

    def messenger_managed_counter_total(retailer_counter = nil)
      retailer_counter ||= RetailerCounterCache.fetch(retailer.id)
      retailer_counter.fb_messages_na +
        retailer.retailer_users.where(id: managed_agents).sum(:unread_messenger_chats_count)
    end

    def instagram_managed_counter_total(retailer_counter = nil)
      retailer_counter ||= RetailerCounterCache.fetch(retailer.id)
      retailer_counter.ig_messages_na +
        retailer.retailer_users.where(id: managed_agents).sum(:unread_instagram_chats_count)
    end

    def fb_comments_managed_counter_total(retailer_counter = nil)
      retailer_counter ||= RetailerCounterCache.fetch(retailer.id)
      retailer_counter.fb_comments_na +
        retailer.retailer_users.where(id: managed_agents).sum(:unread_fb_comments_count)
    end

    def ig_comments_managed_counter_total(retailer_counter = nil)
      retailer_counter ||= RetailerCounterCache.fetch(retailer.id)
      retailer_counter.ig_comments_na +
        retailer.retailer_users.where(id: managed_agents).sum(:unread_ig_comments_count)
    end

    def set_current
      return if first_record?
      return unless current

      current_agent&.update(current: false)
    end

    def set_current?
      new_record? ||
        (current_changed? && current == true)
    end

    def update_current(current_user_id)
      return unless current == false && update_current? && current_user_id.present?

      RetailerUser.where.not(id: id).find_by(user_id: current_user_id)&.update(current: true)
    end

    def update_current?
      saved_change_to_current? &&
        saved_change_to_removed_from_team &&
        removed_from_team == true
    end

    def set_current_mobile
      return if first_record?
      return unless current_mobile

      current_agent_mobile&.update(current_mobile: false)
    end

    def set_current_mobile?
      new_record? ||
        (current_mobile_changed? && current_mobile == true)
    end

    def update_current_mobile(current_user_id)
      return nil unless current_mobile == false && update_current_mobile? && current_user_id.present?

      RetailerUser.where.not(id: id).find_by(user_id: current_user_id)&.update(current_mobile: true)
    end

    def update_current_mobile?
      saved_change_to_current_mobile? &&
        saved_change_to_removed_from_team &&
        removed_from_team == true
    end

    def first_record?
      current_agent.nil? && current_agent_mobile.nil?
    end

    def current_agent
      @current_agent ||= RetailerUser.where.not(id: id).find_by(current: true, user_id: user_id)
    end

    def current_agent_mobile
      @current_agent_mobile ||= RetailerUser.where.not(id: id).find_by(current_mobile: true, user_id: user_id)
    end

    def retailer_admin_changed_to_false?
      retailer_admin_changed? && !retailer_admin
    end

    def at_least_one_admin
      return unless RetailerUser.active_admins(retailer.id).where.not(id: id).empty?

      errors.add(:base, 'Debe existir al menos un administrador por cada retailer.')
    end
end
# rubocop:enable Rails/HasManyOrHasOneDependent
# rubocop:enable Rails/InverseOf
# rubocop:enable Metrics/ClassLength
# rubocop:enable Naming/PredicateName
