class AcademyVideo < ApplicationRecord
  belongs_to :academy_category
  has_many :user_academy_progresses, dependent: :destroy

  validates :title, presence: true
  validates :position, presence: true
  validates :external_url, presence: true,
                           format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]), message: :invalid_url }

  def self.ransackable_attributes(_auth_object = nil)
    %w[academy_category_id description id position title external_url]
  end

  def self.ransackable_associations(_auth_object = nil)
    %w[academy_category]
  end

  def video_url
    external_url
  end
end
