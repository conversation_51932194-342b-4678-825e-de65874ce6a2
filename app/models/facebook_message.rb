class FacebookMessage < ApplicationRecord
  include MiaAutomaticAnswerConcern
  include MessengerChatBotActionConcern
  include FacebookMessages
  include FirstLoad::DateRangeable
  include FirstLoad::MessagesConcern

  belongs_to :facebook_comment, optional: true
  belongs_to :scheduled_automation, optional: true

  scope :inbound_messages, -> { where(sent_by_retailer: false) }
  scope :outbound_messages, -> { where(sent_by_retailer: true) }

  enum mia_flag: { 'buy' => 0, 'human_help' => 1 }

  attr_accessor :block_chat_reactivation

  def self.ransackable_attributes(_auth_object = nil)
    %w[created_at customer_id date_read error_message facebook_comment_id facebook_retailer_id file_data
       file_type filename hs_sync id id_client internal_url message_identifier mid note order_note order_summary
       payload reply_to retailer_user_id sender_email sender_first_name sender_last_name sender_uid sent_by_retailer
       sent_from_mercately text updated_at url]
  end

  def self.ransackable_associations(_auth_object = nil)
    %w[customer facebook_comment facebook_retailer retailer_user scheduled_automation]
  end

  private

    def inbound_media?
      !sent_by_retailer && url.present? && %w[video audio image file application/pdf image/jpeg].include?(file_type)
    end
end
