class GupshupWhatsappMessage < ApplicationRecord
  include StatusChatConcern
  include ReactivateChatConcern
  include AgentAssignmentConcern
  include WhatsappAutomaticAnswerConcern
  include MiaAutomaticAnswerConcern
  include WhatsappChatBotActionConcern
  include PushNotificationable
  include CounterMessagesConcern
  # Se incluye aca porque necesita ejecutarse despues del CustomerActiveWhatsappConcern
  include CustomerTmpMessagesConcern
  include CustomerActiveWhatsappConcern
  include MessageSenderInformationConcern
  include RetryGupshupMessageConcern
  include CustomerEvents::ServiceConcern
  include CustomerEvents::MessageConcern
  include FirstLoad::DateRangeable
  include FirstLoad::MessagesConcern
  # include TemplateStatsConcern
  include Messages::GupshupWhatsappMessageBehavior

  self.primary_key = 'id'

  belongs_to :retailer
  belongs_to :customer
  belongs_to :campaign, optional: true
  belongs_to :retailer_user, optional: true
  belongs_to :integration_message_data, optional: true
  # rubocop:disable Rails/HasManyOrHasOneDependent
  has_one :reminder
  # rubocop:enable Rails/HasManyOrHasOneDependent
  has_many :whatsapp_logs, dependent: :destroy

  # rubocop:disable Rails/RedundantPresenceValidationOnBelongsTo
  validates :retailer, :customer, :status, :direction, :source, :destination, :channel, presence: true
  # rubocop:enable Rails/RedundantPresenceValidationOnBelongsTo

  enum status: { 'error' => 0, 'submitted' => 1, 'enqueued' => 2, 'sent' => 3, 'delivered' => 4, 'read' => 5 }
  enum conversation_type: {
    'free_point' => 0,
    'user_initiated' => 1,
    'business_initiated' => 2,
    'free_tier' => 3,
    'marketing' => 4,
    'authentication' => 5,
    'utility' => 6,
    'service' => 7,
    'referral_conversion' => 8,
    'authentication_international' => 9,
    'marketing_lite' => 10
  }
  enum integration: { 'gupshup' => 0, 'qr' => 1 }
  enum mia_flag: { 'buy' => 0, 'human_help' => 1 }

  scope :range_between, -> (start_date, end_date) { where(created_at: start_date..end_date) }
  scope :inbound_messages, -> { where(direction: 'inbound') }
  scope :outbound_messages, -> { where(direction: 'outbound') }
  scope :notification_messages, -> { where(message_type: 'notification').where.not(status: 'error') }
  scope :conversation_messages, -> { where(message_type: 'conversation').where.not(status: 'error') }
  scope :unread, -> { where.not(status: 5) }
  scope :sent_delivered_read, -> { where(status: %w[sent delivered read]) }

  before_validation :nil_to_false
  before_create :set_message_type
  after_create :set_recent_inbound_message_date, if: -> { direction == 'inbound' }
  after_create :assign_agent, unless: :note
  after_save :apply_cost
  after_save :update_reminder
  after_save :update_customer_last_message # TODO: move to EDA
  after_commit :upload_media_resources, if: :inbound_media?, on: :create

  attr_accessor :block_chat_reactivation

  def type
    message_payload.try(:[], 'payload').try(:[], 'type') || message_payload.try(:[], 'type')
  end

  def text
    message_payload.try(:[], 'payload').try(:[], 'payload').try(:[], 'text').presence
  end

  # rubocop:disable Metrics/CyclomaticComplexity
  def message_info
    case type
    when 'file'
      'Archivo'
    when 'image'
      'Imagen'
    when 'video'
      'Video'
    when 'audio', 'voice'
      'Audio'
    when 'location'
      'Ubicación'
    when 'contact'
      'Contacto'
    when 'sticker'
      'Sticker'
    when 'list_reply', 'button_reply'
      message_payload['payload'].try(:[], 'payload').try(:[], 'title')
    when 'order'
      'Nueva solicitud de pedido'
    when 'product'
      'Producto mencionado'
    else
      message_payload['payload'].try(:[], 'payload').try(:[], 'text') || message_payload['text']
    end
  end
  # rubocop:enable Metrics/CyclomaticComplexity

  def from_group
    message_payload.try(:[], 'from_group')
  end

  def inbound_sender_full_name
    message_payload.try(:[], 'payload').try(:[], 'sender').try(:[], 'name')
  end

  def inbound_sender_phone
    message_payload.try(:[], 'payload').try(:[], 'sender').try(:[], 'phone')
  end

  def sent_by_agent?
    direction == 'outbound'
  end

  def get_media_caption
    return unless %w[image audio video file sticker].include?(type)

    message_payload.try(:[], 'caption') ||
      message_payload.try(:[], 'payload').try(:[], 'payload').try(:[], 'caption')
  end

  def location
    return {} unless type == 'location'

    message_payload.dig('payload', 'payload')
  end

  private

    def set_recent_inbound_message_date
      customer.update_column(:recent_inbound_message_date, created_at)
    end

    def set_message_type
      self.message_type = if direction == 'inbound' || customer.is_chat_open?
                            'conversation'
                          else
                            'notification'
                          end
    end

    def apply_cost
      if status == 'error' && (cost.blank? || !cost.zero?)
        retailer.refund_message_cost(cost)

        new_cost = 0
      end

      update_column(:cost, new_cost) if new_cost.present?
    end

    def update_reminder
      return unless error?

      Reminders::UpdateStatusJob.perform_later(id)
    end

    def nil_to_false
      self.note = false if note.nil?
    end

    def inbound_media?
      direction == 'inbound' && (%w[video audio image document file ptt sticker].include?(type) || has_referral_media?)
    end

    def upload_media_resources
      Whatsapp::UploadResourcesJob.perform_later(id)
    end

    def update_customer_last_message
      CustomerMessages::CreateOrUpdate.call(self)
    rescue StandardError => e
      Rails.logger.error("Failed to update CustomerLastMessage: #{e.message}")
    end
end
