# rubocop:disable Metrics/ClassLength
class PaymentPlan < ApplicationRecord
  include PartnersConcern

  belongs_to :retailer
  belongs_to :sales_development, class_name: 'Responsible', optional: true
  belongs_to :sales_agent, class_name: 'Responsible', optional: true
  belongs_to :customer_success, class_name: 'Responsible', optional: true
  belongs_to :support_agent, class_name: 'Responsible', optional: true
  belongs_to :onboarding_specialist, class_name: 'Responsible', optional: true
  belongs_to :pricing_tier, optional: true

  has_many :acquired_addons
  has_many :addons, through: :acquired_addons

  validates :month_interval, numericality: { greater_than: 0 }
  validates :mia_contact_cost, numericality: { greater_than_or_equal_to: 0 }
  validates :price, presence: true
  validate :quantity_of_agents_is_valid?, if: -> { max_agents.present? }
  validate :check_pricing_tier, on: [:create, :update], if: -> { pricing_tier_id.present? }

  before_save :set_end_date, if: :status_changed?
  after_update :chargebee_notify, if: :saved_change_to_plan
  after_update :update_subscriptions, if: :saved_change_to_plan
  after_update :remove_onboarding_notifications, if: :saved_change_to_plan
  after_update :disable_delete_assets, if: :saved_change_to_plan
  after_update :set_first_payment_date, if: :saved_change_to_plan
  after_update :disconnect_ml, if: -> { saved_change_to_status && status_inactive? }
  after_update :match_chargebee_subscription, if: :saved_change_to_chargebee_subscription_id
  after_destroy :update_partner_mrr
  after_save :disconnect_qr, if: :disconnect_qr?
  after_save :update_partner_mrr, if: :mrr_affecting_changes?

  # after_commit :register_in_chargebee, on: :create
  after_commit :create_mia_integrations, on: :update, if: :create_mia_integrations?
  after_commit :delete_mia_integrations, on: :update, if: :delete_mia_integrations?
  after_commit :set_retailer_max_agents, on: [:create, :update], if: :saved_change_to_max_agents

  enum status: { active: 0, inactive: 1 }, _prefix: true
  enum plan: { free: 0, professional: 1, advanced: 2, enterprise: 3, startup: 4, business: 5,
               basic: 6, pro: 7, premium: 8, launch: 9, scale: 10, rocket: 11 }
  enum whatsapp_integration_type: { qr: 0, api: 1 }

  scope :paying, -> { where.not(plan: :free).where(status: :active) }
  scope :soon_to_due_payment, lambda  { |limit|
                                where('next_pay_date BETWEEN ? AND ?', Time.zone.today.beginning_of_day, limit)
                              }

  MAX_ATTEMPTS = 4

  CHARGEBEE_PLANS = {
    basic: ['Basico-USD-Monthly', 'Basico-USD-Every-3-months', 'Basico-USD-Yearly'],
    premium: ['Premium-USD-Monthly', 'Premium-USD-Every-3-months', 'Premium-USD-Yearly'],
    pro: ['Pro-USD-Monthly', 'Pro-USD-Every-3-months', 'Pro-USD-Yearly']
  }.freeze

  CHARGEBEE_SEAT_ADDON_IDS = ['Extra-Agent-USD-Monthly',
                              'Extra-Agent-USD-Every-3-months',
                              'Extra-Agent-USD-Yearly'].freeze

  def self.ransackable_attributes(_auth_object = nil)
    %w[accept_terms_and_conditions charge_attempt chargebee_subscription_id chatbot_ai contact_based contacts_included
       created_at customer_success_id end_date funnel_events_access growth_tools_access id make_my_chatbot max_agents
       mercately_setup month_interval next_pay_date notes notification_sent onboarding_specialist_id payment_start_date
       plan price retailer_id sales_agent_id sales_development_id start_date status support_agent_id updated_at
       whatsapp_integration_type]
  end

  def self.ransackable_associations(_auth_object = nil)
    %w[customer_success onboarding_specialist retailer sales_agent sales_development support_agent]
  end

  def remove_onboarding_notifications
    OnboardingNotification.where(retailer_user_id: retailer.retailer_users.ids).demo.destroy_all
  end

  def allowed_charges
    [
      { internal_column: 'make_my_chatbot', id: 'Hazme-mi-chatbot-USD' },
      { internal_column: 'mercately_setup', id: 'Mercately-Setup-3-hours-USD' }
    ]
  end

  def charge!(force_retry: false, inactivate: true, iterate_cards: false)
    if chargebee_subscription_id.present?
      charge_in_chargebee(force_retry: force_retry, inactivate: inactivate)
    elsif retailer.int_charges
      charge_in_stripe(force_retry: force_retry, inactivate: inactivate, iterate_cards: iterate_cards)
    else
      charge_in_paymentez(force_retry: force_retry, inactivate: inactivate, iterate_cards: iterate_cards)
    end
  rescue StandardError => e
    Rails.logger.error("Error during charge: #{e.message}")
    false
  end

  def notify_slack
    npd = (next_pay_date || Time.zone.today) + month_interval.month

    msg = [
      '🎉 Tiempo de cobrar 🎉',
      "Retailer: (#{retailer.id})#{retailer.name}",
      "Email para la factura: #{retailer.admins.pluck(:email).join(', ')}",
      "Teléfono de contacto: #{retailer.phone_number}",
      "Monto: #{price}",
      "Plan: #{plan}",
      "Meses del plan: #{month_interval}",
      "Status del mes pasado: #{status}",
      "Fecha de próximo cobro: #{npd.strftime('%m/%d/%Y')}",
      "Cantidad de asesores: Admins (#{retailer.admins.count}), Supervisores (#{retailer.supervisors.count}), " \
      "Asesores (#{retailer.retailer_users.active_agents.count})",
      "Notas: #{notes}"
    ]
    if retailer.facebook_integrated?
      fbms = retailer
        .facebook_retailer
        .facebook_messages
        .where(created_at: (next_pay_date - month_interval.month)..next_pay_date)

      sent_msn = fbms.where(sent_by_retailer: true).count
      msg.push(
        'Messenger:',
        "Cantidad total de mensajes: #{fbms.count}",
        "Cantidad total de enviados: #{sent_msn}",
        "Cantidad total de recibidos: #{fbms.where(sent_by_retailer: false).count}",
        "Cantidad total de media files enviados (imagenes, archivos, etc): #{fbms
                                                                              .where
                                                                              .not(filename: [nil, '']).count}",
        "Promedio de mensajes por asesor: #{sent_msn / retailer.team_agents.count}",
        "Numero de chatbots atendidos: #{retailer.chat_bot_customers.includes(:chat_bot)
                                           .where(created_at: (next_pay_date - month_interval.month)..next_pay_date,
                                                  chat_bots: { platform: :messenger }).count}"
      )
    end

    if retailer.instagram_integrated?
      igms = retailer
        .facebook_retailer
        .instagram_messages
        .where(created_at: (next_pay_date - month_interval.month)..next_pay_date)

      sent_msn = igms.where(sent_by_retailer: true).count
      msg.push(
        'Instagram:',
        "Cantidad total de mensajes: #{igms.count}",
        "Cantidad total de enviados: #{sent_msn}",
        "Cantidad total de recibidos: #{igms.where(sent_by_retailer: false).count}",
        "Cantidad total de media files enviados (imagenes, archivos, etc): #{igms
                                                                               .where
                                                                               .not(filename: [nil, '']).count}",
        "Promedio de mensajes por asesor: #{sent_msn / retailer.team_agents.count}"
      )
    end

    slack_client.ping(msg.join("\n"))
  end

  def active?
    status == 'active'
  end

  def process_cancellation
    if cancel_now?
      return true if status_inactive!

      return false
    end

    true
  end

  def cancel_now?
    next_pay_date.blank? || next_pay_date <= Time.zone.today
  end

  def await_cancellation?
    retailer.plan_cancellations
      .exists?(['cancellation_date IS NOT NULL AND cancellation_date > ? AND desist_cancellation = FALSE',
                Time.zone.today])
  end

  def update_subscriptions
    retailer.retailer_users.each(&:subscribe)
  end

  def monthly_recurring_revenue
    price / month_interval
  end

  def month_interval_label
    case month_interval
    when 1
      I18n.t('retailer.profile.payment_plans.month_interval_label.monthly')
    when 2
      I18n.t('retailer.profile.payment_plans.month_interval_label.bimonthly')
    when 3
      I18n.t('retailer.profile.payment_plans.month_interval_label.trimonthly')
    when 6
      I18n.t('retailer.profile.payment_plans.month_interval_label.six_monthly')
    when 12
      I18n.t('retailer.profile.payment_plans.month_interval_label.yearly')
    else
      month_interval
    end
  end

  def changed_to_paying_plan
    saved_change_to_plan? && plan != 'free'
  end

  def chargebee_subscription
    result = chargebee.subscription(chargebee_subscription_id)
    result&.subscription
  end

  def chargebee_plan_id
    return unless chargebee_subscription_id || pricing_tier_id

    if chargebee_subscription_id
      chargebee.subscription(chargebee_subscription_id).subscription.subscription_items.first.item_price_id
    else
      pricing_tier&.identifier
    end
  end

  def chargebee_invoices
    return [] unless chargebee_subscription_id

    invoices = chargebee.invoices(chargebee_subscription_id)
    invoices.map do |invoice|
      {
        id: invoice.id,
        date: Time.at(invoice.date).utc.in_time_zone('Eastern Time (US & Canada)'),
        amount: invoice.total.to_f / 100,
        status: invoice.status,
        pdf: chargebee.invoice_pdf(invoice.id).download_url
      }
    end
  end

  def match_chargebee_subscription
    return unless chargebee_subscription_id

    subscription = chargebee_subscription
    return unless subscription

    plan_id = subscription.subscription_items.first.item_price_id
    plan_name = find_plan(plan_id)
    max_agents = max_agents_included(subscription.subscription_items, plan_name)

    update(
      plan: plan_name,
      month_interval: calc_month_interval(subscription.billing_period, subscription.billing_period_unit),
      price: subscription.subscription_items.map(&:amount).compact.sum / 100,
      next_pay_date: Time.at(subscription.next_billing_at.to_i).utc.in_time_zone('Eastern Time (US & Canada)'),
      max_agents: max_agents,
      accept_terms_and_conditions: true,
      status: 'active',
      end_date: nil,
      charge_attempt: 0
    )
  end

  def assign_and_charge!(subscription)
    plan_id = subscription[:id]
    tier = PricingTier.find_by(identifier: plan_id)

    total = plans_utils.calculate_total_price(subscription, tier)
    @lines = plans_utils.transaction_lines(self, subscription, tier)

    execute_charge(
      force_retry: true,
      month_interval: tier.month_interval,
      amount: (total / 100).round(2),
      description: "Suscripción plan #{tier.priceable.name} de #{tier.month_interval} " \
        "#{tier.month_interval > 1 ? 'meses' : 'mes'} a Mercately"
    )

    payment_params = {
      plan: tier.priceable.identifier.downcase,
      price: plans_utils.calculate_total_price(subscription, tier, false) / 100,
      month_interval: tier.month_interval,
      next_pay_date: tier.month_interval.months.from_now.change(day: Time.zone.today.day),
      accept_terms_and_conditions: true,
      status: 'active',
      end_date: nil,
      charge_attempt: 0,
      pricing_tier: tier
    }

    plans_utils.process_entitlements(tier, subscription, payment_params)

    if @result[:success]
      { success: update(payment_params) }
    elsif @result[:status] != 'payment_failed'
      {
        success: false,
        message: 'Su pago fue procesado, pero necesita pasos adicionales para completar su compra.',
        redirect: true
      }
    else
      { success: false, message: @result[:message] }
    end
  end

  def bought_addons
    return [] if pricing_tier_id.blank?

    acquired_addons.map do |addon|
      tier = PricingTier.where(priceable_type: 'Addon', billing_cycle: pricing_tier.billing_cycle,
                               priceable_id: addon.addon_id).first
      next if tier.blank?

      {
        amount: addon.quantity * tier.price,
        item_price_id: tier.identifier,
        quantity: addon.quantity,
        unit_price: tier.price,
        item_type: 'addon'
      }
    end.compact
  end

  def update_and_charge!(subscription)
    plan_id = subscription[:id]
    tier = PricingTier.find_by(identifier: plan_id)
    current_tier = PricingTier.find(pricing_tier_id)
    change_npd = false

    if tier.id != current_tier.id
      if tier.month_interval == current_tier.month_interval
        prorated_left_days_new_plan = plans_utils.prorated_price_by_day(self, tier)
        prorated_left_days_current_plan = plans_utils.prorated_price_by_day(self, current_tier)

        total = prorated_left_days_new_plan
        total += plans_utils.calculate_addons_charges(self, subscription)
        total -= prorated_left_days_current_plan
        @lines = plans_utils.transaction_lines(self, subscription, tier, true)
        @credits = (prorated_left_days_current_plan / 100).round(2)

        execute_charge(
          force_retry: true,
          month_interval: tier.month_interval,
          amount: (total / 100).round(2),
          description: "Plan #{tier.priceable.name} - Prorated Charges")
      else
        prorated_left_days_current_plan = plans_utils.prorated_price_by_day(self)

        total = plans_utils.calculate_total_price(subscription, tier)
        total -= prorated_left_days_current_plan
        change_npd = true
        @lines = plans_utils.transaction_lines(self, subscription, tier)
        @credits = (prorated_left_days_current_plan / 100).round(2)

        execute_charge(
          force_retry: true,
          month_interval: tier.month_interval,
          amount: (total / 100).round(2),
          description: "Plan #{tier.priceable.name} - Prorated Charges")
      end
    else
      total = plans_utils.calculate_addons_charges(self, subscription)
      @lines = plans_utils.transaction_lines(self, subscription, tier, true)

      execute_charge(
        force_retry: true,
        month_interval: current_tier.month_interval,
        amount: (total / 100).round(2),
        description: "Plan #{current_tier.priceable.name} - Prorated Charges")
    end

    if @result[:success]
      if (status == 'inactive' || charge_attempt.positive?) && !total.positive?
        return {
          success: false,
          message: 'Debe cancelar los pagos pendientes antes de hacer cambios a su plan actual.'
        }
      end

      payment_params = {
        plan: tier.priceable.identifier.downcase,
        price: plans_utils.calculate_total_price(subscription, tier, false) / 100,
        pricing_tier: tier,
        status: 'active',
        charge_attempt: 0
      }

      if change_npd
        payment_params[:month_interval] = tier.month_interval
        payment_params[:next_pay_date] = tier.month_interval.months.from_now.change(day: Time.zone.today.day)
      end

      plans_utils.process_entitlements(tier, subscription, payment_params)

      { success: update(payment_params) }
    elsif @result[:status] != 'payment_failed'
      {
        success: false,
        message: 'Su pago fue procesado, pero necesita pasos adicionales para completar su compra.',
        redirect: true
      }
    else
      payment_failed(force_retry: true) if status == 'inactive'
      { success: false, message: @result[:message] }
    end
  end

  def reactivate_and_charge!(subscription)
    plan_id = subscription[:id]
    tier = PricingTier.find_by(identifier: plan_id)
    current_tier = PricingTier.find(pricing_tier_id)

    prorated_left_days_current_plan = plans_utils.prorated_price_by_day(self)
    total = plans_utils.calculate_total_price(subscription, tier)
    total -= prorated_left_days_current_plan
    @lines = plans_utils.transaction_lines(self, subscription, tier)
    @credits = (prorated_left_days_current_plan / 100).round(2)

    execute_charge(
      force_retry: true,
      month_interval: tier.month_interval,
      amount: (total / 100).round(2),
      description: "Plan #{tier.priceable.name}")

    if @result[:success]
      payment_params = {
        plan: tier.priceable.identifier.downcase,
        price: plans_utils.calculate_total_price(subscription, tier, false) / 100,
        pricing_tier: tier,
        month_interval: tier.month_interval,
        next_pay_date: tier.month_interval.months.from_now.change(day: Time.zone.today.day),
        charge_attempt: 0,
        status: 'active',
        end_date: nil
      }

      plans_utils.process_entitlements(tier, subscription, payment_params)

      { success: update(payment_params) }
    elsif @result[:status] != 'payment_failed'
      {
        success: false,
        message: 'Su pago fue procesado, pero necesita pasos adicionales para completar su compra.',
        redirect: true
      }
    else
      payment_failed(force_retry: true)
      { success: false, message: @result[:message] }
    end
  end

  def get_tier_name
    return unless pricing_tier_id

    tier = PricingTier.find(pricing_tier_id)
    tier.priceable.name
  end

  private

    def set_retailer_max_agents
      retailer.update_column(:max_agents, max_agents)
    end

    def find_plan(plan_id)
      found_plan = CHARGEBEE_PLANS.map { |plan, ids| plan.to_s if ids.include?(plan_id) }.compact.first
      found_plan.nil? ? 'free' : found_plan
    end

    def default_max_agents(plan)
      plan_mapping = {
        'free' => 1,
        'startup' => 3,
        'business' => 5,
        'enterprise' => 10,
        'basic' => 3,
        'pro' => 5,
        'premium' => 15
      }

      plan_mapping[plan] || 0
    end

    def calc_month_interval(period, unit)
      case unit
      when 'month'
        period
      when 'year'
        period * 12
      end
    end

    def max_agents_included(items, new_plan = nil)
      max_agents = default_max_agents(new_plan)
      items.each do |item|
        max_agents += item.quantity if CHARGEBEE_SEAT_ADDON_IDS.include?(item.item_price_id)
      end

      max_agents
    end

    def slack_client
      Slack::Notifier.new ENV.fetch('SLACK_WEBHOOK', nil)
    end

    def payment_failed(force_retry: false, inactivate: true)
      increment!(:charge_attempt)
      return unless inactivate

      status_inactive! if charge_attempt > MAX_ATTEMPTS && !force_retry
    end

    def disconnect_qr?
      retailer.qr_integrated? && saved_change_to_status? && status_inactive?
    end

    def disconnect_qr
      # TODO: Validar forma de eliminar instancias en caso de que maytapi cobre por instancia
      #       Actualmente se elimina el número vinculado a QR sin embargo el id de la instancia se mantiene guardado
      #       si se elimina el id de la instancia del retailer, la instancia quedaría huerfana y si el retailer se
      #       integra nuevamente por qr se crearía una nueva instancia.
      Whatsapp::MayTapi::Auth.new(retailer).logout
    end

    def disconnect_ml
      retailer.meli_retailer&.destroy
    end

    def set_end_date
      self.end_date = active? ? nil : DateTime.now
    end

    def register_in_chargebee
      return unless %w[production staging].include?(ENV.fetch('ENVIRONMENT', nil)) || ENV['ENABLE_CHARGEBEE'] == 'true'
      return if retailer.chargebee_customer_id.present?

      chargebee_id = chargebee.create_customer(retailer).id
      retailer.update(chargebee_customer_id: chargebee_id) if chargebee_id
    end

    def chargebee_notify
      return if notification_sent? || chargebee_subscription_id.blank?

      return unless %w[basic pro premium].include?(plan)

      notify_chargebee_to_slack
    end

    def notify_chargebee_to_slack
      return unless production_environment?

      channel = setup_slack_channel
      message = build_message
      update_column(:notification_sent, true)

      send_notification(channel, message)
    end

    def additional_agents
      max_agents - default_max_agents(plan)
    end

    def build_message
      message_parts = [
        '🎉 Nuevo plan contratado 🎉',
        "Retailer: (#{retailer.id}) #{retailer.name}",
        "Plan: #{plan}",
        "Intervalo: #{month_interval} #{month_interval > 1 ? I18n.t('months') : I18n.t('month')}"
      ]

      message_parts.concat(additional_info) unless additional_info.empty?

      message_parts.push("Próximo pago: #{next_pay_date.strftime('%m/%d/%Y')}", "Total: #{price}")

      message_parts.join("\n")
    end

    def additional_info
      info = []
      info << 'Adicionales:' if mercately_setup? || make_my_chatbot?
      info << 'Mercately Setup' if mercately_setup?
      info << 'Hazme mi chatbot' if make_my_chatbot?
      info << "Agentes: #{additional_agents}" if additional_agents.positive?
      info
    end

    def send_notification(channel, message)
      channel.ping(message)
    end

    def production_environment?
      ENV['ENVIRONMENT'] == 'production'
    end

    def setup_slack_channel
      Slack::Notifier.new(ENV.fetch('SLACK_PAYMENTS_CHARGEBEE', nil), channel: '#pagos-chargebee')
    end

    def chargebee
      Chargebee::Api.new
    end

    def min_quantity_of_agents
      retailer.count_active_team_agents || 0
    end

    def quantity_of_agents_is_valid?
      return false unless max_agents < min_quantity_of_agents

      errors.add(:max_agents, "can't be less than the quantity of active agents")
    end

    def quantity_of_agents_to_be_added(items_to_add)
      item_price_id = chargebee_subscription.subscription_items.first.item_price_id || nil
      return if item_price_id.nil? || items_to_add.nil?

      plan_name = find_plan(item_price_id)
      default_max_agents(plan_name) + items_to_add
    end

    def charge_in_chargebee(force_retry: false, inactivate: true)
      invoice_due = last_due_invoice
      payment_source_id = chargebee_main_payment_method
      return false if invoice_due.blank? || invoice_due.try(:[], :success) == false || payment_source_id.blank?

      invoice_due_id = invoice_due.invoice.id
      payment_attempt = chargebee.collect_payment(invoice_due_id, payment_source_id)
      if payment_attempt.try(:[], :success) == false
        payment_failed(force_retry: force_retry, inactivate: inactivate)
        return false
      end

      invoice = payment_attempt&.invoice
      if payment_success?(invoice)
        match_chargebee_subscription
        return true
      end

      payment_failed(force_retry: force_retry, inactivate: inactivate)
    end

    def charge_in_paymentez(force_retry: false, inactivate: true, iterate_cards: false)
      payment_methods = iterate_cards ? retailer.paymentez_credit_cards : [retailer.paymentez_credit_cards.main]
      attempt = false
      payment_methods.each do |payment_method|
        attempt = payment_method.create_transaction(true)
        break if attempt
      rescue StandardError => e
        Rails.logger.error("Error processing payment method #{payment_method.id}: #{e.message}")
        next
      end

      unless attempt
        payment_failed(force_retry: force_retry, inactivate: inactivate)
        return false
      end

      npd = if next_pay_date + 15.days >= Time.zone.today
              next_pay_date + month_interval.months
            else
              month_interval.months.from_now.change(day: Time.zone.today.day)
            end

      update_columns(next_pay_date: npd, charge_attempt: 0, status: :active)
      true
    end

    def charge_in_stripe(force_retry: false, inactivate: true, iterate_cards: false)
      return false unless retailer.payment_methods.any?

      payment_methods = iterate_cards ? retailer.payment_methods : [retailer.payment_methods.main]
      desc = month_interval > 1 ? "Mercately #{month_interval} Months Subscription" : 'Mercately Monthly Subscription'

      payment_methods.each do |payment_method|
        stripe_transaction = retailer.stripe_transactions.create(
          amount: price.to_i,
          payment_method: payment_method,
          month_interval: month_interval,
          create_charge: true
        )
        result = stripe_service.charge_attempt(stripe_transaction, desc)
        return true if result[:success]
      rescue StandardError => e
        Rails.logger.error("Error processing payment method #{payment_method.id}: #{e.message}")
        next
      end

      payment_failed(force_retry: force_retry, inactivate: inactivate)
      false
    end

    def payment_success?(invoice)
      invoice.present? && invoice.status == 'paid'
    end

    def chargebee_main_payment_method
      retailer.payment_methods.find_by(main: true)&.chargebee_id
    end

    def last_due_invoice
      chargebee.last_due_invoice(chargebee_subscription_id)
    end

    def disable_delete_assets
      return unless %w[pro premium].include?(plan)

      retailer.update(delete_assets: false) if retailer.delete_assets?
    end

    def set_first_payment_date
      previous_plan, new_plan = saved_change_to_plan
      return unless previous_plan == 'free' && new_plan.present? && new_plan != 'free'

      update_column(:payment_start_date, Time.current) if payment_start_date.blank?
    end

    def stripe_service
      @stripe_service ||= Stripe::Api.new
    end

    def create_mia_integrations
      %w[guides chatbot products].each do |integration|
        mia_integration_type = MiaIntegrationType.find_by(name: integration)
        mia_integration = retailer.mia_integrations.find_or_initialize_by(mia_integration_type: mia_integration_type)
        next if mia_integration.persisted?

        mia_integration.openai_key = ENV.fetch('OPENAI_KEY', nil)
        mia_integration.save
      end
    end

    def create_mia_integrations?
      saved_changes.keys.intersect?(%w[status chatbot_ai]) && active? && chatbot_ai?
    end

    def delete_mia_integrations
      retailer.update(mia_chatbot_active: false, mia_products_sync: false, mia_products_synced: 'not_synced')
      retailer.mia_integrations.destroy_all
    end

    def delete_mia_integrations?
      saved_changes.keys.intersect?(%w[status chatbot_ai]) && (status_inactive? || !chatbot_ai?)
    end

    def update_partner_mrr
      retailer.partner.update_mrr! if retailer&.partner
    end

    def mrr_affecting_changes?
      saved_changes.keys & %w[price month_interval status] != []
    end

    def plans_utils
      @plans_utils ||= Plans::Utils.new
    end

    def execute_charge(force_retry: false, month_interval: 1, amount: 0, description: nil)
      @result = { success: true }
      return unless amount&.positive?

      payment_method = retailer.payment_methods.main
      return if payment_method.blank?

      desc = month_interval > 1 ? "Mercately #{month_interval} Months Subscription" : 'Mercately Monthly Subscription'
      stripe_transaction = retailer.stripe_transactions.create(
        amount: amount,
        payment_method: payment_method,
        month_interval: month_interval,
        create_charge: true,
        do_not_move_day: true,
        description: description,
        lines: @lines,
        credits: @credits
      )

      result = stripe_service.charge_attempt(stripe_transaction, desc)
      @result = result

      result[:success]
    end

    def check_pricing_tier
      return if plan == pricing_tier.priceable.identifier.downcase && month_interval == pricing_tier.month_interval

      errors.add(:plan, 'Plan no coincide con el plan de la tabla de precios')
    end
end
# rubocop:enable Metrics/ClassLength
