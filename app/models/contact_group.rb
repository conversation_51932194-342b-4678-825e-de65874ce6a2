class ContactGroup < ApplicationRecord
  include WebIdGenerateableConcern

  belongs_to :retailer
  belongs_to :retailer_user, optional: true
  has_many :contact_group_customers
  has_many :customers, through: :contact_group_customers, dependent: :delete_all, validate: false
  has_many :campaigns

  validate :check_customers, unless: :imported
  validates :name, uniqueness: { scope: :retailer_id, case_sensitive: false, unless: :archived? }
  validates :name, presence: true

  after_update :cancel_campaigns, if: :archived

  scope :not_archived, -> { where(archived: false) }
  scope :with_customers, lambda {
    joins(:contact_group_customers)
      .group('contact_groups.id')
      .having('COUNT(contact_group_customers.id) > 0')
  }
  scope :by_name, ->(name) { where('lower(name) = (?)', name.downcase.strip) }

  def to_param
    web_id
  end

  def archived!
    update(archived: true)
  end

  def self.ransackable_attributes(auth_object = nil)
    ["archived", "created_at", "id", "imported", "name", "retailer_id", "retailer_user_id", "updated_at", "web_id"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["campaigns", "contact_group_customers", "customers", "retailer", "retailer_user"]
  end

  private

    def archived?
      ContactGroup.exists?(name: name, archived: true)
    end

    def check_customers
      return if customer_ids.present?

      errors.add(:customer_ids, 'Debe agregar clientes')
    end

    def cancel_campaigns
      campaigns.pending.update_all(status: :cancelled)
    end
end
