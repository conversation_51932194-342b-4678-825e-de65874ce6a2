class User < ApplicationRecord
  include Delegations::User
  include TransliteratableEmailConcern
  include EmailableConcern

  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable
  devise :invitable, :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :omniauthable, omniauth_providers: %i[facebook]
  has_many :retailer_users, dependent: :restrict_with_error
  has_many :retailers, through: :retailer_users

  validates :agree_terms, presence: true, unless: -> { not_ask_terms == 'true' }
  validates :email, presence: true, uniqueness: true

  accepts_nested_attributes_for :retailers
  accepts_nested_attributes_for :retailer_users

  attr_accessor :created_by_partner, :not_ask_terms, :shop_domain, :shop_id

  ransacker :full_name do |parent|
    Arel::Nodes::NamedFunction.new('CONCAT_WS', [
                                     Arel::Nodes.build_quoted(' '),
                                     parent.table[:first_name],
                                     parent.table[:last_name],
                                     parent.table[:email]
                                   ])
  end

  scope :search_by_text, (lambda do |search_text|
    where("CONCAT(REPLACE(lower(users.first_name), '\s', ''),
                  REPLACE(lower(users.last_name), '\s', '')
                 ) ILIKE ?
          OR lower(users.email) iLIKE ?",
          "%#{search_text.downcase.delete(' ')}%",
          "%#{search_text}%")
  end)

  def self.ransackable_attributes(_auth_object = nil)
    %w[agree_terms api_session_device api_session_expiration api_session_token created_at email
       encrypted_password first_name full_name id invitation_accepted_at invitation_created_at invitation_limit
       invitation_sent_at invitation_token invitations_count invited_by_id invited_by_type last_name
       remember_created_at reset_password_sent_at reset_password_token updated_at]
  end

  # rubocop:disable Lint/UnusedMethodArgument
  def self.ransackable_associations(auth_object = nil)
    %w[invited_by retailer_users retailers]
  end
  # rubocop:enable Lint/UnusedMethodArgument

  def current_retailer_user
    retailer_users.find_by(current: true)
  end

  def current_mobile_retailer_user
    retailer_users.find_by(current_mobile: true)
  end

  def current_retailer
    current_retailer_user&.retailer
  end

  def current_mobile_retailer
    current_mobile_retailer_user&.retailer
  end

  def build_retailer
    retailers.new
  end

  def full_name
    "#{first_name} #{last_name}"
  end

  def generate_api_token!(generate_device = false)
    new_token = SecureRandom.hex
    attrs = { api_session_token: new_token, api_session_expiration: 1.year.from_now }
    attrs[:api_session_device] = SecureRandom.hex(3) if generate_device || api_session_device.nil?
    update(attrs)

    new_token
  end

  def renew_invitation_token
    return unless invitation_token

    enc, raw = Devise.token_generator.generate(self.class, :invitation_token)
    update_column(:invitation_token, raw)
    save(validate: false)
    enc
  end

  def destroy_api_token!
    update(api_session_token: nil, api_session_expiration: nil)
  end

  private

    # overrides devise validation
    def password_required?
      return false if created_by_partner && new_record?

      super
    end
end
