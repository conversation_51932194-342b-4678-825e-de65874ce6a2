class MiaIntegration < ApplicationRecord
  attr_encrypted :openai_key, :public_key, key: ENV.fetch('SECRET_KEY_BASE', nil), mode: :per_attribute_iv_and_salt

  belongs_to :retailer
  belongs_to :mia_integration_type

  validates :openai_key, presence: true
  validates :retailer_id, uniqueness: { scope: :mia_integration_type_id }

  before_create :generate_service_url
  after_commit :generate_public_key, on: :create, unless: -> { public_key.present? }
  after_commit :sync_retailer_with_mia, on: :create, if: -> { mia_integration_type.name == 'chatbot' }
  after_commit :delete_remote_file, on: :destroy, if: -> { mia_integration_type.name == 'chatbot' }

  def self.ransackable_attributes(_auth_object = nil)
    %w[created_at id kind mia_integration_type_id retailer_id service_url updated_at]
  end

  def self.ransackable_associations(_auth_object = nil)
    %w[mia_integration_type retailer]
  end

  def retry_create_and_send_credentials
    CreateAndSendMiaCredentialsJob.perform_later(id)
  end

  class << self
    def integration_with(kind)
      joins(:mia_integration_type)
        .where(mia_integration_types: { name: kind })
        .first
    end
  end

  private

    def generate_public_key
      integration_type = mia_integration_type.name.to_sym
      public_key_builders[integration_type].call
    end

    def public_key_builders
      @public_key_builders ||= {
        chatbot: method(:generate_chatbot_public_key),
        products: method(:generate_standard_public_key),
        guides: method(:generate_standard_public_key),
        suggestions: method(:generate_suggestions_public_key)
      }
    end

    def generate_chatbot_public_key
      key = SecureRandom.random_bytes(32)
      encoded_key = Base64.urlsafe_encode64(key)
      return unless update(public_key: encoded_key)

      MiaNotificationMailer.integration_confirm_email(self).deliver_later(queue: 'mailers')
      CreateAndSendMiaCredentialsJob.perform_later(id)
    end

    def generate_standard_public_key
      update(public_key: ENV.fetch('MIA_MULTI_TENANT_SECRET_KEY', nil))
    end

    def generate_suggestions_public_key
      key = SecureRandom.random_bytes(32)
      encoded_key = Base64.urlsafe_encode64(key)
      update(public_key: encoded_key)
    end

    def generate_service_url
      integration_type = mia_integration_type.name.to_sym
      service_url_builders[integration_type].call
    end

    def service_url_builders
      @service_url_builders ||= {
        chatbot: method(:generate_chatbot_service_url),
        products: method(:generate_products_service_url),
        guides: method(:generate_guides_service_url),
        suggestions: method(:generate_suggestions_service_url)
      }
    end

    def generate_chatbot_service_url
      subdomain = "mia-#{mia_integration_type.name.downcase}-#{retailer.catalog_slug.dasherize}"
      env_environment = ENV.fetch('ENVIRONMENT', '')
      subdomain = "#{subdomain}-#{env_environment}" if env_environment != 'production'
      self.service_url = "https://#{subdomain}.mercately.ai"
    end

    def sync_retailer_with_mia
      retailer.update_column(:feature_flag, true)
      Mia::Chatbot::SyncRetailerTimezoneJob.set(wait: 5.minutes).perform_later(retailer_id)
    end

    def generate_products_service_url
      self.service_url = ENV.fetch('MIA_PRODUCTS_SERVICE_URL', nil)
    end

    def generate_guides_service_url
      self.service_url = ENV.fetch('MIA_GUIDES_SERVICE_URL', nil)
    end

    def generate_suggestions_service_url
      self.service_url = ENV.fetch('MIA_SUGGESTIONS_SERVICE_URL', nil)
    end

    def delete_remote_file
      Mia::DeleteRemoteMiaIntegrationFileJob.perform_later(mia_integration_type.name, retailer.slug)
      MiaNotificationMailer
        .delete_integration_confirm_email(retailer, service_url)
        .deliver_later(queue: 'mailers')
    end
end
