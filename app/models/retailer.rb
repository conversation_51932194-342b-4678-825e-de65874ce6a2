# frozen_string_literal: true

# rubocop:disable Rails/I18nLocaleTexts
# rubocop:disable Metrics/ClassLength
# rubocop:disable Rails/HasManyOrHasOneDependent
# rubocop:disable Rails/InverseOf
# rubocop:disable Rails/WhereNotWithMultipleConditions
# rubocop:disable Naming/PredicateName
class Retailer < ApplicationRecord
  include PhoneNumberConcern
  include TaggableConcern
  include AddSalesChannelConcern
  include ShopSynchronizableConcern
  include ColorsConcern
  include MiaIntegrable
  include RetailerNotifiableUpdateConcern
  include MiaPlatformSupport

  attr_encrypted :api_key,
                 mode: :per_attribute_iv_and_salt,
                 key: ENV.fetch('SECRET_KEY_BASE', nil)

  belongs_to :partner, optional: true
  has_one :meli_retailer, dependent: :destroy
  has_one :mp_retailer, dependent: :destroy
  has_one :stripe_retailer, dependent: :destroy
  has_one :retailer_user, dependent: :destroy
  has_one :facebook_retailer, dependent: :destroy
  has_one :facebook_catalog, dependent: :destroy
  has_one :payment_plan, dependent: :destroy
  has_one :retailer_bill_detail, dependent: :destroy
  has_one :shopify_shop
  has_one :retailer_onboarding, dependent: :destroy
  has_one_attached :avatar
  has_one_attached :background
  has_one :ctx_service, dependent: :destroy
  has_one :partner_config, dependent: :destroy
  has_many :products, dependent: :destroy
  has_many :customers, dependent: :destroy
  has_many :customer_related_data, through: :customers
  has_many :retailer_users, dependent: :destroy
  has_many :users, through: :retailer_users
  has_many :retailer_addresses, dependent: :destroy
  has_many :retailer_schedules, -> { order(:weekday) }, dependent: :destroy
  has_many :templates, dependent: :destroy
  has_many :gupshup_whatsapp_messages, dependent: :destroy
  has_many :whatsapp_messages, lambda {
                                 order(created_at: :asc, id: :asc)
                               }, class_name: 'GupshupWhatsappMessage', dependent: :destroy
  has_many :automatic_answers, dependent: :destroy
  has_many :payment_methods, dependent: :destroy
  has_many :paymentez_credit_cards, dependent: :destroy
  has_many :paymentez_transactions
  has_many :stripe_transactions
  has_many :funnels
  has_many :funnel_exports
  has_many :deals
  has_many :whatsapp_templates, dependent: :destroy
  has_many :top_ups, dependent: :destroy
  has_many :tags, dependent: :destroy
  has_many :sales_channels, dependent: :destroy
  has_many :chat_bots, dependent: :destroy
  has_many :team_assignments, dependent: :destroy
  has_many :chat_bot_customers, through: :customers
  has_many :customer_related_fields
  has_many :calendar_events, dependent: :destroy
  has_many :reminders, dependent: :destroy
  has_many :contact_groups, dependent: :destroy
  has_many :campaigns, dependent: :destroy
  has_many :hubspot_fields
  has_many :customer_hubspot_fields
  has_many :plan_cancellations
  has_many :retailer_average_response_times
  has_many :retailer_unfinished_message_blocks
  has_many :retailer_whatsapp_conversations
  has_many :message_blocks
  has_many :retailer_most_used_tags
  has_many :retailer_conversations
  has_many :retailer_amount_messages
  has_many :retailer_business_rules, dependent: :destroy
  has_many :retailer_business_rule_data, dependent: :destroy
  has_many :business_rules, through: :retailer_business_rules
  has_many :retailer_amount_messages_by_hours
  has_many :hubspot_owners
  has_many :agent_hubspot_owners
  has_many :quotes, class_name: 'Crm::Quote'
  has_many :customer_fb_posts
  has_many :shop_orders
  has_many :customer_exports, dependent: :destroy
  has_many :customer_event_categories, class_name: 'Crm::CustomerEventCategory', dependent: :destroy
  has_many :integration_message_data, class_name: 'IntegrationMessageData'
  has_many :integration_messages
  has_many :teams, dependent: :destroy
  has_many :automations
  has_many :scheduled_automations
  has_many :deal_automations, dependent: :destroy
  has_many :retailer_customers, dependent: :destroy
  has_many :funnel_deal_histories, dependent: :destroy
  has_many :template_stats, through: :whatsapp_templates
  has_many :roles, dependent: :destroy
  has_many :retailer_mia_platforms
  has_many :mia_platforms, through: :retailer_mia_platforms
  has_many :import_contacts_loggers, dependent: :destroy
  has_many :widget_configs
  has_many :conversation_topics, dependent: :destroy
  has_many :pipedream_integrations, dependent: :destroy

  validates :name, presence: true, unless: -> { validate_name == 'false' }
  validates :currency, presence: true
  validates :catalog_slug, uniqueness: true, if: -> { catalog_slug.present? }
  validates :domain, uniqueness: { allow_blank: true }
  validates :slug, uniqueness: true, if: -> { slug.present? }
  validates :catalog_slug, length: { maximum: 25 }
  validates :description, length: { maximum: 140 }
  validates :facebook_url, :instagram_url, :twitter_url, :whatsapp_url, :tiktok_url,
            format: { with: %r{\A(http://www\.|https://www\.|http://|https://)?[a-z0-9]+([-.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(/.*)?\z}x,
                      allow_blank: true }
  validates :mia_chatbot_active, inclusion: { in: [true, false] }
  validates :mia_products_sync, inclusion: { in: [true, false] }
  validate :mia_platforms_amount, if: :mia_chatbot_active?

  before_validation :attributes_to_nil
  before_validation :strip_name
  before_validation :set_catalog_slug
  before_validation :generate_whatsapp_url, if: :will_save_change_to_gupshup_phone_number?

  validates :slug,
            exclusion: { in: %w[www],
                         message: '%<value>s is reserved.' }

  before_save :store_prev_catalog_slug
  before_save :format_phone_number
  before_save :generate_font_color, if: :will_save_change_to_shop_main_color?
  before_save :set_ml_domain, if: :will_save_change_to_ml_site?
  before_save :set_mp_domain, if: :will_save_change_to_mp_site?
  after_create :save_free_plan
  after_create :send_to_slack
  after_create :create_retailer_onboarding
  before_update :remove_mia_platforms_associations_if_chatbot_disabled
  after_update :add_sales_channel, if: :sync_sales_channel?
  after_update :import_hubspot_properties, if: lambda { |obj|
                                                 obj.hubspot_integrated? && obj.hs_access_token_before_last_save.nil?
                                               }
  after_update :import_hubspot_owners, if: lambda { |obj|
                                             obj.hubspot_integrated? && obj.hs_access_token_before_last_save.nil?
                                           }

  after_save :update_gupshup_info, if: :saved_change_to_gupshup_src_name?
  after_save :update_hs_next_sync, if: :saved_change_to_hs_sync_conversation?

  after_commit :send_follow_up, on: %i[create update], if: -> { name.present? && !welcome_message_sent }
  after_commit :generate_slug, on: %i[create update], if: -> { name.present? }
  after_commit :generate_slug_email, on: :create, unless: -> { name.present? }
  after_commit :update_connection_date, if: :saved_change_to_qr_phone_number?, on: [:create, :update]
  after_commit :create_roles, on: :create
  after_commit :update_shop, on: :update, if: :update_shop?

  scope :active, -> { joins(:payment_plan).where(payment_plans: { status: :active }) }
  scope :inactive, lambda {
    joins(:payment_plan).where(payment_plans: { status: :inactive }).where.not(payment_plans: { plan: 0 })
  }
  scope :paying_retailers, lambda {
    joins(:payment_plan)
      .where.not(payment_plans: { plan: 0 })
      .where.not(payment_plans: { status: PaymentPlan.statuses[:inactive] })
  }
  scope :with_growth_tools_access, -> { joins(:payment_plan).where(payment_plans: { growth_tools_access: true }) }

  delegate :monthly_recurring_revenue, to: :payment_plan
  delegate :price, to: :payment_plan, prefix: true, allow_nil: true
  delegate :contacts_sync, to: :shopify_shop, allow_nil: true
  delegate :addresses_sync, to: :shopify_shop, allow_nil: true
  delegate :max_agents, to: :payment_plan, allow_nil: true

  enum id_type: { cedula: 0, pasaporte: 1, ruc: 2, rut: 3, otro: 4 }
  enum hubspot_match: { phone_or_email: 0, phone: 1, email: 2 }, _prefix: true
  enum shipping_cost_method: { fixed_cost: 0, location_cost: 1 }
  enum mia_products_synced: { not_synced: 0, pending: 1, complete: 2, sync_failed: 3 }, _prefix: true

  accepts_nested_attributes_for :retailer_bill_detail, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :retailer_schedules, reject_if: :all_blank
  accepts_nested_attributes_for :mp_retailer, allow_destroy: true
  accepts_nested_attributes_for :retailer_onboarding, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :retailer_mia_platforms, allow_destroy: true
  accepts_nested_attributes_for :partner_config, allow_destroy: true

  attr_accessor :validate_name, :from_domain

  ransacker :name, type: :string do
    Arel.sql('unaccent("name")')
  end

  def self.ransackable_attributes(_auth_object = nil)
    %w[active_shop address all_customers_hs_integrated allow_bots allow_chat_videos allow_qr retailer_number
       allow_send_videos allow_voice_notes bm_verified campaign_chunk_size catalog_slug chargebee_customer_id
       city connect_qr_mercately country_code created_at currency delete_assets description domain ecu_charges
       enabled_customer_events enabled_new_bots facebook_url first_payment_date font_color gupshup_connection_date
       gupshup_phone_number gupshup_src_name gupshup_timezone hs_access_token hs_conversacion_sync_time hs_expires_in
       hs_id hs_next_sync hs_refresh_token hs_sync_conversation hs_tags hubspot_match id id_number id_type instagram_url
       int_charges is_capi is_workspace last_api_key_modified_date last_sync_with_churnzero_date manage_team_assignment
       max_agents ml_domain ml_site mp_domain mp_site multiple_fast_answers name partner_id payphone_connected
       payphone_id phone_id phone_number phone_verified qr_phone_id qr_phone_number registration_mail_sent
       retailer_number rotate_images_in_catalog send_max_size_files shipping_cost_method shop_active_send_order
       shop_active_store_pickup shop_main_color shop_updated shopify_integrated shopify_paid_version show_basic_stats
       show_stats slug state tax_amount terms_conditions_content tiktok_url timezone twitter_url unique_key
       unlimited_account updated_at welcome_message_sent whats_app_enabled whatsapp_url ws_balance ws_conversation_cost
       ws_next_notification_balance ws_notification_cost zip_code hide_product_prices]
  end

  def self.ransackable_associations(_auth_object = nil)
    %w[agent_hubspot_owners automatic_answers automations avatar_attachment avatar_blob background_attachment
       background_blob business_rules calendar_events campaigns chat_bot_customers chat_bots contact_groups
       ctx_service customer_event_categories customer_exports customer_fb_posts customer_hubspot_fields
       customer_related_data customer_related_fields customers deal_automations deals facebook_catalog
       facebook_retailer funnel_deal_histories funnel_exports funnels gupshup_whatsapp_messages hubspot_fields
       hubspot_owners integration_message_data integration_messages meli_retailer message_blocks mia_integrations
       mia_platforms mp_retailer partner payment_methods payment_plan paymentez_credit_cards paymentez_transactions
       plan_cancellations products quotes reminders retailer_addresses retailer_amount_messages
       retailer_amount_messages_by_hoursretailer_average_response_times retailer_bill_detail retailer_business_rule_data
       retailer_business_rules retailer_conversations retailer_customers retailer_mia_platforms
       retailer_most_used_tags retailer_onboarding retailer_schedules retailer_unfinished_message_blocks retailer_user
       retailer_users retailer_whatsapp_conversations roles sales_channels scheduled_automations shop_orders
       shopify_shop stripe_retailer stripe_transactions tags team_assignments teams template_stats templates top_ups
       users whatsapp_messages whatsapp_templates]
  end

  # filter recibe un valor booleano
  def self.platform_filter(platform, filter)
    if filter
      integrated_retailers(platform)
    else
      non_integrated_retailers(platform)
    end
  end

  # rubocop:disable Metrics/CyclomaticComplexity
  def self.integrated_retailers(platform)
    active_retailers = Retailer.paying_retailers.order(:id)
    case platform
    when 'whatsapp_integrated'
      active_retailers.where('whats_app_enabled IS TRUE AND
                             ((gupshup_phone_number IS NOT NULL AND gupshup_src_name IS NOT NULL) OR
                             (qr_phone_number IS NOT NULL AND qr_phone_id IS NOT NULL))')
    when 'facebook_integrated'
      active_retailers.joins(:facebook_retailer)
        .where.not(facebook_retailers: { uid: nil,
                                         access_token: nil,
                                         messenger_integrated: false })
    when 'instagram_integrated'
      active_retailers.joins(:facebook_retailer)
        .where.not(facebook_retailers: { instagram_uid: nil,
                                         access_token: nil,
                                         instagram_integrated: false })
    when 'fb_comments_integrated'
      active_retailers.joins(:facebook_retailer).where(facebook_retailers: { facebook_comments: true })
    when 'ig_comments_integrated'
      active_retailers.joins(:facebook_retailer).where(facebook_retailers: { instagram_comments: true })
    when 'ml_integrated'
      active_retailers.joins(:meli_retailer).where.not(meli_retailer: nil)
    when 'stripe_integrated'
      active_retailers.joins(:stripe_retailer).where.not(stripe_retailers: { confirmed: nil })
    when 'mercado_pago_integrated'
      active_retailers.joins(:mp_retailer).where.not(mp_retailer: nil)
    else
      Rails.logger.info("Plataforma no eocntrada: #{platform}")
    end
  end
  # rubocop:enable Metrics/CyclomaticComplexity

  # rubocop:disable Rails/WhereEquals
  # rubocop:disable Metrics/CyclomaticComplexity
  def self.non_integrated_retailers(platform)
    active_retailers = Retailer.paying_retailers.order(:id)
    case platform
    when 'whatsapp_integrated'
      active_retailers.where('whats_app_enabled IS FALSE AND
                             ((gupshup_phone_number IS NULL AND gupshup_src_name IS NULL) OR
                             (qr_phone_number IS NULL AND qr_phone_id IS NULL))')
    when 'facebook_integrated'
      active_retailers.left_joins(:facebook_retailer).where('facebook_retailers.uid IS NULL OR
                                                            facebook_retailers.access_token IS NULL OR
                                                            facebook_retailers.messenger_integrated IS FALSE')
    when 'instagram_integrated'
      active_retailers.left_joins(:facebook_retailer).where('facebook_retailers.instagram_uid IS NULL OR
                                                            facebook_retailers.access_token IS NULL OR
                                                            facebook_retailers.instagram_integrated IS FALSE')
    when 'fb_comments_integrated'
      active_retailers.left_joins(:facebook_retailer)
        .where('facebook_retailers IS NULL OR facebook_retailers.facebook_comments IS FALSE')
    when 'ig_comments_integrated'
      active_retailers.left_joins(:facebook_retailer)
        .where('facebook_retailers IS NULL OR facebook_retailers.instagram_comments IS FALSE')
    when 'ml_integrated'
      active_retailers.left_joins(:meli_retailer).where('meli_retailers IS NULL')
    when 'stripe_integrated'
      active_retailers.left_joins(:stripe_retailer)
        .where('stripe_retailers IS NULL OR stripe_retailers.confirmed IS FALSE')
    when 'mercado_pago_integrated'
      active_retailers.left_joins(:mp_retailer).where('mp_retailers IS NULL')
    else
      Rails.logger.info("Plataforma no eocntrada: #{platform}")
    end
  end
  # rubocop:enable Rails/WhereEquals
  # rubocop:enable Metrics/CyclomaticComplexity

  def prev_catalog_slug
    self[:prev_catalog_slug].presence || catalog_slug
  end

  def store_prev_catalog_slug
    return if catalog_slug == self[:prev_catalog_slug]

    self[:prev_catalog_slug] = catalog_slug_was || catalog_slug
  end

  def facebook_unread_messages(retailer_user)
    facebook_retailer&.facebook_unread_messages(retailer_user)
  end

  def instagram_unread_messages(retailer_user)
    facebook_retailer&.instagram_unread_messages(retailer_user)
  end

  def to_param
    slug
  end

  def generate_slug
    return if saved_change_to_slug? && slug.present?

    new_slug = if Retailer.where('LOWER(name) LIKE ?', "%#{name.downcase}%").where.not(id: id).exists?
                 "#{name}-#{id}".parameterize
               else
                 name.parameterize
               end
    return if slug == new_slug

    update slug: new_slug
  rescue StandardError => e
    error_message = "Retailer: #{id} #{name} no pudo crear el slug"
    handle_slug_generation_error(new_slug, e, error_message)
  end

  def generate_slug_email
    return if saved_change_to_slug?

    slug_text = retailer_users.first&.email
    if slug_text
      slug_text = slug_text.split('@').first
      slug_text = slug_text.parameterize
    end

    new_slug = if Retailer.where('LOWER(slug) LIKE ?', "%#{slug_text.downcase}%").where.not(id: id).exists?
                 "#{slug_text}-#{id}"
               else
                 slug_text
               end

    return if slug == new_slug

    self.validate_name = 'false'
    update slug: new_slug
  rescue StandardError => e
    error_message = "Retailer: #{id} #{name} no pudo crear el slug email"
    handle_slug_generation_error(new_slug, e, error_message)
  end

  def handle_slug_generation_error(new_slug, error, custom_error_message)
    update_slug_query(new_slug)
    Rails.logger.error(error.message)
    SlackError.send_error(error, custom_error_message, true)
    Rails.logger.error("Exception in Retailer: #{error.class} - #{error.message}\n#{error.backtrace&.join("\n")}")
  end

  # Actualiza el token de ML si esta a punto de vencer
  def update_meli_access_token
    return if meli_retailer.meli_token_updated_at.to_i > (DateTime.current - 4.hours).to_i

    MercadoLibre::Auth.new(self).refresh_access_token
  end

  # rubocop:disable Rails/WhereExists
  def unread_messages
    Message.includes(:customer).where(date_read: nil, answer: nil, customers: { retailer_id: id }).exists?
  end

  def unread_questions
    Question.includes(:customer).where(date_read: nil, customers: { retailer_id: id }).exists?
  end
  # rubocop:enable Rails/WhereExists

  def unread_questions_records
    Question.includes(:customer).where(date_read: nil, customers: { retailer_id: id })
  end

  def unread_orders
    Order.joins(:customer).where('orders.count_unread_messages > 0 AND customers.retailer_id = ?', id)
  end

  def transactions
    if int_charges?
      stripe_transactions
    else
      paymentez_transactions
    end
  end

  # rubocop:disable Lint/UnreachableCode
  def gupshup_unread_whatsapp_messages(retailer_user)
    return []

    messages = gupshup_whatsapp_messages.includes(:customer).where.not(status: 'read', whatsapp_message_id: nil)
      .where(direction: 'inbound', customers: { retailer_id: id })
    return messages if retailer_user.admin? || retailer_user.supervisor?

    if retailer_user.only_assigned?
      messages.includes(customer: :agent_customer).where(agent_customers: { retailer_user_id: retailer_user.id })
    else
      messages.includes(customer: :agent_customer)
        .where(agent_customers: { retailer_user_id: [retailer_user.id, nil] })
    end
  end
  # rubocop:enable Lint/UnreachableCode

  def incomplete_meli_profile?
    id_number.blank? || address.blank? || city.blank? || state.blank?
  end

  def generate_api_key
    api_key = SecureRandom.hex
    update(api_key: api_key, last_api_key_modified_date: Time.zone.now)

    api_key
  end

  def public_phone_number
    phone_number.presence || qr_phone_number
  end

  def team_agents
    retailer_users.joins(:user).where(removed_from_team: false, users: { invitation_token: nil })
  end

  def admins(agent_id = nil)
    admins = retailer_users.joins(:user)
      .where(retailer_admin: true, removed_from_team: false, users: { invitation_token: nil })

    if agent_id.present?
      admins = admins.where('retailer_users.managed_agents = \'[]\' OR
                             retailer_users.managed_agents IS NULL OR
                             retailer_users.managed_agents @> ?', [agent_id].to_json)
    end

    admins
  end

  def supervisors(agent_id = nil)
    supervisors = retailer_users.joins(:user)
      .where(retailer_supervisor: true, removed_from_team: false, users: { invitation_token: nil })

    if agent_id.present?
      supervisors = supervisors.where('retailer_users.managed_agents = \'[]\' OR
                             retailer_users.managed_agents IS NULL OR
                             retailer_users.managed_agents @> ?', [agent_id].to_json)
    end

    supervisors
  end

  def positive_balance?(_customer = nil)
    return true if connect_bsp_mercately

    ws_balance > -10.0
  end

  def mia_insufficient_balance?
    ws_balance < -5.0
  end

  def gupshup_temporal_messages
    GupshupTemporalMessageState.where(
      retailer_id: id
    ).order_by({ event_timestamp: -1 })
  end

  def whatsapp_phone_number(with_plus_sign = true)
    number = gupshup_phone_number || qr_phone_number || self[:phone_number]
    return '' if number.blank?

    with_plus_sign ? "+#{number.gsub('+', '')}" : number.gsub('+', '')
  end
  # rubocop:enable Style/OptionalBooleanParameter

  def karix_integrated?
    whats_app_enabled? && karix_whatsapp_phone.present? &&
      karix_account_uid.present? && karix_account_token.present?
  end

  def gupshup_integrated?
    whats_app_enabled? &&
      gupshup_phone_number.present? &&
      gupshup_src_name.present?
  end

  def qr_integrated?
    whats_app_enabled? &&
      qr_phone_number.present? &&
      qr_phone_id.present?
  end

  def whatsapp_welcome_message
    get_automatic_answer('whatsapp', 0)
  end

  def whatsapp_inactive_message
    get_automatic_answer('whatsapp', 1)
  end

  def messenger_welcome_message
    get_automatic_answer('messenger', 0)
  end

  def messenger_inactive_message
    get_automatic_answer('messenger', 1)
  end

  def instagram_welcome_message
    get_automatic_answer('instagram', 0)
  end

  def instagram_inactive_message
    get_automatic_answer('instagram', 1)
  end

  def retailer_user_connected_to_fb
    retailer_users.where.not(uid: nil, provider: nil, facebook_access_token: nil).first
  end

  def whatsapp_integrated?
    gupshup_integrated? || qr_integrated?
  end

  def hubspot_integrated?
    hs_access_token.present?
  end

  def facebook_integrated?
    return false unless facebook_retailer

    facebook_retailer.connected?
  end

  def instagram_integrated?
    facebook_retailer&.instagram_integrated?
  end

  def ml_integrated?
    meli_retailer.present?
  end

  def mercado_pago_integrated?
    mp_retailer.present?
  end

  def fb_comments_integrated?
    facebook_retailer&.facebook_comments
  end

  def ig_comments_integrated?
    facebook_retailer&.instagram_comments
  end

  def stripe_integrated?
    return false unless stripe_retailer

    stripe_retailer&.confirmed
  end

  def payphone_integrated?
    payphone_connected?
  end

  def electronic_payments_connected?
    stripe_integrated? || mercado_pago_integrated? || payphone_integrated?
  end

  def main_paymentez_credit_card
    paymentez_credit_cards.find_by(main: true)
  end

  def hs_properties
    hubspot.contact_properties.pluck('name')
  end

  def hs_owners
    hubspot.owners.pluck('id')
  end

  def refund_message_cost(cost)
    Retailers::BalanceQuery.new(id).add(cost.to_f)
  end

  def create_retailer_onboarding
    return if is_workspace || retailer_onboarding.present?

    RetailerOnboarding.create(retailer: self)
  end

  def current_funnel
    funnels&.first
  end

  def update_gs_info
    update_gupshup_info
  end

  def send_failed_charge_email
    card_type = if ecu_charges
                  PaymentezCardHelper.brand(paymentez_credit_cards.last&.card_type)
                elsif int_charges
                  payment_methods.last&.card_type
                end

    recipients = RetailerUser.active(id).where(retailer_admin: true)
      .or(RetailerUser.active(id).where(retailer_supervisor: true))

    recipients.each do |r|
      RetailerMailer.failed_charge(self, r, card_type).deliver_now
    end
  end

  def sync_ml_unread
    unread = unread_questions || unread_orders.exists?

    retailer_users.update_all(
      ml_unread: unread,
      unread_ml_chats_count: unread_orders.count,
      unread_ml_questions_count: unread_questions_records.count,
      total_unread_ml_count: unread_orders.count + unread_questions_records.count
    )
  end

  def currency_symbol
    Money::Currency.new(currency)&.symbol || currency
  end

  def remaining_free_conversations
    time = gupshup_timezone ? ActiveSupport::TimeZone.new(gupshup_timezone).utc_to_local(Time.now.utc) : Time.now
    year = time.year
    month = time.month
    retailer_ws_convs = retailer_whatsapp_conversations.where(year: year, month: month)
    return 1000 unless retailer_ws_convs.any?

    total_free = retailer_ws_convs.sum(:free_tier_total)
    1000 - total_free
  end

  def has_rule?(identifier)
    business_rules.where(identifier: identifier).any?
  end

  def attach_agent_name?
    has_rule?('attach_agent_name_in_messages')
  end

  def pending_orders
    return {} if Rails.env.test? || order_shop.nil?

    order_shop.pending_orders
  rescue StandardError => e
    Rails.logger.error(e.message)
    SlackError.send_error(e)
    Rails.logger.error("Exception in Retailer pending orders: #{e.class} - #{e.message}\n#{e.backtrace&.join("\n")}")
    {}
  end

  def owner
    admins.order(created_at: :asc).first.presence || team_agents.order(created_at: :asc).first
  end

  delegate :email, to: :owner_user, allow_nil: true
  delegate :first_name, :last_name, to: :owner, allow_nil: true

  def connect_to_qr_code(number)
    update_columns(
      qr_phone_number: number,
      whats_app_enabled: true,
      unlimited_account: true
    )
  end

  def disconnect_qr_code
    return false if qr_phone_number.nil?

    update_column(:qr_phone_number, nil)
    notify_disconnection
  end

  def business_rule_data(identifier)
    rule = business_rules.find_by(identifier: identifier)
    return {} if rule.nil?

    data = retailer_business_rule_data.find_by(business_rule_id: rule.id)
    data ? data.data : {}
  end

  def workspaces_count
    RetailerUser.where(user_id: users.ids).pluck(:retailer_id).uniq.count
  end

  def time_with_timezone(time)
    if timezone.present?
      ActiveSupport::TimeZone.new(timezone).utc_to_local(time.utc)
    else
      time.localtime
    end
  end

  def plan_details
    return {} if payment_plan.chargebee_subscription_id.blank?

    plan = chargebee.subscription(payment_plan.chargebee_subscription_id).subscription

    start_date = Time.at(plan.current_term_start).utc.in_time_zone('Eastern Time (US & Canada)')
    end_date = Time.at(plan.current_term_end).utc.in_time_zone('Eastern Time (US & Canada)') + 1.day
    current_time = Time.now.utc.in_time_zone('Eastern Time (US & Canada)')
    days = end_date.day - current_time.day

    if plan.next_billing_at
      next_billing_date = Time.at(plan.next_billing_at).utc.in_time_zone('Eastern Time (US & Canada)')
        .strftime('%d/%m/%Y')
    end

    subscription_plan_item = plan.subscription_items.find { |p| p.item_type == 'plan' }

    {
      plan_id: plan.id,
      plan_currency_code: plan.currency_code,
      billing_period: plan.billing_period,
      days_to_next_billing: days,
      billing_period_unit: plan.billing_period_unit,
      next_billing_at: next_billing_date,
      current_term_start: start_date.strftime('%F'),
      current_term_end: end_date.strftime('%F'),
      status: plan.status,
      mrr: plan.mrr,
      addons: plan.subscription_items.select { |item| item.item_type == 'addon' },
      chargebee_plan_id: subscription_plan_item.item_price_id
    }
  end

  def payment_plan_details
    plan = payment_plan
    return {} if plan.pricing_tier_id.blank?

    start_date = Time.parse(plan.payment_start_date.to_s).utc.in_time_zone('Eastern Time (US & Canada)')
    end_date = Time.parse(plan.next_pay_date.to_s).utc.in_time_zone('Eastern Time (US & Canada)') + 1.day

    if plan.next_pay_date
      next_billing_date = Time.parse(plan.next_pay_date.to_s).utc.in_time_zone('Eastern Time (US & Canada)')
        .strftime('%d/%m/%Y')
    end

    subscription_plan_item = plan.pricing_tier

    {
      plan_id: subscription_plan_item.identifier,
      plan_currency_code: 'USD',
      next_billing_at: next_billing_date,
      current_term_start: start_date.strftime('%F'),
      current_term_end: end_date.strftime('%F'),
      status: plan.active? ? 'active' : 'cancelled',
      addons: plan.bought_addons,
      chargebee_plan_id: subscription_plan_item.identifier,
      bought_agents: plan.max_agents,
      has_ai: subscription_plan_item.contains_ai?
    }
  end

  def count_active_team_agents
    retailer_users.where(removed_from_team: false).size
  end

  def max_agents=(value)
    payment_plan.update(max_agents: value)
  end

  def mia_platforms_amount
    return unless retailer_mia_platforms.reject(&:marked_for_destruction?).empty?

    errors.add(:base, 'Debe seleccionar una plataforma')
  end

  def customers_with_gupshup_messages(unassigned: nil, per_page: 25, page: 1)
    offset = (page - 1) * per_page
    query = Customer.joins(:gupshup_whatsapp_messages)
      .select('customers.id, customers.first_name, customers.last_name, customers.email, customers.phone,
        customers.web_id, customers.last_chat_interaction AS last_interaction, customers.retailer_user_id AS agent_id,
        COUNT(gupshup_whatsapp_messages.id) AS message_count')
      .group('customers.id, customers.first_name, customers.last_name, customers.email, customers.phone,
        customers.web_id, customers.last_chat_interaction, customers.retailer_user_id')
      .order(last_chat_interaction: :desc)
      .limit(per_page)
      .offset(offset)

    query = query.where("customers.retailer_user_id #{unassigned ? 'IS NULL' : 'IS NOT NULL'}") unless unassigned.nil?
    query
  end

  def customers_with_facebook_messages(unassigned: nil, per_page: 25, page: 1)
    offset = (page - 1) * per_page
    query = Customer.joins(:facebook_messages)
      .select('customers.id, customers.first_name, customers.last_name, customers.email, customers.phone,
        customers.web_id, customers.last_msn_interaction AS last_interaction, customers.retailer_user_id AS agent_id,
        COUNT(facebook_messages.id) AS message_count')
      .group('customers.id, customers.first_name, customers.last_name, customers.email, customers.phone,
        customers.web_id, customers.last_msn_interaction, customers.retailer_user_id')
      .order(last_msn_interaction: :desc)
      .limit(per_page)
      .offset(offset)

    query = query.where("customers.retailer_user_id #{unassigned ? 'IS NULL' : 'IS NOT NULL'}") unless unassigned.nil?
    query
  end

  def retailer_counter
    # Use thread-local cache to avoid multiple MongoDB queries within the same request
    RetailerCounterCache.fetch(id)
  end

  private

    def owner_user
      owner&.user
    end

    def create_roles
      %w[Administrador Agente Supervisor].each { |role| roles.create(name: role) }
    end

    def chargebee
      @chargebee ||= Chargebee::Api.new
    end

    def send_follow_up_message(customer, retailer_user)
      template = WhatsappTemplate.find_by(id: ENV.fetch('WHATSAPP_TEMPLATE', nil))
      return unless template

      template_params = [name, retailer_user.first_name]
      replace_params = { template_params: [name, retailer_user.first_name] }
      aux_message = template.template_text(replace_params).gsub('\\*', '*')
      aux_message = aux_message.gsub(/(\r)/, '')
      params = {
        gupshup_template_id: template.gupshup_template_id,
        template_params: template_params,
        template: 'true',
        type: 'image',
        content_type: 'image/png',
        file_url: 'https://d1zmac1huz14sq.cloudfront.net/caracteristicas+servicio+3.png',
        url: 'https://d1zmac1huz14sq.cloudfront.net/caracteristicas+servicio+3.png',
        file_name: 'caracteristicas-servicio-3.png',
        caption: aux_message
      }
      gws = Whatsapp::Outbound::Msg.new(Retailer.find(ENV.fetch('MERCATELY_RETAILER_ID', nil)), customer)
      gws.send_message(type: 'file', params: params)
    end

    def find_or_create_follow_up_customer(retailer_user)
      customer = Customer.find_by(phone: retailer_number, retailer_id: ENV.fetch('MERCATELY_RETAILER_ID', nil))
      agent_id = Customers::AssignResponsible.call

      if customer.present?
        # si ya existe actualizar el from_registration a true y setear el id del retailer del que proviene
        customer.update_columns(from_registration: true, company_name: name, registered_retailer_id: id)
        return customer
      end

      customer_params = {
        retailer_id: ENV.fetch('MERCATELY_RETAILER_ID', nil),
        registered_retailer_id: id,
        first_name: retailer_user.first_name,
        last_name: retailer_user.last_name,
        country_id: country_code,
        phone: self[:retailer_number],
        send_for_opt_in: true,
        company_name: name,
        from_registration: true,
        created_in: 'Retailer registration'
      }

      if agent_id.present?
        customer_params[:retailer_user_id] = agent_id
        customer_params[:agent_customer_attributes] = { retailer_user_id: agent_id }
      end

      Customer.create(customer_params)
    end

    def send_follow_up
      retailer_user = retailer_users.active_admins(id).first
      customer = find_or_create_follow_up_customer(retailer_user)
      response = send_follow_up_message(customer, retailer_user)

      if response
        tag_id = ENV.fetch('REGISTER_TAG_ID', nil)
        tag = Tag.find_by(id: tag_id)

        unless customer.tags.exists?(id: tag_id)
          customer.tags << tag
          customer.save
        end
      end

      update_column(:welcome_message_sent, true)
    rescue StandardError => e
      SlackError.send_error(e)
    end

    def generate_whatsapp_url
      return if gupshup_phone_number.blank? && retailer_number.blank?

      self.whatsapp_url = "https://wa.me/+#{retailer_number.presence || gupshup_phone_number}"
    end

    def save_free_plan
      PaymentPlan.create(retailer: self)
    end

    def send_to_slack
      return unless Rails.env.production?

      slack_client = Slack::Notifier.new(ENV.fetch('SLACK_SDRLEADS', nil), channel: 'sdrleads')
      slack_client.ping([
        'Nuevo Cliente Registrado',
        "Comercio: #{retailer_user.email}",
        "Nombre: #{retailer_user&.first_name} #{retailer_user&.last_name}",
        "Teléfono: #{retailer_number}"
      ].join("\n"))
    rescue StandardError
      Rails.logger.error('Slack disabled')
    end

    # rubocop:disable Metrics/CyclomaticComplexity
    # rubocop:disable Metrics/PerceivedComplexity
    def format_phone_number
      return if retailer_number.blank?

      if country_code
        country = ISO3166::Country.new(country_code)
        splitted_phone = split_phone
        prefix = splitted_phone&.[](0)
        aux_phone = retailer_number.gsub('+', '')

        self.retailer_number = if prefix == country&.country_code
                                 "+#{aux_phone}"
                               else
                                 "+#{country&.country_code}#{aux_phone}"
                               end
      else
        self.retailer_number = "+593#{retailer_number[1, 9]}" if
          retailer_number.size == 10 && retailer_number[0] == '0'

        self.retailer_number = "+#{retailer_number}" if
          retailer_number.size == 12 && retailer_number[0, 3] == '593'
      end
    end
    # rubocop:enable Metrics/CyclomaticComplexity
    # rubocop:enable Metrics/PerceivedComplexity

    def attributes_to_nil
      self.gupshup_src_name = nil if gupshup_src_name.blank?
      self.timezone = nil if timezone.blank?
    end

    def import_hubspot_properties
      contact_properties = hubspot.contact_properties
      return if contact_properties.blank?

      properties = contact_properties.pluck('name', 'label', 'type').map do |name, label, type|
        { hubspot_field: name, hubspot_label: label, hubspot_type: type }
      end
      hubspot_fields.import(
        properties,
        validate: true,
        validate_with_context: :bulk_import,
        on_duplicate_key_update: {
          conflict_target: [:retailer_id, :hubspot_field]
        }
      )
    end

    # rubocop:disable Naming/VariableName, Naming/BlockParameterName
    def import_hubspot_owners
      owners = hubspot.owners.pluck('id', 'firstName', 'lastName').map do |id, firstName, lastName|
        { owner_id: id, first_name: firstName, last_name: lastName }
      end
      hubspot_owners.import(
        owners,
        validate: true,
        validate_with_context: :bulk_import,
        on_duplicate_key_update: {
          conflict_target: [:retailer_id, :owner_id],
          columns: [:first_name, :last_name]
        }
      )
    end
    # rubocop:enable Naming/VariableName, Naming/BlockParameterName

    def hubspot
      @hubspot = HubspotService::Api.new(hs_access_token)
    end

    def set_ml_domain
      ml_country = MlCountry.find_by(site: ml_site)
      self.ml_domain = ml_country&.domain
    end

    def set_mp_domain
      mp_country = IntegrationSetting.find_by(country_code: mp_site)
      self.mp_domain = mp_country&.mp_domain
    end

    def update_gupshup_info
      if gupshup_src_name.blank?
        update(gupshup_app_id: nil, gupshup_app_token: nil)
      else
        update_connection_date
        aux_app_id = gs_service_api.set_app_id(gupshup_src_name)
        return if aux_app_id.blank?

        aux_app_token = gs_service_api.set_app_token(aux_app_id)
        update(gupshup_app_id: aux_app_id, gupshup_app_token: aux_app_token)
      end
    end

    def update_connection_date
      update_column(:gupshup_connection_date, Time.now) unless gupshup_connection_date
    end

    def update_hs_next_sync
      update(hs_next_sync: hs_conversacion_sync_time.hours.from_now) if hs_sync_conversation
    end

    def gs_service_api
      @gs_service_api ||= GupshupPartners::Api.new
    end

    def shop
      @shop ||= Shops::RetailerSync.new(self)
    end

    def update_shop?
      changed_attributes = %i[
        name description retailer_number country_code currency unique_key
        slug catalog_slug tax_amount facebook_url instagram_url twitter_url
        whatsapp_url tiktok_url timezone shop_main_color font_color active_shop
        encrypted_api_key last_api_key_modified_date shop_active_send_order
        shop_active_store_pickup gupshup_phone_number qr_phone_number phone_number
        website facebook_app_id merchant_id crm_key fulfillment_center
        send_order_notifications delivery_url authorized_payment_name
        authorize_send_order_notifications payphone_connected payphone_id avatar
        background rotate_images_in_catalog terms_conditions_content shipping_cost_method
        mia_products_sync hide_product_prices
      ]

      saved_change = changed_attributes.any? do |attr|
        saved_change_to_attribute?(attr)
      end

      attachment_changes = (avatar.attached? && avatar.attachment.blob.saved_changes?) ||
                           (background.attached? && background.attachment.blob.saved_changes?)

      (saved_change || attachment_changes) && !from_domain
    end

    def sync_sales_channel?
      saved_change_to_attribute?(:whats_app_enabled) ||
        saved_change_to_attribute?(:gupshup_phone_number) ||
        saved_change_to_attribute?(:gupshup_src_name) ||
        saved_change_to_attribute?(:qr_phone_number) ||
        saved_change_to_attribute?(:qr_phone_id)
    end

    def set_catalog_slug
      @prev_catalog_slug = catalog_slug_was || catalog_slug
      return if name.blank?

      if catalog_slug.present?
        self.catalog_slug = catalog_slug.parameterize.gsub('-', '')
        return
      end

      if catalog_slug.nil?
        generated_catalog_slug = name.first(20)
        generated_catalog_slug = generated_catalog_slug.parameterize
        generated_catalog_slug.gsub!('-', '')
        retailers_with_same_catalog_slug = Retailer.where('catalog_slug ILIKE ?', "#{generated_catalog_slug}%")
          .where.not(id: id).order(id: :desc)
        if retailers_with_same_catalog_slug.exists?
          latest_catalog_slug = retailers_with_same_catalog_slug.first.catalog_slug.to_s.scan(/\d/).last.to_i + 1
          generated_catalog_slug = "#{generated_catalog_slug}#{latest_catalog_slug}"
        end
        self.catalog_slug = generated_catalog_slug
      else
        self.catalog_slug = catalog_slug.parameterize.gsub('-', '')
      end
      @set_catalog_slug ||= catalog_slug
    end

    def get_automatic_answer(platform, message_type)
      service = AutomaticAnswers::AutomaticAnswerQuery.new

      message = service.always_active_answer(id, platform, message_type)
      return message if message.present?

      time = if timezone
               Time.now.in_time_zone(timezone)
             else
               Time.now
             end

      service.match_schedule_answer(id, platform, message_type, time)
    end

    def order_shop
      @order_shop ||= Shops::Order.new(self)
    end

    def split_phone
      return if retailer_number.blank?

      begin
        Phony.split(retailer_number.gsub('+', ''))
      rescue Phony::SplittingError => e
        Rails.logger.error(e)
        nil
      end
    end

    def notify_disconnection
      mail_notify_whatsapp_disconnection
      push_notify_whatsapp_disconnection
    end

    def mail_notify_whatsapp_disconnection
      admins.each do |admin|
        RetailerMailer.whatsapp_disconnect_notice(admin).deliver_now
      end
    end

    def push_notify_whatsapp_disconnection
      emails = admins.map(&:email)
      host = Rails.application.config.default_url_options[:host]
      url = host + "/retailers/#{slug}/integrations/whatsapp"
      OneSignalPushNotification.send_qr_disconnection_push(emails, url)
    end

    def strip_name
      self.name = name.strip if name.present?
    end

    def update_slug_query(new_slug)
      sql = ActiveRecord::Base.sanitize_sql_array([
                                                    'UPDATE retailers SET slug = ? WHERE id = ?',
                                                    new_slug,
                                                    id
                                                  ])
      ActiveRecord::Base.connection.execute(sql)
    end

    def remove_mia_platforms_associations_if_chatbot_disabled
      return unless mia_chatbot_active_changed? && !mia_chatbot_active

      retailer_mia_platforms.destroy_all
    end
end
# rubocop:enable Rails/I18nLocaleTexts
# rubocop:enable Metrics/ClassLength
# rubocop:enable Rails/HasManyOrHasOneDependent
# rubocop:enable Rails/InverseOf
# rubocop:enable Rails/WhereNotWithMultipleConditions
# rubocop:enable Naming/PredicateName
