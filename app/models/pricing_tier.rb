class PricingTier < ApplicationRecord
  belongs_to :priceable, polymorphic: true
  has_many :entitlements
  has_many :payment_plans

  validates :price, presence: true, numericality: { greater_than_or_equal_to: 0 }

  enum billing_cycle: { monthly: 0, quarterly: 1, yearly: 2, one_time: 3 }
  enum pricing_model: { tiered: 0, per_unit: 1, flat_fee: 2 }
  enum status: { active: 0, inactive: 1 }

  accepts_nested_attributes_for :entitlements, reject_if: :all_blank, allow_destroy: true

  scope :active, -> { where(status: :active) }

  def self.ransackable_attributes(_auth_object = nil)
    %w[billing_cycle created_at id price updated_at priceable_type]
  end

  def self.ransackable_associations(_auth_object = nil)
    %w[priceable]
  end

  def month_interval
    case billing_cycle
    when 'monthly'
      1
    when 'quarterly'
      3
    when 'yearly'
      12
    else
      0
    end
  end

  def acquired?(payment_plan)
    payment_plan.acquired_addons.exists?(addon_id: priceable_id)
  end

  def contains_ai?
    entitlements.exists?(identifier: 'mia-contacts-included')
  end
end
