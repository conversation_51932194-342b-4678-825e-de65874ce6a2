# frozen_string_literal: true

class Campaign < ApplicationRecord # rubocop:disable Metrics/ClassLength
  include WebIdGenerateableConcern

  belongs_to :retailer
  belongs_to :whatsapp_template, optional: true
  belongs_to :contact_group
  # rubocop:disable Rails/InverseOf
  has_many :gupshup_whatsapp_messages, -> { order(created_at: :asc, id: :asc) }, dependent: :restrict_with_exception
  # rubocop:enable Rails/InverseOf
  has_many :campaign_chunks, dependent: :delete_all
  has_many :customers, through: :contact_group
  has_one_attached :file

  validate :max_250_messages_by_date, if: -> { retailer.qr_integrated? }, on: :create
  validates :whatsapp_template, presence: { if: -> { retailer.gupshup_integrated? } }
  validates :name, :send_at, presence: true
  # validate :time_frame, if: :send_at, on: :create

  before_create :set_estimated_cost
  before_create :set_customers_scope
  before_create :set_timezone, if: -> { retailer.gupshup_integrated? }
  before_create :generate_template_text, if: -> { retailer.gupshup_integrated? }
  before_create :set_chunks, if: -> { retailer.qr_integrated? }

  after_commit :success, if: :sent?, on: :update
  after_commit :send_reason, if: :failed?, on: :update
  after_commit :cancel_chunks!, if: :cancelled?, on: :update
  after_commit :check_template_urls, on: :create

  scope :qr_pending, -> { where(status: [:pending, :in_process], in_chunks: true) }

  enum status: { pending: 0, sent: 1, cancelled: 2, failed: 3, processing: 4, in_process: 5 }

  enum sender_strategy: {
    synchronous: 'synchronous', # Flujo sincrónico actual
    kafka: 'kafka',               # Kafka events
    batch: 'batch',               # Futuro: procesamiento por lotes
    api_direct: 'api_direct',     # Futuro: API directa
    scheduled: 'scheduled'        # Futuro: programado
  }

  attr_accessor :campaign_timezone

  ransacker :name, type: :string do
    Arel.sql('unaccent("name")')
  end

  def self.ransackable_attributes(_auth_object = nil)
    %w[chunks contact_group_id content_params cost created_at current_chunk id in_chunks name
       reason retailer_id send_at send_chunk_at status template_text updated_at web_id whatsapp_template_id]
  end

  def to_param
    web_id
  end

  def calculate_estimated_cost
    return 0.0 if retailer.qr_integrated? || whatsapp_template.blank?

    begin
      cost = if send_at < Time.zone.local(2022, 1, 31).end_of_day
               calculate_pre_2022_cost
             elsif send_at_gs_timezone < Time.zone.local(2023, 6, 1)
               calculate_pre_june_2023_cost
             else
               calculate_post_june_2023_cost
             end

      # Asegurar que siempre retornemos un número válido
      cost.is_a?(Numeric) ? cost.to_f : 0.0
    rescue StandardError => e
      Rails.logger.error("Error calculando costo estimado para campaña #{id}: #{e.message}")
      0.0
    end
  end

  def calculate_cost
    return 0 unless sent? && retailer.gupshup_integrated?

    gupshup_whatsapp_messages.not_error.sum(:cost)
  end

  def customer_details_template(customer)
    template_text.gsub(/{{\w*}}/) do |match|
      vars = replace_params(match, customer)

      vars.gsub!(/{|}/, '')
    end
  end

  def customer_content_params(customer)
    return [] if content_params.blank?

    content_params.map do |cp|
      cp.gsub(/{{\w*}}/) do |match|
        vars = replace_params(match, customer)
        vars.gsub!(/{|}/, '')
      end
    end
  end

  def time_with_zone(data)
    if retailer.timezone.present?
      ActiveSupport::TimeZone.new(retailer.timezone).utc_to_local(data)
    else
      data.localtime
    end
  end

  def last_chunk_time_with_zone
    return time_with_zone(send_at) unless chunks?

    time_with_zone(send_at + (chunks - 1).days)
  end

  def file_url
    file.public_url
  end

  def update_current_chunk
    self.current_chunk += 1
    if current_chunk == chunks
      if not_failed?
        sent!
      else
        update(reason: :service_down, status: :failed)
      end
    else
      self.send_chunk_at = send_at + current_chunk.days
      in_process!
    end
  end

  def sent_to?(phone)
    contact_group.customers.find_by(phone: phone).present?
  end

  def not_failed?
    gupshup_whatsapp_messages.where.not(status: :error).any?
  end

  def can_be_cancelled?
    pending? || processing? || in_process?
  end

  def recipients
    @recipients ||= contact_group.customers.where(retailer_id: retailer.id)
  end

  # Métodos relacionados con estrategias de envío
  def mark_as_kafka_sent!
    update!(sender_strategy: :kafka)
  end

  def mark_as_synchronous_sent!
    update!(sender_strategy: :synchronous)
  end

  def sent_via_kafka?
    kafka?
  end

  def sent_via_sync?
    synchronous?
  end

  private

    def check_template_urls
      return unless whatsapp_template.contain_urls

      slack_client = Slack::Notifier.new(ENV.fetch('SLACK_CONFIG_CAMPAIGNS', nil), channel: '#config-campaigns')
      slack_client.ping([
        'Campanna con URL configurada',
        "Nombre de la campanna: #{name}",
        "Contenido: #{template_text}",
        "Botones interactivos: #{whatsapp_template.buttons}",
        "Retailer: #{retailer.id} #{retailer.name}",
        "Plantilla: #{whatsapp_template.id} #{whatsapp_template.name}",
        "Hora de envío: #{time_with_zone(send_at)} #{retailer.timezone}"
      ].join("\n"))
    rescue StandardError
      Rails.logger.error('Slack disabled')
    end

    def time_frame
      return unless ((time_with_zone(send_at) - time_with_zone(Time.now)) / 3600) < 0.5

      errors.add(:base, 'Fecha de envío debe ser por lo menos 30 minutos mayor a la actual')
    end

    def generate_template_text
      txt = whatsapp_template.text.gsub(/(?<!\\)\*/).with_index do |match, i|
        match.gsub('*', content_params[i])
      end
      txt.gsub!('\\*', '*')
      self.template_text = txt
    end

    def success
      RetailerUser.active_admins(retailer.id).each do |ru|
        CampaignMailer.success(self, ru).deliver_now
      end
    end

    # "reason" es simplemente un campo en la db con el nombre del método a ejecutar
    def send_reason
      send reason
    end

    def cancel_chunks!
      case status_before_last_save
      when 'processing', 'in_process'
        campaign_chunks.pending_to_send.destroy_all
      when 'pending'
        campaign_chunks.destroy_all
      end
    end

    def service_down
      RetailerUser.active_admins(retailer.id).each do |ru|
        CampaignMailer.service_down(self, ru).deliver_now
      end
    end

    def insufficient_balance
      return unless retailer.gupshup_integrated?

      RetailerUser.active_admins(retailer.id).each do |ru|
        CampaignMailer.insufficient_balance(self, ru).deliver_now
      end
    end

    def set_timezone
      time_zone = ActiveSupport::TimeZone.new(retailer.timezone.strip) if retailer.timezone.present?
      self.send_at = if time_zone
                       time_zone.local_to_utc(send_at)
                     else
                       send_at&.change(offset: campaign_timezone&.insert(3, ':'))
                     end
    end

    def replace_params(match, customer)
      match.gsub(/\w+/) do |method|
        if method.in?(Customer.public_fields)
          customer.send(method).presence || ' '
        else
          crf = retailer.customer_related_fields.find_by(identifier: method)
          next ' ' if crf.nil?

          crf.customer_related_data.find_by(customer: customer)&.data || ' '
        end
      end
    end

    def set_chunks
      contacts = ContactGroupCustomer.where(contact_group_id: contact_group_id).size
      self.chunks = (contacts / retailer.campaign_chunk_size.to_f).ceil
      self.in_chunks = true
      self.send_chunk_at = send_at
      build_chunks(contacts)
    end

    def max_250_messages_by_date
      set_timezone
      campaign_chunk_size = retailer.campaign_chunk_size

      tomorrow_time_range = send_at..(send_at + 1.day - 1.minute)
      yesterday_time_range = (send_at - 1.day + 1.minute)..send_at
      tomorrow_messages_count = CampaignChunk.joins(campaign: :retailer)
        .where(send_at: tomorrow_time_range, campaigns: { retailer: retailer })
        .sum(:contacts)
      yesterday_messages_count = CampaignChunk.joins(campaign: :retailer)
        .where(send_at: yesterday_time_range, campaigns: { retailer: retailer })
        .sum(:contacts)
      current_messages_count = ContactGroupCustomer.where(contact_group_id: contact_group_id).size

      if tomorrow_messages_count == campaign_chunk_size || yesterday_messages_count == campaign_chunk_size
        errors.add(:send_at, "ha alcanzado el límite de #{campaign_chunk_size} mensajes cada 24 horas")
      elsif (tomorrow_messages_count.positive? || yesterday_messages_count.positive?) &&
            ((tomorrow_messages_count + current_messages_count.to_f) > campaign_chunk_size ||
              (yesterday_messages_count + current_messages_count.to_f) > campaign_chunk_size)
        errors.add(:send_at, "no debe superar los #{campaign_chunk_size} mensajes en 24 horas")
      end
    end

    def build_chunks(contacts)
      contacts_chunks = if contacts > retailer.campaign_chunk_size
                          split_contacts(contacts)
                        else
                          [contacts]
                        end
      contacts_chunks.each_with_index do |chunk, index|
        campaign_chunks.new(send_at: (send_at + index.days), contacts: chunk)
      end
    end

    def split_contacts(contacts)
      contacts_chunks = [retailer.campaign_chunk_size] * (chunks - 1)
      contacts_chunks << (contacts - contacts_chunks.sum)
    end

    def send_at_gs_timezone
      if retailer.gupshup_timezone.present?
        ActiveSupport::TimeZone.new(retailer.gupshup_timezone).utc_to_local(send_at)
      else
        send_at
      end
    end

    def calculate_pre_2022_cost
      (contact_group.customers.sum(:ws_notification_cost) || 0.0).to_f
    end

    def calculate_pre_june_2023_cost
      remaining_conversations = [retailer.remaining_free_conversations, 0].max

      return 0.0 if remaining_conversations >= recipients.size

      costs = recipients.where.not(ws_bic_cost: nil)
        .order(ws_bic_cost: :desc)
        .pluck(:ws_bic_cost)

      (costs[remaining_conversations..].sum || 0.0).to_f
    end

    def calculate_post_june_2023_cost
      category = whatsapp_template.category&.downcase
      return 0.0 if category.blank?

      partner_config = PartnerConfig.find_by(partner_id: retailer.partner_id, retailer_id: retailer.id)

      if partner_config && partner_config.send("#{category}_cost").present?
        return (recipients.size * partner_config.send("#{category}_cost").to_f).to_f
      end

      cost_attr = "ws_#{category}_cost"
      customers = recipients.includes(:country_price)

      customers.sum { |c| c.respond_to?(cost_attr) ? c.send(cost_attr).to_f : 0.0 }.to_f
    end

    def set_estimated_cost
      self.estimated_cost = calculate_estimated_cost
    end

    def set_customers_scope
      self.customers_scope = retailer.qr_integrated? ? recipients.qr_messageable.size : recipients.api_messageable.size
    end

    def actions
      acciones = ['statistics']
      acciones << 'download' if %w[sent failed].include?(status)
      acciones
    end
end
