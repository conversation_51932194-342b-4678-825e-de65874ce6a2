# rubocop:disable Metrics/ClassLength
class ChatBotOption < ApplicationRecord
  include FileDimensionable
  has_ancestry

  has_one :chat_bot_option_api, dependent: :destroy
  has_one :chat_bot_option_form, dependent: :destroy
  has_many :customer_bot_options, dependent: :destroy
  belongs_to :chat_bot
  has_many :customers, through: :customer_bot_options
  has_many :chat_bot_actions, dependent: :destroy
  has_many :option_sub_lists, dependent: :destroy
  has_many :additional_bot_answers, dependent: :destroy
  validate :answer_length, on: [:create, :update]
  validate :answer_minimum_length, on: [:create, :update]
  validate :children_length, on: :update
  validate :children_name_length, on: :update
  has_one_attached :file

  before_save :check_skip_option
  before_save :validate_interactive
  before_create :set_position
  after_update :reassign_positions, if: :saved_change_to_option_deleted?
  after_update :purge_attachment_file
  before_destroy :check_destroy_requirements
  after_save :update_descendants_on_delete
  after_save :clear_endpoint_actions
  after_save :clear_sub_list
  after_save :save_dimensions_now
  after_commit :insert_option, on: :create, if: -> { former_child_id.present? }

  attribute :file_deleted, :boolean

  attr_accessor :former_child_id, :option_params

  accepts_nested_attributes_for :chat_bot_actions, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :option_sub_lists, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :additional_bot_answers, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :chat_bot_option_api, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :chat_bot_option_form, reject_if: :all_blank, allow_destroy: true

  enum option_type: {
    decision: 0, form: 1, message: 2, jump_to: 3, failure: 4, active_mia: 5, api: 6, resolve_chat: 7
  }
  enum interactive: { text: 0, buttons: 1, list: 2, fake: 3 }
  enum message_type: { text: 0, image: 1, document: 2, audio: 3, video: 4 }, _prefix: true
  enum answer_type: { text: 0, anything: 1, unique: 2, location: 3 }, _prefix: true

  scope :active, -> { where(option_deleted: false).where.not(option_type: :failure) }
  scope :active_index, -> { where(option_deleted: false) }
  scope :fake_options, -> { where(fake_option: true) }
  scope :positioned, -> { order(:position) }

  def file_url
    if file.is_aws
      file.public_url
    elsif file.content_type.include?('video/') || file.content_type.include?('audio/')
      "https://res.cloudinary.com/#{ENV.fetch('CLOUDINARY_CLOUD_NAME', nil)}/video/upload/#{file.key}"
    else
      "https://res.cloudinary.com/#{ENV.fetch('CLOUDINARY_CLOUD_NAME', nil)}/image/upload/#{file.key}"
    end
  end

  # rubocop:disable Rails/WhereExists
  def execute_endpoint?
    chat_bot_actions.where(action_type: :exec_callback).exists?
  end

  def jump_to_option?
    chat_bot_actions.where(action_type: :jump_to_option).exists?
  end
  # rubocop:enable Rails/WhereExists

  # rubocop:disable Naming/PredicateName
  def has_sub_list?
    option_sub_lists.present?
  end

  # rubocop:disable Rails/WhereExists
  def is_auto_generated?
    return parent.chat_bot_actions.where(action_type: :auto_generate_option).exists? if parent.present?

    false
  end
  # rubocop:enable Rails/WhereExists

  def has_additional_answers_filled?
    additional_bot_answers.with_attached_file.each do |aba|
      return true if aba.text.present? || aba.file.attached?
    end

    false
  end

  def has_return_options?
    go_past_option || go_start_option
  end

  def has_dynamic_list?(customer)
    return false if parent.blank?

    response = customer.customer_bot_responses.where(chat_bot_option_id: parent.id, status: :success).first&.response
    response&.options.present?
  end
  # rubocop:enable Naming/PredicateName

  # rubocop:disable Rails/DynamicFindBy
  def save_data_action_complete
    action = chat_bot_actions.find_by_action_type(:save_on_db)
    return unless action.present? && (action.target_field.present? || action.customer_related_field.present?)

    action
  end
  # rubocop:enable Rails/DynamicFindBy

  def file_type
    return unless file.attached?
    return 'image' if file.content_type.include?('image/')
    return 'file' if file.content_type == 'application/pdf'
    return 'audio' if file.content_type.include?('audio/')

    'video' if file.content_type.include?('video/')
  end

  def items_list
    return option_sub_lists unless has_return_options?

    items = option_sub_lists.to_a
    size = items.size
    if go_past_option
      size += 1
      items << OptionSubList.new(
        chat_bot_option: self,
        position: size,
        value_to_save: '_back_',
        value_to_show: 'Volver'
      )
    end

    if go_start_option
      size += 1
      items << OptionSubList.new(
        chat_bot_option: self,
        position: size,
        value_to_save: '_start_',
        value_to_show: 'Ir al menú principal'
      )
    end

    items
  end

  def height
    file.metadata['height']
  end

  def width
    file.metadata['width']
  end

  def failure_option
    children.where(option_deleted: false, fake_option: true, option_type: :failure).first
  end

  def first_active_option
    first = children.active.first
    return unless first
    return first unless first.fake_option

    form? || api? ? first.first_active_option : first
  end

  def contains_exit_action?
    chat_bot_actions.exists?(action_type: :get_out_bot)
  end

  def closest_api_ancestor
    api_ancestors = ancestors.where(option_type: :api)
    api_ancestors.last # Retorna la más cercana (última en la jerarquía)
  end

  private

    def set_position
      return if former_child_id

      self.position = parent.present? ? parent.children.active.size + 1 : 0
    end

    def check_destroy_requirements
      throw(:abort)
    end

    def update_descendants_on_delete
      return unless option_deleted == true

      if fake_option? && !failure? && parent.form?
        children.active_index.first&.update(source: source, parent: parent, position: 1)
      else
        descendants.update_all(option_deleted: true)
      end
    end

    def clear_endpoint_actions
      return if execute_endpoint?

      chat_bot_actions.where(classification: %w[success failed]).delete_all
    end

    def reassign_positions
      return unless option_deleted

      return unless parent

      parent.children.active.order(:id).except(self).each_with_index do |cbo, index|
        cbo.update_column(:position, index + 1)
      end
    end

    def clear_sub_list
      return if option_type == 'form'

      option_sub_lists.delete_all
      self.go_past_option = false
      self.go_start_option = false
    end

    def check_skip_option
      self.skip_option = false if option_type == 'decision'
    end

    def validate_interactive
      return if interactive.present?

      self.interactive = 'fake' if fake_option
    end

    # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    def children_length
      return if interactive == 'text'

      case option_type
      when 'decision'
        if interactive == 'buttons'
          if children.active.size > 3
            errors.add(:base,
                       'La opción tiene más de 3 hijos. No puede usar el formato de botones')
          end
        elsif children.active.size > 10
          errors.add(:base,
                     'La opción tiene más de 10 hijos. No puede usar el formato de lista')
        end
      when 'form'
        if interactive == 'buttons'
          if items_list.size > 3
            errors.add(:base,
                       'La sublista tiene más de 3 items. No puede usar el formato de botones')
          end
        elsif items_list.size > 10
          errors.add(:base,
                     'La sublista tiene más de 10 items. No puede usar el formato de lista')
        end
      end
    end

    def children_name_length
      return if interactive == 'text' || fake_option

      case option_type
      when 'decision'
        if interactive == 'buttons'
          if children.active.pluck(:text).any? { |name| name.size > 20 }
            errors.add(
              :text,
              'Los nombres de las opciones hijo tienen más de 20 caracteres. No puede usar el formato de botones'
            )
          end
        elsif children.active.pluck(:text).any? { |name| name.size > 24 }
          errors.add(
            :text,
            'Los nombres de las opciones hijo tienen más de 24 caracteres. No puede usar el formato de lista'
          )
        end
        children.active.pluck(:text).any? { |name| name.size > 24 }
      when 'form'
        if interactive == 'buttons'
          errors.add(:text, 'La sublista tiene items de más de 20 caracteres. No puede usar el formato de botones') if
            items_list.pluck(:value_to_show).any? { |value| value.size > 20 }
        elsif items_list.pluck(:value_to_show).any? { |value| value.size > 24 }
          errors.add(:text, 'La sublista tiene items de más de 24 caracteres. No puede usar el formato de lista')
        end
        items_list.pluck(:value_to_show).any? { |value| value.size > 24 }
      end
    end
    # rubocop:enable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity

    def answer_length
      return if interactive != 'list'

      return unless answer && answer.size > 1024

      errors.add(:answer,
                 'La respuesta de la opción tiene más de 1024 caracteres. No puede usar el formato de lista')
    end

    def answer_minimum_length
      return if interactive == 'text' || fake_option

      errors.add(:answer, 'La respuesta de la opción está vacía. No puede usar un formato distinto de texto') if
        answer.blank? && !is_auto_generated?
    end

    def insert_option
      former_option = ChatBotOption.find_by(id: former_child_id)
      update_column(:position, former_option.position)
      former_option.update(parent: self, position: 1, source: node_id)
    end

    def purge_attachment_file
      return if option_params.blank?

      file.purge if option_params['file_deleted'] == 'true' && !option_params['file']
    end
end
# rubocop:enable Metrics/ClassLength
