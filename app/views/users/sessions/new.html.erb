<% content_for :meta_title, t('seo.titles.login') %>
<% content_for :meta_description, t('seo.meta_descriptions.login') %>

<div class="container-fluid">
  <div class="row">
    <div class="col-xs-12">
      <div class="center">
        <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
          <p class="login_error_message"><%= flash[:alert] %></p>
          <%= render "users/shared/error_messages", resource: resource %>
          <% if flash[:message] %>
            <p class="flash-message-login"><%= flash[:message] %></p>
          <% end %>
          <h1 class="sign mt-100"><%= t('seo.h1_tags.login') %></h1>
          <div class="group">
            <%= f.email_field :email, autofocus: true, autocomplete: "email", placeholder: '', class: 'input-standarization' %>
            <%= f.label :email, 'Correo' %>
            <span class="bar"></span>
          </div>

          <div class="group">
            <%= f.password_field :password, autocomplete: "current-password", placeholder: '', class: 'input-standarization' %>
            <%= f.label :password, 'Contraseña' %>
            <span class="bar"></span>
            <% if @shop_domain || @shop_id %>
              <%= f.hidden_field :shop_domain, value: @shop_domain %>
              <%= f.hidden_field :shop_id, value: @shop_id %>
            <% end %>
          </div>

          <% if devise_mapping.rememberable? %>
            <div class="group">
              <%= f.check_box :remember_me, class: 'f-left', style: 'width: auto;' %>
              <%= f.label :remember_me, 'Recordarme', class: 'p-static' %>
            </div>
          <% end %>

          <div class="btn-box">
            <%= f.submit 'Entrar', class: 'btn-btn btn-submit w-100' %>
          </div>
        <% end %>

        <%= render "users/shared/links" %>
      </div>
    </div>
  </div>
</div>
