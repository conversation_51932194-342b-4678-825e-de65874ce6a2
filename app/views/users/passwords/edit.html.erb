<% content_for :meta_title, t('seo.titles.password_edit') %>
<% content_for :meta_description, t('seo.meta_descriptions.password_edit') %>

<%= stylesheet_link_tag "application" %>
<div class="container-fluid">
  <div class="row">
    <div class="col-xs-12">
      <div class="center">
        <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put }) do |f| %>
          <%= render "users/shared/error_messages", resource: resource %>
          <%= f.hidden_field :reset_password_token %>

          <% if params[:from_partners] == 'true' %>
            <%= f.hidden_field :from_partners, value: true %>
          <% end %>

          <h1 class="sign mt-100">
            <% if params[:from_partners] == 'true' %>
              <%= t('seo.h1_tags.password_edit') %>
            <% else %>
              <%= t('seo.h1_tags.password_edit') %>
            <% end %>
          </h1>

          <div class="group">
            <%= f.password_field :password, autofocus: true, autocomplete: "new-password", placeholder: '', class: 'input-standarization' %>
            <%= f.label :password, "Nueva Contraseña" %>
            <span class="bar"></span>
          </div>

          <div class="group">
            <%= f.password_field :password_confirmation, autocomplete: "new-password", placeholder: '', class: 'input-standarization' %>
            <%= f.label :password_confirmation, "Confirmar Contraseña" %>
            <span class="bar"></span>
          </div>

          <div class="btn-box">
            <%= f.submit params[:from_partners] == 'true' ? 'Establecer contraseña' : 'Actualizar contraseña', class: 'btn-btn btn-submit w-100' %>
          </div>
        <% end %>

        <%= render "users/shared/links" %>
      </div>
    </div>
  </div>
</div>
