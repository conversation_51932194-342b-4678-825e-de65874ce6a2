<% content_for :meta_title, t('seo.titles.password_reset') %>
<% content_for :meta_description, t('seo.meta_descriptions.password_reset') %>

<div class="container-fluid">
  <div class="row">
    <div class="col-xs-12">
      <div class="center">
        <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post }) do |f| %>
          <%= render "users/shared/error_messages", resource: resource %>
          <h1 class="sign mt-100"><%= t('seo.h1_tags.password_reset') %></h1>

          <div class="group">
            <%= f.email_field :email, autofocus: true, autocomplete: "email", placeholder: '', class: 'input-standarization' %>
            <%= f.label :email %>
            <span class="bar"></span>
          </div>

          <div class="btn-box">
            <%= f.submit 'Enviar instrucciones de recuperación', class: 'btn-btn btn-submit w-100' %>
          </div>
        <% end %>

        <%= render "users/shared/links" %>
      </div>
    </div>
  </div>
</div>
