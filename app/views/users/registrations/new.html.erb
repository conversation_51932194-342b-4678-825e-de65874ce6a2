<% content_for :meta_title, t('seo.titles.register') %>
<% content_for :meta_description, t('seo.meta_descriptions.register') %>
<div class="container-fluid p-0">
  <div class="row m-0">
    <div class="col-md-4 p-0 register-background-left">
      <%= render partial: 'registration_left_bar' %>
    </div>
    <div class="col-md-8 col-xs-12 p-0 register-container">
      <%= render partial: 'registration_back_link' %>
      <div class="center-div">
        <div class="greetings-container">
          <h1 class="greetings">
            <%= I18n.t('register.user.title') %>
          </h1>
          <span class="fs-30 ml-15">👋</span>
        </div>
        <div class="auxiliar-text mb-20 mt-10">
          <span><%= I18n.t('register.user.caption') %></span>
        </div>
        <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { class: 'validate-form', id: "new_user" }) do |f| %>

          <%= render "users/shared/error_messages", resource: resource %>

          <div class="input-mercately-container mb-20">
            <%= f.label :email, I18n.t('register.user.email'), class: 'label-mercately' %><small class="ml-10 error-msg"></small>
            <%= f.text_field :email, autofocus: true, class: 'input-mercately validate-required-new validate-email-new', id: 'email'  %>

            <% if @shop_domain || @shop_id %>
              <%= f.hidden_field :shop_domain, value: @shop_domain %>
              <%= f.hidden_field :shop_id, value: @shop_id %>
            <% end %>
          </div>

          <%= f.fields_for :retailers, f.object.current_retailer || f.object.build_retailer do |retailer| %>
            <div class="phone-container mb-20 d-flex">
              <%= retailer.hidden_field :validate_name, value: false %>
              <% if @partner.present? %>
                <%= retailer.hidden_field :partner_id, value: @partner.id %>
              <% end %>
              <div class="input-mercately-container country">
                <%= retailer.label :country_code, I18n.t('register.user.country_code'), class: 'label-mercately' %>
                <%= retailer.country_select("country_code", { format: :with_flag_code, selected: 'EC' }, { class: 'input-mercately', id:'country_code' }) %>
              </div>
              <div class="input-mercately-container phone">
                <%= retailer.label :retailer_number, I18n.t('register.user.retailer_number'), class: 'label-mercately' %><small class="ml-10 error-msg"></small>
                <%= retailer.text_field :retailer_number, class: 'input-mercately validate-required-new', id: 'retailer_number', maxlength: '12', onkeypress: 'onlyNumber(event)', onkeyup: 'validateMinLength(this, 8)' %>
              </div>
            </div>

            <div class="mb-20 d-flex justify-content-between">
              <div class="input-mercately-container" style="width:49%">
                <%= f.label :first_name, I18n.t('register.user.first_name'), class: 'label-mercately' %><small class="ml-10 error-msg"></small>
                <%= f.text_field :first_name, autofocus: true, class: 'input-mercately validate-required-new', onkeyup: 'validateMaxLength(this, 20)',  id: 'first_name'  %>
              </div>

              <div class="input-mercately-container" style="width:49%">
                <%= f.label :last_name, I18n.t('register.user.last_name'), class: 'label-mercately' %><small class="ml-10 error-msg"></small>
                <%= f.text_field :last_name, class: 'input-mercately validate-required-new', onkeyup: 'validateMaxLength(this, 20)',  id: 'last_name' %>
              </div>
            </div>

            <%= retailer.fields_for :retailer_onboarding, retailer.object.retailer_onboarding || retailer.object.build_retailer_onboarding do |retailer_onboarding| %>
              <%= retailer_onboarding.hidden_field :utm_source, value: params[:utm_source] %>
              <%= retailer_onboarding.hidden_field :utm_medium, value: params[:utm_medium] %>
              <%= retailer_onboarding.hidden_field :utm_campaign, value: params[:utm_campaign] %>
              <%= retailer_onboarding.hidden_field :utm_content, value: params[:utm_content] %>
              <%= retailer_onboarding.hidden_field :utm_term, value: params[:utm_term] %>
              <%= retailer_onboarding.hidden_field :utm_campaign_id, value: params[:utm_campaign_id] %>
              <%= retailer_onboarding.hidden_field :attributer_channel, value: "[channel]" %>
              <%= retailer_onboarding.hidden_field :attributer_channeldrilldown1, value: "[channeldrilldown1]" %>
              <%= retailer_onboarding.hidden_field :attributer_channeldrilldown2, value: "[channeldrilldown2]" %>
              <%= retailer_onboarding.hidden_field :attributer_channeldrilldown3, value: "[channeldrilldown3]" %>
              <%= retailer_onboarding.hidden_field :attributer_channeldrilldown4, value: "[channeldrilldown4]" %>
              <%= retailer_onboarding.hidden_field :attributer_landingpage, value: "[landingpage]" %>
              <%= retailer_onboarding.hidden_field :attributer_landingpagegroup, value: "[landingpagegroup]" %>
              <%= retailer_onboarding.hidden_field :ab_type, value: @last_ab_type %>
            <% end %>
          <% end %>

          <div class="input-mercately-container mb-20">
            <%= f.label :password, I18n.t('register.user.password'), class: 'label-mercately' %><small class="ml-10 error-msg"></small>
            <div class="pass-container">
              <%= f.password_field :password, class: 'password-input validate-required-new', id: 'password-input' %>
              <span class="far fa-eye-slash eye-icon" id="eye-icon"></span>
            </div>
          </div>

          <div class="group mb-30">
            <%= f.check_box :agree_terms, { checked: true, style: 'display:none;' }, '1', '0' %>
            <span class="link-text"><%= I18n.t('register.user.accepts') %> <%= link_to I18n.t('register.user.terms'), 'https://www.mercately.com/terms-and-conditions', target: :_blank %></span>
          </div>

          <div class="btn-box">
            <%= f.button I18n.t('register.button_create'), type: 'submit', class: 'btn-submit w-100 fs-14', data: { disable_with: false } %>
          </div>
        <% end %>

        <% if controller_name != 'sessions' %>
          <p class="mt-15 link-text">
            <%= I18n.t('register.user.account') %>
            <%= link_to I18n.t('register.user.login'),
                        new_session_path(resource_name, shop_domain: @shop_domain, shop_id: @shop_id) %>
          </p><br />
        <% end %>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const params = new URLSearchParams(window.location.search);
    const utms = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];

    utms.forEach(key => {
      const urlValue = params.get(key);
      const storedValue = localStorage.getItem(key);

      if (urlValue && !storedValue) {
        localStorage.setItem(key, urlValue);
      }
    });
  });
</script>

<script>
  const allowedHosts = ['www.mercately.com', 'mercately.com', 'app.mercately.com'];
  if (allowedHosts.includes(window.location.hostname)) {
    console.log("Enviando Datos...")

    function getCookieValue(name) {
      const cookies = decodeURIComponent(document.cookie).split(';');
      for (let c of cookies) {
        c = c.trim();
        if (c.startsWith(name + "=")) {
          return c.substring(name.length + 1);
        }
      }
      return null;
    }

    function getUTMs() {
      const params = new URLSearchParams(window.location.search);
      const utms = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];
      let data = {};

      utms.forEach(key => {
        const urlValue = params.get(key);
        const storedValue = localStorage.getItem(key);

        if (urlValue) {
          // si hay UTM en la URL, guarda y usa ese
          data[key] = urlValue;
          localStorage.setItem(key, urlValue);
        } else if (storedValue) {
          // si no está en la URL pero lo teníamos guardado
          data[key] = storedValue;
        }
      });

      return data;
    }

    async function sendToHubSpot() {
      const portalId = '46081215';
      const formId = '2096af56-8fc7-4c09-9a1c-40990efdce63';
      const hutk = getCookieValue('hubspotutk');
      const utmParams = getUTMs();

      const countryPhoneCodes = { 
        AF: '+93', AL: '+355', DZ: '+213', AO: '+244', AR: '+54', AM: '+374', AU: '+61', 
        AT: '+43', AZ: '+994', BH: '+973', BD: '+880', BY: '+375', BE: '+32', BO: '+591', 
        BA: '+387', BW: '+267', BR: '+55', BN: '+673', BG: '+359', KH: '+855', CM: '+237', 
        CA: '+1', CL: '+56', CN: '+86', CO: '+57', CR: '+506', HR: '+385', CU: '+53', 
        CY: '+357', CZ: '+420', DK: '+45', DO: '+1', EC: '+593', EG: '+20', SV: '+503', 
        EE: '+372', ET: '+251', FI: '+358', FR: '+33', GE: '+995', DE: '+49', GH: '+233', 
        GR: '+30', GT: '+502', HN: '+504', HK: '+852', HU: '+36', IS: '+354', IN: '+91', 
        ID: '+62', IR: '+98', IQ: '+964', IE: '+353', IL: '+972', IT: '+39', JM: '+1', 
        JP: '+81', JO: '+962', KZ: '+7', KE: '+254', KR: '+82', KW: '+965', KG: '+996', 
        LA: '+856', LV: '+371', LB: '+961', LY: '+218', LT: '+370', LU: '+352', MO: '+853', 
        MK: '+389', MG: '+261', MW: '+265', MY: '+60', MV: '+960', ML: '+223', MT: '+356', 
        MR: '+222', MU: '+230', MX: '+52', MD: '+373', MN: '+976', ME: '+382', MA: '+212', 
        MZ: '+258', MM: '+95', NA: '+264', NP: '+977', NL: '+31', NZ: '+64', NI: '+505', 
        NE: '+227', NG: '+234', NO: '+47', OM: '+968', PK: '+92', PA: '+507', PY: '+595', 
        PE: '+51', PH: '+63', PL: '+48', PT: '+351', QA: '+974', RO: '+40', RU: '+7', 
        RW: '+250', SA: '+966', SN: '+221', RS: '+381', SG: '+65', SK: '+421', SI: '+386', 
        ZA: '+27', ES: '+34', LK: '+94', SD: '+249', SE: '+46', CH: '+41', SY: '+963', 
        TW: '+886', TJ: '+992', TZ: '+255', TH: '+66', TN: '+216', TR: '+90', TM: '+993', 
        UG: '+256', UA: '+380', AE: '+971', GB: '+44', US: '+1', UY: '+598', UZ: '+998', 
        VE: '+58', VN: '+84', YE: '+967', ZM: '+260', ZW: '+263' 
      };

      const countryISO = document.getElementById('country_code')?.value;
      const countryCode = countryPhoneCodes[countryISO] || '';

      const email = document.getElementById('email')?.value?.trim();
      const firstname = document.getElementById('first_name')?.value?.trim();
      const lastname = document.getElementById('last_name')?.value?.trim();
      const phoneNumber = document.getElementById('retailer_number')?.value?.trim() || '';
      const fullPhone = `${countryCode}${phoneNumber}`;

      const fields = [
        { name: 'email', value: email },
        { name: 'firstname', value: firstname },
        { name: 'lastname', value: lastname },
        { name: 'phone', value: fullPhone },
        ...Object.entries(utmParams).map(([key, value]) => ({
          name: key,
          value: value
        }))
      ].filter(f => f.value); // solo campos con valor

      const payload = {
        fields: fields,
        context: {
          hutk: hutk,
          pageUri: window.location.href,
          pageName: document.title,
          referrer: document.referrer
        }
      };

      try {
        console.log("Payload enviado a HubSpot:", JSON.stringify(payload, null, 2));
        const response = await fetch(`https://api.hsforms.com/submissions/v3/integration/submit/${portalId}/${formId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(payload)
        });
        const result = await response.json();
      } catch (err) {
        console.error('❌ Error al enviar a HubSpot:', err);
      }
    }

    document.addEventListener('DOMContentLoaded', function () {
      const form = document.querySelector('.validate-form');
      if (form) {

        form.addEventListener('submit', function (e) {
          const errors = form.querySelectorAll('.error-msg');
          let hasVisibleErrors = false;
        
          errors.forEach(error => {
            if (error.textContent && error.textContent.trim() !== '') {
              hasVisibleErrors = true;
            }
          });
        
          if (hasVisibleErrors) {
            console.log("❌ Formulario tiene errores visibles. No se envía a HubSpot.");
            return;
          }
          sendToHubSpot();
        });
      }
    });
  }
</script>
