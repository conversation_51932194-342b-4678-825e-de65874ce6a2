<!DOCTYPE html>
<html lang="es-EC" translate="no">
  <head>
    <title><%= content_for?(:meta_title) ? yield(:meta_title) : "Mercately" %></title>
    <link rel="canonical" href="<%= canonical_url %>" />
    <meta name="title" content="<%= content_for?(:meta_title) ? yield(:meta_title) : "Mercately" %>">
    <meta name="description" content="<%= content_for?(:meta_description) ? yield(:meta_description) : "Mercately" %>">

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag 'application', media: 'all', 'data-turbolinks-track': 'reload' %>
    <%= javascript_include_tag 'application', 'data-turbolinks-track': 'reload' %>
    
    <style>
      .center { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; }
      .sign { font-size: 22px; font-family: 'Poppins-Bold'; }
      .btn-submit { background-color: #00B4FF; color: #f6f6f6; border-radius: 3px; border: none; padding: 10px 20px; min-height: 48px; }
      .group { position: relative; margin: 45px 0; }
      .group input { background: none; color: #000; font-size: 18px; padding: 10px 0px; display: block; width: 100%; border: none; border-radius: 0; border-bottom: 1px solid #cac8c8; min-height: 48px; }
      #mercately-logo { max-height: 60px; }
    </style>
    
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  </head>

  <body>
    <div class="container-fluid">
      <div class="row middle-xs">
        <div class="col-xs-12">
          <%= link_to root_path do %>
            <%= image_tag 'logo-mercately-h.png', id: 'mercately-logo', alt: t('seo.alt_text.mercately_logo') %>
          <% end %>
        </div>
      </div>
    </div>
    <%= yield %>
  </body>
</html>
