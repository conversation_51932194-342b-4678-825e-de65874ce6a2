<!DOCTYPE html>
<html lang="es-EC" translate="no">
  <head>
    <title><%= content_for?(:meta_title) ? yield(:meta_title) : "Mercately" %></title>
    <link rel="canonical" href="<%= canonical_url %>" />
    <meta name="title" content="<%= content_for?(:meta_title) ? yield(:meta_title) : "Mercately" %>">
    <meta name="description" content="<%= content_for?(:meta_description) ? yield(:meta_description) : "Mercately" %>">

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag 'chats/chat', media: 'all', 'data-turbolinks-track': 'reload' %>
    <%= javascript_include_tag 'registration', 'data-turbolinks-track': 'reload' %>
    
    <!-- Critical CSS inline for above-the-fold content -->
    <style>
      .register-container { background-color: #FFFFFF; height: 100vh; overflow-y: auto; }
      .center-div { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; }
      .greetings { font-size: 22px; font-family: 'Poppins-Bold'; }
      .btn-submit { background-color: #3CAAE1; color: #FFFFFF; border-radius: 12px; border: none; padding: 10px 20px; min-height: 48px; }
      .register-background-left { background: #EBF7FC; height: 100vh; }
      @media screen and (max-width: 767px) { .register-background-left { display: none; } }
    </style>
    
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">

    <% if ENV['ENVIRONMENT'] == 'production' %>
      <!-- Start of HubSpot Embed Code -->
        <script type="text/javascript" id="hs-script-loader" async defer src="//js.hs-scripts.com/46081215.js"></script>
      <!-- End of HubSpot Embed Code -->
    <% end %>
    <%= render partial: 'layouts/gtm_head' %>
  </head>

  <body>
    <%= render partial: 'layouts/gtm_body' %>
    <%= yield %>
  </body>
</html>
