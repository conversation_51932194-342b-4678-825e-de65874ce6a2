<div class="content_width ml-sm-108 py-25 px-15 no-left-margin-xs fz-14">
  <div class="row">
    <div class="col-12">
      <% if flash[:alert] %>
        <div class="warning-box" role="alert">
          <strong>¡Ups!</strong> <%= flash[:alert] %>
        </div>
      <% end %>
      <h1 class="d-inline fz-40 font-poppins-bold c-grey-dark"><%= t('hello', name: current_retailer_user.first_name) %></h1>
      <h5 class="fz-18-bold c-grey-dark mt-8">Estos son tus primeros pasos en Mercately</h5>
    </div>
  </div>

  <div class="row mb-24">
    <% if show_demo? %>
      <div class="col-12 col-md-6">
        <div class="card border-16 m-0 mh-auto b-transparent shadow-none mt-24">
          <div class="card-body">
            <div class="row">
              <div class="col-9">
                <h5 class="fz-18-bold c-grey-dark"><%= t('views.dashboard.demo.title') %></h5>
                <div class="fz-14 c-grey-light mt-16"><%= t('views.dashboard.demo.description') %></div>
              </div>
              <div class="col-3 p-0">
                <%= image_tag 'dashboard/dashboard_calendar.png', class: 'w-120 h-120' %>
              </div>
            </div>
            <a href="https://api.whatsapp.com/send?l=es&phone=12393231403&text=Hola%20me%20gustar%C3%ADa%20agendar%20una%20demo%20con%20un%20experto" target="_blank" class="btn btn-light mt-16 ml-0 c-grey-dark">
              <%= t('views.dashboard.begin') %> >
            </a>
            <%= link_to 'Ahora no', skip_onboarding_demo_path, method: :put, class: 'btn btn-light mt-16 ml-12 c-grey-dark' %>
          </div>
        </div>
      </div>
    <% end %>
    <% if connect_whatsapp? && !current_retailer.whatsapp_integrated? %>
      <div class="col-12 col-md-6">
        <div class="card border-16 m-0 mh-auto b-transparent shadow-none mt-24">
          <div class="card-body">
            <div class="row">
              <div class="col-9">
                <h5 class="fz-18-bold c-grey-dark"><%= t('views.dashboard.whatsapp.title') %></h5>
                <div class="fz-14 c-grey-light mt-16"><%= t('views.dashboard.whatsapp.description') %></div>
              </div>
              <div class="col-3 p-0">
                <%= image_tag 'onboarding/ws-qr/wpp_connect.png', class: 'w-121 h-118' %>
              </div>
            </div>
            <%= link_to "#{t('views.dashboard.begin')} >", "/retailers/#{current_retailer.slug}/integrations/whatsapp_business", class: 'btn btn-light mt-16 ml-0 c-grey-dark' %>
          </div>
        </div>
      </div>
    <% end %>
    <% if show_plan? %>
      <div class="col-12 col-md-6">
        <div class="card border-16 m-0 mh-auto b-transparent shadow-none mt-24">
          <div class="card-body">
            <div class="row">
              <div class="col-9">
                <h5 class="fz-18-bold c-grey-dark"><%= t('views.dashboard.plan.title') %></h5>
                <div class="fz-14 c-grey-light mt-16"><%= t('views.dashboard.plan.description') %></div>
              </div>
              <div class="col-3 p-0">
                <%= image_tag 'onboarding/ws-qr/plan_icon.png', class: 'w-121 h-118' %>
              </div>
            </div>
            <%= link_to "#{t('views.dashboard.begin')} >", "/retailers/#{current_retailer.slug}/plans", class: 'btn btn-light mt-16 ml-0 c-grey-dark' %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <h5 class="fz-18-bold c-grey-dark my-40"><%= t('views.dashboard.greetings') %></h5>

  <div class="row">
    <div class="col-12 mb-32">
      <b class="fz-16"><%= t('views.dashboard.quick_check') %></b>
      <br />
      <%= @start_date_format %> - <%= @end_date_format %>
    </div>
  </div>
  <div class="row">
    <div class="col-12 col-md-4">
      <div class="card border-16 m-0 mh-auto b-transparent shadow-none" style="background-color: #EBF7FC;">
        <div class="card-body">
          <div class="text-center">
            <h1 class="funnels__title c-grey-dark d-block fz-40"><%= @shops_data['orders_count'] || 0 %></h1>
            <div class="c-grey-light fz-16"><%= t('views.dashboard.successful_sales') %></div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-md-4">
      <div class="card border-16 m-0 mh-auto b-transparent shadow-none" style="background-color: #F8EBF8;">
        <div class="card-body">
          <div class="text-center">
            <h1 class="funnels__title c-grey-dark d-block fz-40"><%= @shops_data['customers_count'] || 0 %></h1>
            <div class="c-grey-light fz-16"><%= t('views.dashboard.customers_registered') %></div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-md-4">
      <div class="card border-16 m-0 mh-auto b-transparent shadow-none" style="background-color: #FDF5EC;">
        <div class="card-body">
          <div class="text-center">
            <h1 class="funnels__title c-grey-dark d-block fz-40"><%= @wa_msgs_count %></h1>
            <div class="c-grey-light fz-16"><%= t('views.dashboard.messages') %></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-12 col-md-9">
      <div class="card border-16 mx-0 mh-auto b-transparent shadow-none">
        <div class="card-body">
          <div class="row">
            <div class="col-6">
              <b class="fz-18"><%= t('views.dashboard.recent_orders') %></b>
            </div>
            <div class="col-6 text-right">
              <%= link_to t('views.dashboard.view_all'), retailers_orders_path(current_retailer, status: 'all') %>
            </div>
          </div>
          <% if @first_five_orders.present? %>
            <table class="w-100">
              <thead>
                <tr class="c-grey-light border-bottom">
                  <th class="py-16"><%= t('views.dashboard.orders.info') %></th>
                  <th class="py-16"><%= t('views.dashboard.orders.status') %></th>
                  <th class="py-16"><%= t('views.dashboard.orders.customer') %></th>
                  <th class="py-16"><%= t('views.dashboard.orders.total') %></th>
                </tr>
              </thead>
              <tbody>
                <%= render partial: 'retailers/pages/order', collection: @first_five_orders, as: :order %>
              </tbody>
            </table>
          <% else %>
            <div class="text-center"><%= t('views.dashboard.no_orders') %></div>
          <% end %>
        </div>
      </div>
    </div>
    <div class="col-12 col-md-3">
      <div class="card border-16 mx-0 mh-auto b-transparent shadow-none" style="height: 420px;">
        <div class="card-body">
          <b class="fz-16"><%= t('views.dashboard.integrate_with') %></b>
          <div class="row align-items-center py-8 my-26">
            <div class="col-3">
              <%= image_tag 'new_design/wa.png' %>
            </div>
            <div class="col-9 truncate text-left">
              <b>WhatsApp</b>
              <br />
              <span class="subtitle">
                <% if current_retailer.whatsapp_integrated? %>
                  <%= image_tag 'new_design/green_dot.png' %>
                  <%= t('views.dashboard.connected') %>
                <% else %>
                  <%= link_to t('views.dashboard.configure'), retailers_integrations_path(current_retailer) %>
                <% end %>
              </span>
            </div>
          </div>
          <div class="row align-items-center py-8 my-26">
            <div class="col-3">
              <%= image_tag 'new_design/msm.png' %>
            </div>
            <div class="col-9 truncate text-left">
              <b>Messenger</b>
              <br />
              <span class="subtitle">
                <% if current_retailer.facebook_integrated? %>
                  <%= image_tag 'new_design/green_dot.png' %>
                  <%= t('views.dashboard.connected') %>
                <% else %>
                  <%= link_to t('views.dashboard.configure'), retailers_integrations_path(current_retailer) %>
                <% end %>
              </span>
            </div>
          </div>
          <div class="row align-items-center py-8 my-26">
            <div class="col-3">
              <%= image_tag 'new_design/ig.png' %>
            </div>
            <div class="col-9 truncate text-left">
              <b>Instagram</b>
              <br />
              <span class="subtitle">
                <% if current_retailer.instagram_integrated? %>
                  <%= image_tag 'new_design/green_dot.png' %>
                  <%= t('views.dashboard.connected') %>
                <% else %>
                  <%= link_to t('views.dashboard.configure'), retailers_integrations_path(current_retailer) %>
                <% end %>
              </span>
            </div>
          </div>
          <% if current_retailer.created_at && current_retailer.created_at < Date.new(2023, 12, 11) %>
            <div class="row align-items-center py-8 mt-26">
              <div class="col-3">
                <%= image_tag 'new_design/ml.png' %>
              </div>
              <div class="col-9 truncate text-left">
                <b>MercadoLibre</b>
                <br />
                <span class="subtitle">
                  <% if current_retailer.ml_integrated? %>
                    <%= image_tag 'new_design/green_dot.png' %>
                    <%= t('views.dashboard.connected') %>
                  <% else %>
                    <%= link_to t('views.dashboard.configure'), retailers_integrations_path(current_retailer) %>
                  <% end %>
                </span>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
      window.dataLayer = window.dataLayer || [];
      if (document.referrer.includes("/login")) {
          dataLayer.push({
            'event': 'LoginCount',
            'email': document.getElementById('user_email').value,
            'nombre': document.getElementById('user_first_name').value,
            'apellido': document.getElementById('user_last_name').value,
            'telefono': document.getElementById('user_retailer_phone').value,
            'empresa': document.getElementById('user_retailer_name').value,
        });
      }
    });
</script>
