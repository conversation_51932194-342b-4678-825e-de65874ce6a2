<div class="container-payment-plan container-my-account p-0">
  <div class="row m-0">
    <%= render partial: 'retailers/my_business/shared/sidemenu' %>
  <div class="col-12 col-md mx-auto p-0 store-content">
    <%= form_for(current_retailer, url: update_general_information_path(current_retailer.slug), method: :put, html: { id: 'update_general_info', class: 'validate-form' }) do |f| %>
        <div class="row fieldset mx-0 container-payment-plan_card">
          <div class="col-lg-12 col-md-12">
            <fieldset>
              <div class="business-background mt-12 col-md-12 col-12 figma-field px-0 m-0" style="background: white">
                <% if f.object.background.attached? %>
                  <div class="text-center">
                    <%= image_tag("#{f.object.background.public_url}", id: 'background', class: "rounded img-fluid w-100", style: { height: 230, crop: "pad" }) %>
                  </div>
                <% else %>
                  <div class="text-center">
                    <img class="" id="background" />
                  </div>
                <% end %>
                <div class="container-logo col-xs-3">
                  <% if f.object.avatar.attached? %>
                    <%= image_tag("#{f.object.avatar.public_url}", id:'logo', class: "rounded-circle logo", style: { width: 96, height: 96, crop: "pad" }) %>
                  <% else %>
                    <img class="" id="logo" />
                  <% end %>
                  <div class="col-md-12">
                    <%= f.file_field :avatar, class: 'uploadedImages d-none input', onchange: "previewImage(this,'#logo', 96);", accept: 'image/jpg, image/png' %>
                    <label class='btn rounded-circle mr-0 mb-0 pr-0 pb-0 upload-logo' for="retailer_avatar">
                      <%=  image_tag "dashboard/my-business/btn-camera.png", class: "mr-0 mb-0 pr-0 pb-0", title: 'logo', height: 40 %>
                    </label>
                  </div>
                </div>

                <div class="upload-background">
                  <%= f.file_field :background, class: 'uploadedImages d-none input', onchange: "previewImage(this,'#background');", accept: 'image/jpg, image/png' %>
                  <label class='btn rounded-circle mx-0 my-0 p-0' for="retailer_background">
                    <%=  image_tag "dashboard/my-business/btn-camera.png", class: "mr-0 mb-0 pr-0 pb-0", title: 'background', height: 40 %>
                  </label>
                </div>
              </div>
              <div class="mb-24">
                <span class="subtitle">
                  * <%= t('retailer.profile.my_business.edit.general_information.warning') %>
                  <br />
                  * <%= t('retailer.profile.my_business.edit.general_information.logo_recomendations') %>
                  <br />
                  * <%= t('retailer.profile.my_business.edit.general_information.bg_recomendations') %>
                </span>
              </div>
              <p class="title-card">
                <%= t('retailer.profile.my_business.edit.general_information.menu') %>
              </p>
              <div class="figma-field">
                <%= f.label :name, t('retailer.profile.my_business.edit.general_information.name') %>
                <%= f.text_field :name, autofocus: true, class: 'input' %>
                <%= tag.i f.object.errors[:name].join(","), class: "validation-msg"  %>
              </div>

              <div class="row col-md-12 mx-0 px-0" style="background: white">
                <div class="col-md-4 pr-8 pl-0">
                  <div class="figma-field">
                    <div class="custom-control custom-switch figma-switch">
                      <%= f.check_box :active_shop, class: 'custom-control-input', onchange: 'toggleBox(this)' %>
                      <%= f.label :active_shop, t('retailer.profile.my_business.edit.general_information.active_shop'), id: 'togglebox--text', class: 'custom-control-label pt-5' %>
                    </div>
                  </div>
                </div>

                <div class="col-md-4 pr-8 pl-0">
                  <div class="figma-field">
                    <div class="custom-control custom-switch figma-switch">
                      <%= f.check_box :hide_product_prices, class: 'custom-control-input' %>
                      <%= f.label :hide_product_prices, t('retailer.profile.my_business.edit.general_information.hide_product_prices'), id: 'togglehideproductimages--text', class: 'custom-control-label pt-5' %>
                    </div>
                  </div>
                </div>

                <div class="col-md-4 pr-8 pl-0">
                  <div class="figma-field">
                    <div class="custom-control custom-switch figma-switch">
                      <%= f.check_box :rotate_images_in_catalog, class: 'custom-control-input' %>
                      <%= f.label :rotate_images_in_catalog, t('retailer.profile.my_business.edit.general_information.rotate_images_in_catalog'), id: 'togglerotateimages--text', class: 'custom-control-label pt-5' %>
                    </div>
                  </div>
                </div>
              </div>

              <input type="checkbox" id="show-catalog" class="check-toggler d-none" />
              <div class="show-on-check figma-field">
                <%= f.label :catalog_slug, t('retailer.profile.my_business.edit.general_information.slug') %>
                <%= f.text_field :catalog_slug, class: 'input' %>
                <%= tag.i f.object.errors[:catalog_slug].join(","), class: "validation-msg"  %>
                <span class="subtitle">
                  <%= t('retailer.profile.my_business.edit.general_information.slug_description') %>
                  <%= link_to "https://#{f.object.catalog_slug}.mercately.shop", target: '_blank' do %>
                    <strong><%= f.object.catalog_slug %></strong>.mercately.shop
                  <% end %>
                </span>
              </div>
              <div class="figma-field p-relative">
                <%= f.label :description, t('retailer.profile.my_business.edit.general_information.description') %><small class="ml-10 error-msg"></small>
                <%= f.text_area :description, class: 'input height-textarea validate-max-length-120 count-chars mb-6' %>
                <div class="row">
                  <div class="col-6">
                  </div>
                  <div class="col-6 text-right">
                    <span class="char-count">0</span>/120
                  </div>
                </div>
              </div>
              <div class="row col-md-12 mx-0 px-0 figma-field" style="background: white">
                <div class="col-md-6 pr-8 pl-0">
                  <div class="figma-field" >
                    <%= f.label :country_code, t('retailer.profile.my_business.edit.general_information.country_code') %>
                    <%= f.country_select(:country_code, {}, { class: 'input country-select-box' }) %>
                    <%= tag.i f.object.errors[:country_code].join(","), class: "validation-msg"  %>
                  </div>
                </div>
                <div class="col-md-6 pr-8 pl-0">
                  <div class="figma-field" >
                    <%= f.label :timezone, t('retailer.profile.my_business.edit.general_information.timezone') %>
                    <%= f.select :timezone, timezones_list, { include_blank: true }, class: 'input country-select-box' %>
                    <%= tag.i f.object.errors[:timezone].join(","), class: "validation-msg"  %>
                  </div>
                </div>
              </div>
              <div class="row col-md-12 mx-0 px-0 figma-field" style="background: white">
                <div class="col-md-6 pl-8 pr-0">
                  <div class="figma-field">
                    <%= f.label :tax_amount, t('retailer.profile.my_business.edit.general_information.tax') %>
                    <%= f.text_field :tax_amount, class: 'input', onkeypress: 'onlyNumber(event)' %>
                    <%= tag.i f.object.errors[:tax_amount].join(","), class: "validation-msg"  %>
                  </div>
                </div>
                <div class="col-md-6 pl-8 pr-0">
                  <div class="figma-field">
                    <%= f.label :shop_main_color, t('retailer.profile.my_business.edit.general_information.shop_main_color') %>
                    <%= f.color_field :shop_main_color, class: 'input' %>
                    <%= tag.i f.object.errors[:shop_main_color].join(","), class: "validation-msg"  %>
                  </div>
                </div>
              </div>
              <div class="row col-md-12 mx-0 px-0 figma-field" style="background: white">
                <div class="col-md-12 pl-8 pr-0">
                  <%= f.label :terms_conditions_content, t('retailer.profile.my_business.edit.general_information.terms_and_conditions'), style: 'font-size: 12px; color: #3CAAE1;' %><small class="ml-10 error-msg"></small>
                  <%= f.hidden_field :terms_conditions_content, class: 'ckeditor mb-6', id: 'terms_conditions_content' %>
                  <div id="terms-conditions-editor"><%= raw current_retailer.terms_conditions_content %></div>
                </div>
              </div>
              <hr class=" col-md-12 my-20 px-0 mt-20" />
              <div class="btn-box t-right col-md-12 pr-0 mb-20">
                <%= f.submit t('retailer.profile.my_business.edit.submit'), class: 'btn btn-improve fs-14', id: 'submit-my-business', data: { disable_with: false } %>
              </div>
            </fieldset>
          </div>
        </div>
    <% end  %>
  </div>
</div>
</div>
<%= javascript_tag do %>
  changed = false;

  $('.input').on('change input select', () => {
    changed = true;
  });

  $('#submit-my-business').on('click', () => {
    changed = false;
  })

  window.onbeforeunload = s => changed ? "Tienes cambios sin guardar" : null

  function previewImage(fileInput, showIn, size = undefined) {
    if ( fileInput.files && fileInput.files[0] ) {
      var reader = new FileReader();
      const className = showIn == '#logo' ? "rounded-circle logo" : "rounded img-fluid w-100";
      reader.onload = (e) => {
        $(showIn)
            .attr('src', e.target.result)
            .width(size || 850)
            .height(size || 192)
            .addClass(className);
      };
    }

    reader.readAsDataURL(fileInput.files[0]);
  }
<% end %>
<script>
  $(function() {
    document.getElementById('show-catalog').checked = <%= current_retailer.active_shop %>;
  });
  function toggleBox(el) {
    document.getElementById("show-catalog").checked = el.checked;
    let text;
    if (el.checked) {
      text = '<%= t('retailer.profile.my_business.edit.general_information.active_shop') %>';
    } else {
      text = '<%= t('retailer.profile.my_business.edit.general_information.deactive_shop') %>';
    }
    document.getElementById('togglebox--text').innerText = text;
  }
</script>
<script>
  $(function() {
    $('.char-count').text($('.count-chars').val().length);
    $('.count-chars').on('input', function(e) {
      const el = e.target;
      $('.char-count').text(el.value.length);
    })
  })
</script>
<style>
  .select2-container {
    width: 20% !important;
  }
</style>
<script>
  document.addEventListener("DOMContentLoaded", function() {
    const toolbarOptions = [
      [{ 'header': [1, 2, false] }],
      ['bold', 'italic', 'underline'],
      [{ 'list': 'bullet' }, { 'list': 'ordered' }]
    ];

    const quill = new Quill('#terms-conditions-editor', {
      theme: 'snow',
      modules: {
        toolbar: toolbarOptions
      }
    });

    const form = document.getElementById('update_general_info');
    form.onsubmit = function() {
      const contentInput = document.querySelector('#terms_conditions_content');
      contentInput.value = quill.root.innerHTML;
    };
  });
</script>
