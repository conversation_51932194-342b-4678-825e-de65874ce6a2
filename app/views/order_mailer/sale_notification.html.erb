<%= stylesheet_link_tag "mailers/sale_notification" %>
<div class="main-container">
  <div class="mercately-logo-container">
    <%= image_tag 'logo-mercately-h.png', class: "image-logo-reset" %>
  </div>

  <div class="logo-container">
    <% if @retailer.avatar.attached? %>
      <img src="<%= @retailer.avatar.is_aws ? @retailer.avatar.public_url : Cloudinary::Utils.cloudinary_url(@retailer.avatar.key) %>" class="logo" />
    <% end %>
  </div>

  <div class="order-info">
    <%= image_tag 'Checkmark-circle-2-fill.png', class: 'new-order-icon' %>
    <p class="new-order-title fw-bold">Nueva orden</p>
  </div>

  <div class="items-container">
    <p class="subtitle fw-bold">Resumen de pedido</p>
    <% @params['items'].each do |item| %>
      <div class="individual-item d-flex-center">
        <div class="item-image">
          <img src="<%= item['image_url'] %>" class="product-image" />
        </div>
        <div class="item-info color-content">
          <span><%= item['title'] %></span><br />
          <span>Cantidad: <%= item['quantity'] %></span>
        </div>
        <% unless @retailer.hide_product_prices %>
          <div class="item-total color-content">
            <span><%= @retailer.currency_symbol %> <%= item['price'] %></span>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>

  <% unless @retailer.hide_product_prices %>
    <div class="order-total-container">
      <div class="order-subtotal d-flex-center color-content pb-10">
        <div class="order-label-container">
          <span class="order-label">Subtotal</span>
        </div>
        <div class="order-amount-container">
          <span class="order-amount"><%= @retailer.currency_symbol %> <%= @params['subtotal'] %></span>
        </div>
      </div>
      <div class="order-tax d-flex-center color-content pb-10">
        <div class="order-label-container">
          <span class="order-label">Impuesto (<%= @retailer.tax_amount %>%)</span>
        </div>
        <div class="order-amount-container">
          <span class="order-amount"><%= @retailer.currency_symbol %> <%= @params['tax_amount'] %></span>
        </div>
      </div>
      <div class="order-shipping d-flex-center color-content pb-10">
        <div class="order-label-container">
          <span class="order-label">Envío</span>
        </div>
        <div class="order-amount-container">
          <span class="order-amount"><%= @retailer.currency_symbol %> <%= @params['shipping_value'] %></span>
        </div>
      </div>
      <div class="order-total d-flex-center pb-10">
        <div class="order-label-container">
          <span class="order-label fw-bold">Total</span>
        </div>
        <div class="order-amount-container">
          <span class="order-amount fw-bold"><%= @retailer.currency_symbol %> <%= @params['total'] %></span>
        </div>
      </div>
    </div>
  <% end %>

  <div class="customer-info-container">
    <p class="subtitle fw-bold">Información de contacto y dirección de envío</p>
    <p class="color-content"><%= @params['customer']['full_name'] %></p>
    <p class="color-content"><%= @params['customer']['email'] %></p>
    <p class="color-content"><%= @params['customer']['phone'] %></p>

    <% custom_answers = @params['custom_answers'].reject { |e| e.nil? || e.empty? } %>
    <% unless custom_answers.blank? %>
      <p class="sent-address fw-bold">Campos personalizados:</p>
      <% custom_answers.each do |answer| %>
        <p class="color-content"><b><%= answer['label'] %>:</b> <%= answer['answer'] %></p>
      <% end %>
    <% end %>

    <p class="sent-address fw-bold">Dirección de envío:</p>
    <% if @params['delivery_method'] == 'send_order' %>
      <p class="address color-content"><%= @params['address'] + ', ' + @params['city'] + ', ' + @params['state'] + ', ' + @params['zip_code'] %></p>
    <% elsif @params['delivery_method'] == 'store_pickup' %>
      <p class="color-content">Se recogerá en tienda</p>
    <% end %>
  </div>

  <% unless @params['payment_method'].blank? %>
    <div class="customer-info-container">
      <p class="subtitle fw-bold">Método de pago</p>
      <p class="color-content"><%= @params['payment_method'] %></p>
    </div>
  <% end %>

  <div class="button-container">
    <% if Rails.env.development? %>
      <%= link_to "http://localhost:3000/retailers/#{@retailer.slug}/orders/#{@params['web_id']}", class: 'view-sale-link', target: '_blank' do %>
        <button class="view-sale-button fw-bold">Ver pedido</button>
      <% end %>
    <% elsif Rails.env.production? %>
      <%= link_to "https://#{ENV["HOST_URL"]}/retailers/#{@retailer.slug}/orders/#{@params['web_id']}", class: 'view-sale-link', target: '_blank' do %>
        <button class="view-sale-button fw-bold">Ver pedido</button>
      <% end %>
    <% end %>
  </div>
</div>
