module Mia
  module Chatbot
    class WhatsappJob < ChatbotBaseJob
      queue_as :default

      private

        def message_key
          @message_key ||= :question
        end

        def message_value
          @message_value ||= @inbound_type == 'text' ? inbound_message : @media_params[:url]
        end

        def message_builder
          return media_builder if %w[image file audio].include?(@inbound_type)

          text_builder
        end

        def media_builder
          {
            type: @inbound_type,
            media_url: message_value,
            caption: media_params[:caption],
            file_name: media_params[:file_name]
          }.compact
        end

        def referenced_message
          @referenced_message ||= @customer.gupshup_whatsapp_messages
            .find_by(whatsapp_message_id: referenced_message_id)
        end

        def message_payload
          return @message_payload if defined? @message_payload

          @message_payload = referenced_message.message_payload['payload']
          @message_payload = referenced_message.message_payload if @message_payload.blank?
          @message_payload
        end

        def type
          @type ||= referenced_message.type
        end

        def referenced_message_payload
          return @referenced_message_payload if defined?(@referenced_message_payload)

          @referenced_message_payload = type == 'text' ? build_text_message : build_media_message
        end

        def build_text_message
          {
            type: type,
            content: message_payload.dig('payload', 'text') || message_payload['text']
          }
        end

        def build_media_message
          payload = {
            type: type,
            media_url: extract_media_url
          }

          payload[:caption] = referenced_message.get_media_caption if %w[image file].include?(type)

          payload[:file_name] = extract_file_name if type == 'file'

          payload
        end

        def extract_media_url
          message_payload['originalUrl'] ||
            message_payload.dig('payload', 'url') ||
            message_payload['url']
        end

        def extract_file_name
          message_payload.dig('payload', 'name') ||
            message_payload.dig('payload', 'filename') ||
            message_payload['name'] ||
            message_payload['filename']
        end
    end
  end
end
