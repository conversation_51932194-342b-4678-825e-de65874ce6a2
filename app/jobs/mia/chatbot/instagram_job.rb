module Mia
  module Chatbot
    class InstagramJob < ChatbotBaseJob
      queue_as :default

      private

        def phone_number
          @phone_number ||= "#{platform}_#{customer.web_id}"
        end

        def referenced_message
          @referenced_message ||= @customer.instagram_messages.find_by(mid: referenced_message_id)
        end

        def type
          @type ||= referenced_message.url.blank? ? 'text' : referenced_message.file_type
        end

        def referenced_message_payload
          return @referenced_message_payload if defined?(@referenced_message_payload)

          @referenced_message_payload = type == 'text' ? build_text_message : build_media_message
        end

        def build_text_message
          {
            type: type,
            content: referenced_message.text
          }
        end

        def build_media_message
          {
            type: type,
            media_url: referenced_message.url,
            file_name: referenced_message.filename
          }.compact
        end
    end
  end
end
