module Mia
  module Chatbot
    class SyncRetailerTimezoneJob < ApplicationJob
      queue_as :default

      def perform(retailer_id, attempt = 1)
        @retailer = Retailer.find_by(id: retailer_id)
        return if @retailer.blank?

        timezone_result = chatbot_client.update_timezone({ timezone: @retailer.timezone || 'America/Guayaquil' })
        raise 'Error interno de MIA' unless timezone_result.success?

        Rails.logger.info("Timezone actualizada correctamente para retailer #{@retailer.id}")
      rescue StandardError => e
        Rails.logger.error("Error en SyncRetailerTimezoneJob: #{e.message}")
        return if attempt == 3

        Mia::Chatbot::SyncRetailerTimezoneJob.set(wait: 5.minutes).perform_later(retailer_id, attempt + 1)
      end

      private

        def chatbot_client
          @chatbot_client ||= MercatelyMiaApi::V2::Chatbot.new(
            api_key: mia_integration.openai_key,
            secret_key: mia_integration.public_key,
            base_url: mia_integration.service_url
          )
        end

        def mia_integration
          @mia_integration ||= @retailer.mia_integration('chatbot')
        end
    end
  end
end
