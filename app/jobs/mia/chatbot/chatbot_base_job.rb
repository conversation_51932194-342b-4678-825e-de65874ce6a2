module Mia
  module Chatbot
    class Chatbot<PERSON><PERSON>Job < ApplicationJob
      queue_as :default

      attr_reader :customer_id, :inbound_type, :media_params, :referenced_message_id

      def perform(customer_id, inbound_type, referenced_message_id, media_params = {})
        initialize_main_vars(customer_id, inbound_type, referenced_message_id, media_params)
        return unless valid_customer_and_message?(inbound_type)

        mia_response = process_inbound_message
        ActiveRecord::Base.connection.reconnect!

        handler_class.new(
          mia_response: mia_response,
          customer: @customer,
          platform: platform
        ).call
      end

      private

        def platform
          self.class.name.demodulize.sub('Job', '').downcase
        end

        def handler_class
          "Mia::Chatbot::ResponseHandler::#{platform.camelize}".constantize
        end

        def initialize_main_vars(customer_id, inbound_type, referenced_message_id, media_params)
          @customer_id = customer_id
          @media_params = media_params
          @referenced_message_id = referenced_message_id
          @inbound_type = inbound_type
          @customer = customer
        end

        def customer
          @customer ||= Customer.find_by(id: customer_id)
        end

        def redis
          @redis ||= Redis.new(url: ENV.fetch('REDIS_PROVIDER', 'redis://localhost:6379/1'))
        end

        def inbound_message
          return @inbound_message if defined? @inbound_message

          @inbound_message = redis.get("#{platform}_messages_#{customer.id}")
          redis.del("#{platform}_messages_#{customer.id}")
          @inbound_message
        end

        def valid_customer_and_message?(inbound_type)
          return false if customer.blank?
          return inbound_message.present? if inbound_type == 'text'

          media_params[:url].present?
        end

        def process_inbound_message
          chatbot_client.send_message(send_message_params)
        end

        def send_message_params
          return @send_message_params if defined?(@send_message_params)

          @send_message_params = { customer_id: customer_id.to_s, phone_number: }
          @send_message_params[message_key] = message_value if defined?(message_key)
          @send_message_params[:context] = process_context
          @send_message_params[:question] = message_builder

          if referenced_message_id && %w[text audio image file].include?(type)
            @send_message_params[:question][:referenced_message] = referenced_message_payload
          end

          @send_message_params
        end

        def message_builder
          return media_builder if %w[image file audio].include?(inbound_type)

          text_builder
        end

        def media_builder
          {
            type: inbound_type,
            media_url: message_value,
            file_name: media_params[:file_name]
          }.compact
        end

        def text_builder
          {
            type: 'text',
            content: message_value
          }
        end

        def message_value
          @message_value ||= inbound_type == 'text' ? inbound_message : media_params[:url]
        end

        def chatbot_client
          @chatbot_client ||= MercatelyMiaApi::V2::Chatbot.new(
            api_key: mia_integration.openai_key,
            secret_key: mia_integration.public_key,
            base_url: mia_integration.service_url
          )
        end

        def mia_integration
          @mia_integration ||= retailer.mia_integration('chatbot')
        end

        def retailer
          @retailer ||= customer.retailer
        end

        def process_context
          context_value = context_message&.value || []
          Rails.logger.info "***** Context value: #{context_value}"
          context_message.update(value: []) if context_value.present?
          context_value
        end

        def context_message
          @context_message ||= CustomerContextMessage.find_by(customer_id: customer_id, platform:)
        rescue Mongoid::Errors::DocumentNotFound
          nil
        end

        def phone_number
          @customer.phone_number
        end
    end
  end
end
