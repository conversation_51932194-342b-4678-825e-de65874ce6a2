module Mia
  class ProductsSyncJob < ApplicationJob
    queue_as :default

    def perform(retailer_id)
      @retailer = Retailer.find(retailer_id)
      @retailer.update(mia_products_synced: 'pending')
      retailer_sync_error.sync_errors.blank? || retailer_sync_error.update(sync_errors: [])
      mia_products_synced = sync_products

      ActiveRecord::Base.connection.reconnect!
      @retailer.reload.update(mia_products_synced:)
    end

    private

      def retailer_sync_error
        @retailer_sync_error ||= RetailerSyncProductError.find_or_create_by(retailer_id: @retailer.id)
      end

      def shop_products_client
        @shop_products_client ||= Shops::Product.new(@retailer)
      end

      def mia_integration
        @mia_integration ||= @retailer.mia_integration('products')
      end

      def mia_product_api
        @mia_product_api ||= ::MercatelyMiaApi::Product.new(
          api_key: mia_integration.openai_key,
          secret_key: mia_integration.public_key,
          base_url: mia_integration.service_url,
          retailer_id: mia_integration.retailer_id
        )
      end

      def sync_products
        page = 1
        loop do
          shop_products_data = shop_products_client.all_active_prodcuts(50, page)
          shop_products = shop_products_data['products']
          return 'not_synced' if shop_products.blank?

          sync_batch(shop_products)
          break if page == shop_products_data['total_pages']

          page += 1
        end

        if retailer_sync_error.changed?
          retailer_sync_error.save
          return 'sync_failed'
        end
        'complete'
      rescue StandardError => e
        Rails.logger.error("Error sincronizando productos: #{e.message}")
        retailer_sync_error.sync_errors << e.message
        retailer_sync_error.save
        'sync_failed'
      end

      def sync_batch(shop_products)
        shop_products.each do |product|
          result = sync_product(product)
          result.success? && next

          retailer_sync_error.sync_errors << "Product: #{product['title']}. Error sync with MIA: #{result.body}"
        end
      end

      def sync_product(product)
        params = product_params(product)
        mia_product_response = mia_product_api.retrieve(params[:UUID])
        if mia_product_response.success?
          return mia_product_api.update(params[:UUID], { product: params }) if product['active']

          mia_product_api.delete(params[:UUID])
        else
          return OpenStruct.new(success?: true) unless product['active']

          mia_product_api.create({ products: [params] })
        end
      end

      def product_params(product)
        {
          UUID: product['id'],
          name: product['title'],
          description: product['description'],
          price: product['price'].to_f,
          purchase_link: "https://#{@retailer.catalog_slug}/products/#{product['id']}",
          images: jpg_urls(product['image_urls']),
          currency: "#{@retailer.currency}#{@retailer.currency_symbol}",
          variants: product_variant_combinations(product)
        }
      end

      def jpg_urls(images)
        images.each_with_object([]) do |image, urls|
          url = image['jpgUrl'].presence || image['url'].presence
          urls << url
          urls
        end.compact
      end

      def product_variant_combinations(product)
        product_variants = product['product_variants']
        product['product_variant_combinations'].each_with_object([]) do |combination, acc|
          detail = variant_detail(combination['variant_option_web_ids'], product_variants)
          data = {
            detail: detail.join(' - '),
            price: combination['price'],
            stock: combination['selling_without_stock'] ? nil : combination['quantity']
          }

          combination['attached_image'].present? && data[:image] = { url: combination['attached_image']['jpgUrl'] }
          acc << data
        end
      end

      def variant_detail(variant_option_web_ids, product_variants)
        variant_option_web_ids.each_with_object([]) do |web_id, acc|
          variant = product_variants.find do |product_variant|
            product_variant['product_variant_options'].any? { |opt| opt['web_id'] == web_id }
          end

          variant_option = variant['product_variant_options'].find { |opt| opt['web_id'] == web_id }
          acc << "#{variant['name']} (#{variant_option['name']})"
        end
      end
  end
end
