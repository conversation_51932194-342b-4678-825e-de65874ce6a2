module Whatsapp
  class UploadResourcesJob < ApplicationJob
    require 'open-uri'

    queue_as :low

    def perform(id)
      gwm = GupshupWhatsappMessage.find(id)
      max_attempts = 3

      max_attempts.times do
        if gwm.reload.internal_url.blank?
          download_and_upload(gwm)
          break
        end
      rescue StandardError => e
        Rails.logger.error(e)
        Rails.logger.info(e.backtrace.slice(0, 10))
        SlackError.send_error(e)
      end
    end

    def broadcast(msg)
      Notifications::Web::Messages.new(msg).broadcast_upload
    end

    def download_and_upload(gwm)
      payload = gwm.message_payload
      url = get_url(payload)
      filename = payload.try(:[], 'payload').try(:[], 'payload').try(:[], 'name') ||
                 "media_#{gwm.id}_#{gwm.retailer_id}"
      # Descargar el archivo usando URI.open
      open_media = URI.open(url)

      # Si es un StringIO, escribir en un archivo temporal
      if open_media.is_a?(String<PERSON>)
        parse_audio_files(filename, open_media, gwm)
      else
        # Si no es un StringIO, simplemente subir el archivo
        upload_to_s3(open_media, filename, gwm)
      end
    end

    def upload_to_s3(file, filename, gwm)
      s3_response = Uploaders::S3Aws.new(gwm.retailer_id)
        .upload_file(file, custom_filename: filename,
                           bucket: ENV.fetch('AWS_BUCKET_56_DAYS', nil),
                           cloudfront: ENV.fetch('AWS_56_DAYS_CLOUD_FRONT_URL', nil))
      gwm.update_columns(internal_url: s3_response['secure_url'])
      broadcast(gwm)
    end

    def parse_audio_files(filename, open_media, gwm)
      payload = gwm.message_payload
      Tempfile.create(filename) do |tempfile|
        tempfile.binmode
        tempfile.write(open_media.read)
        tempfile.rewind
        content_type = payload.try(:[], 'payload').try(:[], 'payload').try(:[], 'contentType')
        tempfile.define_singleton_method(:content_type) { content_type }
        tempfile.define_singleton_method(:tempfile) { tempfile }

        upload_to_s3(tempfile, filename, gwm)
      end
    end

    def get_url(payload)
      payload.try(:[], 'payload').try(:[], 'payload').try(:[], 'url').presence ||
        payload.try(:[], 'payload').try(:[], 'referral').try(:[], 'image_url').presence ||
        payload.try(:[], 'payload').try(:[], 'referral').try(:[], 'video_url')
    end
  end
end
