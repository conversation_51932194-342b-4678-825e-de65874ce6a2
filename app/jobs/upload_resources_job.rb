class UploadResourcesJob < ApplicationJob
  require 'open-uri'

  queue_as :low

  def perform(id, platform)
    msg = if platform == 'instagram'
            InstagramMessage.find(id)
          else
            FacebookMessage.find(id)
          end
    max_attempts = 3

    max_attempts.times do
      break if msg.reload.internal_url.present?

      download_and_upload(msg, platform)
    rescue StandardError => e
      Rails.logger.error(e)
      Rails.logger.info(e.backtrace.slice(0, 10))
      SlackError.send_error(e)
    end
  end

  def broadcast(msg, platform)
    facebook_helper = FacebookNotificationHelper
    facebook_helper.broadcast_upload_data(msg.retailer, msg, msg.customer, platform)
  end

  # rubocop:disable Security/Open
  def download_and_upload(msg, platform)
    url = msg.url
    filename = "fb_media_#{msg.id}_#{msg.facebook_retailer_id}"
    # Download file using URI.open
    # WARNING: Using URI.open can be a security risk (SSRF, etc).
    # Make sure URLs are validated and only trusted sources are allowed.
    # If something changes in the future, remember to also validate the
    # upload_resources_job.rb file from whatsapp that is similar to this one.
    open_media = URI.open(url)

    # If is a StringIO, write it in a temp file
    if open_media.is_a?(StringIO)
      parse_string_io_files(filename, open_media, msg, platform)
    else
      # If is not a StringIO, just upload the file
      upload_to_s3(open_media, filename, msg, platform)
    end
  end
  # rubocop:enable Security/Open

  def upload_to_s3(file, filename, msg, platform)
    s3_response = Uploaders::S3Aws.new(msg.facebook_retailer.retailer_id)
      .upload_file(file, custom_filename: filename,
                         bucket: ENV.fetch('AWS_BUCKET_56_DAYS', nil),
                         cloudfront: ENV.fetch('AWS_56_DAYS_CLOUD_FRONT_URL', nil))
    msg.update_columns(internal_url: s3_response['secure_url'])
    broadcast(msg, platform)
  end

  def parse_string_io_files(filename, open_media, msg, platform)
    Tempfile.create(filename) do |tempfile|
      tempfile.binmode
      tempfile.write(open_media.read)
      tempfile.rewind
      content_type = (open_media.respond_to?(:content_type) ? open_media.content_type : nil) ||
                     (open_media.respond_to?(:meta) ? open_media.meta['content-type'] : nil) ||
                     fetch_content_type(msg.url)
      tempfile.define_singleton_method(:content_type) { content_type }
      tempfile.define_singleton_method(:tempfile) { tempfile }

      upload_to_s3(tempfile, filename, msg, platform)
    end
  end

  def fetch_content_type(url)
    uri = URI.parse(url)
    Net::HTTP.start(uri.host, uri.port, use_ssl: uri.scheme == 'https') do |http|
      response = http.head(uri.request_uri)
      return response['Content-Type']
    end
  rescue StandardError => e
    Rails.logger.error("Error fetching content type: #{e.message}")
    nil
  end
end
