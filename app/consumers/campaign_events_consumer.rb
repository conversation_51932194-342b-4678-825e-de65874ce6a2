# frozen_string_literal: true

# rubocop:disable Metrics/ClassLength

# Processes campaign-related events from Kafka topics
#
# This consumer handles various campaign events in an event-driven architecture,
# delegating specific event types to specialized consumers for processing.
# Supports campaign lifecycle events including start, message processing, and completion.
#
# @example Expected event format
#   {
#     "event_type": "campaign_started_event",
#     "campaign_id": 123,
#     "estimated_recipients": 500
#   }

# IMPORTANTE: Clase Dummy requerida para Zeitwerk
# ===============================================
#
# Zeitwerk (el autoloader de Rails 6+) requiere que cada archivo defina la constante
# que corresponde a su nombre de archivo. En este caso, el archivo se llama
# 'campaign_events_consumer.rb', por lo que DEBE definir 'CampaignEventsConsumer'.
#
# Sin la clase dummy, cuando KAFKA_CONSUMER_ENABLED != 'true', el archivo no define
# ninguna constante, causando el error:
# "expected file to define constant CampaignEventsConsumer, but didn't"
#
# La clase dummy es un patrón estándar en Rails para:
# - Satisfacer los requisitos de Zeitwerk
# - Permitir carga condicional de funcionalidad
# - Mantener la aplicación estable en diferentes configuraciones de nodos
#
# Referencias:
# - https://guides.rubyonrails.org/autoloading_and_reloading_constants.html
# - https://github.com/fxn/zeitwerk#file-structure

# Solo cargar la implementación real en nodos que actúan como consumidores
if ENV['KAFKA_CONSUMER_ENABLED'] == 'true'
  require 'karafka'

  class CampaignEventsConsumer < Karafka::BaseConsumer
    # Processes a batch of campaign events from Kafka
    #
    # Parses each message, identifies the event type, and routes
    # to the appropriate handler method based on the event type.
    # Handles non-JSON messages by marking them as processed to avoid reprocessing.
    #
    # @return [void]
    def consume
      Rails.logger.info("🔔 KARAFKA: CampaignEventsConsumer processing #{messages.count} messages")
      Rails.logger.info('🔔 KARAFKA: ===== INICIO PROCESAMIENTO BATCH =====')

      messages.each_with_index do |message, index|
        Rails.logger.info("🔔 KARAFKA: Procesando mensaje #{index + 1}/#{messages.count}")
        log_message_receipt(message)

        # Intentar parsear el mensaje
        data = parse_message(message.payload)

        # Si el mensaje no es JSON válido, registrarlo y marcarlo como procesado
        unless data
          Rails.logger.warn("⚠️ KARAFKA: Ignorando mensaje no válido en offset #{message.offset}. " \
                            'Este mensaje será marcado como procesado y no se volverá a intentar.')

          # Continuar con el siguiente mensaje
          next
        end

        # Log detallado del evento antes de procesarlo
        event_type = data['event_type']
        campaign_id = data['campaign_id']
        Rails.logger.info("🎯 KARAFKA: EVENT_TYPE detectado: '#{event_type}' para campaña: #{campaign_id}")
        Rails.logger.info("📦 KARAFKA: Payload completo: #{data.inspect}")

        # Procesar el evento
        process_event(data)

        Rails.logger.info("✅ KARAFKA: Mensaje #{index + 1}/#{messages.count} procesado exitosamente")
      end

      Rails.logger.info('🔔 KARAFKA: ===== FIN PROCESAMIENTO BATCH =====')
    end

    private

      # Logs message receipt information
      #
      # @param message [Karafka::Messages::Message] The Kafka message
      # @return [void]
      def log_message_receipt(message)
        Rails.logger.debug do
          "\e[1;34m📨 KARAFKA: Message received in partition #{message.partition}, offset #{message.offset}\e[0m"
        end
        Rails.logger.info("📨 Message received in partition #{message.partition}, offset #{message.offset}")
        Rails.logger.debug { "📨 Complete message: #{message.inspect}" }
        Rails.logger.debug { "📨 Payload: #{message.payload.inspect}" }
        Rails.logger.debug { "📨 Key: #{message.key}" }
      end

      # Parses message payload into a hash
      #
      # @param payload [String, Hash] The message payload
      # @return [Hash, nil] Parsed message data or nil if parsing fails
      def parse_message(payload)
        if payload.is_a?(String)
          begin
            data = JSON.parse(payload).with_indifferent_access
            Rails.logger.debug "\e[1;32m✅ KARAFKA: Message parsed as JSON\e[0m"
            Rails.logger.info('✅ Message parsed as JSON')
            return data
          rescue JSON::ParserError => e
            Rails.logger.debug { "\e[1;31m❌ KARAFKA: Error parsing message: #{e.message}\e[0m" }
            Rails.logger.error("❌ Error parsing message: #{e.message}")
            return nil
          end
        end

        payload
      end

      # Processes an event based on its type
      #
      # @param data [Hash] The event data
      # @return [void]
      def process_event(data)
        return log_invalid_data(data) unless data.is_a?(Hash)

        # Convert to indifferent access to handle both string and symbol keys
        data = data.with_indifferent_access

        # Extract event data
        event_data = extract_event_data(data)

        # Log detailed event information
        log_event_details(*event_data.values)

        # Route to the appropriate handler based on event type
        route_event(event_data[:event_type], data)
      end

      # Logs invalid data error
      #
      # @param data [Object] The invalid data
      # @return [void]
      def log_invalid_data(data)
        Rails.logger.debug "\e[1;31m❌ KARAFKA: Message is not a Hash\e[0m"
        Rails.logger.error("❌ Message is not a Hash: #{data.inspect}")
      end

      # Extracts event data from the message
      #
      # @param data [Hash] The event data
      # @return [Hash] Extracted event data
      def extract_event_data(data)
        # Convert to indifferent access to handle both string and symbol keys
        data = data.with_indifferent_access

        {
          event_type: data[:event_type],
          campaign_id: data[:campaign_id],
          customer_id: data[:customer_id],
          message_id: data[:message_id],
          status: data[:status]
        }
      end

      # Routes the event to the appropriate handler
      #
      # @param event_type [String] The type of event
      # @param data [Hash] The event data
      # @return [void]
      def route_event(event_type, data)
        case event_type
        when 'campaign_started_event'
          process_campaign_started(data)
        when 'campaign_message_pending_event'
          Rails.logger.info('🔄 PRUEBA: Habilitando procesamiento de campaign_message_pending_event')
          process_message_pending(data)
        when 'campaign_message_sent_event'
          Rails.logger.info('🔄 PRUEBA: Habilitando procesamiento de campaign_message_sent_event')
          process_message_sent(data)
        when 'campaign_completed_event'
          Rails.logger.info('🔄 PRUEBA: Habilitando procesamiento de campaign_completed_event')
          process_campaign_completed(data)
        else
          log_unknown_event_type(event_type)
        end
      end

      # Logs unknown event type
      #
      # @param event_type [String] The unknown event type
      # @return [void]
      def log_unknown_event_type(event_type)
        Rails.logger.debug { "\e[1;33m⚠️ KARAFKA: Unknown event type: #{event_type}\e[0m" }
        Rails.logger.warn("⚠️ Unknown event type: #{event_type}")
      end

      # Logs detailed event information
      #
      # @param event_type [String] Type of the event
      # @param campaign_id [Integer] ID of the campaign
      # @param customer_id [Integer, nil] ID of the customer if applicable
      # @param message_id [String, nil] ID of the message if applicable
      # @param status [String, nil] Status if applicable
      # @return [void]
      def log_event_details(event_type, campaign_id, customer_id, message_id, status)
        event_info = "Type: #{event_type}, Campaign: #{campaign_id}"
        event_info += ", Customer: #{customer_id}" if customer_id
        event_info += ", Message: #{message_id}" if message_id
        event_info += ", Status: #{status}" if status

        Rails.logger.debug { "\e[1;32m✅ KARAFKA: Processing event - #{event_info}\e[0m" }
        Rails.logger.info("✅ KARAFKA: Processing event - #{event_info}")
      end

      # Processes a campaign_started_event
      #
      # Delegates processing to the specialized CampaignStartedConsumer
      #
      # @param data [Hash] Event data containing campaign information
      # @return [void]
      def process_campaign_started(data)
        campaign_id = data[:campaign_id]
        estimated_recipients = data[:estimated_recipients]

        Rails.logger.info("🚀 KARAFKA: Campaign started - ID: #{campaign_id}, Recipients: #{estimated_recipients}")
        Rails.logger.info("🔍 KARAFKA: Full payload for campaign_started_event: #{data.inspect}")

        begin
          Rails.logger.info('📣 KARAFKA: Delegando evento campaign_started_event para campaña ' \
                            "#{campaign_id} a CampaignStartedConsumer")

          # Verificar si la campaña existe
          campaign = Campaign.find_by(id: campaign_id)
          if campaign
            Rails.logger.info("✅ KARAFKA: Campaña #{campaign_id} encontrada en la base de datos")
            Rails.logger.info("📊 KARAFKA: Campaña #{campaign_id} tiene #{campaign.customers.count} clientes")
          else
            Rails.logger.error("❌ KARAFKA: Campaña #{campaign_id} NO encontrada en la base de datos")
          end

          # Crear el procesador y procesar el evento
          processor = Campaigns::Processors::CampaignStartedEventProcessor.new
          Rails.logger.info('✅ KARAFKA: CampaignStartedEventProcessor creado correctamente')

          # Procesar el evento
          processor.process(data)
          Rails.logger.info("✅ KARAFKA: Procesamiento de campaña #{campaign_id} completado exitosamente")
        rescue StandardError => e
          Rails.logger.error("❌ KARAFKA: Error procesando campaña #{campaign_id}: #{e.message}")
          Rails.logger.error(e.backtrace.join("\n"))
          log_processing_error('campaign', campaign_id, e)
        end
      end

      # Processes a campaign_message_pending_event
      #
      # Delegates processing to the specialized CampaignMessageConsumer
      #
      # @param data [Hash] Event data containing message information
      # @return [void]
      def process_message_pending(data)
        campaign_id = data[:campaign_id]
        customer_id = data[:customer_id]

        Rails.logger.debug do
          "\e[1;32m📥 KARAFKA: Message pending - Campaign: #{campaign_id}, Customer: #{customer_id}\e[0m"
        end
        Rails.logger.info("Message pending - Campaign: #{campaign_id}, Customer: #{customer_id}")

        begin
          Rails.logger.debug "\e[1;34m📣 KARAFKA: Delegating processing to CampaignMessageConsumer\e[0m"
          consumer = Campaigns::Consumers::CampaignMessageConsumer.new
          consumer.process(data)
          Rails.logger.debug "\e[1;32m✅ KARAFKA: Pending message processing completed successfully\e[0m"
        rescue StandardError => e
          log_processing_error("pending message for campaign #{campaign_id}, customer", customer_id, e)
        end
      end

      # Processes a campaign_message_sent_event
      #
      # Updates Redis counters and delegates processing to MessageSentConsumer
      #
      # @param data [Hash] Event data containing sent message information
      # @return [void]
      def process_message_sent(data)
        campaign_id = data[:campaign_id]
        customer_id = data[:customer_id]
        message_id = data[:message_id]
        status = data[:status] || 'sent'

        Rails.logger.debug do
          "\e[1;32m📤 KARAFKA: Message sent - Campaign: #{campaign_id}, Customer: #{customer_id}, " \
            "Message: #{message_id}, Status: #{status}\e[0m"
        end
        message_info = "Message sent - Campaign: #{campaign_id}, Customer: #{customer_id}, " \
                       "Message: #{message_id}, Status: #{status}"
        Rails.logger.info(message_info)

        begin
          Rails.logger.debug "\e[1;34m📣 KARAFKA: Delegating processing to MessageSentConsumer\e[0m"

          # Initialize Redis counters if needed
          initialize_redis_counters(campaign_id)

          # Process the event
          processor = Campaigns::Processors::MessageSentEventProcessor.new
          processor.process(data)

          # NOTE: Removed Redis :sent counter increment as it's not used anywhere in the codebase
          # The MessageSentConsumer already handles the :pending_messages decrement

          Rails.logger.debug "\e[1;32m✅ KARAFKA: Sent message processing completed successfully\e[0m"
        rescue StandardError => e
          log_processing_error("sent message for campaign #{campaign_id}, customer", customer_id, e)
        end
      end

      # Processes a campaign_completed_event
      #
      # Updates campaign status and delegates to CampaignCompletedConsumer if available
      #
      # @param data [Hash] Event data containing campaign completion information
      # @return [void]
      def process_campaign_completed(data)
        campaign_id = data[:campaign_id]
        success_count = data[:success_count]
        failure_count = data[:failure_count]

        message = "Campaign completed - ID: #{campaign_id}, Successes: #{success_count}, Failures: #{failure_count}"
        Rails.logger.debug { "\e[1;32m🏁 KARAFKA: #{message}\e[0m" }
        Rails.logger.info(message)

        begin
          if defined?(Campaigns::Processors::CampaignCompletedEventProcessor)
            Rails.logger.debug "\e[1;34m📣 KARAFKA: Delegating processing to CampaignCompletedEventProcessor\e[0m"
            processor = Campaigns::Processors::CampaignCompletedEventProcessor.new(data)
            processor.process
            Rails.logger.debug "\e[1;32m✅ KARAFKA: Completion processing completed successfully\e[0m"
          else
            # Mark campaign as sent directly
            mark_campaign_as_sent!(campaign_id)
          end
        rescue StandardError => e
          log_processing_error('campaign completion', campaign_id, e)
        end
      end

      # Initializes Redis counters for campaign tracking
      #
      # @param campaign_id [Integer] ID of the campaign
      # @return [void]
      def initialize_redis_counters(campaign_id)
        redis_key = "campaign:#{campaign_id}"
        total_key = "#{redis_key}:total"

        # Check if total counter exists
        total = Redis.current.get(total_key).to_i

        # Initialize counters if total is 0
        return unless total.zero?

        campaign = Campaign.find_by(id: campaign_id)
        return unless campaign

        total_messages = campaign.customers.count
        Rails.logger.debug do
          "\e[1;33m⚠️ KARAFKA: Initializing Redis counters for campaign #{campaign_id} " \
            "with #{total_messages} messages\e[0m"
        end

        # Initialize basic counters
        Redis.current.set(total_key, total_messages)
        Redis.current.set("#{redis_key}:pending_messages", total_messages)
      end

      # Marks campaign as sent
      #
      # @param campaign_id [Integer] ID of the campaign to mark as sent
      # @return [void]
      def mark_campaign_as_sent!(campaign_id)
        campaign = Campaign.find_by(id: campaign_id)
        if campaign
          campaign.update(status: :sent)
          Rails.logger.debug { "\e[1;32m✅ KARAFKA: Campaign #{campaign_id} marked as sent\e[0m" }
        else
          Rails.logger.debug { "\e[1;31m❌ KARAFKA: Campaign #{campaign_id} not found\e[0m" }
        end
      end

      # Logs processing error with backtrace
      #
      # @param entity_type [String] Type of entity being processed
      # @param entity_id [Integer] ID of the entity
      # @param error [StandardError] The error that occurred
      # @return [void]
      def log_processing_error(entity_type, entity_id, error)
        Rails.logger.debug { "\e[1;31m❌ KARAFKA: Error processing #{entity_type}: #{error.message}\e[0m" }
        Rails.logger.error("Error processing #{entity_type} #{entity_id}: #{error.message}")
        Rails.logger.error(error.backtrace.join("\n"))
      end
  end

else
  # Clase Dummy para satisfacer los requisitos de Zeitwerk
  # =====================================================
  #
  # Esta clase dummy es ESENCIAL para el correcto funcionamiento de la aplicación.
  #
  # PROBLEMA SIN CLASE DUMMY:
  # Zeitwerk requiere que cada archivo defina la constante correspondiente a su nombre.
  # Sin esta clase, cuando KAFKA_CONSUMER_ENABLED != 'true', el archivo no define
  # CampaignEventsConsumer, causando el error fatal:
  # "expected file to define constant CampaignEventsConsumer, but didn't"
  #
  # SOLUCIÓN:
  # La clase dummy garantiza que la constante CampaignEventsConsumer siempre esté
  # definida, permitiendo que la aplicación funcione correctamente en nodos que
  # no actúan como consumidores de Kafka (ej: nodos web, workers, etc.).
  #
  # CASOS DE USO:
  # - Nodos web que solo producen mensajes
  # - Nodos worker que procesan jobs pero no consumen Kafka
  # - Entornos de testing donde Kafka no está disponible
  # - Deploys donde algunos servidores no tienen Karafka configurado
  #
  # IMPORTANTE: NO REMOVER esta clase dummy sin una solución alternativa
  # que garantice la compatibilidad con Zeitwerk.
  class CampaignEventsConsumer
    def self.consume
      Rails.logger.info('CampaignEventsConsumer no disponible - nodo configurado como productor únicamente')
    end

    def consume
      Rails.logger.info('CampaignEventsConsumer no disponible - nodo configurado como productor únicamente')
    end
  end

  Rails.logger.info('CampaignEventsConsumer cargado como clase dummy - nodo configurado como productor únicamente')
end

# rubocop:enable Metrics/ClassLength
