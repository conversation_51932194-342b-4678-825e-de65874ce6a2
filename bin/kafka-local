#!/bin/bash

# Script para manejar Kafka local con Docker
# Uso: ./bin/kafka-local [start|stop|restart|status|logs|ui|console]

set -e

COMPOSE_FILE="docker-compose.kafka.yml"
PROJECT_NAME="mercately-kafka"

case "$1" in
  start)
    echo "🚀 Iniciando Kafka local..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d
    echo "✅ Kafka iniciado. Esperando que esté listo..."
    sleep 10
    echo "🌐 Kafka UI disponible en: http://localhost:8080"
    echo "📡 Kafka broker en: localhost:9092"
    ;;
    
  stop)
    echo "🛑 Deteniendo Kafka local..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down
    echo "✅ Kafka detenido"
    ;;
    
  restart)
    echo "🔄 Reiniciando Kafka local..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d
    echo "✅ Kafka reiniciado"
    ;;
    
  status)
    echo "📊 Estado de Kafka local:"
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME ps
    ;;
    
  logs)
    echo "📋 Logs de Kafka:"
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f kafka
    ;;
    
  ui)
    echo "🌐 Abriendo Kafka UI..."
    open http://localhost:8080 2>/dev/null || xdg-open http://localhost:8080 2>/dev/null || echo "Abrir manualmente: http://localhost:8080"
    ;;
    
  console)
    echo "💻 Consola de Kafka (producer):"
    echo "Escribe mensajes y presiona Enter. Ctrl+C para salir."
    docker exec -it mercately-kafka kafka-console-producer --bootstrap-server localhost:9092 --topic mercately_campaign_events
    ;;
    
  topics)
    echo "📋 Topics disponibles:"
    docker exec mercately-kafka kafka-topics --bootstrap-server localhost:9092 --list
    ;;
    
  clean)
    echo "🧹 Limpiando volúmenes de Kafka..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down -v
    echo "✅ Volúmenes limpiados"
    ;;
    
  *)
    echo "Uso: $0 {start|stop|restart|status|logs|ui|console|topics|clean}"
    echo ""
    echo "Comandos disponibles:"
    echo "  start    - Inicia Kafka local"
    echo "  stop     - Detiene Kafka local"
    echo "  restart  - Reinicia Kafka local"
    echo "  status   - Muestra estado de contenedores"
    echo "  logs     - Muestra logs de Kafka"
    echo "  ui       - Abre Kafka UI en el navegador"
    echo "  console  - Abre consola de producer"
    echo "  topics   - Lista topics disponibles"
    echo "  clean    - Limpia volúmenes (reset completo)"
    exit 1
    ;;
esac
