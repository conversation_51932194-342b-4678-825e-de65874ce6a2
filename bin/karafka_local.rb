#!/usr/bin/env ruby
# frozen_string_literal: true

# Script para iniciar Karafka en modo local para desarrollo

require 'bundler/setup'

# Configurar el entorno
ENV['RAILS_ENV'] ||= 'development'
ENV['KAFKA_ENABLED'] = 'true'
ENV['USE_KAFKA_SENDER'] = 'true'

# Cargar Rails
require_relative '../config/environment'

# Configurar Redis para usar la instancia local
Redis.current = Redis.new(url: 'redis://localhost:6379/0')

puts "🚀 Iniciando Karafka en modo local..."
puts "📊 Configuración:"
puts "   - Kafka: localhost:9092"
puts "   - Redis: localhost:6379"
puts "   - Entorno: #{Rails.env}"
puts "   - Client ID: mercately_rails6_local"
puts ""

# Cargar la configuración de Karafka local
require_relative '../../config/karafka_local'

# Mostrar información de las rutas
puts "📋 Rutas configuradas:"
KarafkaApp.routes.each do |consumer_group|
  puts "   - Grupo: #{consumer_group.name}"
  consumer_group.topics.each do |topic|
    puts "     - Tópico: #{topic.name} -> #{topic.consumer}"
  end
end

puts ""
puts "✅ Karafka iniciado. Presiona Ctrl+C para detener."

# Manejar la señal de interrupción
trap('INT') do
  puts "\n🛑 Deteniendo Karafka..."
  exit(0)
end

# Iniciar el servidor
puts "🔄 Iniciando servidor Karafka..."
Karafka::Server.run
