#!/usr/bin/env ruby
# frozen_string_literal: true

# Asegurarse de que Logger esté cargado antes de ActiveSupport
require 'logger'

ENV['RAILS_ENV'] ||= 'development'
ENV['KARAFKA_ENV'] = ENV['RAILS_ENV']

require ::File.expand_path('../config/environment', __dir__)

# Cargar Mercately::Karafka
require 'mercately/karafka'

# We need to load Karafka after Rails is loaded
require 'karafka/cli'

Karafka::Cli.start
