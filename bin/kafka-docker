#!/usr/bin/env bash

# Kafka Docker Management Script
# Simplified script for managing Kafka campaigns in Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Load environment variables
load_env() {
    if [ -f .env.docker ]; then
        log_info "Loading Docker environment variables..."
        export $(cat .env.docker | grep -v '^#' | xargs)
    else
        log_warning ".env.docker not found. Using default configuration."
    fi
}

# Show usage
usage() {
    echo "🚀 Kafka Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start Kafka and Karafka services"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  status      Show status of all services"
    echo "  logs        Show logs from all services"
    echo "  logs-kafka  Show only Kafka logs"
    echo "  logs-karafka Show only Karafka consumer logs"
    echo "  ui          Open Kafka UI in browser"
    echo "  test        Test campaign functionality"
    echo "  clean       Stop and remove all containers and volumes"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start all services"
    echo "  $0 logs-karafka            # Monitor Karafka consumer"
    echo "  $0 test                     # Test campaign sending"
}

# Start services
start_services() {
    log_info "Starting Kafka and Karafka services..."
    load_env
    check_docker
    
    docker-compose -f docker-compose.kafka.yml up -d
    
    log_success "Services started successfully!"
    log_info "Kafka UI available at: http://localhost:8080"
    log_info "Use '$0 logs' to monitor the services"
}

# Stop services
stop_services() {
    log_info "Stopping Kafka services..."
    docker-compose -f docker-compose.kafka.yml down
    log_success "Services stopped successfully!"
}

# Restart services
restart_services() {
    log_info "Restarting Kafka services..."
    stop_services
    sleep 2
    start_services
}

# Show status
show_status() {
    log_info "Service Status:"
    docker-compose -f docker-compose.kafka.yml ps
}

# Show logs
show_logs() {
    docker-compose -f docker-compose.kafka.yml logs -f
}

# Show Kafka logs only
show_kafka_logs() {
    docker-compose -f docker-compose.kafka.yml logs -f kafka
}

# Show Karafka logs only
show_karafka_logs() {
    docker-compose -f docker-compose.kafka.yml logs -f karafka-consumer
}

# Open Kafka UI
open_ui() {
    log_info "Opening Kafka UI..."
    if command -v open > /dev/null; then
        open http://localhost:8080
    elif command -v xdg-open > /dev/null; then
        xdg-open http://localhost:8080
    else
        log_info "Please open http://localhost:8080 in your browser"
    fi
}

# Test campaign functionality
test_campaign() {
    log_info "Testing campaign functionality..."
    load_env
    
    # Check if services are running
    if ! docker-compose -f docker-compose.kafka.yml ps | grep -q "Up"; then
        log_error "Services are not running. Please run '$0 start' first."
        exit 1
    fi
    
    log_info "Running campaign test..."
    bundle exec rails runner "
    puts '🧪 Testing Kafka campaign functionality...'
    
    # Test that classes are available
    puts '✅ Campaigns::SenderStrategySelector: ' + Campaigns::SenderStrategySelector.to_s
    puts '✅ Campaigns::SenderStrategies::KafkaEvent: ' + Campaigns::SenderStrategies::KafkaEvent.to_s
    
    puts '🎯 Kafka campaigns are ready for testing!'
    puts 'You can now create and send campaigns via Kafka.'
    "
}

# Clean everything
clean_all() {
    log_warning "This will remove all containers and volumes. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up all containers and volumes..."
        docker-compose -f docker-compose.kafka.yml down -v --remove-orphans
        docker system prune -f
        log_success "Cleanup completed!"
    else
        log_info "Cleanup cancelled."
    fi
}

# Main script logic
case "${1:-help}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    logs-kafka)
        show_kafka_logs
        ;;
    logs-karafka)
        show_karafka_logs
        ;;
    ui)
        open_ui
        ;;
    test)
        test_campaign
        ;;
    clean)
        clean_all
        ;;
    help|--help|-h)
        usage
        ;;
    *)
        log_error "Unknown command: $1"
        echo ""
        usage
        exit 1
        ;;
esac
