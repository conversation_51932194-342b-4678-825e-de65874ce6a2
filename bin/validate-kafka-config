#!/usr/bin/env ruby
# frozen_string_literal: true

puts "🔍 Validando configuración de Kafka"
puts "=" * 50

# Variables requeridas para Kafka
required_vars = {
  'KAFKA_BOOTSTRAP_SERVERS' => 'Servidores de Kafka',
  'KAFKA_CLIENT_ID' => 'ID del cliente Kafka',
  'KAFKA_SECURITY_PROTOCOL' => 'Protocolo de seguridad',
  'KAFKA_SASL_MECHANISM' => 'Mecanismo SASL',
  'KAFKA_USERNAME' => 'Usuario de Kafka',
  'KAFKA_PASSWORD' => 'Contraseña de Kafka',
  'KAFKA_CA_CERT' => 'Certificado SSL'
}

# Variables opcionales con valores por defecto
optional_vars = {
  'KAFKA_ENABLED' => 'false',
  'KAFKA_CONSUMER_ENABLED' => 'false', 
  'KAFKA_PRODUCER_ENABLED' => 'false',
  'USE_KAFKA_SENDER' => 'false',
  'KAFKA_CONCURRENCY' => '1',
  'KAFKA_MAX_WAIT_TIME' => '10',
  'KAFKA_PRODUCER_POOL_SIZE' => '5',
  'KAFKA_PRODUCER_MAX_IDLE_TIME' => '300'
}

puts "\n1️⃣  Verificando variables requeridas..."
missing_required = []
required_vars.each do |var, description|
  value = ENV[var]
  if value.nil? || value.strip.empty?
    puts "   ❌ #{var} (#{description}): NO CONFIGURADA"
    missing_required << var
  else
    # Ocultar contraseñas en el output
    display_value = var.include?('PASSWORD') ? '[OCULTA]' : value
    puts "   ✅ #{var} (#{description}): #{display_value}"
  end
end

puts "\n2️⃣  Verificando variables opcionales..."
optional_vars.each do |var, default_value|
  value = ENV[var] || default_value
  puts "   📋 #{var}: #{value}"
end

puts "\n3️⃣  Verificando archivos..."
cert_path = ENV['KAFKA_CA_CERT']
if cert_path && File.exist?(cert_path)
  puts "   ✅ Certificado SSL encontrado: #{cert_path}"
else
  puts "   ❌ Certificado SSL no encontrado: #{cert_path}"
  missing_required << 'KAFKA_CA_CERT (archivo)'
end

puts "\n4️⃣  Verificando conectividad..."
if ENV['KAFKA_BOOTSTRAP_SERVERS']
  servers = ENV['KAFKA_BOOTSTRAP_SERVERS'].split(',')
  servers.each do |server|
    host, port = server.split(':')
    begin
      require 'socket'
      socket = Socket.new(Socket::AF_INET, Socket::SOCK_STREAM, 0)
      socket.connect(Socket.sockaddr_in(port.to_i, host))
      socket.close
      puts "   ✅ Conectividad a #{server}: OK"
    rescue => e
      puts "   ❌ Conectividad a #{server}: ERROR (#{e.message})"
    end
  end
end

puts "\n" + "=" * 50
if missing_required.empty?
  puts "🎉 ¡Configuración de Kafka válida!"
  puts "💡 Todas las variables requeridas están configuradas"
else
  puts "❌ Configuración incompleta"
  puts "💡 Variables faltantes: #{missing_required.join(', ')}"
  puts "💡 Consulta .env.example para ver todas las variables necesarias"
  exit 1
end
