#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative '../config/environment'

puts "🧪 Testing Kafka Balance Fix"
puts "=" * 50

# Test 1: Check if we can create a campaign without errors
puts "\n1️⃣  Testing Campaign Creation..."
begin
  # Buscar un retailer con templates de WhatsApp
  retailer = Retailer.joins(:whatsapp_templates).first
  if retailer.nil?
    puts "   ⚠️  No retailer with WhatsApp templates found"
    exit 1
  end

  contact_group = retailer.contact_groups.first
  if contact_group.nil?
    puts "   ⚠️  No contact groups found for retailer #{retailer.id}"
    exit 1
  end

  whatsapp_template = retailer.whatsapp_templates.first
  if whatsapp_template.nil?
    puts "   ⚠️  No WhatsApp templates found for retailer #{retailer.id}"
    exit 1
  end

  campaign = Campaign.new(
    retailer: retailer,
    contact_group: contact_group,
    whatsapp_template: whatsapp_template,
    send_at: 1.hour.from_now,
    template_text: whatsapp_template.text
  )

  puts "   📊 Campaign details:"
  puts "      Retailer ID: #{retailer.id}"
  puts "      Retailer BSP: #{retailer.connect_bsp_mercately}"
  puts "      Retailer Balance: #{retailer.ws_balance}"
  puts "      Estimated Cost: #{campaign.calculate_estimated_cost}"
  puts "      Recipients: #{campaign.recipients.count}"

  puts "   ✅ Campaign created successfully"
rescue => e
  puts "   ❌ Campaign creation failed: #{e.message}"
  puts "   💡 Backtrace: #{e.backtrace.first(5).join("\n      ")}"
  exit 1
end

# Test 2: Test eligibility service
puts "\n2️⃣  Testing Kafka Eligibility..."
begin
  result = Campaigns::Eligibility::KafkaService.eligible?(campaign)
  puts "   📊 Eligibility result: #{result}"
  
  # Show eligibility details
  eligibility_result = Campaigns::Eligibility::KafkaService.send(:check_eligibility, campaign)
  if eligibility_result.respond_to?(:checks)
    puts "   📋 Eligibility checks:"
    eligibility_result.checks.each do |check, value|
      status = value ? "✅" : "❌"
      puts "      #{status} #{check}: #{value}"
    end
  end
rescue => e
  puts "   ❌ Eligibility check failed: #{e.message}"
  puts "   💡 Backtrace: #{e.backtrace.first(5).join("\n      ")}"
  exit 1
end

# Test 3: Test strategy selection
puts "\n3️⃣  Testing Strategy Selection..."
begin
  strategy = Campaigns::SenderStrategySelector.call(campaign: campaign)
  puts "   📊 Selected strategy: #{strategy.class.name}"
  puts "   ✅ Strategy selection successful"
rescue => e
  puts "   ❌ Strategy selection failed: #{e.message}"
  puts "   💡 Backtrace: #{e.backtrace.first(5).join("\n      ")}"
  exit 1
end

# Test 4: Test balance validation specifically
puts "\n4️⃣  Testing Balance Validation..."
begin
  validator = Campaigns::Eligibility::Validators::RetailerBalance.new
  result = Campaigns::Eligibility::KafkaService::Result.new
  
  validator.validate(campaign, result)
  
  balance_check = result.checks[:has_balance]
  puts "   📊 Balance check result: #{balance_check}"
  puts "   ✅ Balance validation successful"
rescue => e
  puts "   ❌ Balance validation failed: #{e.message}"
  puts "   💡 Backtrace: #{e.backtrace.first(5).join("\n      ")}"
  exit 1
end

puts "\n🎉 All tests passed! The balance fix is working correctly."
puts "💡 The 30% fallback issue should now be resolved."
