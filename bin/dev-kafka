#!/bin/bash

# Script para desarrollo local con Kafka
# Uso: ./bin/dev-kafka [start|stop|status|logs|ui|test]

set -e

COMPOSE_FILE="docker-compose.kafka.yml"
PROJECT_NAME="mercately-kafka"

case "$1" in
  start)
    echo "🚀 Iniciando entorno de desarrollo Kafka..."
    
    # Iniciar Kafka
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d
    
    echo "⏳ Esperando que Kafka esté listo..."
    sleep 15
    
    echo "✅ Kafka iniciado correctamente!"
    echo ""
    echo "🌐 Kafka UI: http://localhost:8080"
    echo "📡 Kafka Broker: localhost:9092"
    echo ""
    echo "📋 Próximos pasos:"
    echo "1. Terminal 1: rails server"
    echo "2. Terminal 2: source .env.kafka.local && bundle exec karafka server"
    echo "3. Terminal 3: Probar campaña desde Rails console"
    ;;
    
  stop)
    echo "🛑 Deteniendo entorno Kafka..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down
    echo "✅ Kafka detenido"
    ;;
    
  status)
    echo "📊 Estado del entorno:"
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME ps
    ;;
    
  logs)
    echo "📋 Logs de Kafka:"
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f kafka
    ;;
    
  ui)
    echo "🌐 Abriendo Kafka UI..."
    open http://localhost:8080 2>/dev/null || xdg-open http://localhost:8080 2>/dev/null || echo "Abrir manualmente: http://localhost:8080"
    ;;
    
  test)
    echo "🧪 Probando conexión a Kafka..."
    
    # Verificar que Kafka esté corriendo
    if ! docker ps | grep -q mercately-kafka; then
      echo "❌ Kafka no está corriendo. Ejecuta: ./bin/dev-kafka start"
      exit 1
    fi
    
    # Probar conexión
    echo "📡 Probando conexión al broker..."
    docker exec mercately-kafka kafka-broker-api-versions --bootstrap-server localhost:9092 > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
      echo "✅ Conexión a Kafka exitosa"
      
      echo "📋 Topics disponibles:"
      docker exec mercately-kafka kafka-topics --bootstrap-server localhost:9092 --list
      
      echo ""
      echo "🎯 Listo para probar campañas!"
    else
      echo "❌ Error conectando a Kafka"
      exit 1
    fi
    ;;
    
  console-producer)
    echo "💻 Consola de producer (escribe mensajes):"
    docker exec -it mercately-kafka kafka-console-producer --bootstrap-server localhost:9092 --topic mercately_campaign_events
    ;;
    
  console-consumer)
    echo "👂 Consola de consumer (lee mensajes):"
    docker exec -it mercately-kafka kafka-console-consumer --bootstrap-server localhost:9092 --topic mercately_campaign_events --from-beginning
    ;;
    
  *)
    echo "Uso: $0 {start|stop|status|logs|ui|test|console-producer|console-consumer}"
    echo ""
    echo "Comandos disponibles:"
    echo "  start             - Inicia entorno Kafka completo"
    echo "  stop              - Detiene entorno Kafka"
    echo "  status            - Muestra estado de contenedores"
    echo "  logs              - Muestra logs de Kafka"
    echo "  ui                - Abre Kafka UI en navegador"
    echo "  test              - Prueba conexión a Kafka"
    echo "  console-producer  - Abre consola para enviar mensajes"
    echo "  console-consumer  - Abre consola para leer mensajes"
    exit 1
    ;;
esac
