default: &default
  kafka:
    bootstrap.servers: <%= ENV['KAFKA_BOOTSTRAP_SERVERS'] %>
    client.id: <%= ENV['KAFKA_CLIENT_ID'] %>
    security.protocol: <%= ENV['KAFKA_SECURITY_PROTOCOL'] %>
<% if <PERSON>NV['KAFKA_SECURITY_PROTOCOL'] == 'SASL_PLAINTEXT' || ENV['KAFKA_SECURITY_PROTOCOL'] == 'SASL_SSL' %>
    sasl.mechanisms: <%= ENV['KAFKA_SASL_MECHANISM'] %>
    sasl.username: <%= <PERSON>NV['KAFKA_USERNAME'] %>
    sasl.password: <%= ENV['KAFKA_PASSWORD'] %>
    <% if <PERSON>NV['KAFKA_CA_CERT'].present? %>
    ssl.ca.location: <%= ENV['KAFKA_CA_CERT'] %>
    <% end %>
<% end %>
  deliver: true
  middleware: []

development:
  <<: *default

test:
  <<: *default

production:
  <<: *default
