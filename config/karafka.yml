default: &default
  consumer_groups:

    # Grupo para eventos de campañas
    - id: <%= Rails.env.production? ? 'mercately_rails6_production_group_campaigns' : 'mercately_rails6_staging_group_campaigns' %>
      topics:
        - name: mercately_campaign_events
          consumer: CampaignEventsConsumer
          max.message.bytes: 1048576
          cleanup.policy: delete
          retention.ms: 604800000
      session.timeout.ms: 30000
      heartbeat.interval.ms: 10000
      max.poll.interval.ms: 300000
      auto.offset.reset: earliest
      enable.auto.commit: true

    # Grupo para mensajes de control de campañas - DESHABILITADO (tópico no existe en DO)
    # - id: <%= Rails.env.production? ? 'mercately_rails6_production_group_campaign_control' : 'mercately_rails6_staging_group_campaign_control' %>
    #   topics:
    #     - name: campaign_control
    #       consumer: CampaignControlConsumer
    #       max.message.bytes: 1048576
    #       cleanup.policy: delete
    #       retention.ms: 604800000
    #   session.timeout.ms: 300000
    #   heartbeat.interval.ms: 60000
    #   max.poll.interval.ms: 600000
    #   auto.offset.reset: earliest
    #   enable.auto.commit: true

    # Grupo para mensajes individuales de campañas - DESHABILITADO (tópico no existe en DO)
    # - id: <%= Rails.env.production? ? 'mercately_rails6_production_group_campaign_messages' : 'mercately_rails6_staging_group_campaign_messages' %>
    #   topics:
    #     - name: campaign_messages
    #       consumer: CampaignMessagesConsumer
    #       max.message.bytes: 1048576
    #       cleanup.policy: delete
    #       retention.ms: 604800000
    #   session.timeout.ms: 300000
    #   heartbeat.interval.ms: 60000
    #   max.poll.interval.ms: 600000
    #   auto.offset.reset: earliest
    #   enable.auto.commit: true
  client_id: <%= Rails.env.production? ? 'mercately_rails6_production' : 'mercately_rails6_staging' %>
  max_wait_time: 5
  worker_threads: 2
  shutdown_timeout: 30

development:
  <<: *default

test:
  <<: *default

production:
  <<: *default
