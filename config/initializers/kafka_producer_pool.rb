# frozen_string_literal: true

# Set default pool size if not already set (good neighbor configuration)
ENV['KAFKA_PRODUCER_POOL_SIZE'] ||= '5' # Conservador: menos productores concurrentes
ENV['KAFKA_PRODUCER_MAX_IDLE_TIME'] ||= '300' # 5 minutos (era 10 minutos)

# Initialize the Kafka producer pool
Rails.application.config.after_initialize do
  # Only initialize if Kafka is enabled
  if ENV['KAFKA_ENABLED'] == 'true' || ENV['KAFKA_PRODUCER_ENABLED'] == 'true'
    Rails.logger.info("Initializing Kafka producer pool with size #{ENV.fetch('KAFKA_PRODUCER_POOL_SIZE', nil)}")

    # Register a callback to close all producers when the application is shutting down
    at_exit do
      # Skip closing producers in test environment to avoid RSpec::Mocks::OutsideOfExampleError
      unless Rails.env.test?
        Rails.logger.info('Closing all Kafka producers in pool')
        Mercately::Kafka::ProducerPool.close_all
      end
    end
  end
end
