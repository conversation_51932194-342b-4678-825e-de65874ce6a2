# frozen_string_literal: true

# Este inicializador configura Karafka para usar la configuración correcta de Kafka
# en todos los entornos.

# Solo configurar Karafka si está definido y este nodo debe actuar como consumidor
if defined?(Karafka) && ENV['KAFKA_CONSUMER_ENABLED'] == 'true'
  Rails.logger.info("Configurando Karafka en nodo: #{begin
    Socket.gethostname
  rescue StandardError
    'unknown'
  end}")

  # Verificar si ya hay otra instancia en ejecución
  if KarafkaManager.running?
    Rails.logger.warn('¡ADVERTENCIA! Ya hay otra instancia de Karafka en ejecución en este nodo.')
    Rails.logger.warn("Estado de Karafka: #{KarafkaManager.status}")
  end

  # Validar que las variables de entorno requeridas estén configuradas
  required_env_vars = %w[
    KAFKA_BOOTSTRAP_SERVERS
    KAFKA_CLIENT_ID
    KAFKA_SECURITY_PROTOCOL
    KAFKA_SASL_MECHANISM
    KAFKA_USERNAME
    KAFKA_PASSWORD
    KAFKA_CA_CERT
  ]

  missing_vars = required_env_vars.select { |var| ENV[var].blank? }

  if missing_vars.any?
    Rails.logger.error("❌ Variables de entorno faltantes para Kafka: #{missing_vars.join(', ')}")
    Rails.logger.error("❌ Karafka no se configurará correctamente")
    return
  end

  # Usar variables de entorno directamente (sin valores por defecto hardcodeados)
  kafka_servers = ENV['KAFKA_BOOTSTRAP_SERVERS']
  kafka_client_id = ENV['KAFKA_CLIENT_ID']

  Rails.logger.info("✅ Configurando Kafka con: Bootstrap servers: #{kafka_servers}, Client ID: #{kafka_client_id}, Security protocol: #{ENV['KAFKA_SECURITY_PROTOCOL']}, Certificate path: #{ENV['KAFKA_CA_CERT']}")

  # Configurar Karafka directamente
  Karafka::App.setup do |config|
    config.client_id = kafka_client_id

    # Configurar concurrencia para usar un solo worker thread
    config.concurrency = ENV.fetch('KAFKA_CONCURRENCY', '1').to_i
    config.max_wait_time = ENV.fetch('KAFKA_MAX_WAIT_TIME', '10').to_i

    config.kafka = {
      'bootstrap.servers': kafka_servers,
      'security.protocol': ENV['KAFKA_SECURITY_PROTOCOL'],
      'sasl.mechanisms': ENV['KAFKA_SASL_MECHANISM'],
      'sasl.username': ENV['KAFKA_USERNAME'],
      'sasl.password': ENV['KAFKA_PASSWORD'],
      'socket.max.fails': ENV.fetch('KAFKA_SOCKET_MAX_FAILS', '3').to_i,
      'connections.max.idle.ms': ENV.fetch('KAFKA_CONNECTIONS_MAX_IDLE_MS', '60000').to_i,
      'socket.connection.setup.timeout.ms': ENV.fetch('KAFKA_SOCKET_CONNECTION_SETUP_TIMEOUT_MS', '10000').to_i,
      'error_handler': ENV.fetch('KAFKA_ERROR_HANDLER', 'Mercately::Kafka::CustomExceptionHandler')
    }

    # Agregar configuración SSL si está disponible
    ca_cert_path = ENV['KAFKA_CA_CERT']
    if File.exist?(ca_cert_path)
      config.kafka[:'ssl.ca.location'] = ca_cert_path
      Rails.logger.info("✅ Certificado SSL configurado: #{ca_cert_path}")
    else
      Rails.logger.error("❌ Certificado SSL no encontrado en: #{ca_cert_path}")
      Rails.logger.error("❌ Karafka no funcionará correctamente sin certificado SSL")
    end
  end

  # Agregar un hook para liberar el bloqueo cuando Karafka se detenga
  begin
    Karafka::App.monitor.subscribe('app.stopping') do
      Rails.logger.info('Karafka se está deteniendo, liberando bloqueo...')
      KarafkaLock.release
    end
  rescue StandardError => e
    Rails.logger.warn("No se pudo suscribir al evento app.stopping: #{e.message}")
  end

  # Intentar adquirir el bloqueo al iniciar la aplicación
  # Esto es útil si Karafka se inicia automáticamente con la aplicación
  if ENV['AUTO_START_KARAFKA'] == 'true' && !KarafkaManager.running?
    Rails.logger.info('Iniciando Karafka automáticamente...')
    result = KarafkaManager.start
    Rails.logger.info("Resultado: #{result}")
  end

  Rails.logger.info('Inicializador karafka_config cargado correctamente')
else
  Rails.logger.info("Karafka no está habilitado en este nodo: #{begin
    Socket.gethostname
  rescue StandardError
    'unknown' || 'desconocido'
  end}")
end
