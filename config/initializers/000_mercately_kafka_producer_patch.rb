# Monkey patch para agregar método flush faltante en Mercately::Ka<PERSON>ka::Producer
#
# El método flush es necesario para asegurar que todos los mensajes pendientes
# se envíen antes de continuar. La gema mercately-kafka no incluye este método
# pero es requerido por algunos consumers.

if defined?(Mercately::Kafka::Producer)
  module Mercately
    module Ka<PERSON>ka
      class Producer
        # Flush all pending messages to Kafka
        #
        # Este método asegura que todos los mensajes pendientes en el buffer
        # del productor se envíen inmediatamente a Kafka.
        #
        # @param timeout [Integer] Timeout en segundos (default: 5)
        # @return [Boolean] true si el flush fue exitoso, false en caso contrario
        def flush(timeout = 5)
          return true unless @producer # Si no hay productor, no hay nada que hacer

          begin
            # Si el productor interno es un Rdkafka::Producer, usar su método flush
            if @producer.respond_to?(:flush)
              Rails.logger.debug("🔄 KAFKA_PRODUCER_PATCH: Ejecutando flush en Rdkafka::Producer")
              @producer.flush(timeout * 1000) # Rdkafka espera timeout en milisegundos
              Rails.logger.debug("✅ KAFKA_PRODUCER_PATCH: Flush completado exitosamente")
              true
            else
              # Si no tiene flush, simplemente retornar true (no-op)
              Rails.logger.debug("⚠️ KAFKA_PRODUCER_PATCH: Productor no soporta flush, continuando")
              true
            end
          rescue => e
            Rails.logger.error("❌ KAFKA_PRODUCER_PATCH: Error en flush: #{e.message}")
            Rails.logger.error("❌ KAFKA_PRODUCER_PATCH: Backtrace: #{e.backtrace.join("\n")}")
            false
          end
        end

        # Alias para compatibilidad
        alias_method :sync, :flush
      end
    end
  end

  Rails.logger.info("✅ KAFKA_PRODUCER_PATCH: Monkey patch aplicado a Mercately::Kafka::Producer")
else
  Rails.logger.warn("⚠️ KAFKA_PRODUCER_PATCH: Mercately::Kafka::Producer no está definido, patch no aplicado")
end
