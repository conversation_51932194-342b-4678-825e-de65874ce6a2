# frozen_string_literal: true

# Kafka Docker Configuration
# Automatically configures Kafka settings for Docker-based development

# Only run this configuration in development or when explicitly using Docker
if Rails.env.development? || ENV['KAFKA_DOCKER_MODE'] == 'true'
  
  # Check if we're running in Docker environment
  docker_kafka_enabled = ENV['KAFKA_BOOTSTRAP_SERVERS'] == 'localhost:9092' || 
                         ENV['KAFKA_DOCKER_MODE'] == 'true' ||
                         File.exist?('/.dockerenv')

  if docker_kafka_enabled
    Rails.logger.info('🐳 [KAFKA-DOCKER] Configuring Kafka for Docker environment')
    
    # Set default Kafka configuration for Docker usando variables de entorno
    ENV['KAFKA_BOOTSTRAP_SERVERS'] ||= ENV.fetch('KAFKA_DOCKER_BOOTSTRAP_SERVERS', 'localhost:9092')
    ENV['USE_KAFKA_SENDER'] ||= ENV.fetch('KAFKA_DOCKER_USE_SENDER', 'true')
    ENV['KAFKA_ENABLED'] ||= ENV.fetch('KAFKA_DOCKER_ENABLED', 'true')
    ENV['KAFKA_CONSUMER_ENABLED'] ||= ENV.fetch('KAFKA_DOCKER_CONSUMER_ENABLED', 'true')
    ENV['KAFKA_PRODUCER_ENABLED'] ||= ENV.fetch('KAFKA_DOCKER_PRODUCER_ENABLED', 'true')

    # Docker-specific settings usando variables de entorno
    ENV['KAFKA_SECURITY_PROTOCOL'] ||= ENV.fetch('KAFKA_DOCKER_SECURITY_PROTOCOL', 'PLAINTEXT')
    ENV['KAFKA_USERNAME'] ||= ENV.fetch('KAFKA_DOCKER_USERNAME', 'mercately')
    ENV['KAFKA_PASSWORD'] ||= ENV.fetch('KAFKA_DOCKER_PASSWORD', 'mercately-dev')
    
    # Redis configuration for Docker - usar configuración existente del sistema
    # No sobrescribir REDIS_URL para evitar romper configuraciones existentes
    # Evitar configurar REDIS_PROVIDER que puede romper el tiempo real
    # ENV['REDIS_PROVIDER'] se mantiene sin tocar para preservar configuración existente
    
    Rails.logger.info('✅ [KAFKA-DOCKER] Docker Kafka configuration applied')
    Rails.logger.info("🔗 [KAFKA-DOCKER] Kafka Bootstrap Servers: #{ENV['KAFKA_BOOTSTRAP_SERVERS']}")
    Rails.logger.info("📊 [KAFKA-DOCKER] Kafka UI available at: http://localhost:8080")
  end
end
