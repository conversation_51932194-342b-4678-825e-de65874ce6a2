# frozen_string_literal: true

# Monkey patch para Rdkafka::Consumer para manejar la diferencia en la firma del método store_offset
# entre las versiones de rdkafka.
#
# En rdkafka 0.19.0, store_offset espera un solo argumento, pero Karafka podría estar intentando
# llamarlo con dos argumentos. Este patch hace que store_offset acepte argumentos adicionales
# y los ignore, llamando al método original con solo el primer argumento.

if defined?(Rdkafka::Consumer)
  # Verificar la versión de rdkafka
  rdkafka_version = Gem.loaded_specs['rdkafka']&.version || Gem::Version.new('0.0.0')

  # Solo aplicar el patch si estamos usando rdkafka 0.19.0 o superior
  if rdkafka_version >= Gem::Version.new('0.19.0')
    Rails.logger.info("Aplicando monkey patch para Rdkafka::Consumer#store_offset (versión #{rdkafka_version})")

    module Rdkafka
      class Consumer
        # Verificar si el método ya ha sido parcheado para evitar aplicar el patch múltiples veces
        unless method_defined?(:original_store_offset)
          # Guardar el método original
          alias original_store_offset store_offset

          # Redefinir el método para que acepte uno o dos argumentos
          def store_offset(message, *args)
            if args.any?
              Rails.logger.debug do
                "Llamando a store_offset con #{args.size + 1} argumentos, ignorando argumentos adicionales"
              end
            end

            # Ignorar argumentos adicionales y llamar al método original con solo el primer argumento
            original_store_offset(message)
          rescue StandardError => e
            Rails.logger.error("Error en store_offset: #{e.message}")
            Rails.logger.error(e.backtrace.join("\n"))
            raise e
          end
        end
      end
    end

    Rails.logger.info('Monkey patch aplicado correctamente para Rdkafka::Consumer#store_offset')
  else
    Rails.logger.info("No es necesario aplicar el monkey patch para Rdkafka::Consumer#store_offset (versión #{rdkafka_version})")
  end
else
  Rails.logger.warn('No se pudo aplicar el monkey patch para Rdkafka::Consumer#store_offset: Rdkafka o Rdkafka::Consumer no están definidos')
end
