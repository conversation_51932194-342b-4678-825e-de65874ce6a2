# frozen_string_literal: true

Mercately::Kafka.configure do |config|
  # Validar que las variables de entorno requeridas estén configuradas
  required_vars = %w[KAFKA_BOOTSTRAP_SERVERS KAFKA_CLIENT_ID]
  missing_vars = required_vars.select { |var| ENV[var].blank? }

  if missing_vars.any?
    Rails.logger.error("❌ Variables de entorno faltantes para Mercately::Kafka: #{missing_vars.join(', ')}")
    Rails.logger.error("❌ Mercately::Kafka no se configurará correctamente")
    return
  end

  # Configuración básica usando solo variables de entorno
  config.brokers = ENV['KAFKA_BOOTSTRAP_SERVERS'].split(',')
  config.client_id = ENV['KAFKA_CLIENT_ID']

  # Configuración del consumidor
  config.consumer_config = {
    'enable.auto.commit': true,
    'auto.offset.reset': 'earliest',
    'socket.max.fails': 3,
    'connections.max.idle.ms': 60_000,
    'socket.connection.setup.timeout.ms': 10_000,
    'max.in.flight.requests.per.connection': 5,
    'session.timeout.ms': 30_000
  }

  # Configuración del productor (optimizada para producción)
  config.producer_config = {
    'security.protocol': ENV.fetch('KAFKA_SECURITY_PROTOCOL', 'PLAINTEXT'),
    'bootstrap.servers': ENV['KAFKA_BOOTSTRAP_SERVERS'],
    'client.id': ENV['KAFKA_CLIENT_ID'],

    # Configuración de conexión conservadora (good neighbor)
    'socket.max.fails': 3,
    'connections.max.idle.ms': 180_000, # 3 minutos (era 5)
    'socket.connection.setup.timeout.ms': 10_000, # 10 segundos (era 30)
    'metadata.max.age.ms': 180_000, # 3 minutos (era 5)

    # Configuración de reintentos conservadora
    retries: 3,
    'retry.backoff.ms': 2000, # Más tiempo entre reintentos
    'max.in.flight.requests.per.connection': 3, # Menos requests concurrentes

    # Configuración de timeouts conservadora
    'message.timeout.ms': 30_000, # 30 segundos (era 1 minuto)
    'request.timeout.ms': 15_000, # 15 segundos (era 30)
    'delivery.timeout.ms': 60_000, # 1 minuto (era 2 minutos)

    # Configuración de batching para mejor performance
    'batch.size': 16_384, # 16KB
    'linger.ms': 10, # Esperar 10ms para batching
    'compression.type': 'snappy' # Compresión para reducir ancho de banda

    # Nota: buffer.memory y max.block.ms no son compatibles con rdkafka Ruby
  }

  # Si estamos en producción o staging, configurar seguridad
  if Rails.env.production? || Rails.env.staging?
    # Validar variables de seguridad requeridas
    security_vars = %w[KAFKA_SECURITY_PROTOCOL KAFKA_SASL_MECHANISM KAFKA_USERNAME KAFKA_PASSWORD KAFKA_CA_CERT]
    missing_security_vars = security_vars.select { |var| ENV[var].blank? }

    if missing_security_vars.any?
      Rails.logger.error("❌ Variables de seguridad faltantes: #{missing_security_vars.join(', ')}")
      Rails.logger.error("❌ Configuración de seguridad incompleta")
      return
    end

    # Configuración de seguridad usando variables de entorno
    security_config = {
      'security.protocol': ENV['KAFKA_SECURITY_PROTOCOL'],
      'sasl.mechanisms': ENV['KAFKA_SASL_MECHANISM'],
      'sasl.username': ENV['KAFKA_USERNAME'],
      'sasl.password': ENV['KAFKA_PASSWORD']
    }

    # Fusionar con la configuración existente
    config.producer_config.merge!(security_config)
    config.consumer_config.merge!(security_config)

    # Configurar certificado SSL usando variable de entorno
    ca_cert = ENV['KAFKA_CA_CERT']
    system_cert = '/etc/ssl/certs/ca-certificates.crt'

    # Método helper para configurar certificado
    configure_cert = lambda do |cert_path|
      config.producer_config[:'ssl.ca.location'] = cert_path
      config.consumer_config[:'ssl.ca.location'] = cert_path
      Rails.logger.info("✅ Usando certificado SSL: #{cert_path}")
    end

    # Método helper para usar certificado del sistema
    use_system_cert = lambda do
      return unless File.exist?(system_cert)

      configure_cert.call(system_cert)
    end

    if File.exist?(ca_cert)
      begin
        # Verificar que el certificado es legible
        cert_content = File.read(ca_cert)
        if cert_content.include?('BEGIN CERTIFICATE')
          configure_cert.call(ca_cert)
        else
          Rails.logger.warn("⚠️ El archivo de certificado existe pero no parece válido: #{ca_cert}")
          use_system_cert.call
        end
      rescue StandardError => e
        Rails.logger.error("❌ Error al leer el certificado: #{e.message}")
        use_system_cert.call
      end
    else
      Rails.logger.warn("⚠️ No se encontró certificado SSL en: #{ca_cert}")
      use_system_cert.call
    end

    # Verificar que se configuró algún certificado
    Rails.logger.error('❌ No se encontró un certificado válido') unless config.producer_config[:'ssl.ca.location']
  end

  # Manejo de errores básico
  config.error_handler = :result

  # Loguear la configuración detallada
  Rails.logger.info("🔧 Mercately::Kafka configurado con brokers: #{config.brokers.join(', ')}")
  Rails.logger.info("🔧 Mercately::Kafka configurado con client_id: #{config.client_id}")
  Rails.logger.info("🔧 Mercately::Kafka producer_config keys: #{config.producer_config.keys.join(', ')}")
  Rails.logger.info("🔧 Mercately::Kafka pool size: #{ENV.fetch('KAFKA_PRODUCER_POOL_SIZE', nil)}")
  Rails.logger.info("🔧 Mercately::Kafka max idle time: #{ENV.fetch('KAFKA_PRODUCER_MAX_IDLE_TIME', nil)}s")

  # Log configuraciones críticas
  if config.producer_config[:'ssl.ca.location']
    Rails.logger.info("🔧 SSL CA location: #{config.producer_config[:'ssl.ca.location']}")
  end
  Rails.logger.info("🔧 Security protocol: #{config.producer_config[:'security.protocol']}")
  Rails.logger.info("🔧 Retries: #{config.producer_config[:retries]}")
  Rails.logger.info("🔧 Message timeout: #{config.producer_config[:'message.timeout.ms']}ms")
end
