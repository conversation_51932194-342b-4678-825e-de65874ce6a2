# frozen_string_literal: true

if defined?(<PERSON><PERSON><PERSON>)
  # Cambiar el nivel de log a INFO para eliminar los mensajes de polling
  Karafka.logger.level = Logger::INFO

  # Suscribirse a eventos importantes de Karafka
  Karafka.monitor.subscribe('consumer.consume') do |event|
    puts "\e[1;34m🔔 Karafka consumer.consume: topic=#{event[:caller].topic}, partition=#{event[:caller].partition}, messages=#{event[:caller].messages.count}\e[0m"
  end

  Karafka.monitor.subscribe('consumer.consumed') do |event|
    puts "\e[1;32m✅ Karafka consumer.consumed: topic=#{event[:caller].topic}, partition=#{event[:caller].partition}, messages=#{event[:caller].messages.count}\e[0m"
  end

  # Suscribirse a errores para mostrarlos claramente
  Karafka.monitor.subscribe('error.occurred') do |event|
    puts "\e[1;31m❌ Karafka error: #{event[:error]}\e[0m"
  end

  # Log cuando <PERSON> se inicia y detiene
  <PERSON>.monitor.subscribe('app.initialized') do
    puts "\e[1;35m🚀 Karafka inicializado correctamente\e[0m"
  end

  Karafka.monitor.subscribe('app.running') do
    puts "\e[1;35m⚡ Karafka en ejecución\e[0m"
  end

  Karafka.monitor.subscribe('app.stopping') do
    puts "\e[1;33m🛑 Karafka deteniéndose...\e[0m"
  end
end
