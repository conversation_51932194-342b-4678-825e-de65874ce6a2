# frozen_string_literal: true

# Decorador temporal para ApplicationService de mercately-karafka
#
# Este decorador sobrescribe el ApplicationService de la gema mercately-karafka
# para que funcione igual que en develop, hasta que se pueda arreglar en la gema.
#
# Autor de la gema: Pablo (usuario actual)
# TODO: Arreglar en la gema mañana para que no sobrescriba ApplicationService

Rails.application.config.after_initialize do
  if defined?(ApplicationService)
    Rails.logger.info('🔧 [APP-SERVICE-DECORATOR] Aplicando decorador para ApplicationService de mercately-karafka')

    # Sobrescribir completamente el ApplicationService para que funcione como en develop
    ApplicationService.class_eval do
      # Redefinir el método de clase call para manejar keywords correctamente
      def self.call(*args, **kwargs, &block)
        if kwargs.any?
          new(*args, **kwargs, &block).call
        else
          new(*args, &block).call
        end
      rescue StandardError => e
        Rails.logger.error("Error in #{name}: #{e.class} - #{e.message}")
        Rails.logger.error(e)
        raise e
      end

      # Asegurar que el método de instancia call esté definido
      def call
        raise NotImplementedError, 'Subclasses must implement this method'
      end

      private

      def log_info(message)
        Rails.logger.info(message)
      end

      def log_error(message)
        Rails.logger.error(message)
      end
    end

    Rails.logger.info('✅ [APP-SERVICE-DECORATOR] ApplicationService decorado exitosamente')
    Rails.logger.info('📝 [APP-SERVICE-DECORATOR] TODO: Arreglar en mercately-karafka mañana')
  else
    Rails.logger.error('❌ [APP-SERVICE-DECORATOR] ApplicationService no está definido')
  end
end
