# frozen_string_literal: true

# Este inicializador configura rdkafka globalmente para usar la configuración correcta
# de Kafka en todos los entornos.

if defined?(Rdkafka)
  # No necesitamos configurar nada aquí, ya que 000_rdkafka_patch.rb
  # ya ha configurado todas las variables de entorno necesarias.

  # Configuración global para rdkafka
  Rdkafka::Config.logger = Rails.logger

  Rails.logger.info('Inicializador rdkafka_config cargado correctamente')
end
