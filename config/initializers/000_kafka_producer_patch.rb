# Temporary patch for Mercately::<PERSON><PERSON><PERSON>::Producer to add missing flush method
# This resolves the "undefined method `flush'" error when processing multiple campaign messages

if defined?(Mercately::Ka<PERSON>ka::Producer)
  class Mercately::Kafka::Producer
    # Add flush method if it doesn't exist
    unless method_defined?(:flush)
      def flush
        # The mercately-kafka gem handles delivery asynchronously
        # We don't need to manually flush, but we provide this method
        # to maintain compatibility with code that expects it
        Rails.logger.debug("🔄 KAFKA_PATCH: flush called on Mercately::Kafka::Producer (no-op)")
        true
      end
    end
  end
  
  Rails.logger.info("✅ KAFKA_PATCH: Added flush method to Mercately::Kafka::Producer")
else
  Rails.logger.warn("⚠️ KAFKA_PATCH: Mercately::Kafka::Producer not found, patch not applied")
end
