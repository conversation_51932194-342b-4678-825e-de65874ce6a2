# DEPRECADO: Este archivo contenía URLs y credenciales hardcodeadas.
#
# IMPORTANTE: Todas las variables de entorno de Kafka deben configurarse
# externamente (en el sistema operativo, Docker, etc.) antes de iniciar
# la aplicación.
#
# Consulta .env.example para ver todas las variables requeridas.
#
# Variables críticas que DEBEN estar configuradas:
# - KAFKA_BOOTSTRAP_SERVERS
# - KAFKA_CLIENT_ID
# - KAFKA_SECURITY_PROTOCOL
# - KAFKA_SASL_MECHANISM
# - KAFKA_USERNAME
# - KAFKA_PASSWORD
# - KAFKA_CA_CERT
#
# Para validar la configuración, ejecuta: bin/validate-kafka-config

Rails.logger.info("📋 kafka_env.rb cargado - usando variables de entorno externas")
Rails.logger.info("💡 Para validar configuración: bin/validate-kafka-config")
