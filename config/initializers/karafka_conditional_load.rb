# frozen_string_literal: true

# Carga condicional de Karafka
# Este inicializador maneja la carga de mercately-karafka solo en nodos que lo necesiten
# para evitar interferencias con mercately-kafka en nodos productores.

Rails.logger.info('🔧 [KARAFKA-LOAD] Evaluando carga condicional de Karafka...')

# Determinar si este nodo debe cargar Karafka
# Detectar automáticamente el entorno y configurar apropiadamente
should_load_karafka = case Rails.env
                      when 'test'
                        ENV['KAFKA_CONSUMER_ENABLED'] == 'true'
                      when 'development'
                        # En desarrollo, cargar si está habilitado O si estamos usando Docker
                        ENV['KAFKA_CONSUMER_ENABLED'] == 'true' ||
                        ENV['KAFKA_BOOTSTRAP_SERVERS']&.include?('kafka:29092') ||
                        File.exist?('docker-compose.kafka.yml')
                      else
                        ENV['KAFKA_CONSUMER_ENABLED'] == 'true' || ENV['API_MODE'] == 'true'
                      end

Rails.logger.info("🔧 [KARAFKA-LOAD] KAFKA_CONSUMER_ENABLED: #{ENV.fetch('KAFKA_CONSUMER_ENABLED', nil)}")
Rails.logger.info("🔧 [KARAFKA-LOAD] API_MODE: #{ENV.fetch('API_MODE', nil)}")
Rails.logger.info("🔧 [KARAFKA-LOAD] Should load Karafka: #{should_load_karafka}")

if should_load_karafka
  begin
    # Asegurar que nuestro ApplicationService esté definido antes de cargar mercately-karafka
    Rails.logger.info('🔧 [KARAFKA-LOAD] Verificando ApplicationService antes de cargar mercately-karafka')

    # Intentar cargar mercately-karafka
    require 'mercately/karafka'
    Rails.logger.info('✅ [KARAFKA-LOAD] mercately-karafka cargado exitosamente')

    # Verificar que nuestro ApplicationService sigue disponible
    if defined?(MercatelyApplicationService)
      Rails.logger.info('✅ [KARAFKA-LOAD] MercatelyApplicationService disponible')
    else
      Rails.logger.error('❌ [KARAFKA-LOAD] MercatelyApplicationService no disponible')
    end
    
    # Verificar que Karafka esté disponible
    if defined?(Karafka)
      Rails.logger.info('✅ [KARAFKA-LOAD] Karafka está disponible y listo para configurar')
    else
      Rails.logger.error('❌ [KARAFKA-LOAD] Karafka no está disponible después de cargar mercately-karafka')
    end
  rescue LoadError => e
    Rails.logger.error("❌ [KARAFKA-LOAD] Error cargando mercately-karafka: #{e.message}")
    Rails.logger.error('❌ [KARAFKA-LOAD] Esto puede ser normal si la gema no está instalada en este nodo')
  rescue StandardError => e
    Rails.logger.error("❌ [KARAFKA-LOAD] Error inesperado cargando mercately-karafka: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))
  end
else
  Rails.logger.info('🔧 [KARAFKA-LOAD] Karafka no será cargado en este nodo (nodo productor)')
  Rails.logger.info('🔧 [KARAFKA-LOAD] Solo mercately-kafka estará disponible para producción de mensajes')
end
