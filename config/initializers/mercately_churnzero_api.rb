# frozen_string_literal: true

MercatelyChurnzeroApi.configure do |config|
  config.api_key = ENV.fetch('CHURN_ZERO_APP_KEY', nil)
  config.base_url = ENV.fetch('CHURN_ZERO_BASE_URL', nil)
  config.zapier_base_url = ENV.fetch('CHURN_ZERO_ZAPIER_BASE_URL', nil)
  config.zapier_events_path = ENV.fetch('CHURN_ZERO_ZAPIER_EVENTS_PATH', nil)
  config.jwt_secret_key = ENV.fetch('CHURN_ZERO_JWT_SECRET', nil)
  config.deals_base_url = ENV.fetch('COREHUB_BASE_URL', nil)
  config.deals_jwt_secret_key = ENV.fetch('JWT_SECRET_KEY', nil)
  config.shops_auth_token = ENV.fetch('MERCATELY_AUTH_TOKEN', nil)
  config.shops_base_url = ENV.fetch('SHOPS_FULL_URL', nil)
end
