# config valid only for current version of Capistrano
# lock '3.6.0'
set :application, 'mercately'
set :repo_url, '**************:ThoughtCode/mercately.git' # Edit this to match your repository
set :deploy_via, :remote_cache
set :deploy_to, '/home/<USER>/public_html'
set :pty, true
set :linked_files, %w{config/database.yml config/secrets.yml config/puma.rb config/mongoid.yml config/storage.yml}
set :linked_dirs, %w{log tmp/pids tmp/cache tmp/sockets vendor/bundle public/system public/uploads}
set :keep_releases, 5
set :rvm_type, :user
set :rvm_ruby_version, 'ruby-3.2.5' # Edit this if you are using MRI Ruby
set :rvm_custom_path, '~/.rvm'
set :systemd_unit, -> { "#{fetch :application}.target" }
set :systemd_use_sudo, true
set :systemd_roles, %w(app)
#Custom Daemons
namespace :deploy do
  desc "restart mercately"
  task :restart_mercately do
    on roles(:cron), in: :sequence, wait: 5 do
      execute :sudo, :systemctl, :restart, :mercately
    end
  end

  desc "restart mercately"
  task :restart_mercately2 do
    on roles(:web), in: :sequence, wait: 5 do
      execute :sudo, :systemctl, :restart, :mercately
    end
  end

  desc "restart mercately"
  task :restart_mercately3 do
    on roles(:api), in: :sequence, wait: 5 do
      execute :sudo, :systemctl, :restart, :mercately
    end
  end

  desc "restart mercately"
  task :restart_mercately4 do
    on roles(:karafka), in: :sequence, wait: 5 do
      execute :sudo, :systemctl, :restart, :mercately
    end
  end

  desc "restart karakfa"
  task :restart_karafka do
    on roles(:karafka), in: :sequence, wait: 5 do
      execute :sudo, :systemctl, :restart, :karafka
    end
  end

  desc "restart sidekiq"
  task :restart_sidekiq do
    on roles(:cron), in: :sequence, wait: 5 do
      execute :sudo, :systemctl, :restart, :sidekiq
    end
  end

  desc "restart sidekiq"
  task :restart_sidekiq2 do
    on roles(:web), in: :sequence, wait: 5 do
      execute :sudo, :systemctl, :restart, :sidekiq
    end
  end


  after :finishing, :restart_mercately
  after :finishing, :restart_mercately2
  after :finishing, :restart_mercately3
  after :finishing, :restart_mercately4
  after :finishing, :restart_sidekiq
  after :finishing, :restart_sidekiq2
  after :finishing, :restart_karafka
end
