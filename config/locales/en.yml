en:
  locale: 'EN'
  hello: 'Hello %{name},'
  loading: 'Loading'
  funnels:
    title: Funnels
  orders:
    title: My Business
  products:
    title: My Business
  new_stats:
    title: 'Stats'
  sales_stats:
    title: 'Stats'
  tags_stats:
    title: 'Stats'
  funnels_stats:
    title: 'Stats'
  chatbot_ai:
    title: 'Automations'
    error: 'An error occurred'
    platforms:
      negative_balance: 'You currently have a negative balance in your wallet, please recharge to activate MIA again.'
  chatbots:
    title: 'Automations'
    errors:
      activation_rules: 'Activate for new conversations only and activate for outbound messages can not be activated at the same time, choose only one please'
  quick_answers:
    title: CRM
  growth_tools:
    title: 'Growth tools'
  mongoid:
    errors:
      messages:
        message_title: 'Message'
        summary_title: 'Summary'
        resolution_title: 'Resolution'
        no_client_config:
          message: 'No client config found'
          summary: 'No summary config found'
          resolution: 'No resolution config found'
  navbar:
    funnels:
      title: Funnels
    templates:
      title: Fast answers
    react_pages:
      title: CRM
  horizontal_navbar:
    calendar: 'Calendar'
    help: 'Help'
    configurations: 'Settings'
    team: 'Team'
    payment_plans: 'Payment Plan'
    api_key: 'API Key'
    logout: 'Logout'
    free_tier: Free chats
    included_contacts: Included contacts in plan
    change_status: Set yourself as
    status_available: active
    status_away: away
    refer_and_earn: "Share and Earn"
    live_training: "Live Training"
    view_tutorial: "View tutorials"
    support: "Support"
    failed_charge:
      1: 'Hello %{retailer}, unfortunately we could not execute the charge for the renewal of your plan.'
      2: 'Please, review your payment method as soon as possible so as not to suffer interruptions in the service'
      3: 'here.'
      4: 'Thank you very much for your support. 💪'
    insufficient_balance_alert:
      1: 'Your WhatsApp balance is: $%{ws_balance}, if your balance is less than $-5 you will lose your connection, do not let it happen and top up your balance here:'
      2: 'Add balance'
      3: 'If you want to know the policy click here:'
      4: 'WhatsApp policy'
      5: 'Click here to know a detailed consumption:'
      6: 'Consumption'
    timezone_alert: 'You do not have the time zone configured for sending the campaigns, you can configure it'
    uncompleted_payment: 'Your account has a payment that must be verified for security.'
    cta_complete_alert: 'Please click here and check it out.'
    cta_complete2_alert: 'Click here to complete'
  mailer:
    broken_hs_integration:
      broken_integration: 'Hubspot integration disabled'
      p1: 'Your Hubspot integration has been disabled, this can happen by multiple reasons,'
      p2: 'the most common ones are password changing or account inactivation by Hubspot.'
      p3: 'To re-integrate with Hubspot, go to '
    whatsapp_connection:
      disconnect_notice: You have disconnected from WhatsApp. Please reconnect.
    failed_charge_subject: '[Urgent] Your Mercately payment was not successful'
    failed_charge_greetings: 'Hello %{name}'
    failed_charge_message: 'We were unable to charge your card %{card} for your payment to Mercately, LLC. Update your billing information at the following link:'
    failed_charge_advice: 'In case of 3 failed payments, your service will be canceled.'
    imported_customers_subject: 'Your customers have been imported successfully'
  vertical_navbar:
    business:
      edit: My Business
    clients:
      clients: 'CRM'
      clients_list: 'Clients List'
      custom_fields: 'Custom Fields'
      client_groups: 'Groups'
    business:
      store_config: 'Store configuration'
      my_ecommerce: 'My store'
    products:
      products: 'Products'
    categories:
      categories: 'Categories'
    orders:
      orders: 'Orders'
      orders_list: 'Orders List'
      sales_channels: 'Sales channels'
      digital_payments: 'Digital payments'
      custom_fields: 'Custom fields'
    funnels:
      funnels: 'Funnels'
    campaigns:
      campaigns: 'Campaigns'
    ml:
      questions: 'Questions'
      chats: 'Chats'
    automations:
      automations: 'Automations'
      chatbots: 'Flows'
      chatbot_ai: 'MIA Chatbot con IA'
      reminders: 'Scheduled messages'
      autoassigns: 'Autoassigns'
      shopify: 'Shopify'
    messaging_configuration:
      messaging_configuration: 'Messaging Configuration'
      canned_responses: 'Canned Responses'
      whatsapp_templates: 'WhatsApp Templates'
      welcome_messages: 'Welcome Messages'
      tags: 'Tags'
      automatic_assignment: 'Automatic Assignment'
      messaging_rules: Messaging Rules
      conversation_topics: Conversation Topics
    statistics:
      statistics: 'Statistics'
      basic: 'Basic'
      advanced: 'Messaging'
      sales: 'Sales'
    integrations:
      integrations: 'Integrations'
    messaging:
      inbox: 'Inbox'
    growth_tools:
      automations: 'Growth tools'
    knowledge:
      mercately_academy: 'Mercately Academy'
  activerecord:
    attributes:
      plan:
        name: Name
        description: Description
      addon:
        name: Name
        description: Description
      pricing_tier:
        frequency: Frequency
        price: Price
      chat_bot:
        platforms:
          whatsapp: WhatsApp
          messenger: Messenger
          instagram: Instagram
      chat_bot_option:
        option_types:
          decision: decision
          form: form
          api: API
        interactive:
          text: Text
          buttons: Quick reply buttons
          list: Interactive list
          fake: Menu Option
      order:
        statuses:
          success: Success
          pending: Pending
          cancelled: Cancelled
          canceled: Canceled
      product:
        conditions:
          new_product: New Product
          used: Used
          not_specified: Not specified
      automatic_answer:
        statuses:
          active: Active
          inactive: Inactive
      plan_cancellation:
        reasons:
          expensive: The price is too high
          not_help: The platform did not help me to improve my communication and sales
          add_more_functionality: Needs to incorporate more functionalities
          never_used: I never used the service
          unsupported_from_mercately: I did not receive help and support from Mercately
          other: Other
      retailer/retailer_schedules:
        closing_time: Closing time
      template:
        image: File
      agent_hubspot_owner:
        retailer_user_id: ''
      contact_group:
        name: List name
    errors:
      models:
        academy_video:
          attributes:
            external_url:
              invalid_url: "must be a valid URL"
  time:
    datepicker:
      format: DD/MM/YYYY
      cancelLabel: Cancel
      applyLabel: Apply
      fromLabel: From
      toLabel: To
      customRangeLabel: Edit date
      weekLabel: Wheek
      today: Today
      yesterday: Yesterday
      last_7_days: Last 7 days
      last_30_days: 'Last 30 days'
      this_month: This month
      last_month: Last month
      daysOfWeek:
        sun: 'Sun'
        mon: 'Mon'
        tues: 'Tues'
        wed: 'Wed'
        thurs: 'Thurs'
        fri: 'Fri'
        sat: 'Sat'
      monthNames:
        jan: 'January'
        feb: 'February'
        mar: 'March'
        apr: 'April'
        may: 'May'
        jun: 'June'
        jul: 'July'
        aug: 'August'
        sep: 'September'
        oct: 'Octubre'
        nov: 'November'
        dec: 'December'
    year: Year
    month: Month
    day: Day
  social_networks:
    whatsapp: Whatsapp
    messenger: Messenger
    mercadoLibre: MercadoLibre
    instagram: Instagram
  retailer:
    calendar_events:
      create:
        success: "Reminder created successfully."
      update:
        success: "Reminder updated successfully."
    statistics:
      messenger_service: Messaging service
      description: Statistics of sales, messaging and others
      card:
        messages: Messages
        received: Received
        sent: Sent
        total: Total
      chart:
        title: Total messages by platform / social network
        total_messages: Total messages
      cliente_content:
        clients: Clients
        new_clients: New clients
        recurring_customers: Recurring customers
      performance:
        performance_title: Performance by agent
        agent: Agent
        assigned_chats: Assigned chats
        assigned_chats_answered: Assigned chats answered
        assigned_chats_without_replying: Assigned chats without replying
        mensajes: Messages
        total_clients: Total clients
        recurring_customers: Recurring customers
        new_clients: New clients
      chats_by_platform:
        title: Total chats by platform
        platform: Platform
        total_chats_assigned: Total chats assigned
        total_assigned_chats_answered: Total assigned chats answered
        total_assigned_chats_without_replying: Total assigned chats without replying
      automatic_answer:
        statuses:
          active: Active
          inactive: Inactive
      mia:
        process_stats_success: MIA stats processed successfully
        process_stats_error: Error processing MIA stats
        invalid_token: Invalid token
        retailer_not_found: Retailer not found
        invalid_parameters: Invalid parameters
    settings:
      quick_answers:
        answer_created_successfully: Response created successfully
        answer_updated_successfully: Response updated successfully
        answer_removed_successfully: Response removed successfully
        not_have_permissions_on_answer: You do not have permissions on the response
        breadcrumb:
          root: Dashboard
        list:
          page_title: Canned Responses
          description: Canned responses list
          create_answer: Create response
          type: Type
          usability: Usability
          image_with_caption: Image with caption
          file_with_caption: File with caption
          text: Text
          global: Global
          personal: Personal
          with_additional_answers: "** Has additional responses"
          placeholder_search: Search canned responses
        new:
          create_answer: Create response
        edit:
          edit_answer: Edit response
          view_answer: View response
        show:
          quick_answer: Canned response
          description: Response information
        form:
          description: Response information
          select_file: Select file
          texto_of_answer: Text of response
          alert_global_answer_question: Global response can be sent by all agents. Those that are not, can only be sent and modified by the agent who created them.
          alert_instagram_answer_question: Instagram only accepts images
          mercado_libre_question: MercadoLibre Questions
          note_mercado_libre_question: 'Note: For questions, do not include contact information, such as phone numbers, exact addresses, or personal names.'
          mercado_libre_messages: MercadoLibre Messages
          additional_fast_answers: Additional canned responses
          add_additional_fast_answer: Add response
          cancel: Cancel
          message_type:
            text: Text
            text_image: Text with image
            text_pdf: Text with PDF
            audio: Audio
          new_additional_fast_answer:
            add_image_or_pdf: Add image or PDF
          edit_additional_fast_answer:
            update_image_or_pdf: Update image or PDF
        common:
          title: Title
          content: Content
          platforms: Platforms
          actions: Actions
          image: Image
          file: File
          audio: Audio
          with_caption: ' with caption'
          global_answer_question: is a global response?
          confirm_remove_text: Are you sure to delete the response?
          additional_answer_input_label: Content
          error_size_message: The image must be maximum 5MB and the PDF maximum 25MB
          choose_platforms: Choose platforms
          choose_kind: Choose message type
          error_only_image: only can be image
          error_only_pdf: only can be PDF
          error_only_audio: only can be MP3 audio
          error_only_video: only can be MP4 video
          error_only_text: can't be attached to text message type
          error_only_image_pdf: only can be image or PDF
          error_only_image_pdf_audio: only can be image, PDF or audio
      welcome_messages:
        title: Welcome Messages
        messages_for_new_clients: Message for new customers
        status: Status
        text: Text
        message_text: Message text
        messages_for_inactived_clients: Message for inactive customers
        inactivity_time: Inactivity time
      automatic_answers:
        message_not_found: Message not found
        message_saved_successfully: Message saved successfully
        all_fields_is_required: All fields are required
      business_rule:
        title: Messaging rules
        additional_data:
          schedule: Working hours
    profile:
      my_account:
        index:
          account_config: Account configuration
          commerce_config: Commerce configuration
          integration_config: Integrations
          platform_config: Platform configuration
          whatsapp_config: WhatsApp configuration
          whatsapp:
            title: WhatsApp Settings
            ws_templates: WhatsApp Templates
            description: Create your WhatsApp Templates to contact your clients
            ws_widget: WhatsApp Widget
            ws_widget_desc: Add a WhatsApp widget to your website
          title: My account
          breadcrumb:
            root: Dashboard
            my_account: My account
          my_data:
            title: My data
            description: Configure your account options
          my_business:
            title: My shop
            description: Configure your business options
          my_team:
            title: My team
            description: Add or remove members in your team
          my_api_keys:
            title: API Key
            description: Configure your API Key and connect with other systems
          my_integrations:
            title: Integrations
            description: Integrate all platforms from here
          my_payment_plans:
            title: Payment plans
            description: Payment plans you have in Mercately
          notification_settings:
            title: Notification settings
            description: Set your notifications
      my_business:
        edit:
          general_information:
            warning: 'When changing the logo or the background image, it is changed only in the business Shop, not in the WhatsApp profile'
            logo_recomendations: 'For the logo is recommended an image of 100px x 100px.'
            bg_recomendations: 'For the background image is recommended a resolution of 2500px x 400px.'
            shop_main_color: "Catalog's main color"
            menu: General information
            name: Business name
            country_code: 'Country'
            slug: Catalog's name
            slug_description: "Catalog's name is the subdomain for the shop, example: "
            description: Business description
            phone: Phone number (optional)
            tax: Tax
            active_shop: 'Virtual catalog is active'
            deactive_shop: 'Virtual catalog is inactive'
            hide_product_prices: 'Hide prices'
            timezone: Timezone
            currency: Currency
            rotate_images_in_catalog: 'Rotate images in catalog'
            terms_and_conditions: 'Terms and conditions PDF'
          location:
            menu: Locations
            address: Address
            city: City
            state: State or province
            zip_code: ZIP Code
            country: Country
            title: "Locations"
            add: "Add address"
            edit: "Edit address"
            name: "Business address descriptor"
            save: Save address
            success: Successful saved address
            remove_msg: "Are you sure you want to remove this address?"
          contacts:
            menu: Contacts
            title: "Social networks"
            facebook: "Facebook URL"
            instagram: "Instagram URL"
            twitter: "Twitter URL"
            tiktok: "TikTok URL"
            whatsapp: "WhatsApp phone number"
          schedules:
            menu: Schedules
            schedule: Schedule
            working_days: Working days
            open: "Open"
            close: "Close"
            active: 'Opened'
            inactive: 'Closed'
            monday: Monday
            tuesday: Tuesday
            wednesday: Wednesday
            thursday: Thursday
            friday: Friday
            saturday: Saturday
            sunday: Sunday
            invalid_time: "must be greater than opening time"
          shipping:
            menu: Shipping methods
            active: Active
            inactive: Inactive
          domain:
            menu: Domain
            title: Domain
            domain: Domain
            ssl: Certificate
            ssl_key: Certificate key
            success: Successfully saved domain
            ssl_warning: 'The SSL certificate is required to use the domain'
            failed: Failed to save domain
          payment_methods:
            menu: Payment methods
          submit: Save
          success: Successfully saved business
          edit: "Edit"
          delete: "Delete"
        delete:
          success: Successfully deleted address

      payment_plans:
        index:
          retry: Reactivate
          pay: Pay
          inactive_plan: Your plan has been deactivated, update your card and click on the button to reactivate it.
          unpaid_plan: It was not possible to charge your plan, update your card and click on the button to pay it.
          title: Payment plans
          subtitle: Payment plans
          information: Information
          consumption: Consumption
          current_plan: Current plan
          add_balance: Add balance
          plan: Plan
          ws_balance: WhatsApp balance
          next_pay: Next payment
          cancel_plan: Cancel plan
          desist_cancel_button: I no longer want to cancel my plan
          country_prices: View prices by country
          my_cards: My cards
          add_card: Add card
          charges_history: Top-up history
          cost: Cost
          status: Status
          card: Card
          paid: Paid
          authorization: Authorization
          reference: Reference
          payments_history: Payment history
          ws_consumption: WhatsApp consumption
          monthly_cost: Monthly cost
          message_type: Message type
          consumption_cost: Consumption cost
          messages: Messages
          subtotal: Subtotal
          chatbots_interactions: ChatBots interaction
          interactions: Interactions
          payment_success: Paid
          payment_pending: Pending
          payment_cancelled: Cancelled
          payment_failed: Failed
          payment_refund: Refund
          card_not_found: Card not found
          message_type_conversation: Conversation
          message_type_notification: Notification
          delete_card_confirmation: Are you sure to delete this card?
          payment_methods: Payment methods
          update_card_confirmation: Are you sure to update this card?
          whatsapp_conversations: Conversations cost
          conversation_type: Conversation type
          conversation_total: Total
          conversation_cost_total: Cost
          free_entry_point: Free per entry point
          free_tier: Free per monthly tier
          free_uic: Free user initiated
          free_bic: Free business initiated
          free_mia: Free answered by AI
          paid_uic: User initiated paid
          paid_bic: Business initiated paid
          paid_mia: AI paid.
          desist_cancellation_confirmation: Are you sure not to cancel your plan?
          marketing_paid: Marketing paid
          authentication_paid: Authentication paid
          utility_paid: Utility paid
          service_paid: Service paid
          negative_balance: "You currently have a negative balance in your wallet, please recharge to activate MIA again."
        charge_balance:
          add_balance: Add balance
          minimum_amount: The minimum amount is $10. The amount is in $USD
          recharge_alert: Remember that your WhatsApp balance and your Mercately plan are different. Top up balance does not mean a plan payment.
          more_info: More info...
        cancel_plan:
          question_header: Are you sure you want to leave Mercately?
          answer_header: Thank you very much for your answer
          details_body: Please tell us the reasons why you no longer want to continue with the service, your feedback will be of great help to continue improving
          high_price_option: The price is too high.
          not_help_option: The platform did not help me to improve my communication and sales.
          more_functionality_option: Needs to incorporate more functionalities.
          not_used_option: I never used the service.
          unsupported_from_mercately_option: I did not receive help and support from Mercately.
          other_option: Other.
          cancellation_advise: Your plan will be canceled immediately. After this you will not be able to perform your daily actions in Mercately.
          cancellation_advise_2: Your plan will continue to be active until one day before your next payment date.
          comment_placeholder: Leave a comment
          cancel_button: Cancel plan
          confirmation_advise: Remember that by leaving Mercately you will be losing
          advise_1: The ability to manage your WhatsApp line with multiple agents.
          advise_2: Personal and Business WhatsApp services with your phone line.
          advise_3: The data of clients that you managed with us.
          advise_4: The chats of people who chatted with your company.
          advise_5: The time invested in improving communication and sales with our service.
          unsubscribe_success: Plan canceled successfully
        add_card:
          add_card: Add card
          cardholder_name: Cardholder Name
        set_as_main: Set as main card
        errors:
          invalid_plan: "Invalid plan selected for payment plan"
        month_interval_label:
          monthly: Monthly
          bimonthly: Bimonthly
          trimonthly: Trimonthly
          six_monthly: Six-monthly
          yearly: Yearly
    paymentez:
      added_card_success: Card added successfully
      added_card_error: Error adding card
      deleted_card_success: Card successfully removed.
      deleted_card_error: Failed to delete card.
      added_balance_success: Balance successfully added
      added_balance_error: Error adding balance
      updated_card_success: Card updated successfully
      updated_card_error: Error updating card
    chargebee:
      messages:
        subscription_success: Subscription successfully created
        subscription_not_found: Subscription not found
        update_subscription_success: Subscription successfully updated
        reactivate_subscription_success: Subscription successfully reactivated
    payment_methods:
      added_payment_method_success: Payment method successfully added.
      added_payment_method_failed: Error adding payment method.
      deleted_payment_method_success: Payment method removed successfully.
      payment_method_not_found: Payment method not found.
    plan_cancellations:
      canceled_plan_success: Plan successfully canceled
      canceled_plan_error: An error occurred while canceling the plan
      desist_cancel_plan_success: Your plan will no longer be canceled
      desist_cancel_plan_error: An error occurred while processing the action
    stripe:
      added_balance_success: Balance successfully added
      added_balance_error: Error adding balance
    integrations:
      errors:
        connect:  Connection error
        disconnect: Disconnection error
      common:
        connect_success:  Successful connection
        disconnect_success: Successful disconnection
        disconnect: Disconnect
        connect: Connect
      hubspot:
        sync_conversation:
          select_time_period: Select time period
          updated_fields: Campos fields
      shopify:
        messages:
          config_updated: Updated configuration
        errors:
          error_to_update_config: Error updating configuration
          store_not_found: Store not found
        sync_product:
          title: Sync products from Shopify to Mercately
          desc: Shopify products will be synced from Shopify to Mercately. Any product updates in Mercately will not be reflected in Shopify. With this sync, Shopify will be the single source of truth.
          text_button: Synchronize products
    campaigns:
      title: Campaigns
    breadcrumb:
      campaigns: Campaigns
      messaging_rules: Messaging rules
    team_assignment:
      errors:
        select_platform: At least one platform must be selected
        unique_default: A team with the default assignment already exists
  customer:
    sync_conversation:
      ws_conversations: WhatsApp conversations
      msn_conversations: Messenger conversations
      ig_conversations: Instagram conversations
  widget_config:
    validates:
      unique_by_platform: There is already a widget for this platform
    mailers:
      subjet_widget_script: WhatsApp Widget
  buttons:
    back: Back
    edit: Edit
    remove: Remove
    save: Save
    nested_remove: Remove
    send: Send
  actions:
    view: View
    edit: Edit
    remove: Remove
  views:
    customers:
      title: "Customers"
      introduction: "View Introduction"
      exported_customers: "Exported Customers"
      custom_fields: "Custom Fields"
      add_customer: "Add Customer"
      search_placeholder: "Search by name, phone or email"
      search_button: "Search"
      filters: "Filters"
      import: "Import"
      export: "Export"
      csv: "CSV"
      excel: "EXCEL"
      columns:
        full_name: "Full Name"
        phone: "Phone"
        assigned_to: "Assigned To"
        channel: "Channel"
        actions: "Actions"
      apply_filters: "Apply Filters"
      filters_title: "Filters"
      agent: "Agent"
      lists: "Lists"
      tags: "Tags"
      funnel: "Funnel"
      funnel_stage: "Funnel Stage"
      creation_date: "Creation Date"
      last_interaction: "Last Interaction Date"
      sort_by: "Sort By"
      close: "Close"
      notes: "Notes"
      export:
        notification: "Export in progress. We will send you an email when it is ready. If you do not receive the email, download it from 'Exported Customers' in the CRM menu."
    exported_customers:
      title: "Exported Customers"
      view_introduction: "View Introduction"
      creation_date: "Creation Date"
      expiration_date: "Expiration Date"
      link: "Link"
    dashboard:
      demo:
        title: Plan a demo
        description: Contact a specialist to schedule a demo from here.
      whatsapp:
        title: Connect your WhatsApp number
        description: Connect your number in minutes, improve productivity on WhatsApp and automate your sales.
      plan:
        title: Choose your plan
        description: Select the plan that best suits you and unlock the power of Mercately.
      begin: Start
      greetings: 'Check what is happening in your commerce 🙌'
      quick_check: 'Quick look'
      successful_sales: 'Sales'
      customers_registered: 'Customers registered'
      messages: 'Messages'
      events: 'Events'
      latest_news: 'Latest news'
      integrate_with: 'Integrate with'
      connected: 'Conected'
      configure: 'Configure'
      recent_orders: 'Recent orders'
      view_all: 'View all'
      orders:
        info: 'Information'
        status: 'Status'
        customer: 'Client / Channel'
        total: 'Total'
      best_sellers: 'Best sellers'
      no_products: 'No products to show'
      no_orders: 'No orders to show'
      no_news: 'No news to show'
  invoice:
    invoice: 'Invoice'
    paid: 'Paid'
    billed_to: 'Billed to'
    description: 'Description'
    amount: 'Amount'
    warning: 'Please keep a copy of this invoice for your records and for future reference.'
    questions: 'If you have billing or technical support questions, please email'
    thanks: 'Thank you for your business, and for using Mercately'
    team: 'The Mercately team'
    reference_code: 'Reference code'
    monthly_sub: 'Mercately Monthly Subscription'
    months_sub: 'Mercately %{months} Months Subscription'
    balance_recharge: 'Balance recharge'
    check: 'Check invoice'
    phone: 'Phone'
    email: 'Email'
    address: 'Address'
    quantity: 'Quantity'
    unit_price: 'Unit price'
    credits: 'Credits'
    detail_type:
      subscription: Subscripción Mensual - Mercately
      top_up: Recarga de Saldo
    company:
      national: 'Mercately LLC.'
      international: 'Mercately Inc.'
      id_fiscal: 'ID Fiscal: 85-1447103'
    currency:
      usd: 'Dollars'
      cents: 'Cents'
  retailer_user:
    status:
      success: Status changed successfully
      failed: Status change failed
    availability:
      success: Availability changed successfully
      failed: Availability change failed
      forbidden: You are not allowed to execute this action
  common:
    or: or
  register:
    button: Continue
    button_create: Create Account
    titles:
      company: Company*
      ad_investment: How much do you invest in advertising? (Meta Ads or Google Ads)*
      advertising: Advertising
      user_workers: How many users will you connect to Mercately?*
      users: Users
      current_ecommerce: Do you have an online store platform?*
      online_store: Online Store
      other_ecommerce: Please specify which one
      which: Which
      expectations: What problem do you want to solve?*
      id_attended: Are you being attended by a sales advisor?*
      market: What does your company sell?*
      work_area: What is your area of work?*
      area: Area
      user_role: What role do you play in your company?*
      role: Role
    overlay_left:
      heading: Scale your sales through WhatsApp
      li1: Automatically follow up with all your clients
      li2: Manage all your salespeople and client chats in one place
      li3: Integrate Mercately with your applications
    user:
      title: Hi
      caption: Ready to start?. Fill out your information to register.
      email: Email
      country_code: Country
      retailer_number: Phone Number
      password: Password
      accepts: By registering you accept our
      terms: terms and Conditions
      account: Do you already have an account?
      login: Login
      and_the: and the
      privacy_policy: privacy policy
      first_name: First Name
      last_name: Last Name
    complete_info:
      title: Tell us about your company
      caption: Now, tell us about you and your business.
      name: Company Name
  retailer_onboarding:
    button: Continue
    business_info:
      title: Industry of your company
      caption: What is your company's industry?
      business:
        automotive: Automotive
        advice_consulting: Advice and consulting
        beauty: Beauty
        education_training: Education and training
        massive_consume: Massive consume
        fashion_accessories: Fashion and accessories
        real_estate: Real estate
        financial: Financial
        bss_services: Services
        health_care: Health care
        retail_store: Retail Store
        travels_tourism: Travels and tourism
    company_info:
      title: Information about your company
      title_rol: What is your role in the company?
      user_role:
        c_level: C-Level
        manager: Manager
        leadership: Leadership
        team_leader: Team Leader
        collaborator: Collaborator
        leader: Leader
        ceo: CEO
      title_market: What does your company sell?
      market:
        products: Products
        mrk_services: Services
        both: Both
      title_workers: How many employees does your company currently have?
      title_user_workers: How many collaborators are going to occupy the platform?
      workers_options:
        one: 1 user
        to_five: from 2 to 5
        to_ten: from 6 to 10
        to_fifty: from 11 to 50
        more_fifty: from 50 to more
        two_to_five: 2 to 5 user
        more_than_five: more than 5 users
      ad_investment:
        no_investment: I do not invest in advertising
        less_than_500: Less than $500 usd
        between_500_and_2500: $500 to $2500 usd
        more_than_2500: More than $2500 usd
      id_attended:
        yes_attended: Yes, I'm with an advisor
        no_attended: 'No'
      work_area:
        finance: Finance
        marketing: Marketing
        accounting: Accounting
        logistics: Logistics
        administration: Administration
        commercial: Commercial
        legal: Legal
        human_resources: Human Resources
    expectations_info:
      title: What do you want to achieve with Mercately?
      caption: Choose the options you want to achieve
      expectations:
        sell_whatsapp: Sell ​​more on WhatsApp
        organization: Organize my clients and sellers
        ecommerce_whatsapp: Integrate Ecommerce with WhatsApp
        use_chatbot: Use Chatbots
        commercial_monitoring: Commercial monitoring
        create_catalogs: Create product catalog
        mass_messages: Send mass messages
        digital_payments: Receive digital payments
        connect_team_to_one_number: Connect my team to a single number
        have_whatsapp_chatbots: Have chatbots on WhatsApp
        improve_customer_service: Improve customer service
        automate_business_via_whatsapp: Automate my business via WhatsApp
    experience_info:
      title: Tell us your experience
      title_ecommerce: Do you have an Ecommerce platform?
      ecommerce:
        t: Yes, I have a platform
        f: No, I'm starting
      current_ecommerce:
        shopify: Shopify
        woocommerce: WooCommerce
        tienda_nube: Tienda Nube
        odoo: Odoo
        magento: Magento
        wix: Wix.com
        mercado_libre: Mercado Libre
        other: Other
        no_platform: I don't have a platform
      title_crm: What is your experience with CRMs?
      crm_experience:
        not_used_crm: I have not used a CRM.
        used_crm_before: I used a CRM before.
        using_crm: I am using a CRM.
        crm_admin: I am a CRM administrator.
      title_bot: What is your experience with Chatbots?
      bot_experience:
        not_used_bot: I have not used a chatbot.
        used_bot_before: I used a chatbot before.
        using_bot: I am using a chatbot.
        bot_admin: I am a chatbot administrator.
  integration_message_data:
    time_unit_to_ads:
      days: Days
      weeks: Weeks
      months: Months
  responsible:
    percentage_error: 'The total percentages added cannot exceed 100%'
  partner_commission:
    transaction_already_associated: This transaction is already associated with a commission for this retailer.
  partner:
    invalid_lang: is not a valid language
    invalid_number: Must contain only numbers
  partner_mailer:
    commissions_paid:
      subject: 🎉 Your Mercately Commission Has Been Paid! 🎉
      p1: Hello
      p2: We hope you're having an excellent day! 🚀
      p3: "We're pleased to inform you that your commission for this month has been successfully processed. 💸 Thanks to your effort and dedication, we've been able to continue growing together. Here are the details of your payment:"
      p4: Your commitment to Mercately is essential to our success, and we couldn't be more grateful for the trust you've placed in us. Thank you for being part of our community and for referring customers who, like you, value our solution.
      p5: "Together we grow:"
      p6: We want to continue supporting you every step of the way, so if you have any suggestions or need anything, don't hesitate to contact us. We're here to help you keep growing.
      p7: Remember that the more customers you refer, the higher your commissions will be. Let's keep turning conversations into sales together! 🌟
      p8: Here's to more success!
      p9: Best regards,
      p10: The Mercately Team
      li1: "Amount received:"
      li2: "Payment method:"
      li3: "Account:"
    retailer_deactivated:
      subject: ❌ Account Deactivation Notification
      p1: I hope you're doing well.
      p2: We want to inform you that, unfortunately, the account of your referral %{retailer_name} has been deactivated due to a payment failure. As a result, we will no longer be able to issue commissions for this account.
      p3: We know this is an unfortunate situation, but we want to assure you that you have our full support in trying to recover this customer. We are here to help you with whatever you need, whether it’s contacting the customer or exploring options to reactivate their account.
      p4: If you would like our assistance or have any ideas on how to approach this situation, please do not hesitate to contact us. Together, we can try to reverse this setback.
      p5: Thank you for your continued effort and commitment as our partner.
    retailer_payment_failed:
      subject: ⚠️ Notification of a Referral's Failed Payment
      p1: We want to inform you that we have encountered an issue with one of your referrals, %{retailer_name}, whose payment for this month has failed. If the payment cannot be processed, we may potentially be unable to disburse the corresponding commission.
      p2: "We would greatly appreciate it if you could follow up with the customer to help resolve this issue. Here are some steps that might be helpful:"
      p3: We are here to support you in every way needed. Our support team is at your disposal to ensure you can continue without any setbacks.
      p4: Thank you for your cooperation and for the excellent work you do as a partner.
      l1_b: "Contact the Customer:"
      l1_span: Inform the customer about the payment issue and offer your assistance.
      l2_b: "Explore Alternatives:"
      l2_span: Help them update their payment method or resolve any technical issues they might be facing.
  commission_invoice:
    error_marking_paid: Error marking commission invoice as paid.
    success_marking_paid: Commission invoice %{invoice_id} marked as paid. Email sent to %{partner_email}.
  active_admin:
    charges:
      disable_edit: A payment plan with a Chargebee subscription cannot be edited
      invoice_created: Invoice successfully generated.
      invoice_creation_failed: Error generating the invoice.
      month_interval_invalid: Months must be between 1 and 12.
      charges_attempt_success: Charges attempted successfully.
    retailers:
      user_not_found: User not found
      email_sent: Email sent successfully
      tz_updated: 'Timezones updated successfully'
  mia_integration:
    mailers:
      integration_confirm_email_subject: New AI integration
      delete_integration_confirm_email_subject: AI integration was removed
  role:
    errors:
      name:
        taken: Role has been taken
    controllers:
      chatbots:
        chats:
          accepted_response: You will be assisted by one of our agents shortly.
  campaigns:
    cancel:
      success: Campaign cancelled successfully
      error: Campaign can not be cancelled
    create:
      success: Campaign created successfully
    verify_bm:
      success: Thank you for your verification
      error: Error updating your verification
  alerts:
    register_or_sign_in: 'You must register or sign in before continuing.'
  notices:
    create_customer_list: 'You must create a customer list first.'
  pagy:
    prev: "&lt; Previous"
    next: "Next &gt;"
  customer_conversation_topic:
    taken_with_channel: "The customer already has this conversation topic assigned"
  
  seo:
    titles:
      register: "Sign up for free at Mercately"
      login: "Log in to Mercately"
      password_reset: "Reset password"
      password_edit: "Reset password"
    
    meta_descriptions:
      register: "Create your free Mercately account and transform your sales through WhatsApp. Automation, team management, and integrations. Start now!"
      login: "Access your Mercately account and manage your WhatsApp conversations, clients, and sales team from a single platform. Secure and fast login."
      password_reset: "Forgot your password? Reset it easily and securely. Enter your email and we'll send you instructions to regain access to your Mercately account."
      password_edit: "Create a new secure password for your Mercately account. Enter your new password and regain access to your Mercately account."
    
    h1_tags:
      register: "Sign up for free at Mercately"
      login: "Log in"
      password_reset: "Reset your password"
      password_edit: "Create new password"
    
    alt_text:
      check_follow_up: "Checkmark icon - Automatically follow up with all your clients"
      check_manage: "Checkmark icon - Manage all your salespeople and client chats in one place"  
      check_integrate: "Checkmark icon - Integrate Mercately with your applications"
      
      shopify_logo: "Shopify logo"
      hubspot_logo: "HubSpot logo"
      stripe_logo: "Stripe logo"
      mercado_libre_logo: "Mercado Libre logo"
      
      main_illustration: "Mercately platform illustration"
      mercately_logo: "Mercately logo"
      
      login_illustration: "Login illustration for Mercately platform"
      password_reset_illustration: "Password reset illustration for secure account recovery"
      password_edit_illustration: "Password creation illustration for account security"
