# Configuración para el deploy en modo API
set :branch, 'master'
set :rails_env, 'production'
# Seleccionar el servidor para el deploy
role :karafka, %w{10.116.1.107 10.116.1.108}, user: 'mercately'
# Configurar variables de entorno para el modo API
set :default_env, {
 'RAILS_API_MODE' => 'true',
 'RAILS_SERVE_STATIC_FILES' => 'false',
 'WEBPACKER_COMPILE' => 'false',
 'ASSETS_PRECOMPILE' => 'false'
}
# Desactivar completamente la compilación de assets
Rake::Task["deploy:assets:precompile"].clear if Rake::Task.task_defined?("deploy:assets:precompile")
# Desactivar yarn install
namespace :deploy do
 namespace :check do
   task :yarn_install do
     # No hacer nada
   end
 end
end