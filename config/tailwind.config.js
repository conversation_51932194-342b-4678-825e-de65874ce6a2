/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme');

module.exports = {
  important: '.tw-wrapper',
  prefix: 'tw-',
  corePlugins: {
    preflight: false,
    backgroundOpacity: false,
    borderOpacity: false,
    divideOpacity: false,
    placeholderOpacity: false,
    textOpacity: false
  },
  content: ['./app/javascript/presentation/**/*.js'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Poppins', ...defaultTheme.fontFamily.sans],
        serif: ['Poppins', ...defaultTheme.fontFamily.sans],
        body: ['Poppins', ...defaultTheme.fontFamily.sans]
      },
      colors: {
        blue: {
          "50": "#F1F9FE",
          "100": "#E2F1FC",
          "200": "#BEE3F9",
          "300": "#85CDF4",
          "400": "#43B4ED",
          "500": "#198FCC",
          "600": "#0E7BBB",
          "700": "#0C6298",
          "800": "#0E547E",
          "900": "#124568",
          "950": "#0C2C45"
        },
        green: {
          "50": "#EFFAF3",
          "100": "#D8F3E0",
          "200": "#B4E6C5",
          "300": "#83D2A4",
          "400": "#50B77D",
          "500": "#2E9B62",
          "600": "#1F7C4D",
          "700": "#196340",
          "800": "#164F34",
          "900": "#13412C",
          "950": "#092519"
        },
        red: {
          "50": "#FEF2F2",
          "100": "#FDE6E7",
          "200": "#FBD0D3",
          "300": "#F7AAB0",
          "400": "#F27A86",
          "500": "#E84B5F",
          "600": "#D42A48",
          "700": "#B31D3B",
          "800": "#961B38",
          "900": "#801B36",
          "950": "#470A18"
        },
        yellow: {
          "50": "#FFFCEB",
          "100": "#FFF5C6",
          "200": "#FFEB88",
          "300": "#FEDB4B",
          "400": "#FEC512",
          "500": "#F8A808",
          "600": "#DC7F03",
          "700": "#B65A07",
          "800": "#94450C",
          "900": "#79390E",
          "950": "#461D02"
        },
        vermilion: {
          "50": "#FFF4ED",
          "100": "#FFE6D4",
          "200": "#FFCAA8",
          "300": "#FFA471",
          "400": "#FF7438",
          "500": "#FE4E11",
          "600": "#EF3307",
          "700": "#C62208",
          "800": "#9D1D0F",
          "900": "#7E1B10",
          "950": "#440A06"
        },
        violet: {
          "50": "#F5F4FE",
          "100": "#EEECFB",
          "200": "#DEDBF9",
          "300": "#C5BEF4",
          "400": "#A798ED",
          "500": "#9077E5",
          "600": "#7750D7",
          "700": "#673DC4",
          "800": "#5633A4",
          "900": "#482B87",
          "950": "#2C1A5B"
        },
        lime: {
          "50": "#FDFFE5",
          "100": "#F9FFC7",
          "200": "#F0FF95",
          "300": "#E0FF4F",
          "400": "#CFF625",
          "500": "#B0DD05",
          "600": "#89B100",
          "700": "#678605",
          "800": "#52690B",
          "900": "#44590E",
          "950": "#233201"
        },
        gray: {
          "50": "#F6F8F9",
          "100": "#F0F2F4",
          "200": "#D6DCE1",
          "300": "#B2BEC7",
          "400": "#889BA8",
          "500": "#6A7E8D",
          "600": "#546775",
          "700": "#45535F",
          "800": "#3C4750",
          "900": "#353D45",
          "950": "#23292E"
        },
        orange: {
          "50": "#FDF5EC"
        },
        seagull: {
          "50": "#EAFEFF",
          "300": "#5BEBFF",
          "500": "#00BAE5",
          "700": "#03749B"
        }
      },
      fontSize: {
        '3xs': ['10px', { lineHeight: '14px' }],
        '2xs': ['11px', { lineHeight: '16px' }],
        'xs': ['12px', { lineHeight: '16px' }],
        'default': ['13px', { lineHeight: '23px' }],
        'sm': ['14px', { lineHeight: '24px' }],
        'base': ['16px', { lineHeight: '26px' }],
        'lg': ['18px', { lineHeight: '28px' }],
        'xl': ['22px', { lineHeight: '32px' }],
        '2xl': ['24px', { lineHeight: '34px' }],
        '3xl': ['30px', { lineHeight: '38px' }],
        '4xl': ['32px', { lineHeight: '48px' }],
        '5xl': ['48px', { lineHeight: '56px' }],
        '6xl': ['60px', { lineHeight: '68px' }],
        '7xl': ['72px', { lineHeight: '80px' }],
        '8xl': ['96px', { lineHeight: '104px' }],
        '9xl': ['128px', { lineHeight: '136px' }]
      }
    }
  },
  plugins: []
};
