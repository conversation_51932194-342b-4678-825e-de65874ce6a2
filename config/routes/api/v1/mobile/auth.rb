# config/routes/api/v1/mobile/auth.rb
Rails.application.routes.draw do
  namespace :api do
    namespace :v1 do
      namespace :mobile do
        post 'sign_up', to: 'registrations#create', as: :sign_up
        get 'get_jwt_cookie', to: 'retailer_users#get_jwt_cookie_for_mobile', as: :get_jwt_cookie
        post 'set_app_version', to: 'retailer_users#set_app_version', as: :set_app_version
        put 'toggle_retailer_user_status', to: 'retailer_users#toggle_active', as: :toggle_retailer_user_status
        delete 'destroy_retailer_user', to: 'retailer_users#destroy', as: :destroy_retailer_user
      end
    end
  end
end
