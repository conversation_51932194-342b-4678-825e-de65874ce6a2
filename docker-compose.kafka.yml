version: '3.8'

services:
  # Zookeeper - <PERSON><PERSON><PERSON><PERSON>
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: mercately-zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - kafka-network

  # Kafka Broker
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: mercately-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      # Configuración para desarrollo
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
      KAFKA_LOG_RETENTION_HOURS: 1
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - kafka-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  # Kafka UI para monitoreo y debugging
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: mercately-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: mercately-local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    networks:
      - kafka-network

  # Inicializador de topics
  kafka-init:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      - kafka
    command: |
      bash -c "
        echo '🚀 Esperando a que Kafka esté listo...'
        kafka-topics --bootstrap-server kafka:29092 --list

        echo '📝 Creando topic mercately_campaign_events...'
        kafka-topics --bootstrap-server kafka:29092 --create --if-not-exists \
          --topic mercately_campaign_events \
          --partitions 1 \
          --replication-factor 1 \
          --config cleanup.policy=delete \
          --config retention.ms=604800000

        echo '✅ Topics creados:'
        kafka-topics --bootstrap-server kafka:29092 --list

        echo '📊 Configuración del topic:'
        kafka-topics --bootstrap-server kafka:29092 --describe --topic mercately_campaign_events

        echo '🎉 Kafka listo para desarrollo local!'
      "
    networks:
      - kafka-network



  # Karafka Consumer Service
  karafka-consumer:
    build:
      context: .
      dockerfile: Dockerfile.karafka
    container_name: mercately-karafka-consumer
    depends_on:
      kafka:
        condition: service_healthy
      kafka-init:
        condition: service_completed_successfully
    environment:
      - RAILS_ENV=development
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_CONSUMER_ENABLED=true
      - KAFKA_PRODUCER_ENABLED=false
      - USE_KAFKA_SENDER=true
      - KAFKA_ENABLED=true
      - REDIS_URL=redis://host.docker.internal:6379
    volumes:
      - .:/app
      - bundle-cache:/usr/local/bundle
    networks:
      - kafka-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "pgrep", "-f", "karafka"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  zookeeper-data:
    driver: local
  zookeeper-logs:
    driver: local
  kafka-data:
    driver: local
  bundle-cache:
    driver: local

networks:
  kafka-network:
    driver: bridge
    name: mercately-kafka-network
