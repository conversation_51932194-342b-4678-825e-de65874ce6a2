# 🚀 Kafka Campaigns Documentation

Documentación completa del sistema de campañas basado en Kafka para Mercately.

## 📚 Índice de Documentación

### 🏗️ **Arquitectura**
- **[Arquitectura General](architecture/overview.md)** - Visión general del sistema, componentes y flujo de datos

### ⚙️ **Configuración**
- **[Configuración Buenos Vecinos](configuration/good-neighbor-config.md)** - Configuración optimizada para entorno compartido con BSP

### 🔧 **Desarrollo**
- **[Quick Start](development/quick-start.md)** - Guía rápida para empezar
- **[Setup Local](development/local-setup.md)** - Configuración para desarrollo local
- **[Docker Setup](development/docker-setup.md)** - Configuración con Docker
- **[Docker Local](development/docker-local.md)** - Docker para desarrollo local

### 📊 **Monitoreo**
- **[Troubleshooting](monitoring/troubleshooting.md)** - Solución de problemas comunes

## 🎯 **Resumen del Sistema**

### **Flujo Principal:**
```
K01 (Productor) → Kafka Broker → K02 (Consumidor)
     ↓                              ↓
  Campañas                    Procesamiento
  Eventos                     Mensajes
```

### **Componentes Clave:**
- **K01**: Servidor productor (solo produce eventos)
- **K02**: Servidor consumidor (procesa eventos y envía mensajes)
- **Kafka Broker**: Broker compartido con BSP
- **Configuración Buenos Vecinos**: Configuración respetuosa para entorno compartido

### **Eventos Principales:**
- `campaign_started_event` - Inicio de campaña
- `campaign_message_pending_event` - Mensaje pendiente de envío
- `campaign_message_sent_event` - Mensaje enviado exitosamente

## 🤝 **Configuración Buenos Vecinos**

El sistema está configurado para coexistir armónicamente con BSP en el broker compartido:

- ✅ **Timeouts conservadores** (30s session, 10s heartbeat)
- ✅ **Pool reducido** (5 productores vs 10)
- ✅ **Menos threads** (2 workers vs 4)
- ✅ **Configuración estándar** de industria

## 🔍 **Scripts de Monitoreo**

```bash
# Monitorear eventos de campaña
./bin/mercately/kafka/monitor_topics mercately_campaign_events

# Ver estado de Kafka
./bin/mercately/kafka/kafka_status
```

## 🚨 **Troubleshooting Rápido**

### **Problema: Eventos no se procesan**
1. Verificar que K02 esté ejecutando Karafka
2. Revisar logs de consumer
3. Verificar conectividad con broker

### **Problema: Mensajes no se envían**
1. Verificar configuración de WhatsApp
2. Revisar logs de message sender
3. Verificar estado de la campaña

### **Problema: Conflictos con BSP**
1. Verificar configuración buenos vecinos
2. Revisar timeouts y pool size
3. Coordinar con equipo BSP

## 📈 **Métricas Importantes**

- **Consumer Lag**: Debe ser < 1000 mensajes
- **Rebalances**: Debe ser < 1 por hora
- **Processing Time**: Debe ser < 4 minutos
- **Error Rate**: Debe ser < 1%

## 🔗 **Enlaces Útiles**

- [Gema mercately-kafka](https://github.com/ThoughtCode/mercately-kafka)
- [Gema mercately-karafka](https://github.com/ThoughtCode/mercately-karafka)
- [Documentación Kafka](https://kafka.apache.org/documentation/)
- [Documentación Karafka](https://karafka.io/)

## 📝 **Notas de Desarrollo**

- **Branch principal**: `feature/campaigns-kafka-full-integration-new`
- **Branch logs limpios**: `feature/campaigns-kafka-clean-logs`
- **Configuración K01**: Solo productor (`KAFKA_CONSUMER_ENABLED=false`)
- **Configuración K02**: Productor + Consumidor (`KAFKA_CONSUMER_ENABLED=true`)

---

**Última actualización**: 2025-07-08  
**Versión**: 1.0  
**Mantenido por**: Equipo Mercately
