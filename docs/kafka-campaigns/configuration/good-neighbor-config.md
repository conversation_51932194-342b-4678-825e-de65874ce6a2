# 🤝 Kafka Good Neighbor Configuration

## Problema Original

Mercately comparte el broker de Kafka con BSP (servicio similar a GupShup). Nuestra configuración inicial era muy agresiva y causó bloqueos en BSP cuando tenían un solo consumer.

## Configuraciones Problemáticas Originales

### ❌ Configuración Agresiva (Causaba Bloqueos)

```yaml
# karafka.yml - CONFIGURACIÓN PROBLEMÁTICA
session.timeout.ms: 300000      # 5 minutos - MUY ALTO
heartbeat.interval.ms: 60000     # 1 minuto - MUY ALTO  
max.poll.interval.ms: 600000     # 10 minutos - EXTREMADAMENTE ALTO
worker_threads: 4                # Muchos threads concurrentes
max_wait_time: 10                # Polling agresivo
```

```ruby
# mercately_kafka.rb - CONFIGURACIÓN PROBLEMÁTICA
'connections.max.idle.ms': 300_000,  # 5 minutos - MUY ALTO
'message.timeout.ms': 60_000,        # 1 minuto
'delivery.timeout.ms': 120_000,      # 2 minutos - MUY ALTO
'max.in.flight.requests.per.connection': 5, # <PERSON><PERSON> requests concurrentes
retries: 5,                          # Muchos reintentos
```

```ruby
# kafka_producer_pool.rb - CONFIGURACIÓN PROBLEMÁTICA
ENV['KAFKA_PRODUCER_POOL_SIZE'] ||= '10'        # 10 productores simultáneos
ENV['KAFKA_PRODUCER_MAX_IDLE_TIME'] ||= '600'   # 10 minutos de idle
```

## Configuraciones Good Neighbor Implementadas

### ✅ Configuración Conservadora (Respetuosa)

```yaml
# karafka.yml - CONFIGURACIÓN CONSERVADORA
session.timeout.ms: 30000       # 30 segundos (era 300000)
heartbeat.interval.ms: 10000     # 10 segundos (era 60000)
max.poll.interval.ms: 300000     # 5 minutos (era 600000)
worker_threads: 2                # Menos threads (era 4)
max_wait_time: 5                 # Polling menos agresivo (era 10)
shutdown_timeout: 30             # Shutdown más rápido (era 60)
```

```ruby
# mercately_kafka.rb - CONFIGURACIÓN CONSERVADORA
'connections.max.idle.ms': 180_000,  # 3 minutos (era 5)
'socket.connection.setup.timeout.ms': 10_000, # 10 segundos (era 30)
'metadata.max.age.ms': 180_000,      # 3 minutos (era 5)
'message.timeout.ms': 30_000,        # 30 segundos (era 60)
'request.timeout.ms': 15_000,        # 15 segundos (era 30)
'delivery.timeout.ms': 60_000,       # 1 minuto (era 2)
'max.in.flight.requests.per.connection': 3, # Menos requests (era 5)
'socket.max.fails': 3,               # Menos fallos (era 5)
retries: 3,                          # Menos reintentos (era 5)
'retry.backoff.ms': 2000,            # Más tiempo entre reintentos (era 1000)
```

```ruby
# kafka_producer_pool.rb - CONFIGURACIÓN CONSERVADORA
ENV['KAFKA_PRODUCER_POOL_SIZE'] ||= '5'         # 5 productores (era 10)
ENV['KAFKA_PRODUCER_MAX_IDLE_TIME'] ||= '300'   # 5 minutos (era 10)
```

## Beneficios de la Configuración Good Neighbor

### 🤝 Para BSP (Servicio Vecino):
- ✅ **Menos monopolización** de particiones
- ✅ **Timeouts más rápidos** - liberamos recursos más rápido
- ✅ **Menos conexiones concurrentes** - no saturamos el broker
- ✅ **Heartbeats más frecuentes** - mejor detección de consumers activos
- ✅ **Menos reintentos agresivos** - no saturamos con requests fallidos

### 🚀 Para Mercately:
- ✅ **Funcionalidad preservada** - todo sigue funcionando
- ✅ **Mejor ciudadanía** en el cluster compartido
- ✅ **Menos probabilidad** de causar problemas a otros servicios
- ✅ **Configuración más estable** y predecible
- ✅ **Mejor observabilidad** con timeouts más cortos

## Valores de Referencia de Industria

### Configuraciones Estándar para Entornos Compartidos:
```yaml
session.timeout.ms: 10000-30000     # 10-30 segundos
heartbeat.interval.ms: 3000-10000   # 3-10 segundos
max.poll.interval.ms: 300000        # 5 minutos máximo
worker_threads: 1-2                 # Conservador en entornos compartidos
```

## Monitoreo y Alertas

### Métricas a Monitorear:
- **Consumer lag** - asegurar que no se acumule
- **Rebalance frequency** - debe ser mínimo
- **Connection errors** - indicador de problemas
- **Processing time** - debe estar bajo max.poll.interval.ms

### Alertas Recomendadas:
- Consumer lag > 1000 mensajes
- Rebalances > 1 por hora
- Processing time > 4 minutos (80% del max.poll.interval.ms)

## Testing de la Configuración

### Antes de Deploy:
1. **Verificar consumer lag** en staging
2. **Monitorear rebalances** durante 1 hora
3. **Confirmar con BSP** que no hay impacto
4. **Test de carga** con múltiples campañas

### Después de Deploy:
1. **Monitoreo activo** primeras 24 horas
2. **Coordinación con BSP** para feedback
3. **Rollback plan** si hay problemas

## Contactos y Escalación

- **Equipo BSP**: [contacto del equipo BSP]
- **Kafka Admin**: [contacto del admin de Kafka]
- **Escalación**: [proceso de escalación]

---

**Fecha de Implementación**: 2025-07-07  
**Versión**: 1.0  
**Responsable**: Equipo Mercately  
**Revisado por**: [Pendiente - BSP Team]
