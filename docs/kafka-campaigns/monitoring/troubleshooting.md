# 🔧 Troubleshooting - Campañas Kafka

## 🚨 Problemas Comunes y Soluciones

### 1. **Kafka Connection Issues**

#### Síntoma
```
Error connecting to Kafka broker
```

#### Diagnóstico
```bash
# Verificar conectividad
telnet localhost 9092

# Verificar logs de Kafka
tail -f logs/kafka.log
```

#### Solución
```bash
# Verificar variables de ambiente
echo $KAFKA_BROKERS

# Reiniciar servicios Kafka
sudo systemctl restart kafka
sudo systemctl restart zookeeper
```

### 2. **Consumer No Procesa Eventos**

#### Síntoma
- Eventos se acumulan en el topic
- Campañas quedan en estado "processing"

#### Diagnóstico
```ruby
# En Rails console
Rails.application.config.karafka_consumer_node
# Debe ser true en K02

# Verificar consumers activos
Karafka::App.consumer_groups
```

#### Solución
```bash
# Verificar configuración de nodo
export KARAFKA_CONSUMER_NODE=true

# Reiniciar Karafka
bundle exec karafka server
```

### 3. **Redis Counters Inconsistentes**

#### Síntoma
- Contadores no coinciden con mensajes enviados
- Campañas no se marcan como completadas

#### Diagnóstico
```ruby
# En Rails console
campaign = Campaign.find(ID)
Redis.current.get("campaign:#{campaign.id}:total")
Redis.current.get("campaign:#{campaign.id}:pending_messages")
```

#### Solución
```ruby
# Resetear contadores manualmente
campaign_id = 123
total_customers = Campaign.find(campaign_id).customers.count

Redis.current.multi do |redis|
  redis.set("campaign:#{campaign_id}:total", total_customers)
  redis.set("campaign:#{campaign_id}:pending_messages", total_customers)
end
```

### 4. **Eventos Duplicados**

#### Síntoma
- Clientes reciben múltiples mensajes
- Logs muestran eventos procesados varias veces

#### Diagnóstico
```ruby
# Verificar idempotencia
message_id = "unique_message_id"
Redis.current.get("processed:#{message_id}")
```

#### Solución
```ruby
# Implementar idempotencia en consumers
def process(payload)
  message_id = "#{payload['campaign_id']}_#{payload['customer_id']}"
  
  return if Redis.current.get("processed:#{message_id}")
  
  # Procesar mensaje...
  
  Redis.current.setex("processed:#{message_id}", 3600, "true")
end
```

### 5. **Performance Lento**

#### Síntoma
- Campañas tardan mucho en procesarse
- Alta latencia en envío de mensajes

#### Diagnóstico
```bash
# Verificar lag de consumers
kafka-consumer-groups.sh --bootstrap-server localhost:9092 \
  --describe --group mercately_consumers
```

#### Solución
```ruby
# Optimizar batch processing
def process_customers_in_batches(campaign)
  campaign.customers.find_in_batches(batch_size: 100) do |batch|
    batch.each { |customer| produce_message_event(campaign, customer) }
    producer.flush # Flush cada batch
  end
end
```

## 🔍 Comandos de Diagnóstico

### Kafka Topics
```bash
# Listar topics
kafka-topics.sh --bootstrap-server localhost:9092 --list

# Describir topic
kafka-topics.sh --bootstrap-server localhost:9092 \
  --describe --topic mercately_campaign_events

# Ver mensajes en topic
kafka-console-consumer.sh --bootstrap-server localhost:9092 \
  --topic mercately_campaign_events --from-beginning
```

### Redis Monitoring
```bash
# Conectar a Redis
redis-cli

# Ver todas las keys de campañas
KEYS campaign:*

# Monitorear comandos en tiempo real
MONITOR
```

### Rails Console Helpers
```ruby
# Verificar configuración
Rails.application.config.karafka_consumer_node
Rails.application.config.kafka_producer_node

# Estadísticas de campaña (solo contadores esenciales)
campaign = Campaign.find(ID)
puts "Total customers: #{campaign.customers.count}"
puts "Redis total: #{Redis.current.get("campaign:#{campaign.id}:total")}"
puts "Redis pending: #{Redis.current.get("campaign:#{campaign.id}:pending_messages")}"

# NOTE: Los contadores sent, failed, started_at, completed_at, status fueron eliminados
# ya que no se usaban en el código y eran remanentes de implementaciones anteriores

# Producir evento manualmente
producer = Campaigns::MessageEventProducer.new
producer.produce_campaign_started(campaign)

# Verificar último mensaje enviado
campaign.whatsapp_outbound_msgs.last
```

## 🚨 Alertas y Monitoreo

### Métricas Críticas
- **Kafka Lag**: Diferencia entre mensajes producidos y consumidos
- **Redis Memory**: Uso de memoria para contadores
- **Error Rate**: Porcentaje de mensajes fallidos
- **Processing Time**: Tiempo promedio de procesamiento

### Alertas Recomendadas
```yaml
# Ejemplo de configuración de alertas
kafka_lag_high:
  condition: lag > 1000
  action: notify_team

redis_memory_high:
  condition: memory_usage > 80%
  action: scale_redis

campaign_stuck:
  condition: processing_time > 30min
  action: investigate_campaign
```

## 🔄 Procedimientos de Recuperación

### Reinicio Completo del Sistema
```bash
# 1. Parar consumers
sudo systemctl stop karafka

# 2. Limpiar Redis (CUIDADO!)
redis-cli FLUSHDB

# 3. Reiniciar Kafka
sudo systemctl restart kafka

# 4. Reiniciar consumers
sudo systemctl start karafka
```

### Rollback a Modo Sincrónico
```ruby
# En Rails console - cambiar todas las campañas activas
Campaign.where(status: 'processing').update_all(sender_strategy: 'synchronous')

# Procesar campañas pendientes sincrónicamente
Campaign.where(status: 'processing').each do |campaign|
  Campaigns::ProcessorService.new(campaign).call
end
```

### Recuperación de Campaña Específica
```ruby
# Para una campaña que se quedó colgada
campaign = Campaign.find(ID)

# Opción 1: Reiniciar vía Kafka
producer = Campaigns::MessageEventProducer.new
producer.produce_campaign_started(campaign)

# Opción 2: Procesar sincrónicamente
campaign.update(sender_strategy: 'synchronous')
Campaigns::ProcessorService.new(campaign).call
```

## 📞 Contactos de Emergencia

- **Kafka Issues**: Equipo DevOps
- **Redis Issues**: Equipo Infrastructure  
- **Application Issues**: Equipo Backend
- **WhatsApp API Issues**: Equipo Integrations

---

> ⚠️ **Importante**: Siempre hacer backup de Redis antes de operaciones de limpieza masiva.
