# Desarrollo Local con Kafka

Este documento explica cómo configurar y usar Kafka localmente para desarrollo de campañas.

## 🚀 Setup Inicial

### 1. Iniciar Kafka
```bash
# Iniciar Kafka con Docker
./bin/dev-kafka start

# Verificar que esté funcionando
./bin/dev-kafka test
```

### 2. Configurar Variables de Entorno
```bash
# Cargar configuración para desarrollo local
source .env.kafka.local
```

### 3. Iniciar Rails (Productor)
```bash
# Terminal 1: Rails server
rails server
```

### 4. Iniciar <PERSON><PERSON>ka (Consumidor)
```bash
# Terminal 2: Karafka consumer
bundle exec karafka server
```

## 🧪 Probar el Flujo Completo

### 1. Crear una Campaña de Prueba
```ruby
# Rails console
rails console

# Crear retailer de prueba
retailer = Retailer.first || Retailer.create!(name: "Test Retailer", slug: "test")

# Crear template de WhatsApp
template = WhatsappTemplate.create!(
  retailer: retailer,
  name: "test_template",
  gupshup_template_id: "test_123",
  content: "Hola {{customer_name}}, este es un mensaje de prueba."
)

# Crear customers de prueba
customers = []
3.times do |i|
  customers << Customer.create!(
    retailer: retailer,
    phone_number: "1234567890#{i}",
    name: "Cliente Test #{i+1}"
  )
end

# Crear campaña
campaign = Campaign.create!(
  retailer: retailer,
  whatsapp_template: template,
  name: "Campaña de Prueba Local",
  sender_strategy: "kafka"
)

# Asociar customers a la campaña
campaign.customers = customers

# Enviar campaña
Campaigns::ProcessorService.new(campaign).call
```

### 2. Monitorear el Flujo

#### Logs de Rails
```bash
# Ver logs de Rails (productor)
tail -f log/development.log | grep CAMPAIGN
```

#### Logs de Karafka
```bash
# Ver logs de Karafka (consumidor)
# Los logs aparecen en el terminal donde ejecutaste karafka server
```

#### Kafka UI
```bash
# Abrir interfaz web de Kafka
./bin/dev-kafka ui
# O ir a: http://localhost:8080
```

## 📊 Verificar Resultados

### 1. Contadores en Redis
```ruby
# Rails console
campaign_id = Campaign.last.id

# Ver contadores
Redis.current.get("campaign:#{campaign_id}:total")
Redis.current.get("campaign:#{campaign_id}:pending_messages")
```

### 2. Mensajes Creados
```ruby
# Ver mensajes mock creados
GupshupWhatsappMessage.where(campaign_id: campaign_id)
```

### 3. Estado de la Campaña
```ruby
# Ver estado final
Campaign.find(campaign_id).status
```

## 🔧 Comandos Útiles

### Kafka
```bash
# Ver estado de contenedores
./bin/dev-kafka status

# Ver logs de Kafka
./bin/dev-kafka logs

# Detener Kafka
./bin/dev-kafka stop

# Limpiar datos (reset completo)
./bin/dev-kafka clean
```

### Debugging
```bash
# Ver mensajes en el topic
./bin/dev-kafka console-consumer

# Enviar mensajes manualmente
./bin/dev-kafka console-producer
```

## 🎯 Flujo Esperado

1. **Rails produce** `campaign_started_event`
2. **Karafka consume** y produce `campaign_message_pending_event` (uno por customer)
3. **Karafka consume** pending events y "envía" mensajes (mock)
4. **Karafka produce** `campaign_message_sent_event`
5. **Karafka actualiza** contadores Redis
6. **Karafka produce** `campaign_completed_event`
7. **Campaña finalizada** con estado `sent`

## 🐛 Troubleshooting

### Kafka no inicia
```bash
# Verificar Docker
docker ps

# Reiniciar Kafka
./bin/dev-kafka stop
./bin/dev-kafka start
```

### Karafka no consume
```bash
# Verificar configuración
echo $KAFKA_BOOTSTRAP_SERVERS
echo $KAFKA_CONSUMER_ENABLED

# Recargar variables
source .env.kafka.local
```

### Configuración de WhatsApp
```bash
# WhatsApp mock fue eliminado por seguridad
# Ahora se usa siempre el servicio real de WhatsApp
# Para tests, usar mocks de RSpec en lugar de mocks en código de aplicación
```

## 📝 Notas

- **Servicio real**: Se usa siempre el servicio real de WhatsApp (más seguro)
- **Datos locales**: Todo se guarda en tu DB local
- **Redis local**: Usar tu instancia local de Redis
- **Logs detallados**: Todos los pasos están loggeados para debugging
- **Testing**: Para tests unitarios, usar mocks de RSpec en lugar de código mock
