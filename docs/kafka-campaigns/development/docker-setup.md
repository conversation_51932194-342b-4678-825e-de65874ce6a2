# 🚀 Kafka Campaigns - Docker Setup

**Simple, universal setup for Kafka campaigns that works everywhere!**

## 🎯 Quick Start (2 minutes)

### 1. Start Kafka Services
```bash
# Start everything with one command
./bin/kafka-docker start
```

### 2. Test Campaign
```bash
# Open Rails console
rails console

# Create and send a campaign
campaign = Campaign.last
Campaigns::ProcessorService.new(campaign: campaign).call
```

### 3. Monitor Results
- **Kafka UI**: http://localhost:8080
- **Logs**: `./bin/kafka-docker logs`

That's it! 🎉

## 📋 What You Get

- ✅ **Kafka Broker** - Message streaming
- ✅ **Karafka Consumer** - Processes campaign events  
- ✅ **Kafka UI** - Visual monitoring and debugging
- ✅ **Auto-configured Topics** - Ready to use
- ✅ **Health Checks** - Ensures everything is working

## 🛠️ Management Commands

```bash
# Start services
./bin/kafka-docker start

# Stop services  
./bin/kafka-docker stop

# Restart services
./bin/kafka-docker restart

# Check status
./bin/kafka-docker status

# View logs (all services)
./bin/kafka-docker logs

# View logs (specific service)
./bin/kafka-docker logs kafka
./bin/kafka-docker logs karafka-consumer

# Test setup
./bin/kafka-docker test

# Clean everything (removes data)
./bin/kafka-docker clean

# Help
./bin/kafka-docker help
```

## 🔍 Monitoring & Debugging

### Kafka UI (Recommended)
- **URL**: http://localhost:8080
- **Features**: Topics, messages, consumers, producers
- **Perfect for**: Visual debugging and monitoring

### Command Line
```bash
# View all logs
./bin/kafka-docker logs

# View specific service logs
./bin/kafka-docker logs kafka          # Kafka broker
./bin/kafka-docker logs karafka-consumer # Campaign processor
./bin/kafka-docker logs kafka-ui       # Web interface

# Check service status
./bin/kafka-docker status

# Test connectivity
./bin/kafka-docker test
```

### Rails Logs
```bash
# In another terminal, watch Rails logs
tail -f log/development.log | grep KAFKA
```

## 🧪 Testing Campaigns

### Basic Test
```ruby
# Rails console
campaign = Campaign.last
campaign.update(sender_strategy: 'kafka')

# Send campaign
Campaigns::ProcessorService.new(campaign: campaign).call

# Check results in Kafka UI or logs
```

### Advanced Testing
```ruby
# Create test campaign
retailer = Retailer.find_by(kafka_enabled: true)
campaign = retailer.campaigns.create!(
  name: "Docker Test Campaign",
  sender_strategy: 'kafka',
  # ... other attributes
)

# Add customers
campaign.customers = retailer.customers.limit(5)

# Send via Kafka
Campaigns::ProcessorService.new(campaign: campaign).call
```

## 🌍 Environment Support

This setup works identically across:

- ✅ **Local Development** - Any developer's machine
- ✅ **Staging Environments** - Consistent testing
- ✅ **CI/CD Pipelines** - Automated testing
- ✅ **Production Alternative** - Backup to K01/K02

## ⚙️ Configuration

### Default Configuration
The setup uses sensible defaults that work out of the box:

- **Kafka**: `localhost:9092`
- **Kafka UI**: `localhost:8080`  
- **Topics**: Auto-created with proper settings
- **Retention**: 7 days (development-friendly)

### Custom Configuration
Edit `.env.docker` to customize:

```bash
# Kafka settings
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
USE_KAFKA_SENDER=true
KAFKA_ENABLED=true

# Redis (if needed)
REDIS_URL=redis://localhost:6379

# Debug settings
KAFKA_DEBUG=true
```

## 🔧 Troubleshooting

### Services Won't Start
```bash
# Check Docker is running
docker info

# Check ports are free
lsof -i :9092  # Kafka
lsof -i :8080  # Kafka UI

# Clean and restart
./bin/kafka-docker clean
./bin/kafka-docker start
```

### Campaigns Not Processing
```bash
# Check Karafka consumer logs
./bin/kafka-docker logs karafka-consumer

# Test Kafka connectivity
./bin/kafka-docker test

# Check Rails configuration
rails console
puts ENV['USE_KAFKA_SENDER']
puts ENV['KAFKA_ENABLED']
```

### No Messages in Kafka UI
```bash
# Check topic exists
./bin/kafka-docker logs kafka-init

# Check producer logs
tail -f log/development.log | grep KAFKA-SENDER

# Verify campaign configuration
rails console
campaign = Campaign.last
puts campaign.sender_strategy
```

## 🚀 Production Notes

### K01/K02 vs Docker
- **K01/K02**: Primary production setup (dedicated servers)
- **Docker**: Development, staging, and production alternative
- **Both**: Use the same codebase and configuration

### Scaling
```bash
# Scale Karafka consumers (if needed)
docker-compose -f docker-compose.kafka.yml up -d --scale karafka-consumer=3
```

## 📚 Additional Resources

- **Kafka UI**: http://localhost:8080 (when running)
- **Campaign Logs**: `tail -f log/development.log | grep CAMPAIGN`
- **Karafka Docs**: https://karafka.io/
- **Docker Compose**: https://docs.docker.com/compose/

## 🎯 Developer Benefits

1. **Zero Configuration** - Works immediately
2. **Universal** - Same setup everywhere  
3. **Visual Debugging** - Kafka UI included
4. **Fast Iteration** - Quick start/stop/restart
5. **Isolated** - Doesn't affect other services
6. **Production-Ready** - Same code as K01/K02

---

**Need help?** Check the troubleshooting section or run `./bin/kafka-docker help`
