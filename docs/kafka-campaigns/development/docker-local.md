# 🐳 Docker-Based Kafka Campaigns Setup

## Overview

This setup provides a **simplified, Docker-based solution** for running Kafka campaigns that works consistently across all environments. Any Mercately developer can now test and develop campaigns functionality locally with minimal configuration.

## 🚀 Quick Start (3 Steps)

### 1. Start Kafka Services
```bash
./bin/kafka-docker start
```

### 2. Test the Setup
```bash
./bin/kafka-docker test
```

### 3. Test Campaigns in Rails Console
```ruby
# Rails console
rails console

# Create a test campaign
retailer = Retailer.find_by(slug: 'your-retailer')
retailer.update(kafka_enabled: true)

campaign = Campaign.last
Campaigns::ProcessorService.new(campaign: campaign).call
```

**That's it!** 🎉 Your campaigns will now be processed via Kafka.

## 📋 What You Get

- **Kafka Broker** running on `localhost:9092`
- **Kafka UI** available at `http://localhost:8080`
- **Karafka Consumer** processing events automatically
- **Pre-configured topics** for campaign events
- **Automatic health checks** and service management

## 🛠️ Available Commands

```bash
# Start all Kafka services
./bin/kafka-docker start

# Stop all services
./bin/kafka-docker stop

# Restart services
./bin/kafka-docker restart

# Check service status
./bin/kafka-docker status

# View logs (all services)
./bin/kafka-docker logs

# View logs for specific service
./bin/kafka-docker logs kafka
./bin/kafka-docker logs karafka-consumer

# Test the setup
./bin/kafka-docker test

# Clean up everything (removes all data)
./bin/kafka-docker clean

# Show help
./bin/kafka-docker help
```

## 🔧 Configuration

### Environment Variables

The setup uses `.env.docker` for configuration. Key variables:

```bash
# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
USE_KAFKA_SENDER=true
KAFKA_ENABLED=true
KAFKA_CONSUMER_ENABLED=true

# Redis (use your existing Redis)
REDIS_URL=redis://localhost:6379
```

### Retailer Configuration

Enable Kafka for specific retailers:

```ruby
# Rails console
retailer = Retailer.find_by(slug: 'your-retailer')
retailer.update(kafka_enabled: true)
```

## 📊 Monitoring and Debugging

### Kafka UI Dashboard
- **URL**: http://localhost:8080
- **Features**: View topics, messages, consumer groups, and performance metrics

### Logs
```bash
# All services
./bin/kafka-docker logs

# Specific services
./bin/kafka-docker logs kafka          # Kafka broker logs
./bin/kafka-docker logs karafka-consumer  # Consumer logs
./bin/kafka-docker logs kafka-ui       # UI logs
```

### Rails Logs
```bash
# Campaign-specific logs
tail -f log/development.log | grep CAMPAIGN
tail -f log/development.log | grep KAFKA
```

## 🧪 Testing Campaigns

### 1. Basic Campaign Test
```ruby
# Rails console
campaign = Campaign.last
result = Campaigns::ProcessorService.new(campaign: campaign).call
puts "Campaign processed: #{result}"
```

### 2. Monitor the Flow
```bash
# Terminal 1: Watch Kafka logs
./bin/kafka-docker logs karafka-consumer

# Terminal 2: Watch Rails logs
tail -f log/development.log | grep CAMPAIGN

# Terminal 3: Rails console (run campaign)
rails console
```

### 3. Check Results
```ruby
# Rails console
campaign_id = Campaign.last.id

# Check Redis counters
Redis.current.get("campaign:#{campaign_id}:total")
Redis.current.get("campaign:#{campaign_id}:pending_messages")

# Check campaign status
Campaign.find(campaign_id).status
```

## 🌍 Environment Support

This Docker setup works in:

- ✅ **Local Development** - Any developer's machine
- ✅ **Staging Environments** - Consistent behavior
- ✅ **CI/CD Pipelines** - Automated testing
- ✅ **Production Alternative** - Can replace K01/K02 if needed

## 🔄 Migration from Existing Setup

### From bin/dev-kafka Scripts
The new Docker setup replaces the old `bin/dev-kafka` scripts:

```bash
# Old way
./bin/dev-kafka start
./bin/dev-kafka ui

# New way
./bin/kafka-docker start
# UI automatically available at localhost:8080
```

### From Manual Kafka Installation
No need to install Kafka locally anymore:

```bash
# Old way: Install Kafka, configure, start services manually
# New way: Just run Docker
./bin/kafka-docker start
```

## 🚨 Troubleshooting

### Services Won't Start
```bash
# Check Docker is running
docker info

# Check for port conflicts
lsof -i :9092  # Kafka
lsof -i :8080  # Kafka UI

# Clean and restart
./bin/kafka-docker clean
./bin/kafka-docker start
```

### Campaigns Not Processing
```bash
# Check service status
./bin/kafka-docker status

# Check logs
./bin/kafka-docker logs karafka-consumer

# Test Kafka connection
./bin/kafka-docker test
```

### Consumer Not Receiving Messages
```bash
# Check topic exists
./bin/kafka-docker logs kafka-init

# Check consumer group
# Visit http://localhost:8080 and check consumer groups
```

## 🎯 Benefits

### For Developers
- **Zero Kafka knowledge required** - Just run one command
- **Consistent environment** - Same setup for everyone
- **Easy debugging** - Built-in UI and logging
- **Fast setup** - Ready in minutes, not hours

### For Teams
- **Reduced onboarding time** - New developers productive immediately
- **Consistent testing** - Same behavior across all environments
- **Better collaboration** - Everyone uses the same setup
- **Easier troubleshooting** - Standardized tools and processes

### For Operations
- **Production alternative** - Can scale to replace K01/K02
- **Environment parity** - Dev/staging/prod consistency
- **Simplified deployment** - Docker-based infrastructure
- **Better monitoring** - Built-in observability tools

## 📚 Next Steps

1. **Start using it**: `./bin/kafka-docker start`
2. **Test campaigns**: Follow the testing guide above
3. **Monitor performance**: Use Kafka UI dashboard
4. **Share feedback**: Help improve the setup for everyone

---

**Need help?** Check the troubleshooting section or run `./bin/kafka-docker help`
