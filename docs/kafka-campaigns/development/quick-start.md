# ⚡ Kafka Campaigns - Quick Start

**Get Kafka campaigns running in under 5 minutes!**

## 🚀 One-Command Setup

```bash
# 1. Start Kafka services
./bin/kafka-docker start

# 2. Test the setup
./bin/test-kafka-campaigns

# 3. Send a campaign
rails console
```

That's it! 🎉

## 📱 Send Your First Kafka Campaign

```ruby
# In Rails console

# 1. Find or create a campaign
campaign = Campaign.last
# OR create a new one:
# campaign = Retailer.first.campaigns.create!(name: "My Kafka Test", sender_strategy: 'kafka')

# 2. Send via Kafka
Campaigns::ProcessorService.new(campaign: campaign).call

# 3. Watch it process
# Open http://localhost:8080 to see messages flowing through Kafka
```

## 🔍 Monitor Results

- **Kafka UI**: http://localhost:8080
- **Rails Logs**: `tail -f log/development.log | grep KAFKA`
- **Service Logs**: `./bin/kafka-docker logs`

## 🛠️ Common Commands

```bash
# Start/Stop
./bin/kafka-docker start
./bin/kafka-docker stop

# Monitor
./bin/kafka-docker logs
./bin/kafka-docker status

# Test
./bin/test-kafka-campaigns

# Clean up
./bin/kafka-docker clean
```

## ❓ Troubleshooting

### "Services won't start"
```bash
# Make sure Docker is running
docker info

# Clean and restart
./bin/kafka-docker clean
./bin/kafka-docker start
```

### "Campaign not processing"
```bash
# Check logs
./bin/kafka-docker logs karafka-consumer

# Test setup
./bin/test-kafka-campaigns
```

### "No messages in Kafka UI"
```bash
# Check if campaign uses Kafka
rails console
campaign = Campaign.last
puts campaign.sender_strategy  # Should be 'kafka'

# Check retailer configuration
puts campaign.retailer.kafka_enabled  # Should be true
```

## 🎯 What's Different from Production?

| Aspect | Docker (Dev) | K01/K02 (Prod) |
|--------|-------------|----------------|
| **Setup** | `./bin/kafka-docker start` | Dedicated servers |
| **Monitoring** | http://localhost:8080 | Production monitoring |
| **Data** | Temporary (dev-friendly) | Persistent |
| **Performance** | Single machine | Distributed |
| **Code** | **Identical** | **Identical** |

## 🚀 Ready for Production?

Your Docker-tested campaigns will work identically in production (K01/K02) because:

- ✅ Same codebase
- ✅ Same services  
- ✅ Same configuration patterns
- ✅ Same message formats

## 📚 Next Steps

1. **Read the full docs**: [README-KAFKA-DOCKER.md](README-KAFKA-DOCKER.md)
2. **Test different scenarios**: Create campaigns with different configurations
3. **Monitor performance**: Use Kafka UI to understand message flow
4. **Deploy to staging**: Same Docker setup works in staging environments

---

**Questions?** Check [README-KAFKA-DOCKER.md](README-KAFKA-DOCKER.md) for detailed documentation.
