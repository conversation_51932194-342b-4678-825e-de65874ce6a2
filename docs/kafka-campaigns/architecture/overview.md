# 🚀 Arquitectura de Campañas con Kafka

## 📋 Resumen Ejecutivo

Este documento describe la migración del sistema de campañas de WhatsApp de un modelo sincrónico a una **arquitectura basada en eventos usando Apache Kafka**. La implementación permite procesamiento asíncrono, mejor escalabilidad y separación de responsabilidades entre nodos productores (K01) y consumidores (K02).

## 🏗️ Arquitectura General

### Antes (Sincrónico)
```
[Campaña] → [Procesamiento Directo] → [Envío WhatsApp] → [Finalización]
```

### Después (Kafka Events)
```
[K01: Productor] → [Kafka Topic] → [K02: Consumer] → [Procesamiento] → [WhatsApp]
```

## 🎯 Componentes Principales

### 1. **Infraestructura de Nodos**

#### Web Nodes Cluster (20+ nodos)
| Componente | Responsabilidad | Configuración |
|------------|-----------------|---------------|
| **Web Nodes 1-20+** | Interfaz web, API endpoints | `KAFKA_PRODUCER_NODE=false` |
| **Load Balancer** | Distribución de tráfico | NGINX/HAProxy |
| **Rails Application** | Lógica de negocio principal | Modo web estándar |

#### Arquitectura por Ambiente

**🔧 STAGING/DESARROLLO (K01 + K02):**
| Nodo | Rol | Responsabilidad | Configuración |
|------|-----|-----------------|---------------|
| **K01** | Producer (simula web) | Inicia campañas, produce eventos | `KAFKA_PRODUCER_NODE=true`<br/>`KARAFKA_CONSUMER_NODE=false` |
| **K02** | Consumer (simula karafka) | Procesa eventos, envía mensajes | `KAFKA_PRODUCER_NODE=false`<br/>`KARAFKA_CONSUMER_NODE=true` |

**🚀 PRODUCCIÓN (Web Nodes + Karafka Nodes):**
| Nodo | Rol | Responsabilidad | Configuración |
|------|-----|-----------------|---------------|
| **Web Nodes (20+)** | Web + Producer | UI + produce evento inicial | `KAFKA_PRODUCER_NODE=true`<br/>`KARAFKA_CONSUMER_NODE=false` |
| **Karafka Nodes (2+)** | Consumer Only | Solo procesan eventos | `KAFKA_PRODUCER_NODE=false`<br/>`KARAFKA_CONSUMER_NODE=true` |

#### Separación de Responsabilidades por Ambiente

**🔧 STAGING (K01 + K02):**

**K01 (simula web nodes):**
- ✅ Recibe requests de creación de campañas
- ✅ Valida datos de campaña
- ✅ Produce eventos `campaign_started_event`
- ✅ Inicializa contadores Redis
- ❌ NO envía mensajes WhatsApp
- ❌ NO consume eventos

**K02 (simula karafka nodes):**
- ✅ Consume todos los eventos Kafka
- ✅ Procesa mensajes individuales
- ✅ Envía mensajes WhatsApp
- ✅ Actualiza contadores y progreso
- ✅ Maneja finalización de campañas
- ❌ NO sirve tráfico web

**🚀 PRODUCCIÓN (Web + Karafka distribuido):**

**Web Nodes (20+) - como K01 distribuido:**
- ✅ Interfaz de usuario (dashboard, API)
- ✅ Autenticación y autorización
- ✅ CRUD operations básicas
- ✅ **Produce evento inicial de campaña** (como K01)
- ✅ Reportes y analytics
- ❌ NO procesan campañas masivas
- ❌ NO consumen eventos Kafka

**Karafka Nodes (2+) - como K02 especializado:**
- ✅ **Solo consumen eventos Kafka** (como K02)
- ✅ Procesa mensajes individuales
- ✅ Envía mensajes WhatsApp
- ✅ Actualiza contadores y progreso
- ✅ Maneja finalización de campañas
- ❌ NO sirve tráfico web
- ❌ NO maneja UI

### 2. **Topic de Kafka**
- **Nombre:** `mercately_campaign_events`
- **Particiones:** 1 (garantiza orden)
- **Replicación:** Configurada según ambiente

### 3. **Tipos de Eventos**

| Evento | Productor | Consumidor | Propósito |
|--------|-----------|------------|-----------|
| `campaign_started_event` | K01 | K02 | Inicia procesamiento de campaña |
| `campaign_message_pending_event` | K02 | K02 | Mensaje listo para envío |
| `campaign_message_sent_event` | K02 | K02 | Mensaje enviado (éxito/fallo) |
| `campaign_completed_event` | K02 | K02 | Campaña finalizada |

## 🔄 Flujo de Procesamiento Completo

### Flujo de Datos entre Nodos

```mermaid
sequenceDiagram
    participant U as Usuario
    participant LB as Load Balancer
    participant W as Web Node (1-20)
    participant K01 as K01 Producer
    participant K as Kafka Topic
    participant K02 as K02 Consumer
    participant WA as WhatsApp API
    participant R as Redis

    U->>LB: Crear campaña
    LB->>W: Route request
    W->>W: Validar datos
    W->>K01: Trigger campaign start
    K01->>R: Initialize counters
    K01->>K: Produce campaign_started_event

    loop Para cada cliente
        K01->>K: Produce message_pending_event
    end

    loop Procesamiento asíncrono
        K->>K02: Consume message_pending_event
        K02->>WA: Send WhatsApp message
        K02->>K: Produce message_sent_event
        K->>K02: Consume message_sent_event
        K02->>R: Update counters
    end

    K02->>K02: Check if campaign complete
    K02->>K: Produce campaign_completed_event
    K->>K02: Consume campaign_completed_event
    K02->>W: Update campaign status
    W->>U: Show completion
```

### Fase 1: Inicio de Campaña (Web → K01)
1. Usuario crea campaña en interfaz web
2. `CampaignStartedConsumer` recibe evento
3. Inicializa contadores en Redis
4. Produce eventos `campaign_message_pending_event` por cada cliente

### Fase 2: Procesamiento de Mensajes (K02)
1. `CampaignMessageConsumer` recibe eventos pendientes
2. Construye mensaje WhatsApp personalizado
3. Envía via BSP/Gupshup API
4. Produce evento `campaign_message_sent_event`

### Fase 3: Tracking y Finalización (K02)
1. `MessageSentConsumer` actualiza contadores
2. Cuando `pending_messages = 0` → `CampaignCompletionService`
3. Produce evento `campaign_completed_event`
4. `CampaignCompletedConsumer` marca campaña como completada

## 📊 Diagramas de Flujo

### Flujo Principal de Eventos

```mermaid
graph TD
    A[Usuario crea campaña] --> B[K01: Campaign Started]
    B --> C[Kafka: campaign_started_event]
    C --> D[K02: CampaignStartedConsumer]
    D --> E[Inicializar Redis counters]
    D --> F[Producir message_pending_events]
    F --> G[Kafka: campaign_message_pending_event]
    G --> H[K02: CampaignMessageConsumer]
    H --> I[Enviar WhatsApp]
    I --> J[Kafka: campaign_message_sent_event]
    J --> K[K02: MessageSentConsumer]
    K --> L{pending = 0?}
    L -->|Sí| M[CampaignCompletionService]
    L -->|No| N[Continuar procesando]
    M --> O[Kafka: campaign_completed_event]
    O --> P[K02: CampaignCompletedConsumer]
    P --> Q[Marcar campaña completada]
```

### Evolución de Arquitectura: Staging → Producción

#### 🔧 STAGING/DESARROLLO (K01 + K02)
```mermaid
graph TB
    subgraph "Staging Environment"
        K01[K01 - Producer<br/>Simula Web Nodes<br/>Campaign Initiation]
        K02[K02 - Consumer<br/>Message Processing<br/>WhatsApp Sending]
    end

    subgraph "Kafka Cluster"
        K[mercately_campaign_events<br/>Topic]
    end

    subgraph "External Services"
        WA[WhatsApp API<br/>BSP/Gupshup]
        R[Redis<br/>Counters & Cache]
        DB[(PostgreSQL<br/>Database)]
    end

    K01 --> K
    K --> K02
    K02 --> WA
    K02 --> R
    K02 --> DB
```

#### 🚀 PRODUCCIÓN (Web Nodes + Karafka Nodes)
```mermaid
graph TB
    subgraph "Load Balancer"
        LB[NGINX Load Balancer]
    end

    subgraph "Web Nodes Cluster (20+ nodes)"
        W1[Web Node 1<br/>Rails + Producer<br/>Como K01]
        W2[Web Node 2<br/>Rails + Producer<br/>Como K01]
        W3[Web Node 3<br/>Rails + Producer<br/>Como K01]
        W4[Web Node ...]
        W20[Web Node 20<br/>Rails + Producer<br/>Como K01]
    end

    subgraph "Kafka Cluster"
        K[mercately_campaign_events<br/>Topic]
    end

    subgraph "Karafka Nodes (2+ specialized)"
        KF1[Karafka Node 1<br/>Consumer Only<br/>Como K02]
        KF2[Karafka Node 2<br/>Consumer Only<br/>Como K02]
    end

    subgraph "External Services"
        WA[WhatsApp API<br/>BSP/Gupshup]
        R[Redis Cluster<br/>Counters & Cache]
        DB[(PostgreSQL<br/>Main Database)]
    end

    LB --> W1
    LB --> W2
    LB --> W3
    LB --> W20

    W1 --> K
    W2 --> K
    W3 --> K
    W20 --> K

    K --> KF1
    K --> KF2

    KF1 --> WA
    KF2 --> WA
    KF1 --> R
    KF2 --> R
    KF1 --> DB
    KF2 --> DB

    W1 --> DB
    W2 --> DB
    W3 --> DB
    W20 --> DB
```

### Topología de Red Completa

```mermaid
graph TB
    subgraph "Internet"
        USERS[👥 Users]
    end

    subgraph "DMZ"
        LB[🔄 Load Balancer<br/>NGINX/HAProxy]
    end

    subgraph "Web Tier (Auto-scaling)"
        W1[🌐 Web-01]
        W2[🌐 Web-02]
        W3[🌐 Web-03]
        WDOTS[🌐 ...]
        W20[🌐 Web-20]
    end

    subgraph "Application Tier"
        K01[🚀 K01-Producer<br/>Campaign Initiation<br/>Event Generation]
    end

    subgraph "Processing Tier"
        K02[⚡ K02-Consumer<br/>Message Processing<br/>WhatsApp Sending]
    end

    subgraph "Message Queue"
        KAFKA[📨 Kafka Cluster<br/>mercately_campaign_events<br/>High Availability]
    end

    subgraph "Data Tier"
        DB[(🗄️ PostgreSQL<br/>Primary + Replicas)]
        REDIS[(🔴 Redis Cluster<br/>Counters + Cache)]
    end

    subgraph "External APIs"
        BSP[📱 BSP API]
        GUPSHUP[📱 Gupshup API]
    end

    USERS --> LB
    LB --> W1
    LB --> W2
    LB --> W3
    LB --> W20

    W1 --> K01
    W2 --> K01
    W3 --> K01
    W20 --> K01

    K01 --> KAFKA
    KAFKA --> K02

    K02 --> BSP
    K02 --> GUPSHUP

    W1 --> DB
    W2 --> DB
    W3 --> DB
    W20 --> DB
    K01 --> DB
    K02 --> DB

    K01 --> REDIS
    K02 --> REDIS

    style K01 fill:#e1f5fe
    style K02 fill:#f3e5f5
    style KAFKA fill:#fff3e0
    style LB fill:#e8f5e8
```

### Arquitectura de Servicios Detallada

```mermaid
graph LR
    subgraph "Web Layer (20+ nodes)"
        A[User Interface]
        B[Campaign Controller]
        C[API Endpoints]
    end

    subgraph "Event Production (K01)"
        D[Campaign Started Events]
        E[Kafka Producer Service]
    end

    subgraph "Kafka Infrastructure"
        F[mercately_campaign_events<br/>Single Partition<br/>Ordered Processing]
    end

    subgraph "Event Consumption (K02)"
        G[Campaign Consumers]
        H[Message Processing]
        I[WhatsApp Integration]
        J[Progress Tracking]
    end

    A --> B
    B --> C
    C --> E
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    H --> J
    J --> F
```

## 🛠️ Implementación Técnica

### Consumers Principales

| Consumer | Archivo | Responsabilidad |
|----------|---------|-----------------|
| `CampaignStartedConsumer` | `app/services/campaigns/consumers/campaign_started_consumer.rb` | Inicializa campaña y produce eventos de mensajes |
| `CampaignMessageConsumer` | `app/services/campaigns/consumers/campaign_message_consumer.rb` | Procesa y envía mensajes individuales |
| `MessageSentConsumer` | `app/services/campaigns/consumers/message_sent_consumer.rb` | Actualiza contadores y detecta finalización |
| `CampaignCompletedConsumer` | `app/services/campaigns/consumers/campaign_completed_consumer.rb` | Marca campaña como completada |

### Servicios de Soporte

| Servicio | Propósito |
|----------|-----------|
| `MessageEventProducer` | Produce eventos a Kafka |
| `CampaignCompletionService` | Detecta y maneja finalización de campañas |
| `SenderStrategySelector` | Selecciona estrategia de envío (Kafka vs Sincrónico) |

## 🔧 Configuración

### Variables de Ambiente

```bash
# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_TOPIC=mercately_campaign_events

# Node Configuration  
KARAFKA_CONSUMER_NODE=true  # Solo en K02
KAFKA_PRODUCER_NODE=true   # Solo en K01

# Redis Configuration
REDIS_URL=redis://localhost:6379
```

### Estrategias de Envío

El sistema soporta múltiples estrategias configurables:

```ruby
# En Campaign model
enum sender_strategy: {
  synchronous: 0,    # Envío directo (legacy)
  kafka: 1,          # Vía eventos Kafka (nuevo)
  batch: 2,          # Procesamiento por lotes
  api_direct: 3,     # API directa
  scheduled: 4       # Programado
}
```

## 📈 Beneficios de la Arquitectura

### ✅ Escalabilidad Horizontal
- **20+ Web Nodes** manejan tráfico web concurrente
- **Separación especializada** K01/K02 optimiza recursos
- **Kafka como buffer** absorbe picos de tráfico
- **Auto-scaling** de web nodes según demanda

#### Capacidad del Sistema
| Componente | Capacidad | Escalabilidad |
|------------|-----------|---------------|
| **Web Nodes** | 1000+ requests/sec | Horizontal (agregar nodos) |
| **K01 Producer** | 10+ campañas/min | Vertical (más CPU/RAM) |
| **K02 Consumer** | 50K+ mensajes/hora | Horizontal (más consumers) |
| **Kafka Topic** | 100K+ eventos/sec | Particionado |

### ✅ Confiabilidad  
- **Persistencia de eventos** en Kafka
- **Retry automático** en caso de fallos
- **Tracking granular** de cada mensaje

### ✅ Mantenibilidad
- **Separación de responsabilidades** clara
- **Eventos auditables** para debugging
- **Fácil adición** de nuevos consumers

### ✅ Flexibilidad
- **Múltiples estrategias** de envío
- **Configuración por ambiente**
- **Rollback** a modo sincrónico si es necesario

## 🚀 Próximos Pasos

1. **Monitoreo avanzado** con métricas de Kafka
2. **Auto-scaling** basado en carga de eventos
3. **Optimización** de batch processing
4. **Integración** con otros sistemas vía eventos

---

> 📝 **Nota:** Esta arquitectura mantiene **100% compatibilidad** con el sistema anterior mientras añade capacidades avanzadas de procesamiento asíncrono.
