namespace :db do
  desc "Add campaign-related indexes safely during off-peak hours"
  task add_campaign_indexes: :environment do
    puts "🚀 Starting campaign indexes creation..."
    
    # Lista de índices a crear - Base de datos principal (mensajes recientes)
    indexes_to_create = [
      {
        table: :gupshup_whatsapp_messages,
        column: [:retailer_id, :campaign_id, :status],
        name: 'idx_gupshup_messages_retailer_campaign_status'
      },
      {
        table: :gupshup_whatsapp_messages,
        column: [:retailer_id, :campaign_id, :created_at],
        name: 'idx_gupshup_messages_retailer_campaign_created',
        order: { created_at: :desc }
      },
      {
        table: :gupshup_whatsapp_messages,
        column: [:retailer_id, :created_at],
        name: 'idx_gupshup_messages_retailer_created',
        order: { created_at: :desc }
      }
    ]
    
    indexes_to_create.each do |index_config|
      table_name = index_config[:table]
      column_name = index_config[:column]
      index_name = index_config[:name]
      index_options = index_config.except(:table, :column, :name)

      begin
        # Verificar si el índice ya existe
        if ActiveRecord::Base.connection.index_exists?(table_name, column_name, name: index_name)
          puts "⏭️  Índice '#{index_name}' ya existe en tabla '#{table_name}'. Saltando..."
          next
        end

        puts "📊 Creando índice '#{index_name}' en tabla '#{table_name}'..."
        start_time = Time.current

        # Crear índice (PostgreSQL)
        # Nota: No se usa CONCURRENT porque gupshup_whatsapp_messages puede estar particionada
        if ActiveRecord::Base.connection.adapter_name.downcase.include?('postgresql')
          if index_options[:order]
            # Para índices con ORDER BY
            order_clause = index_options[:order].map { |col, dir| "#{col} #{dir.to_s.upcase}" }.join(', ')
            ActiveRecord::Base.connection.execute(
              "CREATE INDEX #{index_name} ON #{table_name} (#{order_clause})"
            )
          else
            # Para índices normales
            ActiveRecord::Base.connection.execute(
              "CREATE INDEX #{index_name} ON #{table_name} (#{Array(column_name).join(', ')})"
            )
          end
        else
          # Fallback para otros adaptadores
          ActiveRecord::Base.connection.add_index(table_name, column_name, name: index_name, **index_options)
        end
        
        duration = Time.current - start_time
        puts "✅ Índice '#{index_name}' creado exitosamente en #{duration.round(2)} segundos"
        
      rescue => e
        puts "❌ Error creando índice '#{index_name}': #{e.message}"
        puts "🔍 Backtrace: #{e.backtrace.first(3).join('\n')}"
        # Continuar con el siguiente índice en lugar de fallar completamente
      end
    end
    
    puts "🎉 Proceso de creación de índices completado!"
  end
  
  desc "Remove campaign-related indexes (rollback)"
  task remove_campaign_indexes: :environment do
    puts "🗑️  Starting campaign indexes removal..."
    
    indexes_to_remove = [
      { table: :gupshup_whatsapp_messages, name: 'idx_gupshup_messages_retailer_campaign_status' },
      { table: :gupshup_whatsapp_messages, name: 'idx_gupshup_messages_retailer_campaign_created' },
      { table: :gupshup_whatsapp_messages, name: 'idx_gupshup_messages_retailer_created' }
    ]
    
    indexes_to_remove.each do |index_config|
      table_name = index_config[:table]
      index_name = index_config[:name]
      
      begin
        if ActiveRecord::Base.connection.index_exists?(table_name, nil, name: index_name)
          puts "🗑️  Removiendo índice '#{index_name}' de tabla '#{table_name}'..."
          ActiveRecord::Base.connection.remove_index(table_name, name: index_name)
          puts "✅ Índice '#{index_name}' removido exitosamente"
        else
          puts "⏭️  Índice '#{index_name}' no existe en tabla '#{table_name}'. Saltando..."
        end
      rescue => e
        puts "❌ Error removiendo índice '#{index_name}': #{e.message}"
      end
    end
    
    puts "🎉 Proceso de remoción de índices completado!"
  end
end
