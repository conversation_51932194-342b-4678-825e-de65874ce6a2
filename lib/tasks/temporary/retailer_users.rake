namespace :retailer_users do
  desc 'Create users for retailer users and assign them'
  task create_users: :environment do
    # 1. Copia los datos de retailer_users a users
    copy_users_sql_query = <<~SQL
      INSERT INTO users (
        id,
        email,
        encrypted_password,
        reset_password_token,
        reset_password_sent_at,
        remember_created_at,
        first_name,
        last_name,
        agree_terms,
        invitation_token,
        invitation_created_at,
        invitation_sent_at,
        invitation_accepted_at,
        invitation_limit,
        invited_by_id,
        invited_by_type,
        invitations_count,
        api_session_token,
        api_session_device,
        api_session_expiration,
        created_at,
        updated_at
      )
      SELECT id,
        email,
        encrypted_password,
        reset_password_token,
        reset_password_sent_at,
        remember_created_at,
        first_name,
        last_name,
        agree_terms,
        invitation_token,
        invitation_created_at,
        invitation_sent_at,
        invitation_accepted_at,
        invitation_limit,
        invited_by_id,
        invited_by_type,
        invitations_count,
        api_session_token,
        api_session_device,
        api_session_expiration,
        created_at,
        updated_at

      FROM retailer_users;
    SQL

    ActiveRecord::Base.connection.execute(copy_users_sql_query)

    # 2. Actualiza el secuencial de id en users
    seq = User.maximum(:id) + 1
    set_user_id_sequential_query = <<~SQL
      ALTER SEQUENCE users_id_seq RESTART WITH #{seq};
    SQL
    ActiveRecord::Base.connection.execute(set_user_id_sequential_query)

    # 3. Actualiza el campo user_id en retailer_users
    set_user_id_sql_query = <<~SQL
      UPDATE retailer_users AS ru
      SET user_id = u.id, current = TRUE
      FROM users AS u
      WHERE u.email = ru.email
    SQL
    ActiveRecord::Base.connection.execute(set_user_id_sql_query)
  end

  task update_unread_whatsapp_chats_count: :environment do
    # Admins y supervisores
    update_admins_sql = <<-SQL
    unread_whatsapp_chats_count = (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND customers.ws_active = TRUE
      AND customers.count_unread_messages > 0
      AND (customers.last_chat_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_chat_interaction < CURRENT_TIMESTAMP)
    )
    SQL
    RetailerUser
      .where('retailer_users.retailer_admin = TRUE OR retailer_users.retailer_supervisor = TRUE')
      .update_all(update_admins_sql)

    # Asignados y no asignados
    update_non_assigned_sql = <<-SQL
    unread_whatsapp_chats_count = (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      INNER JOIN agent_customers ON agent_customers.customer_id = customers.id
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND agent_customers.retailer_user_id = retailer_users.id
      AND customers.ws_active = TRUE
      AND customers.count_unread_messages > 0
      AND (customers.last_chat_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_chat_interaction < CURRENT_TIMESTAMP)
    ) + (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      LEFT JOIN agent_customers ON agent_customers.customer_id = customers.id
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND customers.ws_active = TRUE
      AND customers.count_unread_messages > 0
      AND agent_customers.customer_id IS NULL
      AND (customers.last_chat_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_chat_interaction < CURRENT_TIMESTAMP)
    )
    SQL
    RetailerUser
      .all_customers
      .where(retailer_admin: false, retailer_supervisor: false)
      .update_all(update_non_assigned_sql)

    # Solo asignados
    update_only_assigned_sql = <<-SQL
    unread_whatsapp_chats_count = (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      INNER JOIN agent_customers ON agent_customers.customer_id = customers.id
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND agent_customers.retailer_user_id = retailer_users.id
      AND customers.ws_active = TRUE
      AND customers.count_unread_messages > 0
      AND (customers.last_chat_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_chat_interaction < CURRENT_TIMESTAMP)
    )
    SQL
    RetailerUser
      .only_assigned_customers
      .update_all(update_only_assigned_sql)
  end

  task update_unread_messenger_chats_count: :environment do
    # Admins y supervisores
    update_admins_sql = <<-SQL
    unread_messenger_chats_count = (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND customers.pstype = 0
      AND customers.unread_messenger_messages > 0
      AND (customers.last_msn_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_msn_interaction < CURRENT_TIMESTAMP)
    )
    SQL
    RetailerUser
      .where('retailer_users.retailer_admin = TRUE OR retailer_users.retailer_supervisor = TRUE')
      .update_all(update_admins_sql)

    # Asignados y no asignados
    update_non_assigned_sql = <<-SQL
    unread_messenger_chats_count = (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      INNER JOIN agent_customers ON agent_customers.customer_id = customers.id
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND agent_customers.retailer_user_id = retailer_users.id
      AND customers.pstype = 0
      AND customers.unread_messenger_messages > 0
      AND (customers.last_msn_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_msn_interaction < CURRENT_TIMESTAMP)
    ) + (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      LEFT JOIN agent_customers ON agent_customers.customer_id = customers.id
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND customers.pstype = 0
      AND customers.unread_messenger_messages > 0
      AND agent_customers.customer_id IS NULL
      AND (customers.last_msn_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_msn_interaction < CURRENT_TIMESTAMP)
    )
    SQL
    RetailerUser
      .all_customers
      .where(retailer_admin: false, retailer_supervisor: false)
      .update_all(update_non_assigned_sql)

    # Solo asignados
    update_only_assigned_sql = <<-SQL
    unread_messenger_chats_count = (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      INNER JOIN agent_customers ON agent_customers.customer_id = customers.id
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND agent_customers.retailer_user_id = retailer_users.id
      AND customers.pstype = 0
      AND customers.unread_messenger_messages > 0
      AND (customers.last_msn_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_msn_interaction < CURRENT_TIMESTAMP)
    )
    SQL
    RetailerUser
      .only_assigned_customers
      .update_all(update_only_assigned_sql)
  end

  task update_unread_instagram_chats_count: :environment do
    # Admins y supervisores
    update_admins_sql = <<-SQL
    unread_instagram_chats_count = (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND customers.pstype = 1
      AND customers.unread_instagram_messages > 0
      AND (customers.last_ig_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_ig_interaction < CURRENT_TIMESTAMP)
    )
    SQL
    RetailerUser
      .where('retailer_users.retailer_admin = TRUE OR retailer_users.retailer_supervisor = TRUE')
      .update_all(update_admins_sql)

    # Asignados y no asignados
    update_non_assigned_sql = <<-SQL
    unread_instagram_chats_count = (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      INNER JOIN agent_customers ON agent_customers.customer_id = customers.id
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND agent_customers.retailer_user_id = retailer_users.id
      AND customers.pstype = 1
      AND customers.unread_instagram_messages > 0
      AND (customers.last_ig_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_ig_interaction < CURRENT_TIMESTAMP)
    ) + (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      LEFT JOIN agent_customers ON agent_customers.customer_id = customers.id
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND customers.pstype = 1
      AND customers.unread_instagram_messages > 0
      AND agent_customers.customer_id IS NULL
      AND (customers.last_ig_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_ig_interaction < CURRENT_TIMESTAMP)
    )
    SQL
    RetailerUser
      .all_customers
      .where(retailer_admin: false, retailer_supervisor: false)
      .update_all(update_non_assigned_sql)

    # Solo asignados
    update_only_assigned_sql = <<-SQL
    unread_instagram_chats_count = (
      SELECT COUNT(DISTINCT customers.id)
      FROM customers
      INNER JOIN agent_customers ON agent_customers.customer_id = customers.id
      WHERE customers.retailer_id = retailer_users.retailer_id
      AND agent_customers.retailer_user_id = retailer_users.id
      AND customers.pstype = 1
      AND customers.unread_instagram_messages > 0
      AND (customers.last_ig_interaction >= date_trunc('week', CURRENT_TIMESTAMP - interval '4 week')
           AND customers.last_ig_interaction < CURRENT_TIMESTAMP)
    )
    SQL
    RetailerUser
      .only_assigned_customers
      .update_all(update_only_assigned_sql)
  end

  task update_unread_ml_count: :environment do
    RetailerUser.update_all(
      <<-SQL
      unread_ml_questions_count = (
        SELECT COUNT(DISTINCT questions.id)
        FROM questions
        INNER JOIN customers ON customers.id = questions.customer_id
        WHERE (questions.product_id IS NOT NULL)
        AND questions.date_read IS NULL
        AND customers.retailer_id = retailer_users.retailer_id
      ),
      unread_ml_chats_count = (
        SELECT COUNT(DISTINCT orders.id)
        FROM orders
        INNER JOIN customers ON customers.id = orders.customer_id
        WHERE orders.count_unread_messages > 0
        AND customers.retailer_id = retailer_users.retailer_id
      )
      SQL
    )
    RetailerUser.update_all(
      <<-SQL
      total_unread_ml_count = unread_ml_questions_count + unread_ml_chats_count
      SQL
    )
    RetailerUser.where('total_unread_ml_count > 0').update_all(ml_unread: true)
  end

  task update_token_expiration: :environment do
    RetailerUser.where.not(api_session_expiration: nil)
      .where('api_session_expiration > ?', Time.now)
      .update_all(api_session_expiration: 1.year.from_now)
  end

  task set_default_managed_agents: :environment do
    RetailerUser.in_batches do |batch|
      batch.update_all(managed_agents: [])
    end
  end

  task set_default_notifications: :environment do
    RetailerUser.in_batches do |batch|
      batch.update_all(notifications_payload: {})
    end
  end

  desc 'Set current_mobile field for first time replicating the current field value'
  task set_current_mobile: :environment do
    RetailerUser.update_all('current_mobile = current')
  end
end
