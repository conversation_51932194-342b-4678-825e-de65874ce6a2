# Simple data object to hold cached retailer counter data
class CachedRetailerCounter
  attr_reader :id, :retailer_id, :ws_messages, :ws_messages_na, :fb_messages, :fb_messages_na,
              :ig_messages, :ig_messages_na, :fb_comments, :fb_comments_na, :ig_comments, :ig_comments_na

  def initialize(data, retailer_id)
    @id = data['_id']
    @retailer_id = retailer_id
    @ws_messages = data['ws_messages'] || 0
    @ws_messages_na = data['ws_messages_na'] || 0
    @fb_messages = data['fb_messages'] || 0
    @fb_messages_na = data['fb_messages_na'] || 0
    @ig_messages = data['ig_messages'] || 0
    @ig_messages_na = data['ig_messages_na'] || 0
    @fb_comments = data['fb_comments'] || 0
    @fb_comments_na = data['fb_comments_na'] || 0
    @ig_comments = data['ig_comments'] || 0
    @ig_comments_na = data['ig_comments_na'] || 0
  end

  def from_cache?
    true
  end

  def readonly?
    false
  end
end

class RetailerCounterCache
  THREAD_KEY = :retailer_counter_cache
  CACHE_TTL = 30.seconds # Cache for 30 seconds across requests

  def self.fetch(retailer_id)
    is_webhook = webhook_context?

    # For webhook contexts, skip thread-local cache and go directly to Redis/MongoDB
    # This prevents issues with thread isolation in webhook processing
    if is_webhook
      return fetch_for_webhook(retailer_id)
    end

    # First check thread-local cache (fastest)
    thread_cached = cache[retailer_id]
    return thread_cached if thread_cached

    # Then check Redis cache (shared across requests/threads)
    redis_cached = fetch_from_redis(retailer_id)
    if redis_cached
      wrapped_redis = wrap_with_cache_invalidation(redis_cached, retailer_id)
      cache[retailer_id] = wrapped_redis
      return wrapped_redis
    end

    # Finally, fetch from MongoDB and cache in both places
    retailer_counter = RetailerCounter.find_or_create_by(retailer_id: retailer_id)

    # Wrap the counter to invalidate cache on updates
    wrapped_counter = wrap_with_cache_invalidation(retailer_counter, retailer_id)
    cache[retailer_id] = wrapped_counter
    store_in_redis(retailer_id, retailer_counter)
    wrapped_counter
  end

  def self.clear!
    Thread.current[THREAD_KEY] = {}
    # Clear webhook context cache keys
    Thread.current.keys.select { |key| key.to_s.start_with?('webhook_context_') }.each do |key|
      Thread.current[key] = nil
    end
  end

  def self.cache
    Thread.current[THREAD_KEY] ||= {}
  end

  def self.invalidate(retailer_id)
    # Clear from thread-local cache
    cache.delete(retailer_id)

    # Clear from Redis cache
    redis_client.del(redis_key(retailer_id))
  rescue StandardError => e
    Rails.logger.warn "Failed to invalidate retailer_counter cache: #{e.message}"
  end

  def self.wrap_with_cache_invalidation(retailer_counter, retailer_id)
    # Create a wrapper class that automatically invalidates cache
    wrapper = Class.new do
      def initialize(counter, retailer_id)
        @counter = counter
        @retailer_id = retailer_id
      end

      def method_missing(method, *args, &block)
        # For write operations, ensure we have a fresh copy from MongoDB if needed
        if [:inc, :update, :save, :update_attributes, :update_columns].include?(method)
          # If this is a cached object, fetch fresh from MongoDB
          if @counter.is_a?(CachedRetailerCounter)
            @counter = RetailerCounter.find_by(id: @counter.id) ||
                       RetailerCounter.find_or_create_by(retailer_id: @retailer_id)
          end
        end

        result = @counter.send(method, *args, &block)

        # Invalidate cache after any update operation
        if [:inc, :update, :save, :update_attributes, :update_columns].include?(method)
          RetailerCounterCache.invalidate(@retailer_id)
        end

        result
      rescue StandardError => e
        Rails.logger.error "[RetailerCounterCache] Error in #{method}: #{e.message}"
        raise e
      end

      def respond_to_missing?(method, include_private = false)
        @counter.respond_to?(method, include_private)
      end

      # Delegate common methods directly for performance
      def ws_messages
        @counter.ws_messages
      end

      def ws_messages_na
        @counter.ws_messages_na
      end

      def fb_messages
        @counter.fb_messages
      end

      def fb_messages_na
        @counter.fb_messages_na
      end

      def ig_messages
        @counter.ig_messages
      end

      def ig_messages_na
        @counter.ig_messages_na
      end

      def fb_comments
        @counter.fb_comments
      end

      def fb_comments_na
        @counter.fb_comments_na
      end

      def ig_comments
        @counter.ig_comments
      end

      def ig_comments_na
        @counter.ig_comments_na
      end

      def id
        @counter.id
      end

      def reload
        # Only reload if it's a real Mongoid object
        if @counter.is_a?(RetailerCounter)
          @counter.reload
        else
          # For cached objects, fetch fresh from MongoDB
          @counter = RetailerCounter.find_by(id: @counter.id) ||
                     RetailerCounter.find_or_create_by(retailer_id: @retailer_id)
        end
      end

      def readonly?
        @counter.readonly?
      end
    end

    wrapper.new(retailer_counter, retailer_id)
  end

  class << self
    private

    def webhook_context?
      # Cache the webhook context check within the same call stack to avoid expensive caller.any? calls
      # Use a more specific cache key that includes the current call stack depth to avoid cross-request pollution
      cache_key = "webhook_context_#{caller.length}"
      cached_result = Thread.current[cache_key]
      return cached_result unless cached_result.nil?

      # Detect if we're in a webhook context by checking for common webhook indicators
      is_webhook = Thread.current[:webhook_processing] ||
                   caller.any? { |line|
                     line.include?('webhook') ||
                     line.include?('api/v1') ||
                     line.include?('whatsapp_controller') ||
                     line.include?('inbound/message')
                   }

      # Cache the result with a call-stack-specific key that naturally expires
      Thread.current[cache_key] = is_webhook
      is_webhook
    end

    def fetch_for_webhook(retailer_id)
      # For webhooks, use Redis cache or go directly to MongoDB
      # Don't use thread-local cache to avoid thread isolation issues
      redis_cached = fetch_from_redis(retailer_id)
      if redis_cached
        return wrap_with_cache_invalidation(redis_cached, retailer_id)
      end

      # Fetch from MongoDB and cache in Redis only
      retailer_counter = RetailerCounter.find_or_create_by(retailer_id: retailer_id)
      wrapped_counter = wrap_with_cache_invalidation(retailer_counter, retailer_id)
      store_in_redis(retailer_id, retailer_counter)
      wrapped_counter
    end

    def fetch_from_redis(retailer_id)
      cached_data = redis_client.get(redis_key(retailer_id))
      return nil unless cached_data

      # Deserialize the cached RetailerCounter data
      data = JSON.parse(cached_data)

      # Return a simple data object instead of a Mongoid object
      # This avoids Mongoid-related issues and queries
      CachedRetailerCounter.new(data, retailer_id)
    rescue StandardError => e
      Rails.logger.warn "Failed to fetch retailer_counter from Redis: #{e.message}"
      nil
    end

    def store_in_redis(retailer_id, retailer_counter)
      data = retailer_counter.attributes.merge('_id' => retailer_counter.id)
      redis_client.setex(redis_key(retailer_id), CACHE_TTL, data.to_json)
    rescue StandardError => e
      Rails.logger.warn "Failed to store retailer_counter in Redis: #{e.message}"
    end

    def redis_client
      @redis_client ||= Redis.new
    end

    def redis_key(retailer_id)
      "retailer_counter_cache:#{retailer_id}"
    end
  end
end
