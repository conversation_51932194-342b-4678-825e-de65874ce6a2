{"name": "mercately", "private": true, "version": "0.1.0", "engines": {"node": "22.10.0"}, "dependencies": {"@babel/preset-react": "^7.25.9", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@rails/webpacker": "6.0.0-rc.6", "@react-google-maps/api": "^2.20.3", "@react-pdf/renderer": "4.0.0", "@socket.io/redis-adapter": "^8.3.0", "@trendyol-js/react-carousel": "^3.0.2", "@typeform/embed-react": "^4.4.0", "axios": "^1.7.7", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "big.js": "^6.2.2", "chart.js": "^4.4.6", "classnames": "^2.5.1", "clsx": "^2.1.1", "core-js": "^3.39.0", "cors": "^2.8.5", "countries-list": "^3.1.1", "country-list": "^2.3.0", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "currency-list": "^1.0.8", "currency-symbol-map": "^5.1.0", "d3-ease": "^3.0.1", "d3-hierarchy": "^3.1.2", "date-fns": "^4.1.0", "emoji-mart": "^5.2.2", "express": "^4.21.1", "faker": "6.6.6", "flag-icon-css": "^4.1.7", "google-libphonenumber": "^3.2.38", "google-maps-react": "^2.0.6", "history": "^5.3.0", "i18n-iso-countries": "^7.14.0", "i18next": "^24.2.2", "i18next-parser": "^9.1.0", "i18next-polyglot": "^0.1.0", "ioredis": "^5.4.1", "localforage": "^1.10.0", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "mini-css-extract-plugin": "^2.9.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "numeral": "^2.0.6", "path": "^0.12.7", "pdfjs-dist": "4.8", "postcss-flexbugs-fixes": "^5.0.2", "postcss-focus": "^7.0.0", "postcss-import": "^16.1.0", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.0.9", "prop-types": "^15.8.1", "react": "^18.3.1", "react-actioncable-provider": "^2.0.0", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.10.5", "react-bootstrap-daterangepicker": "^8.0.0", "react-card-brand": "^0.1.6", "react-chartjs-2": "^5.2.0", "react-chartkick": "^0.5.4", "react-circular-progressbar": "^2.1.0", "react-color": "^2.19.3", "react-confirm-alert": "3.0.6", "react-content-loader": "^7.0.2", "react-date-range": "^2.0.1", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.1", "react-i18next": "^15.4.0", "react-image-lightbox": "^5.1.4", "react-modal": "^3.16.1", "react-move": "^6.5.0", "react-paginate": "^8.2.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router": "^7.0.1", "react-router-dom": "^7.0.1", "react-router-redux": "^4.0.8", "react-select": "^5.8.2", "react-select-async-paginate": "^0.7.10", "react-show-more-text": "^1.7.1", "react-social-media-embed": "^2.5.17", "react-tooltip": "^5.28.0", "reactflow": "^11.11.4", "read-excel-file": "^5.8.6", "recharts": "^2.15.0", "redux": "^5.0.1", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "rsuite": "^5.74.1", "rxjs": "^7.8.1", "sass": "^1.80.6", "sass-loader": "^16.0.3", "socket.io": "^4.8.1", "style-loader": "^4.0.0", "styled-components": "^6.1.13", "tailwindcss": "^3.4.16", "terser": "^5.36.0", "uuid": "^11.0.2", "validator": "^13.12.0", "webpack": "^5.96.1", "webpack-cli": "^5.1.4", "yoga-layout-prebuilt": "^1.10.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/eslint-parser": "^7.25.9", "@eslint/compat": "^1.2.2", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.14.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "eslint": "9.14.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-i18next": "^6.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.9.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "file-loader": "^6.2.0", "globals": "^15.12.0", "jest": "^29.7.0", "webpack-dev-server": "^4.15.2"}, "browserslist": ["defaults"], "babel": {"presets": ["./node_modules/@rails/webpacker/package/babel/preset.js", "@babel/preset-react"]}}