APPNAME_DATABASE_PASSWORD=xxxxx

CLOUDINARY_API_KEY=xxxxx
CLOUDINARY_API_SECRET=xxxxx
CLOUDINARY_CLOUD_NAME=xxxxx

DATIL_API_KEY=xxxxx
DATIL_API_PASSWORD=xxxxx

DATIL_ENVIRONMENT=xxxxx

DB_IDLE_TIMEOUT=xxxxx
DB_CHECKOUT_TIMEOUT=xxxxx

DOMAIN=xxxxx

ENVIRONMENT=xxxxx

FACEBOOK_APP_ID=xxxxx
FACEBOOK_APP_SECRET=xxxxx
FACEBOOK_PIXEL_ID=xxxxx
FACEBOOK_VERIFY_TOKEN=xxxxx

FCM_KEY=xxxxx

GOOGLE_API_KEY=xxxxx
GOOGLE_TRACKING_ID=xxxxx
GOOGLE_TAG_ID=xxxxx

GITHUB_TOKEN=xxxxx
GITHUB_USER=xxxxx

GUPSHUP_API_KEY=xxxxx
GUPSHUP_EMAIL_PARTNER=xxxxx
GUPSHUP_PASS_PARTNER=xxxxx

HUBSPOT_APP=xxxxx
HUBSPOT_AUTH_URL=xxxxx
HUBSPOT_CLIENT=xxxxx
HUBSPOT_CLIENT_SECRET=xxxxx
HUBSPOT_REDIRECT_URL=xxxxx

KARIX_ACCOUNT_TOKEN=xxxxx
KARIX_ACCOUNT_UID=xxxxx
KARIX_WEBHOOK=xxxxx

LINKEDIN_PARTNER_ID=xxxxx

MAILCHIMP_KEY=xxxxx

MERCADO_LIBRE_ID=xxxxx
MERCADO_LIBRE_KEY=xxxxx
MERCADO_LIBRE_REDIRECT_URI=https://example.com/retailers/integrations/mercadolibre

MERCATELY_AUTH_TOKEN=xxxxx
MERCATELY_DATABASE_USER=xxxxx

MIA_NOTIFICATION_EMAIL=<EMAIL>

PAYMENTEZ_CODE_CLIENT=xxxxx
PAYMENTEZ_CODE_SERVER=xxxxx
PAYMENTEZ_SECRET_CLIENT=xxxxx
PAYMENTEZ_SECRET_SERVER=xxxxx
PAYMENTEZ_URL=xxxxx

PRISMIC_TOKEN=xxxxx
PRISMIC_URL=xxxxx

RAILS_MAX_THREADS=xxxxx

RECAPTCHA_SECRET_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
RECAPTCHA_SECRET_KEY_V2=6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe
RECAPTCHA_SITE_KEY=xxxxx
RECAPTCHA_SITE_KEY_V2=xxxxx

REDIS_BG_JOB_PROVIDER=xxxxx
REDIS_PROVIDER=xxxxx

SECRET_KEY_BASE=xxxxx

SENDGRID_PASSWORD=xxxxx
SENDGRID_USERNAME=xxxxx

SHOPIFY_MESSAGES_URL=xxxxx

SLACK_DEBUG=xxxxx
SLACK_GS_TEMPLATES=xxxxx
SLACK_MARKETING=xxxxx
SLACK_MSG_ERROR=xxxxx
SLACK_QR_INTEGRATIONS=xxxxx
SLACK_SDRLEADS=xxxxx
SLACK_WEBHOOK=https://hooks.slack.com/services/XXXXXXX

STRIPE_GATEWAY_ACCOUNT_ID=xxxxx
STRIPE_PUBLISHABLE_KEY=xxxxx
STRIPE_SECRET=xxxxx
STRIPE_SECRET_KEY=xxxxx
STRIPE_PUBLISHABLE=xxxxx

GITHUB_USER=xxxxxxxxx
GITHUB_TOKEN=xxxxxxxxx
USE_SSH=false
SSH_REPO=**************:ThoughtCode/mercately.git

PARALLEL_PROCESSES=4
POSTGRES_DB=myapp_test


MIA_TUNNEL_SSH_HOST=xxxxxxxxx
MIA_TUNNEL_SSH_PORT=xxxxxxxxx
MIA_TUNNEL_SSH_USER=xxxxxxxxx
MIA_TUNNEL_SSH_KEY_PATH=xxxxxxxxx
MIA_TUNNEL_SSH_REMOTE_FILE_PATH=xxxxxxxxx
MIA_STATS_SECRET_KEY=xxxxxxxxx
MIA_STATS_API_KEY=xxxxxxxxx
MIA_PRODUCTS_SERVICE_URL=xxxxxxxxx
MIA_GUIDES_SERVICE_URL=xxxxxxxxx
MIA_SUGGESTIONS_SERVICE_URL=xxxxxxxxx
MIA_MULTI_TENANT_SECRET_KEY=xxxxxxxxx

DEAL_ENGINE_BASE_URL=xxxxxxxxxx
JWT_ISSUER=xxxxxxxx

# WebSocket URL (debe incluir el slash final)
WEBSOCKET_URL=https://wss.mercately.com/

MIA_NEW_PROMPTS=false

PIPEDREAM_CLIENT_ID=xxxxxxxxxx
PIPEDREAM_CLIENT_SECRET=xxxxxxxxxx
PIPEDREAM_PROJECT_ID=xxxxxxxxxx
PIPEDREAM_PROJECT_ENVIRONMENT=xxxxxxxxxx

# ===== KAFKA CONFIGURATION =====
# Configuración para estrategias de envío de campañas

# Habilita Kafka a nivel global
KAFKA_ENABLED=false

# Habilita la producción de mensajes a Kafka
KAFKA_PRODUCER_ENABLED=false

# Habilita el consumo de mensajes de Kafka (solo en nodos Karafka)
KAFKA_CONSUMER_ENABLED=false

# Modo API (para nodos que solo manejan API)
API_MODE=false

# Usa la estrategia de Kafka para enviar mensajes de campaña
USE_KAFKA_SENDER=false

# Configuración de conexión a Kafka
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_CLIENT_ID=mercately_rails6_development
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_SASL_MECHANISM=PLAIN
KAFKA_USERNAME=xxxxx
KAFKA_PASSWORD=xxxxx
KAFKA_CA_CERT=/path/to/ca-certificate.crt
KAFKA_PRODUCER_POOL_SIZE=5
KAFKA_PRODUCER_MAX_IDLE_TIME=300
