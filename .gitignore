*.rbc
*.png~
/tags
capybara-*.html
/log
/tmp
/public/system
/coverage/
/storage/
/spec/tmp
*.orig
rerun.txt
pickle-email-*.html

# TODO Comment out this rule if you are OK with secrets being uploaded to the repo
config/initializers/secret_token.rb
config/master.key

# Only include if you have production secrets in this file, which is no longer a Rails default
config/secrets.yml
config/database.yml
config/storage.yml

# dotenv
# TODO Comment out this rule if environment variables can be committed
.env

## Environment normalization:
/.bundle
/vendor/bundle

# If using bower-rails ignore default bower_components path bower.json files
/vendor/assets/bower_components
*.bowerrc
bower.json

# Ignore pow environment settings
.powenv

# Ignore Byebug command history file.
.byebug_history

# Ignore node_modules
node_modules/

# Ignore Storage
storage/

/public/packs
/public/packs-test
/node_modules
/yarn-error.log
yarn-debug.log*
.yarn-integrity
.generators
.idea/
.rakeTasks
/dump.rdb
.DS_Store
.node-version
.tool-versions

.editorconfig
reports/rspec.xml
/reports
reports/result.xml

/app/assets/builds/*
!/app/assets/builds/.keep
/.yarn
/.vscode
config/initializers/mongo.rb
/public/assets
.history/

.vscode/
/.github/instructions
.github/copilot-instructions.md
/.github
