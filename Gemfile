source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.2.5'

# Core gems
gem 'bootsnap', require: false

gem 'rails', '~> 6.1.7.8'
gem 'sass-rails'
gem 'webpacker', '6.0.0.rc.6'
gem 'puma', '6.5.0'

# Authentication and Authorization
gem 'activeadmin', '3.2.5'
gem 'activeadmin_froala_editor', '1.1.0'
gem 'activeadmin-searchable_select', '1.8.0'
gem 'cancancan', '3.6.1'
gem 'devise', '4.9.4'
gem 'devise_invitable', '2.0.9'
gem 'jwt', '2.9.3'
gem 'oauth2', '2.0.9'
gem 'omniauth', '2.1.2'
gem 'omniauth-facebook', '10.0.0'
gem 'omniauth-oauth2', '1.8.0'
gem 'omniauth-rails_csrf_protection', '1.0.2'

# API and Data Serialization
gem 'active_model_serializers', '0.10.14'
gem 'jsonapi-serializer', '2.2.0'
gem 'rack-cors', '2.0.2'

# Database and ORM
gem 'activerecord-import', '1.8.1'
gem 'activerecord-precounter', '0.4.0'
gem 'activerecord-session_store', '2.1.0'
gem 'ancestry', '4.3.3'
gem 'globalid', '1.2.1'
gem 'mongoid', '9.0.3'
gem 'mongoid_enumerable', '~> 0.4.4'
gem 'pg', '1.5.9'
gem 'pg_partition_manager', '0.1.0'
gem 'with_advisory_lock', '5.1.0'

# File Processing
gem 'activestorage-cloudinary-service', '0.2.3'
gem 'aws-sdk-s3', '1.174.0'
gem 'caxlsx', '4.1.0'
gem 'caxlsx_rails', '0.6.4'
gem 'cloudinary', '2.2.0'
gem 'csv', "~> 3.3.0"
gem 'mini_magick', '5.0.1'
gem 'roo', '2.10.1'
gem 'rubyzip', '2.3.2'
gem 'streamio-ffmpeg', '3.0.2'
gem 'xlsxtream', '2.4.0'

# Frontend and UI
gem 'bootstrap', '~> *******'
gem 'bootstrap-daterangepicker-rails', '3.0.4'
gem 'chartkick', '5.1.2'
gem 'ckeditor_rails', '4.17.0'
gem 'cocoon', '1.2.15'
gem 'coffee-rails', '5.0.0'
gem 'coffee-script', '2.4.1'
gem 'font_awesome5_rails', '1.5.0'
gem 'groupdate', '6.4.0'
gem 'jquery-rails', '4.6.0'
gem 'momentjs-rails', '********'
gem 'pagy', '9.3.1'
gem 'select2-rails', '4.0.13'

# Integrations and APIs
gem 'ably-rest', '1.2.7'
gem 'exponent-server-sdk', '0.1.0'
gem 'fcm', '2.0.1'
gem 'gibbon', '3.5.0'
gem 'hubspot-ruby', '0.9.0'
gem 'mailgun-ruby', '1.2.15'
gem 'net-scp', '4.0.0'
gem 'net-ssh', '7.3.0'
gem 'prismic.io', '1.8.2', require: 'prismic'
gem 'slack-notifier', '2.4.0'
gem 'socket.io-rails', '2.3.0'

# Payments and Transactions
gem 'chargebee', '2.46.0'
gem 'stripe', '13.2.0'

# Security and Encryption
gem 'attr_encrypted', '4.1.1'
gem 'fernet', '2.3'
gem 'rack-attack', '6.7.0'
gem 'recaptcha', '5.17.0'

# Utilities
gem 'country_select', '10.0.0'
gem 'currency_select', '7.0.0'
gem 'faraday', '2.12.2'
gem 'faraday-multipart', '1.1.0'
gem 'interactor-rails', '2.2.1'
gem 'mime-types', '3.6.0'
gem 'phonelib', '0.6.43'
gem 'phony', '2.20.15'
gem 'premailer-rails', '1.12.0'
gem 'prometheus-client', '4.2.3'
gem 'puma-metrics', '1.4.0'
gem 'rack-reverse-proxy', '0.12.0', require: 'rack/reverse_proxy'
gem 'sidekiq', '<7'
gem 'unaccent', '0.4.0'
gem 'vpim', '24.2.20'

group :production do
  source 'https://gems.contribsys.com/' do
    gem 'sidekiq-pro', '<7'
  end
end

# Internal gems (available in all environments)
source 'https://rubygems.pkg.github.com/ThoughtCode' do
  gem 'mercately_churnzero_extension', '3.12.1'
  gem 'mercately_mia_api', '1.19.0'
  # Kafka gems
  gem 'mercately-kafka', '0.1.4'
  # Instalar siempre pero cargar condicionalmente en tiempo de ejecución
  gem 'mercately-karafka', '0.1.12', require: false
end

group :development, :test do
  gem 'async'
  gem 'brakeman', '6.2.2'
  gem 'bundler-audit', '0.9.2'
  gem 'byebug', platforms: [:mri, :mingw, :x64_mingw]
  gem 'dotenv-rails'
  gem 'factory_bot_rails', '6.4.4'
  gem 'faker', '3.5.1'
  gem 'memory_profiler', '1.1.0'
  gem 'parallel_tests', '4.7.2'
  gem 'rack-mini-profiler', '3.3.1'
  gem 'rspec_junit_formatter', '0.6.0'
  gem 'rspec-rails', '6.1.5'
  gem 'rubocop', '1.69.0', require: false
  gem 'rubocop-factory_bot', '2.26.1', require: false
  gem 'rubocop-performance', '1.23.0', require: false
  gem 'rubocop-rails', '2.27.0', require: false
  gem 'rubocop-rspec', '3.2.0', require: false
  gem 'rubocop-rspec_rails', '2.30.0', require: false
  gem 'stackprof', '0.2.26'
  gem 'undercover', '0.5.0'
  gem 'rspec-sqlimit'
end

group :development do
  gem 'capistrano', require: false
  gem 'capistrano-rvm', require: false
  gem 'capistrano-bundler', require: false
  gem 'capistrano-rails', require: false
  gem 'capistrano-rails-console'
  gem 'capistrano-rails-logs-tail'
  gem 'capistrano-systemd-multiservice', require: false
  gem 'letter_opener', '1.10.0'
  gem 'listen', '3.9.0'
  gem 'rb-readline'
  gem 'web-console', '4.2.1'
end

group :test do
  gem 'dox', '2.4.0', require: false
  gem 'ffprobe', '0.1.0'
  gem 'mongoid-rspec', '4.2.0'
  gem 'rails-controller-testing', '1.0.5'
  gem 'shoulda-matchers', '6.4.0'
  gem 'simplecov', '0.22.0', require: false
  gem 'simplecov-lcov', '0.8.0', require: false
  gem 'timecop', '0.9.10'
  gem 'vcr', '6.3.1'
  gem 'webmock', '3.24.0'
  # Removed Capybara and related gems for a different testing approach
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]
gem 'json', '2.7.1'
gem 'json_pure', '2.6.3'
gem 'webrick', '1.9.0'

gem 'uglifier', '>= 1.3.0'

gem "shopify_app", "~> 22.5"
gem "shopify_api", "~> 14.7"
gem 'humanize', '~> 3.0.0'

gem "geocoder", "~> 1.8"

gem "tailwindcss-rails", "~> 2.5"
