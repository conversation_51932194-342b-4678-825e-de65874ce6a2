require 'rails_helper'

RSpec.describe Api::Mia::GuidesController do
  let(:retailer) { create(:retailer) }
  let(:retailer_user) { create(:retailer_user, :admin, retailer: retailer) }
  let(:body_response) { response.parsed_body }
  let(:mia_integration_type) { create(:mia_integration_type, :guides) }

  before { sign_in retailer_user.user }

  describe '#index' do
    let(:get_request) { get api_mia_guides_path }

    context 'with guide integration' do
      before do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
        allow_any_instance_of(MercatelyMiaApi::Guide).to receive(:list).and_return(guides_object)
        get_request
      end

      describe 'successfully' do
        let(:guides_object) { double(success?: true, status: 200, body: { 'guides' => [] }) }

        it { expect(response).to have_http_status(:ok) }
        it { expect(body_response.keys).to include 'guides' }
      end

      describe 'not found' do
        let(:guides_response) { { 'error' => 'Retailer not found' } }
        let(:guides_object) { double(success?: false, status: 404, body: guides_response) }

        it { expect(response).to have_http_status(:not_found) }
        it { expect(body_response['error']).to match 'Retailer not found' }
      end

      describe 'bad request' do
        let(:guides_response) do
          { 'errors' => [{ 'field' => 'API Key', 'message' => 'Error decrypting the API key' }] }
        end
        let(:guides_object) { double(success?: false, status: 400, body: guides_response) }

        it { expect(response).to have_http_status(:bad_request) }
        it { expect(body_response['errors']).not_to be_empty }
      end
    end

    context 'without guide integration' do
      before { get_request }

      it { expect(response).to have_http_status(:internal_server_error) }
    end
  end

  describe '#show' do
    let(:guide_id) { 'eda10354-3254-4c86-96d0-90152533de85' }
    let(:get_request) { get api_mia_guide_path(guide_id) }

    context 'with guide integration' do
      before do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
        allow_any_instance_of(MercatelyMiaApi::Guide).to receive(:retrieve).and_return(guides_object)
        get_request
      end

      describe 'successfully' do
        let(:guides_object) { double(success?: true, status: 200, body: guides_response) }
        let(:guides_response) do
          {
            'guide' => {
              'created_by' => {
                'first_name' => 'Johnmer',
                'full_name' => 'Johnmer Bencomo',
                'last_name' => 'Bencomo',
                'user_id' => 776_576
              },
              'guide_id' => 'eda10354-3254-4c86-96d0-90152533de85',
              'instructions' => 'hola estas son las intrucciones nuevas',
              'specific_instructions' => [
                'instruction' => 'Instruction 1',
                'instruction_type' => 'image',
                'files' => ['image.png']
              ],
              'name' => 'Guía de Ejemplo uuid',
              'timestamp' => 'Wed, 14 Aug 2024 03:01:28 GMT',
              'topics' => %w[topic1 topic2 topic3]
            }
          }
        end

        it { expect(response).to have_http_status(:ok) }
        it { expect(body_response.keys).to include 'guide' }
        it { expect(body_response['guide']['guide_id']).to eq guide_id }
      end

      describe 'not found' do
        describe 'retailer' do
          let(:guides_response) { { 'error' => 'Retailer not found' } }
          let(:guides_object) { double(success?: false, status: 404, body: guides_response) }

          it { expect(response).to have_http_status(:not_found) }
          it { expect(body_response['error']).to match 'Retailer not found' }
        end

        describe 'guide' do
          let(:guides_response) { { 'error' => 'Guide not found' } }
          let(:guides_object) { double(success?: false, status: 404, body: guides_response) }

          it { expect(response).to have_http_status(:not_found) }
          it { expect(body_response['error']).to match 'Guide not found' }
        end
      end

      describe 'bad request' do
        let(:guides_response) do
          { 'errors' => [{ 'field' => 'API Key', 'message' => 'Error decrypting the API key' }] }
        end
        let(:guides_object) { double(success?: false, status: 400, body: guides_response) }

        it { expect(response).to have_http_status(:bad_request) }
        it { expect(body_response['errors']).not_to be_empty }
      end
    end

    context 'without guide integration' do
      before { get_request }

      it { expect(response).to have_http_status(:internal_server_error) }
    end
  end

  describe '#create' do
    let(:post_request) { post api_mia_guides_path, params: params }
    let(:params) do
      {
        name: 'Guía de Ejemplo uuid',
        instructions: 'hola estas son las intrucciones nuevas',
        topics: %w[topic1 topic2 topic3],
        specific_instructions: [
          instruction: 'Instruction 1',
          instruction_type: 'image',
          files: ['image.png']
        ],
        created_by: {
          user_id: 776_576,
          first_name: 'Johnmer',
          last_name: 'Bencomo',
          full_name: 'Johnmer Bencomo'
        }
      }
    end

    context 'with guide integration' do
      before do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
        allow_any_instance_of(MercatelyMiaApi::Guide).to receive(:create).and_return(guides_object)
        post_request
      end

      describe 'successfully' do
        let(:guides_object) { double(success?: true, status: 200, body: guides_response) }
        let(:guides_response) do
          {
            'message' => 'Guide saved successfully',
            'guide' => {
              'created_by' => {
                'first_name' => 'Johnmer',
                'full_name' => 'Johnmer Bencomo',
                'last_name' => 'Bencomo',
                'user_id' => 776_576
              },
              'guide_id' => 'eda10354-3254-4c86-96d0-90152533de85',
              'instructions' => 'hola estas son las intrucciones nuevas',
              'specific_instructions' => [
                'instruction' => 'Instruction 1',
                'instruction_type' => 'image',
                'files' => ['image.png']
              ],
              'name' => 'Guía de Ejemplo uuid',
              'timestamp' => 'Wed, 14 Aug 2024 03:01:28 GMT',
              'topics' => %w[topic1 topic2 topic3]
            }
          }
        end

        it { expect(response).to have_http_status(:ok) }
        it { expect(body_response.keys).to contain_exactly('guide', 'message') }
        it { expect(body_response['guide']['guide_id']).not_to be_nil }
        it { expect(body_response['message']).to match 'Guide saved successfully' }
      end

      describe 'bad request' do
        let(:guides_response) do
          { 'errors' => [{ 'field' => 'API Key', 'message' => 'Error decrypting the API key' }] }
        end
        let(:guides_object) { double(success?: false, status: 400, body: guides_response) }

        it { expect(response).to have_http_status(:bad_request) }
        it { expect(body_response['errors']).not_to be_empty }
      end
    end

    context 'without guide integration' do
      before { post_request }

      it { expect(response).to have_http_status(:internal_server_error) }
    end
  end

  describe '#update' do
    let(:guide_id) { 'eda10354-3254-4c86-96d0-90152533de85' }
    let(:put_request) { put api_mia_guide_path(guide_id), params: params }
    let(:params) do
      {
        name: 'Guía de Ejemplo uuid',
        instructions: 'hola estas son las intrucciones nuevas',
        specific_instructions: [
          instruction: 'Instruction 1 modified',
          instruction_type: 'image',
          files: ['image.png']
        ],
        topics: %w[topic1 topic2 topic3]
      }
    end

    context 'with guide integration' do
      before do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
        allow_any_instance_of(MercatelyMiaApi::Guide).to receive(:update).and_return(guides_object)
        put_request
      end

      describe 'successfully' do
        let(:guides_object) { double(success?: true, status: 200, body: guides_response) }
        let(:guides_response) do
          {
            'id' => 'eda10354-3254-4c86-96d0-90152533de85',
            'message' => 'Guide successfully updated',
            'data' => {
              'created_by' => {
                'first_name' => 'Johnmer',
                'full_name' => 'Johnmer Bencomo',
                'last_name' => 'Bencomo',
                'user_id' => 776_576
              },
              'guide_id' => 'eda10354-3254-4c86-96d0-90152533de85',
              'instructions' => 'hola estas son las intrucciones nuevas',
              'specific_instructions' => [
                'instruction' => 'Instruction 1 modified',
                'instruction_type' => 'image',
                'files' => ['image.png']
              ],
              'name' => 'Guía de Ejemplo uuid',
              'timestamp' => 'Wed, 14 Aug 2024 03:01:28 GMT',
              'topics' => %w[topic1 topic2 topic3]
            }
          }
        end

        it { expect(response).to have_http_status(:ok) }
        it { expect(body_response.keys).to contain_exactly('id', 'message', 'data') }
        it { expect(body_response['data']['guide_id']).to eq guide_id }
        it { expect(body_response['id']).to eq guide_id }
        it { expect(body_response['message']).to eq 'Guide successfully updated' }
      end

      describe 'not found' do
        describe 'retailer' do
          let(:guides_response) { { 'error' => 'Retailer not found' } }
          let(:guides_object) { double(success?: false, status: 404, body: guides_response) }

          it { expect(response).to have_http_status(:not_found) }
          it { expect(body_response['error']).to match 'Retailer not found' }
        end

        describe 'guide' do
          let(:guides_response) { { 'error' => 'Guide not found' } }
          let(:guides_object) { double(success?: false, status: 404, body: guides_response) }

          it { expect(response).to have_http_status(:not_found) }
          it { expect(body_response['error']).to match 'Guide not found' }
        end
      end

      describe 'bad request' do
        let(:guides_response) do
          { 'errors' => [{ 'field' => 'API Key', 'message' => 'Error decrypting the API key' }] }
        end
        let(:guides_object) { double(success?: false, status: 400, body: guides_response) }

        it { expect(response).to have_http_status(:bad_request) }
        it { expect(body_response['errors']).not_to be_empty }
      end
    end

    context 'without guide integration' do
      before { put_request }

      it { expect(response).to have_http_status(:internal_server_error) }
    end
  end

  describe '#destroy' do
    let(:guide_id) { 'eda10354-3254-4c86-96d0-90152533de85' }
    let(:delete_request) { delete api_mia_guide_path(guide_id) }

    context 'with guide integration' do
      before do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
        allow_any_instance_of(MercatelyMiaApi::Guide).to receive(:delete).and_return(guides_object)
        delete_request
      end

      describe 'successfully' do
        let(:guides_object) { double(success?: true, status: 200, body: guides_response) }
        let(:guides_response) do
          {
            'id' => 'eda10354-3254-4c86-96d0-90152533de85',
            'message' => 'Guide successfully removed'
          }
        end

        it { expect(response).to have_http_status(:ok) }
        it { expect(body_response.keys).to contain_exactly('id', 'message') }
        it { expect(body_response['id']).to eq guide_id }
        it { expect(body_response['message']).to eq 'Guide successfully removed' }
      end

      describe 'not found' do
        describe 'retailer' do
          let(:guides_response) { { 'error' => 'Retailer not found' } }
          let(:guides_object) { double(success?: false, status: 404, body: guides_response) }

          it { expect(response).to have_http_status(:not_found) }
          it { expect(body_response['error']).to match 'Retailer not found' }
        end

        describe 'guide' do
          let(:guides_response) { { 'error' => 'Guide not found' } }
          let(:guides_object) { double(success?: false, status: 404, body: guides_response) }

          it { expect(response).to have_http_status(:not_found) }
          it { expect(body_response['error']).to match 'Guide not found' }
        end
      end

      describe 'bad request' do
        let(:guides_response) do
          { 'errors' => [{ 'field' => 'API Key', 'message' => 'Error decrypting the API key' }] }
        end
        let(:guides_object) { double(success?: false, status: 400, body: guides_response) }

        it { expect(response).to have_http_status(:bad_request) }
        it { expect(body_response['errors']).not_to be_empty }
      end
    end

    context 'without guide integration' do
      before { delete_request }

      it { expect(response).to have_http_status(:internal_server_error) }
    end
  end

  describe '#optimize' do
    let(:post_request) { post optimize_api_mia_guides_path, params: params }
    let(:params) { { instructions: '<p>Guía original</p>' } }

    context 'with guide integration' do
      before do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
        allow_any_instance_of(MercatelyMiaApi::Guide).to receive(:optimize).and_return(guides_object)
        post_request
      end

      describe 'successfully' do
        let(:guides_object) { double(success?: true, status: 200, body: guides_optimize_response) }
        let(:guides_optimize_response) { { 'instructions' => '<p>Guía optimizada</p>' } }

        it { expect(response).to have_http_status(:ok) }
        it { expect(body_response.keys).to contain_exactly('instructions') }
      end

      describe 'bad request' do
        let(:guides_response) do
          { 'errors' => [{ 'field' => 'API Key', 'message' => 'Error decrypting the API key' }] }
        end
        let(:guides_object) { double(success?: false, status: 400, body: guides_response) }

        it { expect(response).to have_http_status(:bad_request) }
        it { expect(body_response['errors']).not_to be_empty }
      end
    end

    context 'without guide integration' do
      before { post_request }

      it { expect(response).to have_http_status(:internal_server_error) }
    end

    context 'when raise a timeout error' do
      describe 'internal_server_error' do
        before do
          create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
          allow_any_instance_of(MercatelyMiaApi::Guide).to receive(:optimize).and_raise(Net::ReadTimeout)
          post_request
        end

        it { expect(response).to have_http_status(:internal_server_error) }
        it { expect(body_response['message']).not_to be_empty }
      end
    end
  end

  describe '#scrape' do
    let(:post_request) { post scrape_api_mia_guides_path, params: params }
    let(:params) { { url: 'https://example.com' } }

    context 'with guide integration' do
      before do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
        allow_any_instance_of(MercatelyMiaApi::Guide).to receive(:scrape).and_return(guides_object)
        post_request
      end

      describe 'successfully' do
        let(:guides_object) { double(success?: true, status: 200, body: { 'urls' => ['https://example.com/page1'] }) }

        it { expect(response).to have_http_status(:ok) }
        it { expect(body_response.keys).to include 'urls' }
      end

      describe 'bad request' do
        let(:guides_response) do
          { 'errors' => [{ 'field' => 'URL', 'message' => 'Invalid URL format' }] }
        end
        let(:guides_object) { double(success?: false, status: 400, body: guides_response) }

        it { expect(response).to have_http_status(:bad_request) }
        it { expect(body_response['errors']).not_to be_empty }
      end
    end

    context 'without guide integration' do
      before { post_request }

      it { expect(response).to have_http_status(:internal_server_error) }
    end
  end
end
