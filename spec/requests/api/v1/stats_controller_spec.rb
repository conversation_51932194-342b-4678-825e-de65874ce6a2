require 'rails_helper'

RSpec.describe Api::V1::StatsController do
  let(:retailer) { create(:retailer) }
  let(:retailer_user) { create(:retailer_user, retailer: retailer) }
  let(:customer) { create(:customer, retailer: retailer) }
  let(:start_date) { '2023-01-01' }
  let(:end_date) { '2023-01-31' }

  before do
    sign_in retailer_user.user
  end

  shared_examples 'a successful request' do |success = true|
    it_msg = success ? 'success' : 'failure'
    it "returns a #{it_msg} response" do
      get path, params: params
      expect(response).to have_http_status(:ok)
    end
  end

  shared_examples 'API response with expect' do |description, params, expected_status, expected_key,
    expected_data, check_size = false|
    it_msg = if [:ok, :created].include?(expected_status)
               "returns #{description}"
             else
               "returns a #{expected_status} response when #{description}"
             end
    it it_msg do
      get path, params: params
      expect(response).to have_http_status(expected_status)
      if check_size
        expect(response.parsed_body[expected_key].size).to eq(expected_data)
      else
        expect(response.parsed_body[expected_key]).to eq(expected_data)
      end
    end
  end

  describe 'GET #messages_by_platform' do
    let(:path) { api_v1_messages_by_platform_path }

    context 'when there are messages' do
      before do
        create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-01', total_ws_messages: 10,
                                         ws_inbound: 5, ws_outbound: 5, total_msn_messages: 0, msn_inbound: 0,
                                         msn_outbound: 0)
        create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-02', total_msn_messages: 8,
                                         msn_inbound: 3, msn_outbound: 5, total_ws_messages: 0, ws_inbound: 0,
                                         ws_outbound: 0)
      end

      context 'with valid date range params' do
        let(:params) { { start_date: start_date, end_date: end_date } }

        include_examples 'a successful request'

        it 'returns the correct message data' do
          get path, params: { start_date: start_date, end_date: end_date }
          json_response = response.parsed_body

          expect(json_response['messages_by_platform']['ws']['total_inbound']).to eq(5)
          expect(json_response['messages_by_platform']['ws']['total_outbound']).to eq(5)
          expect(json_response['messages_by_platform']['ws']['total_messages']).to eq(10)
          expect(json_response['messages_by_platform']['msn']['total_inbound']).to eq(3)
          expect(json_response['messages_by_platform']['msn']['total_outbound']).to eq(5)
          expect(json_response['messages_by_platform']['msn']['total_messages']).to eq(8)
        end
      end

      context 'with invalid date range params' do
        include_examples 'API response with expect', 'start_date is missing', { end_date: '2023-01-31' },
                         :not_found, 'error', 'not records'

        include_examples 'API response with expect', 'end_date is missing', { start_date: '2023-01-01' },
                         :not_found, 'error', 'not records'

        include_examples 'API response with expect', 'both dates are missing', {},
                         :not_found, 'error', 'not records'

        include_examples 'API response with expect', 'dates are invalid',
                         { start_date: 'invalid-date', end_date: 'invalid-date' },
                         :internal_server_error, 'message', 'invalid date'
      end
    end

    context 'when there are multiple messages on the same date' do
      before do
        create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-01',
                                         total_ws_messages: 10, ws_inbound: 5, ws_outbound: 5)
        create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-01',
                                         total_ws_messages: 20, ws_inbound: 10, ws_outbound: 10)

        create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-02',
                                         total_msn_messages: 8, msn_inbound: 3, msn_outbound: 5)
        create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-02',
                                         total_msn_messages: 12, msn_inbound: 6, msn_outbound: 6)

        create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-03',
                                         total_ig_messages: 15, ig_inbound: 7, ig_outbound: 8)
        create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-03',
                                         total_ig_messages: 25, ig_inbound: 12, ig_outbound: 13)
      end

      it 'correctly sums WS messages for the same date' do
        get path, params: { start_date: start_date, end_date: end_date }
        json_response = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(json_response['messages_by_platform']['ws']['total_messages']).to eq(30)
        expect(json_response['messages_by_platform']['ws']['data'].find do |d|
                 d['date'] == '2023-01-01'
               end['amount']).to eq(30)
      end

      it 'correctly sums MSN messages for the same date' do
        get path, params: { start_date: start_date, end_date: end_date }
        json_response = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(json_response['messages_by_platform']['msn']['total_messages']).to eq(20)
        expect(json_response['messages_by_platform']['msn']['data'].find do |d|
                 d['date'] == '2023-01-02'
               end['amount']).to eq(20)
      end

      it 'correctly sums IG messages for the same date' do
        get path, params: { start_date: start_date, end_date: end_date }
        json_response = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(json_response['messages_by_platform']['ig']['total_messages']).to eq(40)
        expect(json_response['messages_by_platform']['ig']['data'].find do |d|
                 d['date'] == '2023-01-03'
               end['amount']).to eq(40)
      end
    end

    context 'when there are no messages' do
      it 'returns not found' do
        get path, params: { start_date: start_date, end_date: end_date }
        json_response = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(json_response['messages_by_platform']['ws']['total_inbound']).to eq(0)
        expect(json_response['messages_by_platform']['ws']['total_outbound']).to eq(0)
        expect(json_response['messages_by_platform']['ws']['total_messages']).to eq(0)
        expect(json_response['messages_by_platform']['msn']['total_inbound']).to eq(0)
        expect(json_response['messages_by_platform']['msn']['total_outbound']).to eq(0)
        expect(json_response['messages_by_platform']['msn']['total_messages']).to eq(0)
      end
    end
  end

  describe 'GET #usage_by_platform' do
    let(:path) { api_v1_usage_by_platform_path }

    context 'with valid date range params' do
      context 'when there are messages in the date range' do
        let(:params) { { start_date: start_date, end_date: end_date } }
        let(:path) { api_v1_usage_by_platform_path }

        before do
          create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-01', total_ws_messages: 10,
                                           ws_inbound: 5, ws_outbound: 5, total_msn_messages: 0, msn_inbound: 0,
                                           msn_outbound: 0, total_ig_messages: 0, total_ml_messages: 0)
          create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-02', total_msn_messages: 8,
                                           msn_inbound: 3, msn_outbound: 5, total_ws_messages: 0, ws_inbound: 0,
                                           ws_outbound: 0, total_ig_messages: 0, total_ml_messages: 0)
        end

        include_examples 'a successful request'

        it 'returns the correct usage data' do
          get path, params: { start_date: start_date, end_date: end_date }

          expect(response.parsed_body['usage_by_platform']['total_ws_messages']).to eq(10)
          expect(response.parsed_body['usage_by_platform']['total_msn_messages']).to eq(8)
          expect(response.parsed_body['usage_by_platform']['total_ig_messages']).to eq(0)
          expect(response.parsed_body['usage_by_platform']['total_ml_messages']).to eq(0)

          total_messages = 10 + 8
          expect(response.parsed_body['usage_by_platform']['percentage_total_ws_messages'])
            .to eq((10.0 / total_messages * 100).round(2))
          expect(response.parsed_body['usage_by_platform']['percentage_total_msn_messages'])
            .to eq((8.0 / total_messages * 100).round(2))
        end
      end

      context 'when there are messages but total count is zero' do
        let(:params) { { start_date: start_date, end_date: end_date } }

        before do
          create(:retailer_amount_message, retailer: retailer, calculation_date: '2023-01-01',
                                           total_ws_messages: 0, ws_inbound: 0, ws_outbound: 0,
                                           total_msn_messages: 0, msn_inbound: 0, msn_outbound: 0,
                                           total_ig_messages: 0, total_ml_messages: 0)
        end

        include_examples 'a successful request'

        it 'returns zero for all message counts and percentages' do
          get path, params: params
          json_response = response.parsed_body

          expect(json_response['usage_by_platform']['total_ws_messages']).to eq(0)
          expect(json_response['usage_by_platform']['total_msn_messages']).to eq(0)
          expect(json_response['usage_by_platform']['total_ig_messages']).to eq(0)
          expect(json_response['usage_by_platform']['total_ml_messages']).to eq(0)

          expect(json_response['usage_by_platform']['percentage_total_ws_messages']).to eq(0)
          expect(json_response['usage_by_platform']['percentage_total_msn_messages']).to eq(0)
          expect(json_response['usage_by_platform']['percentage_total_ig_messages']).to eq(0)
          expect(json_response['usage_by_platform']['percentage_total_ml_messages']).to eq(0)
        end
      end

      context 'when there are no messages in the date range' do
        include_examples 'API response with expect', 'an empty usage data response',
                         { start_date: '2023-01-01', end_date: '2023-01-31' },
                         :ok, 'usage_by_platform', {}
      end
    end

    context 'with invalid date range params' do
      include_examples 'API response with expect', 'start_date is missing', { end_date: '2023-01-31' },
                       :not_found, 'error', 'not records'

      include_examples 'API response with expect', 'end_date is missing', { start_date: '2023-01-01' },
                       :not_found, 'error', 'not records'

      include_examples 'API response with expect', 'both dates are missing', {},
                       :not_found, 'error', 'not records'

      include_examples 'API response with expect', 'dates are invalid',
                       { start_date: 'invalid-date', end_date: 'invalid-date' },
                       :internal_server_error, 'message', 'invalid date'
    end
  end

  describe 'GET #agent_chats' do
    let(:team_assignment) { create(:team_assignment, retailer: retailer) }
    let(:user) { retailer_user.user }
    let(:customer) { create(:customer, retailer: retailer, last_chat_interaction: '2023-01-02', ws_active: true) }
    let(:path) { api_v1_agent_chats_path }

    before do
      create(:agent_customer, retailer_user: retailer_user, customer: customer, team_assignment: team_assignment)
    end

    context 'with valid date range and no platform filter' do
      it 'returns the correct agent chats data' do
        get path, params: { start_date: start_date, end_date: end_date }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['agent_chats'].size).to eq(1)
        expect(json_response['agent_chats'].first['first_name']).to eq(user.first_name)
        expect(json_response['agent_chats'].first['amount_chat_in_process']).to eq(1)
        expect(json_response['agent_chats'].first['amount_chat_resolved']).to eq(0)
      end
    end

    context 'with platform filter ws (platform = 0)' do
      before do
        customer.update(ws_active: true)
      end

      it 'returns the correct agent chats data' do
        get path, params: { start_date: start_date, end_date: end_date, platform: '0' }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['agent_chats'].size).to eq(1)
        expect(json_response['agent_chats'].first['first_name']).to eq(user.first_name)
        expect(json_response['agent_chats'].first['amount_chat_in_process']).to eq(1)
        expect(json_response['agent_chats'].first['amount_chat_resolved']).to eq(0)
      end
    end

    context 'with platform filter msn (platform = 1)' do
      before do
        customer.update(pstype: 0)
      end

      it 'returns the correct agent chats data' do
        get path, params: { start_date: start_date, end_date: end_date, platform: '1' }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['agent_chats'].size).to eq(1)
        expect(json_response['agent_chats'].first['first_name']).to eq(user.first_name)
        expect(json_response['agent_chats'].first['amount_chat_in_process']).to eq(1)
        expect(json_response['agent_chats'].first['amount_chat_resolved']).to eq(0)
      end
    end

    context 'with platform filter ig (platform = 2)' do
      before do
        customer.update(pstype: 1)
      end

      it 'returns the correct agent chats data' do
        get path, params: { start_date: start_date, end_date: end_date, platform: '2' }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['agent_chats'].size).to eq(1)
        expect(json_response['agent_chats'].first['first_name']).to eq(user.first_name)
        expect(json_response['agent_chats'].first['amount_chat_in_process']).to eq(1)
        expect(json_response['agent_chats'].first['amount_chat_resolved']).to eq(0)
      end
    end

    context 'with invalid date range' do
      let(:start_date) { 'invalid-date' }
      let(:end_date) { 'invalid-date' }

      it 'raises an error' do
        expect do
          get path, params: { start_date: start_date, end_date: end_date }
        end.to raise_error(ActiveRecord::StatementInvalid)
      end
    end

    context 'with missing date range' do
      include_examples 'API response with expect', 'start_date is missing', { end_date: '2023-01-31' },
                       :not_found, 'error', 'not records'

      include_examples 'API response with expect', 'end_date is missing', { start_date: '2023-01-01' },
                       :not_found, 'error', 'not records'
    end
  end

  describe 'GET #average_response_times' do
    let(:path) { api_v1_average_response_times_path }

    context 'with valid date range and no additional filters' do
      before do
        create(:retailer_average_response_time, retailer: retailer, calculation_date: '2023-01-15',
                                                calculation_hour: nil, retailer_user: retailer_user)
      end

      include_examples 'API response with expect', 'the correct average response times data',
                       { start_date: '2023-01-01', end_date: '2023-01-31' },
                       :ok, 'average_response_times', 1, true
    end

    context 'with agent and platform filters' do
      let(:platform) { 1 }
      let!(:retailer_average_response_time) do
        create(:retailer_average_response_time, retailer: retailer, calculation_date: '2023-01-15',
                                                calculation_hour: nil, retailer_user: retailer_user, platform: platform)
      end

      it 'returns the correct filtered average response times data' do
        get path,
            params: { start_date: start_date, end_date: end_date, agent: retailer_user.id, platform: platform }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['average_response_times'].size).to eq(1)
        expect(json_response['average_response_times'].first['id']).to eq(retailer_average_response_time.id)
        expect(json_response['average_response_times'].first['calculation_date']).to eq('2023-01-15')
      end
    end

    context 'with only agent filter' do
      before do
        create(:retailer_average_response_time, retailer: retailer, calculation_date: '2023-01-15',
                                                calculation_hour: nil, retailer_user: retailer_user)
      end

      it 'returns the correct filtered average response times data' do
        get path,
            params: { start_date: start_date, end_date: end_date, agent: retailer_user.id }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['average_response_times'].size).to eq(1)
      end
    end

    context 'with only platform filter' do
      let(:platform) { 1 }

      before do
        create(:retailer_average_response_time, retailer: retailer, calculation_date: '2023-01-15', platform: platform,
                                                calculation_hour: nil)
      end

      it 'returns the correct filtered average response times data' do
        get path,
            params: { start_date: start_date, end_date: end_date, platform: platform }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['average_response_times'].size).to eq(1)
      end
    end

    context 'with invalid date range' do
      include_examples 'API response with expect', 'invalid date error',
                       { start_date: 'invalid-date', end_date: 'invalid-date' },
                       :internal_server_error, 'message', 'invalid date'
    end

    context 'with missing date range' do
      include_examples 'API response with expect', 'start_date is missing',
                       { end_date: '2023-01-31' }, :not_found, 'error', 'not records'

      include_examples 'API response with expect', 'end_date is missing',
                       { start_date: '2023-01-01' }, :not_found, 'error', 'not records'
    end
  end

  describe 'GET #response_times_by_hour' do
    let(:business_rule) { create(:business_rule, identifier: 'average_response_time_by_hour') }
    let!(:retailer_business_rule) { create(:retailer_business_rule, retailer: retailer, business_rule: business_rule) }
    let(:rule_data) do
      { 'kind' => 'schedule', 'open' => '8', 'close' => '17', 'value' => %w[true true true true true true true] }
    end
    let!(:business_rule_data) do
      create(:retailer_business_rule_datum, retailer: retailer, business_rule: business_rule, data: rule_data)
    end
    let(:path) { api_v1_response_times_by_hour_path }

    context 'with valid date range and business rule' do
      let!(:inside_schedule) do
        create(:retailer_average_response_time, retailer: retailer, calculation_date: '2023-01-15', platform: 0,
                                                calculation_hour: 10, week_day: 1, retailer_user: retailer_user)
      end
      let!(:outside_schedule) do
        create(:retailer_average_response_time, retailer: retailer, calculation_hour: 16, week_day: 4)
      end

      include_examples 'API response with expect', 'the average response times data filtered by schedule',
                       { start_date: '2023-01-01', end_date: '2023-01-31' }, :ok, 'data', 1, true

      context 'with agent filter' do
        it 'returns the average response times data filtered by agent' do
          get path,
              params: { start_date: start_date, end_date: end_date, agent: retailer_user.id }
          expect(response).to have_http_status(:ok)
          expect(response.parsed_body['data'].size).to eq(1)
          expect(response.parsed_body['data'].first['id']).to eq(inside_schedule.id)
          expect(response.parsed_body['data'].first['calculation_date']).to eq('2023-01-15')
        end
      end

      context 'with platform filter' do
        it 'returns the average response times data filtered by platform' do
          get path, params: { start_date: start_date, end_date: end_date, platform: 0 }
          expect(response).to have_http_status(:ok)
          expect(response.parsed_body['data'].size).to eq(1)
        end
      end

      context 'with both agent and platform filters' do
        it 'returns the average response times data filtered by both agent and platform' do
          get path,
              params: { start_date: start_date, end_date: end_date, agent: retailer_user.id, platform: 0 }
          expect(response).to have_http_status(:ok)
          expect(response.parsed_body['data'].size).to eq(1)
        end
      end

      context 'when no business rule data is found' do
        let!(:business_rule_data) { nil }

        include_examples 'API response with expect', 'an empty array',
                         { start_date: '2023-01-01', end_date: '2023-01-31' }, :ok, 'data', []
      end
    end

    context 'with valid date range but no data available' do
      include_examples 'API response with expect', 'an empty array',
                       { start_date: '2023-01-01', end_date: '2023-01-31' }, :ok, 'data', []
    end

    context 'without valid date range' do
      include_examples 'API response with expect', 'start_date is missing', { end_date: '2023-01-31' },
                       :not_found, 'error', 'not records'

      include_examples 'API response with expect', 'end_date is missing', { start_date: '2023-01-01' },
                       :not_found, 'error', 'not records'
    end

    context 'without active business rule' do
      let(:inactive_business_rule) { create(:business_rule, identifier: 'inactive_rule') }

      before do
        retailer.business_rules.destroy_all
        create(:retailer_business_rule, retailer: retailer, business_rule: inactive_business_rule)
      end

      include_examples 'API response with expect', 'rule is inactive',
                       { start_date: '2023-01-01', end_date: '2023-01-31' },
                       :unprocessable_entity, 'error', 'inactive rule'
    end
  end

  describe 'GET #most_used_tags' do
    let(:path) { api_v1_most_used_tags_path }

    context 'with valid date range' do
      let(:tagx) { create(:tag, retailer: retailer, tag: 'tagx') }
      let(:tagy) { create(:tag, retailer: retailer, tag: 'tagy') }

      before do
        create(:customer_tag, tag: tagx, created_at: '2023-01-10')
        create(:customer_tag, tag: tagx, created_at: '2023-01-15')
        create(:customer_tag, tag: tagy, created_at: '2023-01-20')
      end

      it 'returns the correct most used tags data' do
        get path, params: { start_date: start_date, end_date: end_date }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['most_used_tags'].size).to eq(2)
        expect(json_response['most_used_tags'].first['tag_name']).to eq('tagx')
        expect(json_response['most_used_tags'].first['amount_used']).to eq(2)
      end
    end

    context 'with valid date range but no tags used' do
      include_examples 'API response with expect', 'an empty array',
                       { start_date: '2023-01-01', end_date: '2023-01-31' }, :ok, 'most_used_tags', []
    end

    context 'without valid date range' do
      include_examples 'API response with expect', 'start_date is missing', { end_date: '2023-01-31' },
                       :not_found, 'error', 'not records'

      include_examples 'API response with expect', 'end_date is missing', { start_date: '2023-01-01' },
                       :not_found, 'error', 'not records'
    end
  end

  describe 'GET #new_and_recurring_customers' do
    let(:path) { api_v1_new_and_recurring_customers_path }

    context 'when date_range_params_present' do
      before do
        get path
      end

      it 'return status code 404' do
        expect(response).to have_http_status(:not_found)
      end

      it 'return error not records' do
        expect(response.parsed_body['error']).to eq('not records')
      end
    end

    context 'when the parameters are present' do
      context 'when agent parameter does not exist' do
        let!(:retailer_customer) do
          create(:retailer_customer, retailer: retailer, new_customers: [1, 2], recurring_customers: [3, 4],
                                     calculation_date: Time.zone.now)
        end

        let!(:john) do
          create(:customer, retailer: retailer, last_chat_interaction: 1.day.ago, created_at: 1.day.ago)
        end

        let!(:jane) do
          create(:customer, retailer: retailer, last_chat_interaction: 1.day.ago, created_at: 2.days.ago)
        end

        let!(:james) do
          create(:customer, retailer: retailer, last_chat_interaction: 1.day.ago, created_at: 2.days.ago)
        end

        let!(:agent_customer) { create(:agent_customer, retailer_user: retailer_user, customer: john) }
        let!(:agent_customer1) { create(:agent_customer, retailer_user: retailer_user, customer: jane) }

        context 'when there is no data in retailer_customer table for start_date params' do
          before do
            get path, params: { start_date: 1.day.ago, end_date: Time.zone.now }
          end

          it 'return status code ok' do
            expect(response).to have_http_status(:ok)
          end

          it 'return real time statistics' do
            body = response.parsed_body

            expect(body['customers_data']['new_customers']).to eq(1)
            expect(body['customers_data']['new_customers_percentage']).to eq(33.33)
            expect(body['customers_data']['recurring_customers']).to eq(2)
            expect(body['customers_data']['recurring_customers_percentage']).to eq(66.67)
            expect(body['customers_data']['total_customers']).to eq(3)
          end
        end

        context 'when data exists in retailer_customer table for start_date params' do
          let!(:retailer_without_new_customers) do
            create(:retailer_customer, retailer: retailer, new_customers: [], recurring_customers: [3, 4],
                                       calculation_date: '2024-04-05')
          end
          let!(:retailer_with_new_customers) do
            create(:retailer_customer, retailer: retailer, new_customers: [1, 2], recurring_customers: [4, 5],
                                       calculation_date: '2024-04-06')
          end
          let!(:customer) do
            create(:customer, retailer: retailer, last_chat_interaction: '2024-04-05', created_at: '2024-04-05')
          end

          it 'return data from retailer_customer' do
            get path, params: { start_date: '2024-04-05', end_date: '2024-04-06' }

            body = response.parsed_body

            expect(body['customers_data']['new_customers']).to eq(2)
            expect(body['customers_data']['new_customers_percentage']).to eq(40)
            expect(body['customers_data']['recurring_customers']).to eq(3)
            expect(body['customers_data']['recurring_customers_percentage']).to eq(60)
            expect(body['customers_data']['total_customers']).to eq(5)
          end
        end
      end
    end

    context 'when agent parameter is present and not null' do
      let(:agent) { create(:retailer_user, retailer: retailer) }
      let!(:customer_yesterday) do
        create(:customer, retailer: retailer, last_chat_interaction: 1.day.ago, created_at: 1.day.ago)
      end
      let!(:customer_two_days) do
        create(:customer, retailer: retailer, last_chat_interaction: 1.day.ago, created_at: 2.days.ago)
      end
      let!(:agent_customer) { create(:agent_customer, retailer_user: agent, customer: customer_yesterday) }
      let!(:agent_customer2) { create(:agent_customer, retailer_user: agent, customer: customer_two_days) }

      it 'returns data for the specific agent' do
        get path, params: { start_date: 2.days.ago, end_date: Time.zone.now, agent: agent.id }

        body = response.parsed_body
        expect(body['customers_data']['new_customers']).to eq(1)
        expect(body['customers_data']['recurring_customers']).to eq(1)
        expect(body['customers_data']['total_customers']).to eq(2)
      end
    end

    context 'when there are no customers' do
      it 'returns zero for all values' do
        get path, params: { start_date: 1.day.ago, end_date: Time.zone.now }

        body = response.parsed_body
        expect(body['customers_data']['new_customers']).to eq(0)
        expect(body['customers_data']['new_customers_percentage']).to eq(0)
        expect(body['customers_data']['recurring_customers']).to eq(0)
        expect(body['customers_data']['recurring_customers_percentage']).to eq(0)
        expect(body['customers_data']['total_customers']).to eq(0)
      end
    end

    context 'when fetching data from master table' do
      before do
        allow_any_instance_of(described_class).to receive(:data_for_start_date_on_retailer_customers?).and_return(true)
      end

      context 'when there are customers' do
        let!(:retailer_yesterday) do
          create(:retailer_customer, retailer: retailer, new_customers: [1, 2], recurring_customers: [3, 4],
                                     calculation_date: 1.day.ago)
        end
        let!(:retailer_today) do
          create(:retailer_customer, retailer: retailer, new_customers: [2, 5], recurring_customers: [3, 6],
                                     calculation_date: Time.zone.now)
        end

        it 'returns correct data from retailer_customer table' do
          get path, params: { start_date: 2.days.ago, end_date: Time.zone.now }

          body = response.parsed_body
          expect(body['customers_data']['new_customers']).to eq(3)
          expect(body['customers_data']['recurring_customers']).to eq(3)
          expect(body['customers_data']['total_customers']).to eq(6)
          expect(body['customers_data']['new_customers_percentage']).to eq(50.00)
          expect(body['customers_data']['recurring_customers_percentage']).to eq(50.00)
        end

        context 'when agent parameter is present' do
          let(:agent) { create(:retailer_user, retailer: retailer) }
          let!(:rc3) do
            create(:retailer_customer, retailer: retailer, new_customers: [7], recurring_customers: [8],
                                       calculation_date: Time.zone.now, retailer_user_id: agent.id)
          end

          it 'returns data for the specific agent' do
            get path, params: { start_date: 2.days.ago, end_date: Time.zone.now, agent: agent.id }

            body = response.parsed_body
            expect(body['customers_data']['new_customers']).to eq(1)
            expect(body['customers_data']['recurring_customers']).to eq(1)
            expect(body['customers_data']['total_customers']).to eq(2)
            expect(body['customers_data']['new_customers_percentage']).to eq(50.00)
            expect(body['customers_data']['recurring_customers_percentage']).to eq(50.00)
          end
        end
      end

      context 'when there are no customers' do
        let!(:rc1) do
          create(:retailer_customer, retailer: retailer, new_customers: [], recurring_customers: [],
                                     calculation_date: 1.day.ago)
        end

        it 'returns zero for all values and percentages' do
          get path, params: { start_date: 2.days.ago, end_date: Time.zone.now }

          body = response.parsed_body
          expect(body['customers_data']['new_customers']).to eq(0)
          expect(body['customers_data']['recurring_customers']).to eq(0)
          expect(body['customers_data']['total_customers']).to eq(0)
          expect(body['customers_data']['new_customers_percentage']).to eq(0)
          expect(body['customers_data']['recurring_customers_percentage']).to eq(0)
        end
      end
    end
  end

  describe 'GET #sent_messages_by' do
    let(:platform) { 0 }
    let(:path) { api_v1_sent_messages_by_path }

    context 'with valid date range' do
      before do
        create(:retailer_amount_message, retailer: retailer, retailer_user: retailer_user, ws_outbound: 10,
                                         msn_outbound: 5, ig_outbound: 2, ml_outbound: 1,
                                         calculation_date: '2023-01-10')
        create(:retailer_amount_message, retailer: retailer, retailer_user: retailer_user, ws_outbound: 8,
                                         msn_outbound: 3, ig_outbound: 1, ml_outbound: 2,
                                         calculation_date: '2023-01-15')
        allow_any_instance_of(ActiveRecord::AssociationRelation).to receive(:as_json) do |sent_messages_data, _arg2|
          sent_messages_data.to_a.map do |row|
            {
              retailer_user_id: row.retailer_user_id, first_name: row.first_name, last_name: row.last_name,
              ws_outbound: row.ws_outbound, msn_outbound: row.msn_outbound, ig_outbound: row.ig_outbound,
              ml_outbound: row.ml_outbound, total_messages: row[:total_messages]
            }
          end
        end
      end

      shared_examples 'returns the correct data for platform' do |platform_number, expected_total_messages|
        it "returns the correct sent messages data for platform #{platform_number}" do
          get path, params: { start_date: start_date, end_date: end_date, platform: platform_number.to_s,
                              format: :json }, headers: { 'ACCEPT' => 'application/json' }
          expect(response).to have_http_status(:ok)
          json_response = response.parsed_body
          expect(json_response['sent_messages'].size).to eq(1)
          expect(json_response['sent_messages'].first['total_messages'].to_s).to eq(expected_total_messages)
        end
      end

      include_examples 'returns the correct data for platform', 0, '18'
      include_examples 'returns the correct data for platform', 1, '8'
      include_examples 'returns the correct data for platform', 2, '3'
      include_examples 'returns the correct data for platform', 3, '3'
      include_examples 'returns the correct data for platform', 'unknown', '0'
      include_examples 'returns the correct data for platform', '', (10 + 5 + 2 + 1 + 8 + 3 + 1 + 2).to_s
    end

    context 'with valid date range but no messages' do
      include_examples 'API response with expect', 'an empty array',
                       { start_date: '2023-01-01', end_date: '2023-01-31' }, :ok, 'sent_messages', []
    end

    context 'without valid date range' do
      include_examples 'API response with expect', 'start_date is missing', { end_date: '2023-01-31' },
                       :not_found, 'error', 'not records'

      include_examples 'API response with expect', 'end_date is missing', { start_date: '2023-01-01' },
                       :not_found, 'error', 'not records'
    end
  end

  describe 'GET #messages_by_hours' do
    let(:platform) { 1 }
    let(:path) { api_v1_messages_by_hours_path }

    context 'with valid date range' do
      let!(:other_platform_messages) do
        create_list(:retailer_amount_messages_by_hour, 2, retailer: retailer, platform: 0,
                                                          calculation_date: '2023-01-02')
      end
      let!(:messages) do
        create_list(:retailer_amount_messages_by_hour, 2, retailer: retailer, platform: platform,
                                                          calculation_date: '2023-01-02')
      end

      it 'returns the correct messages by hours data' do
        get path, params: { start_date: start_date, end_date: end_date, platform: platform }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        response_ids = json_response['messages'].pluck('id')
        expect(json_response['messages'].size).to eq(2)
        expect(other_platform_messages.pluck(:id).any? { |i| response_ids.include?(i) }).to be(false)
        expect(messages.pluck(:id).all? { |i| response_ids.include?(i) }).to be(true)
      end

      it 'returns the correct messages by hours data when platform is null' do
        get path, params: { start_date: start_date, end_date: end_date, platform: 'null' }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['messages'].size).to eq(4)
        response_ids = json_response['messages'].pluck('id')
        expect(other_platform_messages.pluck(:id).all? { |i| response_ids.include?(i) }).to be(true)
        expect(messages.pluck(:id).all? { |i| response_ids.include?(i) }).to be(true)
      end
    end

    context 'with valid date range but no messages' do
      it 'returns an empty array' do
        get path, params: { start_date: start_date, end_date: end_date, platform: platform }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response['messages']).to be_empty
      end
    end

    context 'without valid date range' do
      include_examples 'API response with expect', 'start_date is missing', { end_date: '2023-01-31' },
                       :not_found, 'error', 'not records'

      include_examples 'API response with expect', 'end_date is missing', { start_date: '2023-01-01' },
                       :not_found, 'error', 'not records'
    end
  end
end
