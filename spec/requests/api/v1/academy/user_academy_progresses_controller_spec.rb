require 'rails_helper'

RSpec.describe 'Api::V1::Academy::UserAcademyProgresses' do
  let!(:retailer) { create(:retailer) }
  let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
  let!(:academy_category) { create(:academy_category) }
  let!(:academy_video) { create(:academy_video, academy_category: academy_category, external_url: 'https://www.youtube.com/watch?v=video1') }
  let!(:user_academy_progress) do
    create(:user_academy_progress, retailer_user: retailer_user, academy_video: academy_video)
  end

  before { sign_in retailer_user.user }

  describe 'GET /user_academy_progresses' do
    context 'when academy_category_id is provided' do
      it 'returns user academy progresses for the given academy category' do
        get api_v1_academy_user_academy_progresses_path, params: { academy_category_id: academy_category.id }

        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response.length).to eq(1)
        expect(json_response.first['academy_video_id']).to eq(academy_video.id)
      end
    end

    context 'when academy_category_id is not provided' do
      it 'returns an empty array' do
        get api_v1_academy_user_academy_progresses_path

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body).to eq([])
      end
    end

    context 'when an unexpected error occurs' do
      before do
        allow_any_instance_of(ActiveRecord::Relation).to receive(:joins).and_raise(StandardError,
                                                                                   'Something went wrong')
      end

      it 'returns an internal server error' do
        get api_v1_academy_user_academy_progresses_path, params: { academy_category_id: academy_category.id }

        expect(response).to have_http_status(:internal_server_error)
        expect(response.parsed_body['error']).to eq('An internal error occurred.')
      end
    end
  end

  describe 'POST /user_academy_progresses' do
    let(:valid_params) do
      { user_academy_progress: { academy_video_id: academy_video.id, watched: true, completed_at: Time.current } }
    end

    context 'when valid data is provided' do
      it 'creates a new user academy progress' do
        expect do
          post api_v1_academy_user_academy_progresses_path, params: valid_params
        end.to change(UserAcademyProgress, :count).by(1)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body['academy_video_id']).to eq(academy_video.id)
        expect(response.parsed_body['watched']).to be true
      end
    end

    context 'when invalid data is provided' do
      it 'returns an error message' do
        post api_v1_academy_user_academy_progresses_path, params: { user_academy_progress: { watched: true } }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body['error']).to include('Academy video Academy video debe existir')
      end
    end
  end

  describe 'PATCH/PUT /user_academy_progresses/:id' do
    context 'when valid data is provided' do
      it 'updates the user academy progress' do
        put api_v1_academy_user_academy_progress_path(user_academy_progress),
            params: { user_academy_progress: { watched: true, completed_at: nil } }

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body['watched']).to be true
        expect(response.parsed_body['completed_at']).to be_nil
      end
    end

    context 'when invalid data is provided' do
      it 'returns an error message' do
        put api_v1_academy_user_academy_progress_path(user_academy_progress),
            params: { user_academy_progress: { academy_video_id: nil } }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body['error']).to include('Academy video Academy video debe existir')
      end
    end

    context 'when user academy progress is not found' do
      it 'returns a not found error' do
        put api_v1_academy_user_academy_progress_path(id: -1), params: { user_academy_progress: { watched: false } }

        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body['error']).to eq('User academy progress not found.')
      end
    end
  end
end
