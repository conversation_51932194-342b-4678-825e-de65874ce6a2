require 'rails_helper'

RSpec.describe 'Api::V1::Academy::AcademyVideos' do
  let!(:retailer) { create(:retailer) }
  let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
  let!(:academy_category) { create(:academy_category) }
  let!(:video1) { create(:academy_video, academy_category: academy_category, position: 1, external_url: 'https://www.youtube.com/watch?v=video1') }
  let!(:video2) { create(:academy_video, academy_category: academy_category, position: 2, external_url: 'https://www.youtube.com/watch?v=video2') }

  before do
    sign_in retailer_user.user
  end

  describe 'GET /academy_videos' do
    context 'when academy_category_id is provided' do
      it 'returns the videos of the specified academy category' do
        get api_v1_academy_academy_videos_path, params: { academy_category_id: academy_category.id }

        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body

        expect(json_response.length).to eq(2)
        expect(json_response[0]['id']).to eq(video1.id)
        expect(json_response[1]['id']).to eq(video2.id)
        expect(json_response[0]['title']).to eq(video1.title)
        expect(json_response[1]['title']).to eq(video2.title)
        expect(json_response[0]['description']).to eq(video1.description)
        expect(json_response[1]['description']).to eq(video2.description)
      end
    end

    context 'when academy_category_id is not provided' do
      it 'returns an empty array' do
        get api_v1_academy_academy_videos_path

        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response).to eq([])
      end
    end

    context 'when an unexpected error occurs' do
      before do
        allow(AcademyVideo).to receive(:where).and_raise(StandardError.new('Something went wrong'))
      end

      it 'returns an internal server error' do
        get api_v1_academy_academy_videos_path, params: { academy_category_id: academy_category.id }

        expect(response).to have_http_status(:internal_server_error)
        json_response = response.parsed_body
        expect(json_response['error']).to eq('An internal error occurred.')
      end
    end
  end
end
