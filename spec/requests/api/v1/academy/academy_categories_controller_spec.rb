require 'rails_helper'

RSpec.describe 'Api::V1::Academy::AcademyCategories' do
  let!(:retailer) { create(:retailer) }
  let!(:retailer_user) { create(:retailer_user, retailer: retailer) }

  before do
    sign_in retailer_user.user
  end

  describe 'GET /academy_categories' do
    context 'when module_name parameter is provided' do
      it 'returns the category when module_name is exactly the value' do
        category = create(:academy_category, module_name: 'customers')
        get api_v1_academy_academy_categories_path, params: { module_name: 'customers' }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response.first['id']).to eq(category.id)
        expect(json_response.first['module_name']).to eq(category.module_name)
      end

      it 'returns the category when module_name is at the start' do
        category = create(:academy_category, module_name: 'customers;tag;list')
        get api_v1_academy_academy_categories_path, params: { module_name: 'customers' }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response.first['id']).to eq(category.id)
      end

      it 'returns the category when module_name is in the middle' do
        category = create(:academy_category, module_name: 'tag;customers;list')
        get api_v1_academy_academy_categories_path, params: { module_name: 'customers' }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response.first['id']).to eq(category.id)
      end

      it 'returns the category when module_name is at the end' do
        category = create(:academy_category, module_name: 'tag;list;customers')
        get api_v1_academy_academy_categories_path, params: { module_name: 'customers' }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response.first['id']).to eq(category.id)
      end

      it 'does not return category if module_name does not match' do
        create(:academy_category, module_name: 'tag;list')
        get api_v1_academy_academy_categories_path, params: { module_name: 'customers' }
        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response).to eq([])
      end
    end

    context 'when module_name parameter is not provided' do
      it 'returns an empty array' do
        get api_v1_academy_academy_categories_path

        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        expect(json_response).to eq([])
      end
    end

    context 'when an unexpected error occurs' do
      before do
        allow(AcademyCategory).to receive(:where).and_raise(StandardError.new('Something went wrong'))
      end

      it 'returns an internal server error' do
        get api_v1_academy_academy_categories_path, params: { module_name: 'TestModule' }

        expect(response).to have_http_status(:internal_server_error)
        json_response = response.parsed_body
        expect(json_response['error']).to eq('An internal error occurred.')
      end
    end
  end
end
