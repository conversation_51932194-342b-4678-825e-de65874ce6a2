# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::Mobile::WorkspacesController do
  let(:user) { create(:user) }
  let(:retailer1) { create(:retailer, name: 'Retailer 1', unique_key: 'ret1') }
  let(:retailer2) { create(:retailer, name: 'Retailer 2', unique_key: 'ret2') }
  let(:retailer3) { create(:retailer, name: 'Retailer 3', unique_key: 'ret3') }

  let!(:retailer_user) do
    create(:retailer_user, :admin, user: user, retailer: retailer1, current: true, current_mobile: true,
                                   email: user.email,
                                   api_session_token: '6326e634a4ff9dc4c38cae2aec46c444',
                                   api_session_device: 'afb646',
                                   api_session_expiration: 1.day.from_now)
  end

  let!(:retailer_user2) do
    create(:retailer_user, :admin, user: user, retailer: retailer2, current: false, current_mobile: false,
                                   email: user.email,
                                   api_session_token: '6326e634a4ff9dc4c38cae2aec46c444',
                                   api_session_device: 'afb646',
                                   api_session_expiration: 1.day.from_now)
  end

  let!(:retailer_user3) do
    create(:retailer_user, :agent, user: user, retailer: retailer3, current: false, current_mobile: false,
                                   email: user.email,
                                   api_session_token: '6326e634a4ff9dc4c38cae2aec46c444',
                                   api_session_device: 'afb646',
                                   api_session_expiration: 1.day.from_now)
  end

  let :headers do
    {
      email: retailer_user.email,
      device: retailer_user.api_session_device,
      token: retailer_user.api_session_token
    }
  end

  describe 'GET #index' do
    context 'when user has retailers' do
      it 'returns all user workspaces with correct structure' do
        get '/api/v1/mobile/workspaces', headers: headers, as: :json
        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response).to have_key('workspaces')
        expect(json_response['workspaces']).to be_an(Array)
        expect(json_response['workspaces'].length).to eq(3)

        expect(json_response['workspaces']).to all(include('id', 'name', 'unique_key'))

        workspace_names = json_response['workspaces'].pluck('name')
        expect(workspace_names).to include('Retailer 1', 'Retailer 2', 'Retailer 3')
      end

      it 'returns workspaces ordered by retailer association' do
        get '/api/v1/mobile/workspaces', headers: headers, as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        workspaces = json_response['workspaces']

        # Verify all retailers associated with the user are returned
        retailer_ids = workspaces.pluck('id')
        expect(retailer_ids).to contain_exactly(retailer1.id, retailer2.id, retailer3.id)
      end
    end

    context 'when user has minimal retailers' do
      let(:isolated_user) { create(:user) }
      let(:isolated_retailer) { create(:retailer, name: 'Isolated Retailer') }
      let!(:isolated_retailer_user) do
        create(:retailer_user, user: isolated_user, retailer: isolated_retailer, current: true,
                               email: isolated_user.email,
                               api_session_token: '6326e634a4ff9dc4c38cae2aec46c444',
                               api_session_device: 'afb646',
                               api_session_expiration: 1.day.from_now)
      end

      let :headers_without_retailers do
        {
          email: isolated_retailer_user.email,
          device: isolated_retailer_user.api_session_device,
          token: isolated_retailer_user.api_session_token
        }
      end

      it 'returns workspaces for the isolated user' do
        get '/api/v1/mobile/workspaces', headers: headers_without_retailers, as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response).to have_key('workspaces')
        expect(json_response['workspaces']).to be_an(Array)
        expect(json_response['workspaces'].length).to eq(1)
        expect(json_response['workspaces'].first['name']).to eq('Isolated Retailer')
      end
    end

    context 'when user is not authenticated' do
      it 'returns forbidden without proper headers' do
        get '/api/v1/mobile/workspaces', as: :json

        expect(response).to have_http_status(:forbidden)
      end

      it 'returns unauthorized with invalid token' do
        invalid_headers = headers.merge(token: 'invalid_token')

        get '/api/v1/mobile/workspaces', headers: invalid_headers, as: :json

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST #change' do
    context 'with valid retailer_id' do
      it 'changes current workspace successfully' do
        post '/api/v1/mobile/workspaces/change',
             headers: headers,
             params: { retailer_id: retailer2.id },
             as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response).to have_key('message')
        expect(json_response['message']).to eq("Has cambiado a #{retailer2.name}")

        expect(retailer_user2.reload.current_mobile).to be_truthy
      end

      it 'does not set JWT cookie when changing workspace' do
        post '/api/v1/mobile/workspaces/change',
             headers: headers,
             params: { retailer_id: retailer2.id },
             as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response['message']).to eq("Has cambiado a #{retailer2.name}")
      end

      it 'changes from one workspace to another' do
        expect(retailer_user.current_mobile).to be_truthy

        post '/api/v1/mobile/workspaces/change',
             headers: headers,
             params: { retailer_id: retailer3.id },
             as: :json

        expect(response).to have_http_status(:ok)

        expect(retailer_user.reload.current_mobile).to be_falsy
        expect(retailer_user3.reload.current_mobile).to be_truthy
      end
    end

    context 'with invalid retailer_id' do
      let(:other_user) { create(:user) }
      let(:other_retailer) { create(:retailer, name: 'Other Retailer') }
      let!(:other_retailer_user) do
        create(:retailer_user, :admin, user: other_user, retailer: other_retailer)
      end

      it 'returns error when retailer_id does not belong to user' do
        post '/api/v1/mobile/workspaces/change',
             headers: headers,
             params: { retailer_id: other_retailer.id },
             as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response).to have_key('error')
        expect(json_response['error']).to eq('Workspace no encontrado')
      end

      it 'returns error when retailer_id does not exist' do
        post '/api/v1/mobile/workspaces/change',
             headers: headers,
             params: { retailer_id: 99_999 },
             as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response).to have_key('error')
        expect(json_response['error']).to eq('Workspace no encontrado')
      end

      it 'returns error when retailer_id is nil' do
        post '/api/v1/mobile/workspaces/change',
             headers: headers,
             params: { retailer_id: nil },
             as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response).to have_key('error')
        expect(json_response['error']).to eq('Workspace no encontrado')
      end
    end

    context 'without retailer_id parameter' do
      it 'returns error when retailer_id is missing' do
        post '/api/v1/mobile/workspaces/change',
             headers: headers,
             params: {},
             as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response).to have_key('error')
        expect(json_response['error']).to eq('Workspace no encontrado')
      end
    end

    context 'when user is not authenticated' do
      it 'returns forbidden without proper headers' do
        post '/api/v1/mobile/workspaces/change',
             params: { retailer_id: retailer2.id },
             as: :json

        expect(response).to have_http_status(:forbidden)
      end

      it 'returns unauthorized with invalid token' do
        invalid_headers = headers.merge(token: 'invalid_token')

        post '/api/v1/mobile/workspaces/change',
             headers: invalid_headers,
             params: { retailer_id: retailer2.id },
             as: :json

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'Strong parameters' do
    subject(:controller) { described_class.new }

    let(:params) do
      ActionController::Parameters.new(
        retailer_id: 123,
        unauthorized_param: 'should_be_filtered',
        another_param: 'also_filtered'
      )
    end

    it 'permits only retailer_id parameter' do
      controller.params = params
      change_params = controller.send(:change_params)

      expect(change_params.permitted?).to be_truthy
      expect(change_params.keys).to eq(['retailer_id'])
      expect(change_params[:retailer_id]).to eq(123)
    end
  end

  describe 'Serialization' do
    it 'uses the correct serializer format' do
      get '/api/v1/mobile/workspaces', headers: headers, as: :json

      expect(response).to have_http_status(:ok)

      json_response = response.parsed_body
      workspaces = json_response['workspaces']

      # Verify each workspace has exactly the attributes from the serializer
      workspaces.each do |workspace|
        expect(workspace.keys.sort).to eq(%w[id name unique_key])
        expect(workspace['id']).to be_an(Integer)
        expect(workspace['name']).to be_a(String)
        expect(workspace['unique_key']).to be_present
      end
    end

    it 'includes all required attributes from WorkspaceSerializer' do
      get '/api/v1/mobile/workspaces', headers: headers, as: :json

      json_response = response.parsed_body
      workspaces = json_response['workspaces']

      # Verify specific serializer attributes based on the WorkspaceSerializer
      retailer1_workspace = workspaces.find { |ws| ws['id'] == retailer1.id }
      expect(retailer1_workspace).to include(
        'id' => retailer1.id,
        'name' => retailer1.name,
        'unique_key' => retailer1.unique_key
      )
    end
  end

  describe 'Edge cases and error handling' do
    context 'when retailer_user relationship is complex' do
      let(:inactive_retailer) { create(:retailer, name: 'Inactive Retailer') }
      let!(:inactive_retailer_user) do
        create(:retailer_user, user: user, retailer: inactive_retailer, active: false)
      end

      it 'includes all retailers regardless of retailer_user status' do
        get '/api/v1/mobile/workspaces', headers: headers, as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        retailer_names = json_response['workspaces'].pluck('name')

        # Should include the inactive retailer as well since we're getting user.retailers
        expect(retailer_names).to include('Inactive Retailer')
      end
    end

    context 'when trying to change to same workspace' do
      it 'allows changing to the same workspace' do
        # retailer_user is already current for retailer1
        expect(retailer_user.current).to be_truthy

        post '/api/v1/mobile/workspaces/change',
             headers: headers,
             params: { retailer_id: retailer1.id },
             as: :json

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response['message']).to eq("Has cambiado a #{retailer1.name}")
        expect(retailer_user.reload.current).to be_truthy
      end
    end
  end
end
