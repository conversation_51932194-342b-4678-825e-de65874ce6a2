require 'rails_helper'

RSpec.describe Api::V1::Mobile::RetailerUsersController do
  let(:base_url) { ENV.fetch('DEAL_ENGINE_BASE_URL', 'http://localhost:3002/deals') }
  let(:retailer) { create(:retailer, :gupshup_integrated) }
  let!(:retailer_user) { create(:retailer_user, :admin, :logged_in_api, retailer: retailer, active: false) }
  let!(:customer) { create(:customer, retailer: retailer) }
  let!(:agent_team) { create(:agent_team, retailer_user: retailer_user, active: false) }

  let :headers do
    {
      email: retailer_user.email,
      device: retailer_user.api_session_device,
      token: retailer_user.api_session_token
    }
  end

  let(:missing_headers) do
    {
      email: '<EMAIL>',
      device: retailer_user.api_session_device,
      token: retailer_user.api_session_token
    }
  end

  before do
    stub_request(:put, "#{base_url}/deals/remove_agent_from_deals")
      .to_return(status: 200, body: { status: 'ok' }.to_json, headers: {})
  end

  describe 'POST #set_app_version' do
    context 'when app_version is provided' do
      it 'sets app version and mobile type' do
        post api_v1_mobile_set_app_version_path,
             headers: headers,
             params: {
               app_version: '3.1.1',
               mobile_type: 'android'
             },
             as: :json
        expect(response).to have_http_status(:ok)
        expect(retailer_user.reload.app_version).to eq('3.1.1')
        expect(retailer_user.reload.mobile_type).to eq('android')
      end

      it 'sets only app version when mobile_type is not provided' do
        post api_v1_mobile_set_app_version_path,
             headers: headers,
             params: { app_version: '3.1.1' },
             as: :json
        expect(response).to have_http_status(:ok)
        expect(retailer_user.reload.app_version).to eq('3.1.1')
        expect(retailer_user.reload.mobile_type).to be_nil
      end
    end

    context 'when app_version is not provided' do
      it 'returns forbidden status' do
        post api_v1_mobile_set_app_version_path,
             headers: headers,
             params: {},
             as: :json
        expect(response).to have_http_status(:forbidden)
        expect(response.parsed_body['error']).to eq('App version no enviada')
      end
    end

    context 'when user is not found' do
      before do
        allow(User).to receive(:find_by).and_return(nil)
      end

      it 'returns record not found' do
        post api_v1_mobile_set_app_version_path,
             headers: headers,
             as: :json

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'PUT #toggle_active' do
    context 'when toggle is successful' do
      it 'activates inactive retailer user and associated agent teams' do
        expect do
          put api_v1_mobile_toggle_retailer_user_status_path,
              headers: headers,
              as: :json
        end.to change { retailer_user.reload.active }.from(false).to(true)
          .and change { agent_team.reload.active }.from(false).to(true)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body['status']).to be(true)
      end

      it 'deactivates active retailer user and associated agent teams' do
        retailer_user.update(active: true)
        agent_team.update(active: true)

        expect do
          put api_v1_mobile_toggle_retailer_user_status_path,
              headers: headers,
              as: :json
        end.to change { retailer_user.reload.active }.from(true).to(false)
          .and change { agent_team.reload.active }.from(true).to(false)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body['status']).to be(false)
      end
    end

    context 'when toggle fails' do
      it 'returns forbidden status with error message' do
        allow_any_instance_of(RetailerUser).to receive(:update).and_return(false)

        put api_v1_mobile_toggle_retailer_user_status_path,
            headers: headers,
            as: :json

        expect(response).to have_http_status(:forbidden)
        expect(response.parsed_body['error']).to eq(I18n.t('retailer_user.status.failed'))
      end
    end
  end

  describe 'DELETE #destroy' do
    context 'when deletion is successful' do
      let(:deal) { create(:deal, retailer_user: retailer_user) }
      let(:agent_team) { create(:agent_team, retailer_user: retailer_user) }
      let(:team) { create(:team, retailer: retailer) }
      let(:team_retailer_user) { create(:team_retailer_user, retailer_user: retailer_user, team: team) }

      it 'returns Usuario eliminado con éxito.' do
        expect do
          delete api_v1_mobile_destroy_retailer_user_path,
                 headers: headers,
                 as: :json
        end.to change { retailer_user.reload.removed_from_team }.from(false).to(true)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body['message']).to eq('Usuario eliminado con éxito.')
      end

      it 'destroys associated agent_team and team_retailer_user' do
        agent_team_id = agent_team.id
        team_retailer_user_id = team_retailer_user.id
        delete api_v1_mobile_destroy_retailer_user_path,
               headers: headers,
               as: :json

        expect(AgentTeam.exists?(agent_team_id)).to be false
        expect(TeamRetailerUser.exists?(team_retailer_user_id)).to be false
      end

      context 'when user has multiple retailers' do
        let!(:another_retailer) { create(:retailer) }
        let!(:another_retailer_user) do
          create(:retailer_user, user: retailer_user.user, retailer: another_retailer, current: true)
        end

        it 'updates the current retailer user' do
          retailer_user.update(current: false)
          delete api_v1_mobile_destroy_retailer_user_path,
                 headers: headers,
                 as: :json

          another_retailer_user.reload
          expect(another_retailer_user.removed_from_team).to be(true)
          expect(another_retailer_user.user_id).to be_nil
          expect(another_retailer_user.current).to be(false)
          expect(response).to have_http_status(:ok)
        end
      end
    end

    context 'when deletion fails' do
      it 'returns unprocessable_entity status with error messages' do
        errors = double('errors', full_messages: ['Error 1', 'Error 2'])
        allow_any_instance_of(RetailerUser).to receive(:delete_agent).and_return(false)
        allow_any_instance_of(RetailerUser).to receive(:errors).and_return(errors)

        delete api_v1_mobile_destroy_retailer_user_path,
               headers: headers,
               as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        parsed_response = response.parsed_body
        expect(parsed_response['message']).to eq('Error eliminando usuario')
        expect(parsed_response['errors']).to eq('Error 1, Error 2')
      end
    end
  end

  describe 'GET #get_jwt_cookie_for_mobile' do
    context 'when user exists' do
      it 'sets the jwt cookie and returns the token' do
        get api_v1_mobile_get_jwt_cookie_path,
            headers: headers,
            as: :json

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body['jwt_token']).to eq(response.cookies['jwt_token'])
        expect(response.cookies['jwt_token']).to be_present
      end
    end

    context 'when user does not exist' do
      it 'returns not found status and error message' do
        get api_v1_mobile_get_jwt_cookie_path,
            headers: missing_headers,
            as: :json

        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body['message']).to eq('Resource not found')
      end
    end
  end
end
