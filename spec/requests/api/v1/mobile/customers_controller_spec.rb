require 'rails_helper'

RSpec.describe Api::V1::Mobile::CustomersController do
  let!(:retailer) { create(:retailer, name: 'Test Connection') }
  let!(:retailer_user) { create(:retailer_user, :admin, :logged_in_api, retailer:) }

  let :headers do
    {
      email: retailer_user.email,
      device: retailer_user.api_session_device,
      token: retailer_user.api_session_token
    }
  end

  describe 'Get #index' do
    let!(:customers) { create_list(:customer, 10, retailer:) }
    let!(:wa_customer) { create(:customer, retailer:, whatsapp_name: '<PERSON>', first_name: nil, last_name: nil) }

    it 'returns all customers' do
      get api_v1_mobile_customers_path, headers:, as: :json
      json_response = response.parsed_body
      expect(response).to have_http_status :ok
      expect(json_response['customers'].count).to eq(11)
      expect(json_response['results']).to eq(11)
    end

    it 'returns only updated customers' do
      upd_customer = retailer.customers.last

      Timecop.freeze(Time.zone.today - 2) do
        upd_customer.update(first_name: 'New name')
      end
      get api_v1_mobile_customers_path, headers:,
                                        params: {
                                          last_update: DateTime.now.strftime('%Y-%m-%d %H:%M')
                                        }, as: :json

      json_response = response.parsed_body

      expect(response).to have_http_status :ok
      expect(json_response['customers'].count).to eq(10)
      expect(json_response['results']).to eq(10)
    end
  end

  describe 'Post #create' do
    let(:customer) do
      create(:customer, retailer:, created_at: Time.now - 8.days, phone: '987987987', country_id: 'EC')
    end

    let(:valid_params) do
      {
        customer: {
          first_name: 'Juan',
          last_name: 'Campos',
          email: '<EMAIL>',
          phone: '987987987',
          country_id: 'EC'
        }
      }
    end

    let(:update_params) do
      {
        customer: {
          id: customer.id,
          first_name: 'Juan',
          last_name: 'Campos',
          email: '<EMAIL>',
          phone: '987987987',
          country_id: 'EC',
          complete_number: '+593987987987'
        }
      }
    end

    let(:params_with_retailer) do
      {
        customer: valid_params[:customer].merge(retailer_user_id: retailer_user.id)
      }
    end

    let(:invalid_params) do
      {
        customer: {
          first_name: 'Juan',
          last_name: 'Campos',
          email: 'not valid mail',
          phone: '987987987',
          country_id: 'EC'
        }
      }
    end

    let(:expected_json_response) do
      {
        'first_name' => 'Juan',
        'last_name' => 'Campos',
        'is_group' => false,
        'number_to_use' => nil,
        'email' => '<EMAIL>',
        'open_chat?' => false,
        'phone' => '987987987',
        'phone_with_country_code' => '+593987987987',
        'whatsapp_opt_in' => true,
        'retailer_user_id' => nil
      }
    end

    it 'creates a customer' do
      post api_v1_mobile_customers_path, headers:, params: valid_params, as: :json

      json_response = response.parsed_body
      expect(response.content_type).to eq 'application/json; charset=utf-8'
      expect(response).to have_http_status :ok
      expect(json_response.except('id')).to eq(expected_json_response)
    end

    it 'creates a customer and assigns a retailer_user' do
      post api_v1_mobile_customers_path, headers:, params: params_with_retailer, as: :json

      json_response = response.parsed_body
      expect(response.content_type).to eq 'application/json; charset=utf-8'
      expect(response).to have_http_status :ok
      expect(json_response['retailer_user_id']).to eq(retailer_user.id)
    end

    it 'returns 202 if number exists' do
      post api_v1_mobile_customers_path, headers:, params: update_params, as: :json

      expect(response.content_type).to eq 'application/json; charset=utf-8'
      expect(response).to have_http_status :accepted
    end

    it 'returns error for invalid data' do
      allow_any_instance_of(ActionDispatch::Request)
        .to receive(:session).and_return({})

      post(
        api_v1_mobile_customers_path,
        params: invalid_params,
        headers: headers
      )

      expect(response).to have_http_status(:unprocessable_entity)
      expect(response.content_type).to eq('application/json; charset=utf-8')
      expect(response.parsed_body['errors']).to eq('Email invalido')
    end
  end

  describe 'PUT #update' do
    let(:customer) do
      create(:customer, retailer:, created_at: Time.now - 8.days, phone: '987987987', country_id: 'EC')
    end

    let(:update_params) do
      {
        customer: {
          id: customer.id,
          first_name: 'Juan',
          last_name: 'Campos',
          email: '<EMAIL>',
          phone: customer.phone
        }
      }
    end

    let(:update_params_with_retailer) do
      {
        customer: {
          id: customer.id,
          first_name: 'Juan',
          last_name: 'Campos',
          email: '<EMAIL>',
          phone: customer.phone,
          retailer_user_id: retailer_user.id
        }
      }
    end

    it 'updates customer' do
      put api_v1_mobile_customer_path(customer.id), headers:, params: update_params, as: :json

      json_response = response.parsed_body
      expect(response.content_type).to eq 'application/json; charset=utf-8'
      expect(response).to have_http_status :ok
      customer.reload
      expect(json_response['id']).to eq(customer.id)
      expect(json_response['phone_with_country_code']).to eq(customer.phone)
      expect(json_response['phone']).to eq(customer.phone_without_country_code)
      expect(json_response['email']).to eq(customer.email)
      expect(json_response['first_name']).to eq(customer.first_name)
      expect(json_response['last_name']).to eq(customer.last_name)
    end

    it 'updates customer with retailer user' do
      put api_v1_mobile_customer_path(customer.id), headers:, params: update_params_with_retailer, as: :json

      json_response = response.parsed_body
      expect(response.content_type).to eq 'application/json; charset=utf-8'
      expect(response).to have_http_status :ok
      customer.reload
      expect(json_response['retailer_user_id']).to eq(customer.retailer_user_id)
    end
  end
end
