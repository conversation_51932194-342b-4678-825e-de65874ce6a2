require 'rails_helper'

# rubocop:disable RSpec/ExampleLength
# rubocop:disable Layout/LineLength
RSpec.describe Api::V1::WhatsappController do
  let(:retailer) { create(:retailer, :gupshup_integrated) }
  let(:qr_retailer) { create(:retailer, :qr_integrated) }
  let(:karix_successful_response) do
    {
      'meta' => {
        'request_uuid' => '********-c79f-48f7-bf03-b04659611c2e',
        'available_credits' => '1.51226',
        'credits_charged' => '0.004'
      },
      'objects' => [
        {
          'account_uid' => '3a9f05a1-4e59-4504-9ca9-be9ec1934f2b',
          'channel' => 'whatsapp',
          'channel_details' => {
            'whatsapp' => {
              'platform_fee' => '0.004',
              'type' => 'conversation',
              'whatsapp_fee' => '0'
            }
          },
          'content' => {
            'text' => 'New Whatsapp message'
          },
          'content_type' => 'text',
          'country' => 'EC',
          'created_time' => '2020-03-18T15:01:14.624018Z',
          'delivered_time' => nil,
          'destination' => '+************',
          'direction' => 'outbound',
          'error' => nil,
          'redact' => false,
          'refund' => nil,
          'sent_time' => nil,
          'source' => '+***********',
          'status' => 'queued',
          'total_cost' => '0.004',
          'uid' => '87f3c742-95e3-4bb3-a671-cce2705e1a21',
          'updated_time' => nil
        }
      ]
    }
  end
  let(:karix_outbound_data_event) do
    {
      'account_id' => retailer.id,
      'data' => {
        'account_uid' => '3a9f05a1-4e59-4504-9ca9-be9ec1934f2b',
        'channel' => 'whatsapp',
        'channel_details' => {
          'whatsapp' => {
            'platform_fee' => '0',
            'type' => 'notification',
            'whatsapp_fee' => '0'
          }
        },
        'content' => {
          'text' => 'New Whatsapp message'
        },
        'content_type' => 'text',
        'country' => 'EC',
        'created_time' => '2020-03-18T16:05:21.529993Z',
        'delivered_time' => nil,
        'destination' => '+************3',
        'direction' => 'outbound',
        'error' => {
          'code' => '1102',
          'message' => 'For whatsapp channel, \'destination\' param should be a sandbox number. Contact sales to remove all restrictions'
        },
        'redact' => false,
        'refund' => nil,
        'sent_time' => nil,
        'source' => '+***********',
        'status' => 'failed',
        'total_cost' => '0',
        'uid' => '0629d302-d9d6-4fbe-afa5-2c4a504f24e6',
        'updated_time' => nil
      }
    }
  end
  let(:params) do
    {
      agent: 'all',
      tag: 'all',
      campaign: 'none',
      type: 'all',
      searchString: '',
      order: 'received_desc'
    }
  end
  let(:retailer_user) { create(:retailer_user, :admin, retailer: retailer) }
  let(:retailer_gupshup) { create(:retailer, :gupshup_integrated) }
  let(:retailer_maytapi) { create(:retailer, :qr_integrated) }
  let(:retailer_user_gupshup) { create(:retailer_user, :admin, retailer: retailer_gupshup) }
  let(:retailer_user_maytapi) { create(:retailer_user, :admin, retailer: retailer_maytapi) }

  let(:customer1) { create(:customer, retailer: retailer, first_name: 'Pedro', last_name: 'Perez', ws_active: true) }
  let(:customer2) do
    customer = create(:customer, retailer: retailer, first_name: 'Jane', last_name: 'Doe', ws_active: true)
    create(:customer_mia_chatbot, customer: customer, active: true, human_help: true, platform: 'whatsapp')
    customer
  end
  let(:customer3) do
    customer = create(:customer, retailer: retailer_gupshup, first_name: 'Carlos', last_name: 'Gil', ws_active: true)
    create(:customer_mia_chatbot, customer: customer, active: true, human_help: true, platform: 'whatsapp')
    customer
  end
  let(:customer4) { create(:customer, retailer: retailer_maytapi, first_name: 'Not Jane', last_name: 'Not Doe', ws_active: true) }
  let!(:customer5) { create(:customer, retailer: retailer, first_name: 'Monica', ws_active: true, campaignerized: true) }
  let(:customer_gupshup) do
    customer = create(:customer, retailer: retailer_gupshup, first_name: 'Carlos', last_name: 'Gil', ws_active: true)
    create(:customer_mia_chatbot, customer: customer, active: true, human_help: true, platform: 'whatsapp')
    customer
  end

  before do
    # Whatsapp messages for customer1 and customer2
    create_list(:gupshup_whatsapp_message, 2, :inbound, retailer: retailer, customer: customer1, status: 'submitted')
    create_list(:gupshup_whatsapp_message, 2, :inbound, retailer: retailer, customer: customer1, status: 'read')
    create_list(:gupshup_whatsapp_message, 2, :outbound, retailer: retailer, customer: customer1, status: 'delivered')
    create_list(:gupshup_whatsapp_message, 2, :inbound, retailer: retailer, customer: customer1, status: 'error')
    create_list(:gupshup_whatsapp_message, 6, retailer: retailer, customer: customer2)

    retailer_user.generate_api_token!
    sign_in retailer_user.user
  end

  describe 'GET #index' do
    context 'when local request' do
      it 'responses with all customers' do
        get api_v1_karix_customers_path, params: params
        body = response.parsed_body
        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(3)
      end

      it 'responses with all customers with asc order' do
        get api_v1_karix_customers_path, params: params.merge!(order: 'received_asc')
        body = response.parsed_body
        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(3)
      end

      it 'filters customers by first_name' do
        params[:searchString] = customer2.first_name
        get api_v1_karix_customers_path, params: params
        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer2.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by last_name' do
        params[:searchString] = customer2.last_name
        get api_v1_karix_customers_path, params: params
        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer2.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by first_name and last_name' do
        params[:searchString] = "#{customer2.first_name} #{customer2.last_name}"
        get api_v1_karix_customers_path, params: params
        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer2.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by email' do
        params[:searchString] = customer1.email
        get api_v1_karix_customers_path, params: params
        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer1.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by phone' do
        params[:searchString] = customer1.phone
        get api_v1_karix_customers_path, params: params
        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer1.slice(:id, :phone, :first_name, :last_name))
      end

      it 'responses with a 404 when no customers registered' do
        retailer.customers.destroy_all

        get api_v1_karix_customers_path, params: params
        body = response.parsed_body

        expect(response).to have_http_status(:not_found)
        expect(body['message']).to eq('Customers not found')
        expect(body['customers']).to eq([])
      end

      context 'when the retailer user is an admin' do
        context 'when managed_agents field is empty' do
          it 'return all customers with active whatsapp' do
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(body['customers'].count).to eq(3)
          end
        end

        context 'when managed_agents field is not empty' do
          let(:agent1) { create(:retailer_user, :with_retailer, :agent, retailer: retailer) }
          let(:agent2) { create(:retailer_user, :with_retailer, :agent, retailer: retailer) }
          let(:agent3) { create(:retailer_user, :with_retailer, :agent, retailer: retailer) }

          let(:admin_user) do
            create(:retailer_user, :with_retailer, :admin, retailer: retailer, managed_agents: [agent1.id, agent2.id])
          end

          let(:customer6) { create(:customer, retailer: retailer, first_name: 'Marcia', ws_active: true) }

          let!(:agent_customer1) { create(:agent_customer, retailer_user: agent1, customer: customer1) }
          let!(:agent_customer2) { create(:agent_customer, retailer_user: agent1, customer: customer2) }
          let!(:agent_customer3) { create(:agent_customer, retailer_user: agent2, customer: customer5) }
          let!(:agent_customer4) { create(:agent_customer, retailer_user: agent3, customer: customer6) }

          before do
            sign_out retailer_user.user
            sign_in admin_user.user
          end

          it 'return only customers associates to manage_agents ids' do
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(body['customers'].count).to eq(3)
          end
        end
      end

      context 'when the retailer user is an agent' do
        let(:retailer_user_agent) { create(:retailer_user, :with_retailer, :agent, retailer: retailer) }
        let!(:agent_customer1) { create(:agent_customer, retailer_user: retailer_user, customer: customer1) }
        let!(:agent_customer2) { create(:agent_customer, retailer_user: retailer_user_agent, customer: customer2) }

        before do
          sign_out retailer_user.user
          sign_in retailer_user_agent.user
        end

        context 'when does not send "your" as agent param' do
          it 'returns only the customers assigned to it or those not assigned' do
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(2)
          end
        end

        context 'when send "your" as agent param' do
          it 'returns only the customers assigned to it' do
            params.merge!(agent: 'your')
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(1)
          end
        end
      end

      context 'when retailer_user has neither managed_agents nor is an agent nor is admin' do
        before do
          allow_any_instance_of(RetailerUser).to receive_messages(
            can_list_all_chats?: false,
            managed_agents: nil,
            agent?: false
          )
        end

        it 'returns internal_server_error' do
          get api_v1_karix_customers_path, params: params
          expect(response).to have_http_status(:internal_server_error)
        end
      end

      context 'when the retailer user does not have customers' do
        before do
          retailer.gupshup_whatsapp_messages.delete_all
          retailer.customers.update_all(ws_active: false)
        end

        it 'fails, a 404 Error will be responsed' do
          get api_v1_karix_customers_path, params: params
          body = response.parsed_body

          expect(response).to have_http_status(:not_found)
          expect(body['message']).to eq('Customers not found')
        end
      end

      context 'when retailer does not have positive balance' do
        before do
          retailer.update(ws_balance: -20)
        end

        it 'includes balance error info in response' do
          get api_v1_karix_customers_path, params: params
          body = response.parsed_body

          expect(body['balance_error_info']).to eq(
            {
              'status' => 401,
              'message' => 'Usted no tiene suficiente saldo para enviar mensajes de Whatsapp, por favor recargue'
            }
          )
        end
      end

      context 'when the tag filter is present' do
        context 'when the tag filter is "all"' do
          let!(:tag) { create(:tag, retailer: retailer) }
          let!(:customer_tag) { create(:customer_tag, tag: tag, customer: customer1) }

          it 'responses the customers with (any tag assigned/without tags assigned)' do
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(3)
          end
        end

        context 'when the tag filter is not "all"' do
          let(:tag) { create(:tag, retailer: retailer) }
          let!(:customer_tag) { create(:customer_tag, tag: tag, customer: customer1) }

          it 'responses only the customers with the tag assigned' do
            params.merge!(order: 'received_asc', tag: tag.id, customer_id: customer1.id)
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(1)
          end
        end
      end

      context 'when the type filter is present' do
        context 'when the tag filter is "read"' do
          it 'responses only the customers with messages marked as read' do
            params.merge!(type: 'read')
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers']).not_to be_empty
          end
        end

        context 'when the tag filter is "no_read"' do
          it 'responses only the customers with messages marked as no_read' do
            params.merge!(type: 'no_read')
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers']).not_to be_empty
          end
        end
      end

      context 'when the status chat filter is present' do
        context 'when the tag filter is "no_answer"' do
          it 'responses only the customers with status chat no_answer' do
            params.merge!(status: 'no_answer')
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers']).not_to be_empty
          end
        end

        context 'when the tag filter is "all"' do
          context 'without tab params' do
            it 'responses only the customers' do
              params.merge!(status: 'all')
              get api_v1_karix_customers_path, params: params
              body = response.parsed_body

              expect(response).to have_http_status(:ok)
              expect(body['customers']).not_to be_empty
            end
          end

          context 'with tab params' do
            context 'when the params value is pending' do
              it 'responses only the customers' do
                params.merge!(status: 'all', tab: 'pending')
                get api_v1_karix_customers_path, params: params
                body = response.parsed_body

                expect(response).to have_http_status(:ok)
                expect(body['customers']).not_to be_empty
              end
            end

            context 'when the params value is resolved' do
              let!(:customer6) do
                create(:customer, retailer: retailer, first_name: 'Monica', ws_active: true, status_chat: 'resolved')
              end

              it 'responses only the customers' do
                params.merge!(status: 'all', tab: 'resolved')
                get api_v1_karix_customers_path, params: params
                body = response.parsed_body

                expect(response).to have_http_status(:ok)
                expect(body['customers']).not_to be_empty
              end
            end

            context 'when the params value is neither resolved not pending' do
              it 'responses only the customers' do
                params.merge!(status: 'all', tab: 'otherwise')
                get api_v1_karix_customers_path, params: params
                body = response.parsed_body

                expect(response).to have_http_status(:ok)
                expect(body['customers']).not_to be_empty
              end
            end
          end
        end

        context 'when the tag filter is not "no_answer"' do
          it 'responses only the customers with other status chat' do
            params.merge!(status: 'new_chat')
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers']).not_to be_empty
          end
        end
      end

      context 'when the campaign filter is present' do
        context 'when the campaign filter is "none"' do
          it 'responses with all customers' do
            get api_v1_karix_customers_path, params: params
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(retailer.customers.where(campaignerized: [false, true]).count)
          end
        end

        context 'when the campaign filter is "all"' do
          it 'responses with campaign customers' do
            get api_v1_karix_customers_path, params: params.merge!(campaign: 'all')
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(1)
          end
        end

        context 'when the campaign filter is an id' do
          let!(:campaign) { create(:campaign, :with_sent_messages, retailer: retailer) }

          it 'responses with campaign customers' do
            get api_v1_karix_customers_path, params: params.merge!(campaign: campaign.id)
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(1)
          end
        end
      end

      context 'when the human help param is present' do
        it 'returns only the customers with human help needed' do
          get api_v1_karix_customers_path, params: params.merge!(human_help: true)
          body = response.parsed_body

          expect(response).to have_http_status(:ok)
          expect(body['customers'].count).to eq(1)
        end
      end

      context 'when the customer id param is present' do
        it 'returns only the customers with human help needed' do
          get api_v1_karix_customers_path, params: params.merge!(customer_id: customer1.id)
          body = response.parsed_body

          expect(response).to have_http_status(:ok)
          expect(body['customers'].count).to eq(1)
        end
      end
    end

    context 'when mobile request' do
      before do
        sign_out retailer_user.user
      end

      let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user) }

      let(:header_email) { retailer_user.email }
      let(:header_device) { retailer_user.api_session_device }
      let(:header_token) { retailer_user.api_session_token }

      it 'responses with all customers' do
        get api_v1_karix_customers_path,
            headers: { email: header_email, device: header_device, token: header_token },
            params: params
        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(3)
      end

      it 'filters customers by first_name' do
        params[:searchString] = customer2.first_name
        get api_v1_karix_customers_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer2.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by last_name' do
        params[:searchString] = customer2.last_name
        get api_v1_karix_customers_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer2.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by first_name and last_name' do
        params[:searchString] = "#{customer2.first_name} #{customer2.last_name}"
        get api_v1_karix_customers_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer2.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by email' do
        params[:searchString] = customer1.email
        get api_v1_karix_customers_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer1.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by phone' do
        params[:searchString] = customer1.phone
        get api_v1_karix_customers_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer1.slice(:id, :phone, :first_name, :last_name))
      end

      it 'responses with a 404 when no customers registered' do
        retailer.customers.destroy_all

        get api_v1_karix_customers_path,
            headers: { email: header_email, device: header_device, token: header_token },
            params: params
        body = response.parsed_body

        expect(response).to have_http_status(:not_found)
        expect(body['message']).to eq('Customers not found')
        expect(body['customers']).to eq([])
      end

      context 'when the retailer user is an agent' do
        let(:retailer_user_agent) { create(:retailer_user, :with_retailer, :agent, retailer: retailer) }
        let!(:agent_customer1) { create(:agent_customer, retailer_user: retailer_user, customer: customer1) }
        let!(:agent_customer2) { create(:agent_customer, retailer_user: retailer_user_agent, customer: customer2) }

        let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user_agent) }

        let(:header_email) { retailer_user_agent.email }
        let(:header_device) { retailer_user_agent.api_session_device }
        let(:header_token) { retailer_user_agent.api_session_token }

        before do
          retailer_user_agent.generate_api_token!
        end

        it 'returns only the customers assigned to it or those not assigned' do
          get api_v1_karix_customers_path,
              headers: { email: header_email, device: header_device, token: header_token },
              params: params
          body = response.parsed_body

          expect(response).to have_http_status(:ok)
          expect(body['customers'].count).to eq(2)
        end
      end

      context 'when the retailer user does not have customers' do
        before do
          retailer.gupshup_whatsapp_messages.delete_all
          retailer.customers.update_all(ws_active: false)
        end

        it 'fails, a 404 Error will be responsed' do
          get api_v1_karix_customers_path,
              headers: { email: header_email, device: header_device, token: header_token },
              params: params
          body = response.parsed_body

          expect(response).to have_http_status(:not_found)
          expect(body['message']).to eq('Customers not found')
        end
      end

      context 'when the tag filter is present' do
        context 'when the tag filter is "all"' do
          let!(:tag) { create(:tag, retailer: retailer) }
          let!(:customer_tag) { create(:customer_tag, tag: tag, customer: customer1) }

          it 'responses the customers with (any tag assigned/without tags assigned)' do
            get api_v1_karix_customers_path,
                params: params,
                headers: { email: header_email, device: header_device, token: header_token }

            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(3)
          end
        end

        context 'when the tag filter is not "all"' do
          let(:tag) { create(:tag, retailer: retailer) }
          let!(:customer_tag) { create(:customer_tag, tag: tag, customer: customer1) }

          it 'responses only the customers with the tag assigned' do
            params[:tag] = tag.id
            get api_v1_karix_customers_path,
                params: params,
                headers: { email: header_email, device: header_device, token: header_token }

            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(1)
          end
        end
      end
    end
  end

  describe 'GET #index_mobile' do
    context 'when mobile request' do
      before do
        sign_out retailer_user.user
      end

      let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user) }

      let(:header_email) { retailer_user.email }
      let(:header_device) { retailer_user.api_session_device }
      let(:header_token) { retailer_user.api_session_token }

      it 'responses with all customers' do
        get index_mobile_api_v1_karix_whatsapp_index_path,
            headers: { email: header_email, device: header_device, token: header_token },
            params: params
        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(3)
      end

      it 'filters customers by first_name' do
        params[:searchString] = customer2.first_name
        get index_mobile_api_v1_karix_whatsapp_index_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer2.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by last_name' do
        params[:searchString] = customer2.last_name
        get index_mobile_api_v1_karix_whatsapp_index_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer2.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by first_name and last_name' do
        params[:searchString] = "#{customer2.first_name} #{customer2.last_name}"
        get index_mobile_api_v1_karix_whatsapp_index_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer2.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by email' do
        params[:searchString] = customer1.email
        get index_mobile_api_v1_karix_whatsapp_index_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer1.slice(:id, :phone, :first_name, :last_name))
      end

      it 'filters customers by phone' do
        params[:searchString] = customer1.phone
        get index_mobile_api_v1_karix_whatsapp_index_path,
            params: params,
            headers: { email: header_email, device: header_device, token: header_token }

        body = response.parsed_body

        expect(response).to have_http_status(:ok)
        expect(body['customers'].count).to eq(1)
        expect(body['customers'][0]).to include(customer1.slice(:id, :phone, :first_name, :last_name))
      end

      it 'responses with a 404 when no customers registered' do
        retailer.customers.destroy_all

        get index_mobile_api_v1_karix_whatsapp_index_path,
            headers: { email: header_email, device: header_device, token: header_token },
            params: params
        body = response.parsed_body

        expect(response).to have_http_status(:not_found)
        expect(body['message']).to eq('Customers not found')
        expect(body['customers']).to eq([])
      end

      context 'when the retailer user is an agent' do
        let(:retailer_user_agent) { create(:retailer_user, :with_retailer, :agent, retailer: retailer) }
        let!(:agent_customer1) { create(:agent_customer, retailer_user: retailer_user, customer: customer1) }
        let!(:agent_customer2) { create(:agent_customer, retailer_user: retailer_user_agent, customer: customer2) }

        let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user_agent) }

        let(:header_email) { retailer_user_agent.email }
        let(:header_device) { retailer_user_agent.api_session_device }
        let(:header_token) { retailer_user_agent.api_session_token }

        before do
          retailer_user_agent.generate_api_token!
        end

        it 'returns only the customers assigned to it or those not assigned' do
          get index_mobile_api_v1_karix_whatsapp_index_path,
              headers: { email: header_email, device: header_device, token: header_token },
              params: params
          body = response.parsed_body

          expect(response).to have_http_status(:ok)
          expect(body['customers'].count).to eq(2)
        end
      end

      context 'when retailer_user has neither managed_agents nor is an agent nor is admin' do
        let(:retailer_user) { create(:retailer_user, retailer: retailer, retailer_admin: false, retailer_supervisor: false) }

        before do
          retailer_user.generate_api_token!
          sign_in retailer_user.user
        end

        it 'does not call active_whatsapp' do
          allow(retailer_user).to receive(:agent?).and_return(false)
          allow_any_instance_of(described_class).to receive(:current_retailer_user).and_return(retailer_user)

          expect(retailer.customers).not_to receive(:active_whatsapp)
          expect(retailer_user.customers).not_to receive(:active_whatsapp)
          expect do
            get index_mobile_api_v1_karix_whatsapp_index_path,
                headers: { email: header_email, device: header_device, token: header_token },
                params: params
          end.to raise_exception(TypeError)
        end
      end

      context 'when retailer does not have positive balance' do
        before do
          retailer.update(ws_balance: -20)
        end

        it 'includes balance error info in response' do
          get index_mobile_api_v1_karix_whatsapp_index_path,
              headers: { email: header_email, device: header_device, token: header_token },
              params: params
          body = response.parsed_body

          expect(body['balance_error_info']).to eq(
            {
              'status' => 401,
              'message' => 'Usted no tiene suficiente saldo para enviar mensajes de Whatsapp, por favor recargue'
            }
          )
        end
      end

      context 'when the retailer user does not have customers' do
        before do
          retailer.gupshup_whatsapp_messages.delete_all
          retailer.customers.update_all(ws_active: false)
        end

        it 'fails, a 404 Error will be responsed' do
          get index_mobile_api_v1_karix_whatsapp_index_path,
              headers: { email: header_email, device: header_device, token: header_token },
              params: params
          body = response.parsed_body

          expect(response).to have_http_status(:not_found)
          expect(body['message']).to eq('Customers not found')
        end
      end

      context 'when the retailer user is an admin' do
        context 'when managed_agents field is empty' do
          it 'return all customers with active whatsapp' do
            get index_mobile_api_v1_karix_whatsapp_index_path,
                headers: { email: header_email, device: header_device, token: header_token },
                params: params

            body = response.parsed_body

            expect(body['customers'].count).to eq(3)
          end
        end

        context 'when managed_agents field is not empty' do
          let(:agent1) { create(:retailer_user, :with_retailer, :agent, retailer: retailer) }
          let(:agent2) { create(:retailer_user, :with_retailer, :agent, retailer: retailer) }

          let(:admin_user) do
            create(:retailer_user, :with_retailer, :admin, retailer: retailer, managed_agents: [agent1.id])
          end

          let(:customer6) { create(:customer, retailer: retailer, first_name: 'Marcia', ws_active: true) }

          let!(:agent_customer1) { create(:agent_customer, retailer_user: agent1, customer: customer1) }
          let!(:agent_customer2) { create(:agent_customer, retailer_user: agent1, customer: customer2) }
          let!(:agent_customer3) { create(:agent_customer, retailer_user: admin_user, customer: customer5) }
          let!(:agent_customer4) { create(:agent_customer, retailer_user: admin_user, customer: customer6) }

          before do
            sign_out retailer_user.user
            sign_in admin_user.user
          end

          it 'return only customers associates to manage_agents ids' do
            get index_mobile_api_v1_karix_whatsapp_index_path,
                headers: { email: header_email, device: header_device, token: header_token },
                params: params

            body = response.parsed_body

            expect(body['customers'].count).to eq(4)
          end
        end
      end

      context 'when the tag filter is present' do
        context 'when the tag filter is "all"' do
          let!(:tag) { create(:tag, retailer: retailer) }
          let!(:customer_tag) { create(:customer_tag, tag: tag, customer: customer1) }

          it 'responses the customers with (any tag assigned/without tags assigned)' do
            get index_mobile_api_v1_karix_whatsapp_index_path,
                params: params,
                headers: { email: header_email, device: header_device, token: header_token }

            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(3)
          end
        end

        context 'when the tag filter is not "all"' do
          let(:tag) { create(:tag, retailer: retailer) }
          let!(:customer_tag) { create(:customer_tag, tag: tag, customer: customer1) }

          it 'responses only the customers with the tag assigned' do
            params[:tag] = tag.id
            get index_mobile_api_v1_karix_whatsapp_index_path,
                params: params,
                headers: { email: header_email, device: header_device, token: header_token }

            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['customers'].count).to eq(1)
          end
        end
      end
    end
  end

  describe 'POST #create' do
    let(:customer_gupshup) do
      create(:customer, retailer: retailer_gupshup, first_name: 'Carlos', last_name: 'Gil', ws_active: true, human_help: true)
    end

    context 'when local request' do
      let(:headers) { { 'ACCEPT' => 'application/json', 'content-type': 'application/json' } }

      before do
        sign_out retailer_user.user
        sign_in retailer_user_gupshup.user
      end

      context 'when Gupshup integrated' do
        let(:message) { create(:gupshup_whatsapp_message, customer: customer_gupshup) }
        let(:service_instance) { Whatsapp::Outbound::Msg.new }

        before do
          allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message)
            .and_return(message)
          allow(Whatsapp::Outbound::Msg).to receive(:new).and_return(service_instance)
        end

        context 'when the message is submitted' do
          it 'returns a 200 status code and store a new gupshup whatsapp message' do
            post api_v1_karix_send_whatsapp_message_path,
                 params: {
                   customer_id: customer_gupshup.id,
                   message: 'New whatsapp message'
                 }

            body = response.parsed_body
            expect(response).to have_http_status(:ok)
            expect(body['message']).to eq('Notificación enviada')
          end
        end

        context 'when customer doesnt have assigned_agent' do
          let(:customer_gupshup) do
            create(:customer, retailer: retailer_gupshup, first_name: 'Carlos', last_name: 'Gil', ws_active: true, human_help: true)
          end
          let(:gupshup_retailer_users) { retailer_gupshup.retailer_users }

          before do
            sign_in retailer_user_gupshup.user
            allow_any_instance_of(described_class).to receive(:current_retailer).and_return(retailer_gupshup)
            allow_any_instance_of(Customer).to receive(:assigned_agent?).and_return(false)
            allow_any_instance_of(Retailer).to receive(:retailer_users).and_return(gupshup_retailer_users)
          end

          it 'sets retailer_users.all_customers to agents' do
            expect(retailer_gupshup).to receive(:retailer_users).and_return(gupshup_retailer_users).at_least(:once)
            expect(gupshup_retailer_users).to receive(:all_customers).and_call_original

            post api_v1_karix_send_whatsapp_message_path,
                 params: {
                   customer_id: customer_gupshup.id,
                   message: 'New whatsapp message'
                 }

            expect(response).to have_http_status(:ok)
            body = response.parsed_body
            expect(body['message']).to eq('Notificación enviada')
          end
        end
      end

      context 'when sending a template message' do
        let(:msg_double) { instance_double(Whatsapp::Outbound::Msg) }
        let(:post_request) do
          post '/api/v1/karix_send_whatsapp_message',
               params: {
                 customer_id: customer_gupshup.id,
                 message: 'Template message',
                 template: 'true'
               }.to_json, headers: headers
        end

        before { allow(Whatsapp::Outbound::Msg).to receive(:new).and_return(msg_double) }

        it 'sets the type to template' do
          expect(msg_double).to receive(:send_message).with(hash_including(type: 'template'))
          post_request
          expect(response).to have_http_status(:ok)
        end
      end

      context 'when sending a note' do
        let(:msg_double) { instance_double(Whatsapp::Outbound::Msg) }
        let(:post_request) { post '/api/v1/karix_send_whatsapp_message', params: params.to_json, headers: headers }

        before { allow(Whatsapp::Outbound::Msg).to receive(:new).and_return(msg_double) }

        context 'with note' do
          let(:params) { { customer_id: customer3.id, message: 'Note message', note: true, type: 'note' } }

          it 'creates a note instead of sending a message' do
            expect(msg_double).to receive(:create_note)
            post_request

            expect(response).to have_http_status(:ok)
          end
        end

        context 'with order note' do
          let(:params) { { customer_id: customer3.id, message: 'Order note', note: true, order_note: true } }

          it 'handles order notes' do
            expect(msg_double).to receive(:create_note)
            post_request
            expect(response).to have_http_status(:ok)
          end
        end
      end

      context 'when a StandardError occurs' do
        before do
          allow_any_instance_of(described_class).to receive(:assign_agent).and_raise(StandardError)
        end

        it 'rescues the error and renders a bad request status' do
          post api_v1_karix_send_whatsapp_message_path,
               params: {
                 customer_id: customer_gupshup.id,
                 message: 'New whatsapp message'
               }

          expect(response).to have_http_status(:bad_request)
          response_body = response.parsed_body
          expect(response_body['message']).to eq('Error al enviar notificación')
          expect(Time.parse(response_body['recent_inbound_message_date']))
            .to be_within(1.second).of(customer_gupshup.recent_inbound_message_date)
        end
      end

      context 'when MayTapi integrated' do
        before do
          sign_out retailer_user.user
          sign_in retailer_user_maytapi.user
        end

        let(:message) { create(:gupshup_whatsapp_message, customer: customer4) }
        let(:service_instance) { Whatsapp::Outbound::Msg.new }

        context 'when the message is submitted' do
          it 'returns a 200 status code and store a new gupshup whatsapp message' do
            allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message)
              .and_return(message)

            post api_v1_karix_send_whatsapp_message_path,
                 params: {
                   customer_id: customer4.id,
                   message: 'New whatsapp message'
                 }

            body = response.parsed_body
            expect(response).to have_http_status(:ok)
            expect(body['message']).to eq('Notificación enviada')
          end
        end

        context 'when buttons message is submitted' do
          it 'returns a 200 status code and store a new gupshup whatsapp message' do
            success_response = double(HTTParty::Response, success?: true)
            allow(success_response).to receive(:parsed_response).and_return({
              success: true,
              data: {
                chatId: '<EMAIL>',
                msgId: '3EB065B353B9493D00C9'
              }
            })
            allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message_to_maytapi)
              .and_return(success_response)

            post api_v1_karix_send_whatsapp_message_path,
                 params: {
                   customer_id: customer4.id,
                   message: 'New whatsapp message',
                   type: 'buttons',
                   buttons: [
                     {
                       id: '!response 1',
                       text: 'Test Button 1'
                     },
                     {
                       id: '!response 2',
                       text: 'Test Button 2'
                     },
                     {
                       id: '!response 3',
                       text: 'Test Button 3'
                     }
                   ]
                 }

            body = response.parsed_body
            expect(response).to have_http_status(:ok)
            expect(body['message']).to eq('Notificación enviada')
          end
        end
      end
    end

    context 'when mobile request' do
      before do
        sign_out retailer_user.user
      end

      context 'when Gupshup integrated' do
        let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user_gupshup) }

        let(:header_email) { retailer_user_gupshup.email }
        let(:header_device) { retailer_user_gupshup.api_session_device }
        let(:header_token) { retailer_user_gupshup.api_session_token }

        let(:message) { create(:gupshup_whatsapp_message, customer: customer_gupshup) }
        let(:service_instance) { Whatsapp::Outbound::Msg.new }

        before do
          retailer_user_gupshup.generate_api_token!
        end

        context 'when the message is submitted' do
          it 'returns a 200 status code and store a new gupshup whatsapp message' do
            allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message)
              .and_return(message)
            allow(Whatsapp::Outbound::Msg).to receive(:new).and_return(service_instance)

            post api_v1_karix_send_whatsapp_message_path,
                 params: {
                   customer_id: customer_gupshup.id,
                   message: 'New whatsapp message'
                 },
                 headers: { email: header_email, device: header_device, token: header_token }

            body = response.parsed_body
            expect(response).to have_http_status(:ok)
            expect(body['message']).to eq('Notificación enviada')
          end
        end
      end

      context 'when the retailer has an unlimited account' do
        let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user) }

        let(:header_email) { retailer_user.email }
        let(:header_device) { retailer_user.api_session_device }
        let(:header_token) { retailer_user.api_session_token }

        before do
          retailer.update!(unlimited_account: true)
        end

        context 'when it is a HSM message' do
          context 'when it is not a positive balance' do
            it 'returns a 401 status' do
              retailer.update!(ws_balance: -15)

              post api_v1_karix_send_whatsapp_message_path,
                   params: {
                     customer_id: customer1.id,
                     message: 'New whatsapp message',
                     template: true
                   },
                   headers: { email: header_email, device: header_device, token: header_token }

              body = response.parsed_body
              expect(response).to have_http_status(:unauthorized)
              expect(body['message']).to eq('Usted no tiene suficiente saldo para enviar mensajes de Whatsapp, ' \
                                            'por favor recargue')
            end
          end
        end
      end
    end
  end

  describe 'GET #messages' do
    context 'when GupShup integrated' do
      before do
        create_list(:gupshup_whatsapp_message, 2, :inbound, retailer: retailer_gupshup, customer: customer_gupshup, status:
          'delivered')
        create_list(:gupshup_whatsapp_message, 2, :inbound, retailer: retailer_gupshup, customer: customer_gupshup, status:
          'read')
        create_list(:gupshup_whatsapp_message, 2, :outbound, retailer: retailer_gupshup, customer: customer_gupshup, status:
          'delivered')
        create_list(:gupshup_whatsapp_message, 2, :inbound, retailer: retailer_gupshup, customer: customer_gupshup, status:
          'error')

        sign_out retailer_user.user
      end

      context 'when local request' do
        before do
          sign_in retailer_user_gupshup.user
        end

        it 'marks as read only not failed or read inbound messages' do
          total_unread = customer_gupshup.gupshup_whatsapp_messages.where.not(status: %w[read error]).count
          expect(total_unread).to eq(4)

          get "/api/v1/karix_whatsapp_customers/#{customer_gupshup.id}/messages"
          body = response.parsed_body
          expect(response).to have_http_status(:ok)
          expect(body['messages'].count).to eq(8)

          total_unread = customer_gupshup.gupshup_whatsapp_messages.where.not(status: %w[read error]).count
          expect(total_unread).to eq(2)
        end

        context 'when retailer does not have positive balance' do
          let(:retailer_gupshup) { create(:retailer, :gupshup_integrated, ws_balance: -15) }

          it 'includes balance error info in response' do
            get "/api/v1/karix_whatsapp_customers/#{customer_gupshup.id}/messages"
            body = response.parsed_body
            expect(body['balance_error_info']).to eq(
              {
                'status' => 401,
                'message' => 'Usted no tiene suficiente saldo para enviar mensajes de Whatsapp, por favor recargue'
              }
            )
          end
        end

        context 'when gupshup_whatsapp_messages are empty' do
          before do
            GupshupWhatsappMessage.delete_all
            Messages::ArchiveGupshupWhatsappMessage.delete_all
          end

          it 'returns not_found' do
            get "/api/v1/karix_whatsapp_customers/#{customer_gupshup.id}/messages"
            expect(response.parsed_body).to eq({
              'message' => 'Messages not found',
              'gupshup_integrated' => true,
              'allow_send_voice' => true
            })
          end
        end

        context 'when messages are empty' do
          let(:retailer_gupshup) { create(:retailer, whats_app_enabled: false) }

          it 'returns not_found' do
            get "/api/v1/karix_whatsapp_customers/#{customer_gupshup.id}/messages"
            expect(response.parsed_body).to eq({
              'message' => 'Messages not found',
              'gupshup_integrated' => false,
              'allow_send_voice' => true
            })
          end
        end
      end

      context 'when mobile request' do
        before do
          sign_out retailer_user_gupshup.user
          retailer_user_gupshup.generate_api_token!
        end

        let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user_gupshup) }
        let(:header_email) { retailer_user_gupshup.email }
        let(:header_device) { retailer_user_gupshup.api_session_device }
        let(:header_token) { retailer_user_gupshup.api_session_token }

        it 'marks as read only not failed or read inbound messages' do
          total_unread = customer_gupshup.gupshup_whatsapp_messages.where.not(status: %w[read error]).count
          expect(total_unread).to eq(4)

          get "/api/v1/karix_whatsapp_customers/#{customer_gupshup.id}/messages",
              headers: { email: header_email, device: header_device, token: header_token }

          body = response.parsed_body

          expect(response).to have_http_status(:ok)
          expect(body['messages'].count).to eq(8)

          total_unread = customer_gupshup.gupshup_whatsapp_messages.where.not(status: %w[read error]).count
          expect(total_unread).to eq(2)
        end
      end
    end
  end

  describe 'POST #send_file' do
    context 'when local request' do
      context 'when the retailer is gupshup integrated' do
        before do
          sign_out retailer_user.user
          sign_in retailer_user_gupshup.user
          allow_any_instance_of(Uploaders::S3Aws).to receive(:upload_file).and_return(
            {
              'type' => 'upload',
              'secure_url' => 'https://dc2p3rqc7bvrd.cloudfront.net/6612ced7-e5cb-4d5c-8595-5632b7f568b3.mp4',
              'original_filename' => '6612ced7-e5cb-4d5c-8595-5632b7f568b3.mp4'
            }
          )
        end

        let(:message) { create(:gupshup_whatsapp_message, customer: customer_gupshup) }
        let(:service_instance) { Whatsapp::Outbound::Msg.new }

        context 'when the message is sent without errors' do
          context 'when image' do
            it 'successfully, returns a 200 status' do
              allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message)
                .and_return(message)
              allow(Whatsapp::Outbound::Msg).to receive(:new).and_return(service_instance)

              post "/api/v1/karix_whatsapp_send_file/#{customer_gupshup.id}",
                   params: {
                     file_data: fixture_file_upload(Rails.root.join('spec/fixtures/files/profile.jpg'), 'image/jpeg')
                   }

              body = response.parsed_body
              expect(response).to have_http_status(:ok)
              expect(body['message']).to eq('Notificación enviada')
            end
          end

          context 'when video' do
            before do
              sign_out retailer_user_gupshup.user
              sign_in retailer_user_maytapi.user
            end

            it 'successfully, returns a 200 status' do
              post "/api/v1/karix_whatsapp_send_file/#{customer4.id}",
                   params: {
                     file_data: fixture_file_upload(Rails.root.join('spec/fixtures/files/video_test.mp4'), 'video/mp4'),
                     type: 'video'
                   }

              body = response.parsed_body
              expect(response).to have_http_status(:ok)
              expect(body['message']).to eq('Notificación enviada')
            end
          end

          context 'when type is not video' do
            before do
              sign_out retailer_user_gupshup.user
              sign_in retailer_user_maytapi.user
            end

            it 'successfully returns a 200 status' do
              post "/api/v1/karix_whatsapp_send_file/#{customer4.id}",
                   params: {
                     file_data: Rack::Test::UploadedFile.new(Rails.root.join('spec/fixtures/files/video_test.mp4'), 'video/mp4'),
                     type: 'file'
                   }

              body = response.parsed_body

              expect(response).to have_http_status(:ok)
              expect(body['message']).to eq('Notificación enviada')
            end
          end
        end

        context 'when a StandardError occurs' do
          before do
            allow_any_instance_of(described_class).to receive(:type_file).and_raise(StandardError)
          end

          it 'rescues the error and renders a bad request status' do
            post "/api/v1/karix_whatsapp_send_file/#{customer_gupshup.id}",
                 params: {
                   file_data: fixture_file_upload(Rails.root.join('spec/fixtures/files/profile.jpg'), 'image/jpeg')
                 }

            expect(response).to have_http_status(:bad_request)
            response_body = response.parsed_body
            expect(response_body['message']).to eq('Error al enviar notificación')
            expect(Time.parse(response_body['recent_inbound_message_date']))
              .to be_within(1.second).of(customer_gupshup.recent_inbound_message_date)
          end
        end
      end
    end

    context 'when mobile request' do
      before do
        sign_out retailer_user.user
      end

      let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user) }

      let(:header_email) { retailer_user.email }
      let(:header_device) { retailer_user.api_session_device }
      let(:header_token) { retailer_user.api_session_token }

      context 'when the retailer is gupshup integrated' do
        let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user_gupshup) }

        let(:header_email) { retailer_user_gupshup.email }
        let(:header_device) { retailer_user_gupshup.api_session_device }
        let(:header_token) { retailer_user_gupshup.api_session_token }

        let(:message) { create(:gupshup_whatsapp_message, customer: customer_gupshup) }
        let(:service_instance) { Whatsapp::Outbound::Msg.new }

        before do
          retailer_user_gupshup.generate_api_token!
        end

        context 'when the message is sent without errors' do
          it 'successfully, returns a 200 status' do
            allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message)
              .and_return(message)
            allow(Whatsapp::Outbound::Msg).to receive(:new).and_return(service_instance)

            post "/api/v1/karix_whatsapp_send_file/#{customer_gupshup.id}",
                 params: {
                   file_data: fixture_file_upload(Rails.root.join('spec/fixtures/files/profile.jpg'), 'image/jpeg')
                 },
                 headers: { email: header_email, device: header_device, token: header_token }

            body = response.parsed_body
            expect(response).to have_http_status(:ok)
            expect(body['message']).to eq('Notificación enviada')
          end
        end
      end
    end
  end

  describe 'PUT #message_read' do
    context 'when local request' do
      context 'when is gupshup integrated' do
        before do
          sign_out retailer_user.user
          sign_in retailer_user_gupshup.user
        end

        let(:message) { create(:gupshup_whatsapp_message, customer: customer_gupshup) }
        let(:service_instance) { Whatsapp::Outbound::Msg.new }

        context 'when the message is sent without errors' do
          before do
            allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message)
              .and_return(message)
            allow(Whatsapp::Outbound::Msg).to receive(:new).and_return(service_instance)
          end

          it 'successfully, returns a 200 status' do
            put "/api/v1/whatsapp_update_message_read/#{customer_gupshup.id}",
                params: {
                  message_id: message.id
                }

            body = response.parsed_body
            expect(response).to have_http_status(:ok)
            expect(body['message']['id']).to eq(message.id)
          end

          context 'when message has agent' do
            let(:agent) { create(:retailer_user, :with_retailer) }
            let(:customer_gupshup) do
              create(:customer, retailer: retailer_gupshup, first_name: 'Carlos', last_name: 'Gil', ws_active: true, agent: agent)
            end

            it 'calls notify with message agent' do
              mock_object = instance_double(Notifications::Web::Messages)
              allow(Notifications::Web::Messages).to receive(:new).and_return(mock_object)
              expect(mock_object).to receive(:notify_read!).with(retailer_gupshup, customer_gupshup, [agent]).and_return(message)

              put "/api/v1/whatsapp_update_message_read/#{customer_gupshup.id}",
                  params: {
                    message_id: message.id
                  }

              body = response.parsed_body
              expect(response).to have_http_status(:ok)
              expect(body['message']['id']).to eq(message.id)
            end
          end

          context 'when a StandardError occurs' do
            before do
              allow_any_instance_of(Customer).to receive(:whatsapp_messages).and_raise(StandardError)
            end

            it 'rescues the error and renders a bad request status' do
              put "/api/v1/whatsapp_update_message_read/#{customer_gupshup.id}",
                  params: {
                    message_id: message.id
                  }
              expect(response).to have_http_status(:internal_server_error)
              response_body = response.parsed_body
              expect(response_body['message']).to eq('Error al actualizar mensajes')
              expect(Time.parse(response_body['recent_inbound_message_date']))
                .to be_within(1.second).of(customer_gupshup.recent_inbound_message_date)
            end
          end
        end
      end
    end

    context 'when mobile request' do
      before do
        sign_out retailer_user.user
      end

      let(:message) { create(:gupshup_whatsapp_message, customer: customer1) }
      let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user) }

      let(:header_email) { retailer_user.email }
      let(:header_device) { retailer_user.api_session_device }
      let(:header_token) { retailer_user.api_session_token }

      context 'when the message is updated without errors' do
        it 'successfully, returns a 200 status' do
          put "/api/v1/whatsapp_update_message_read/#{customer1.id}",
              params: {
                message_id: message.id
              },
              headers: { email: header_email, device: header_device, token: header_token }

          body = response.parsed_body
          expect(response).to have_http_status(:ok)
          expect(body['message']['status']).to eq('read')
        end
      end

      context 'when the message is not updated because of errors' do
        it 'returns a 500 status' do
          allow_any_instance_of(GupshupWhatsappMessage).to receive(:update_column).and_return(false)

          put "/api/v1/whatsapp_update_message_read/#{customer1.id}",
              params: {
                message_id: message.id
              },
              headers: { email: header_email, device: header_device, token: header_token }

          body = response.parsed_body
          expect(response).to have_http_status(:internal_server_error)
          expect(body['message']).to eq('Error al actualizar mensajes')
        end
      end
    end
  end

  describe 'GET #fast_answers_for_whatsapp' do
    let(:another_retailer_user) { create(:retailer_user, :agent, retailer: retailer) }
    let(:retailer_user_agent) { create(:retailer_user, :agent, retailer: retailer) }

    context 'when local request' do
      before do
        sign_out retailer_user.user
        sign_in retailer_user_agent.user
      end

      context 'when the templates are global' do
        before do
          create_list(:template, 3, :for_whatsapp, :global_template, retailer: retailer, retailer_user: retailer_user)
          create_list(:template, 3, :for_whatsapp, :global_template, retailer: retailer, retailer_user:
            another_retailer_user)
        end

        it 'returns a whatsapp fast answers list to all agents' do
          get api_v1_fast_answers_for_whatsapp_path
          body = response.parsed_body

          expect(response).to have_http_status(:ok)
          expect(body['templates']['data'].count).to eq(6)
        end
      end

      context 'when the templates do not have retailer user associated' do
        before do
          create_list(:template, 3, :for_whatsapp, :global_template, retailer: retailer)
        end

        it 'returns a whatsapp fast answers list to all agents' do
          get api_v1_fast_answers_for_whatsapp_path
          body = response.parsed_body

          expect(response).to have_http_status(:ok)
          expect(body['templates']['data'].count).to eq(3)
        end
      end

      context 'when search param is present' do
        before do
          create(:template, :for_whatsapp, :global_template, retailer: retailer, title: 'Texto de prueba', answer: 'Anything')
          create(:template, :for_whatsapp, :global_template, retailer: retailer, title: 'Anything', answer: 'Contenido de prueba')
          create(:template, :for_messenger, :global_template, retailer: retailer, title: 'Texto de prueba', answer: 'Anything')
          create(:template, :for_messenger, :global_template, retailer: retailer, title: 'Anything', answer: 'Contenido de prueba')
        end
      end

      context 'when the templates are not global and they have retailer user associated' do
        context 'when it is not the templates creator' do
          before do
            create_list(:template, 3, :for_whatsapp, retailer: retailer, retailer_user:
              another_retailer_user)
            create_list(:template, 4, :for_whatsapp, retailer: retailer, retailer_user: retailer_user)
          end

          it 'does not return any template' do
            get api_v1_fast_answers_for_whatsapp_path
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['templates']['data'].count).to eq(0)
          end
        end

        context 'when it is the templates creator' do
          before do
            create_list(:template, 3, :for_whatsapp, retailer: retailer, retailer_user:
              another_retailer_user)
            create_list(:template, 4, :for_whatsapp, retailer: retailer, retailer_user: retailer_user_agent)
          end

          it 'returns the templates belonging to it' do
            get api_v1_fast_answers_for_whatsapp_path
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['templates']['data'].count).to eq(4)
          end
        end
      end
    end

    context 'when mobile request' do
      let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user_agent) }

      let(:header_email) { retailer_user_agent.email }
      let(:header_device) { retailer_user_agent.api_session_device }
      let(:header_token) { retailer_user_agent.api_session_token }

      before do
        retailer_user_agent.generate_api_token!
        sign_out retailer_user.user
      end

      context 'when the templates are global' do
        before do
          create_list(:template, 3, :for_whatsapp, :global_template, retailer: retailer, retailer_user: retailer_user)
          create_list(:template, 3, :for_whatsapp, :global_template, retailer: retailer, retailer_user:
            another_retailer_user)
        end

        it 'returns a whatsapp fast answers list to all agents' do
          get api_v1_fast_answers_for_whatsapp_path, headers: {
            email: header_email,
            device: header_device,
            token: header_token
          }
          body = response.parsed_body

          expect(response).to have_http_status(:ok)
          expect(body['templates']['data'].count).to eq(6)
        end
      end

      context 'when the templates do not have retailer user associated' do
        before do
          create_list(:template, 3, :for_whatsapp, :global_template, retailer: retailer)
        end

        it 'returns a whatsapp fast answers list to all agents' do
          get api_v1_fast_answers_for_whatsapp_path, headers: {
            email: header_email,
            device: header_device,
            token: header_token
          }
          body = response.parsed_body

          expect(response).to have_http_status(:ok)
          expect(body['templates']['data'].count).to eq(3)
        end
      end

      context 'when search param is present' do
        before do
          create(:template, :for_whatsapp, :global_template, retailer: retailer, title: 'Texto de prueba', answer: 'Anything')
          create(:template, :for_whatsapp, :global_template, retailer: retailer, title: 'Anything', answer: 'Contenido de prueba')
          create(:template, :for_messenger, :global_template, retailer: retailer, title: 'Texto de prueba', answer: 'Anything')
          create(:template, :for_messenger, :global_template, retailer: retailer, title: 'Anything', answer: 'Contenido de prueba')
        end
      end

      context 'when the templates are not global and they have retailer user associated' do
        context 'when it is not the templates creator' do
          before do
            create_list(:template, 3, :for_whatsapp, retailer: retailer, retailer_user:
              another_retailer_user)
            create_list(:template, 4, :for_whatsapp, retailer: retailer, retailer_user: retailer_user)
          end

          it 'does not return any template' do
            get api_v1_fast_answers_for_whatsapp_path, headers: {
              email: header_email,
              device: header_device,
              token: header_token
            }
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['templates']['data'].count).to eq(0)
          end
        end

        context 'when it is the templates creator' do
          before do
            create_list(:template, 3, :for_whatsapp, retailer: retailer, retailer_user:
              another_retailer_user)
            create_list(:template, 4, :for_whatsapp, retailer: retailer, retailer_user: retailer_user_agent)
          end

          it 'returns the templates belonging to it' do
            get api_v1_fast_answers_for_whatsapp_path, headers: {
              email: header_email,
              device: header_device,
              token: header_token
            }
            body = response.parsed_body

            expect(response).to have_http_status(:ok)
            expect(body['templates']['data'].count).to eq(4)
          end
        end
      end
    end
  end

  describe 'PUT #set_chat_as_unread' do
    describe 'when chat service is facebook' do
      before do
        allow(FacebookNotificationHelper).to receive(:broadcast_data).and_return(true)
        customer1.update_column(:pstype, :messenger)
      end

      it 'sets the chat as unread' do
        patch "/api/v1/whatsapp_unread_chat/#{customer1.id}",
              params: {
                chat_service: 'facebook'
              }

        expect(response).to have_http_status(:ok)
        expect(customer1.reload.unread_messenger_chat).to be_truthy
      end

      context 'when the retailer user is an agent' do
        let(:retailer_user_agent) { create(:retailer_user, :with_retailer, :agent, retailer: retailer) }
        let!(:agent_customer1) { create(:agent_customer, retailer_user: retailer_user, customer: customer1) }

        before do
          sign_out retailer_user.user
          sign_in retailer_user_agent.user
        end

        it 'sets the chat as unread when customer.agent is present' do
          patch "/api/v1/whatsapp_unread_chat/#{customer1.id}",
                params: {
                  chat_service: 'facebook'
                }

          expect(response).to have_http_status(:ok)
          expect(assigns(:customer)).to be_present
          expect(customer1.reload.unread_messenger_chat).to be_truthy
        end
      end

      context 'when customer is not a messenger user' do
        before do
          customer1.update_column(:pstype, :instagram)
        end

        it 'sets the instagram chat as unread' do
          patch "/api/v1/whatsapp_unread_chat/#{customer1.id}",
                params: {
                  chat_service: 'instagram'
                }

          expect(response).to have_http_status(:ok)
          expect(customer1.reload.unread_instagram_chat).to be_truthy
        end
      end

      context 'when chat_service is blank' do
        it 'does not update any chat status' do
          expect do
            patch "/api/v1/whatsapp_unread_chat/#{customer1.id}",
                  params: {
                    chat_service: ''
                  }
          end.not_to(change { customer1.reload.attributes })

          expect(response).to have_http_status(:ok)
        end
      end
    end

    describe 'when chat service is whatsapp' do
      context 'when local request' do
        describe 'when the retailer is gupshup integrated' do
          before do
            allow_any_instance_of(Notifications::Web::Messages).to receive(:notify_new_counter).and_return(true)

            sign_out retailer_user.user
            sign_in retailer_user_gupshup.user
          end

          it 'sets the chat as unread' do
            patch "/api/v1/whatsapp_unread_chat/#{customer_gupshup.id}",
                  params: {
                    chat_service: 'whatsapp'
                  }

            expect(response).to have_http_status(:ok)
            expect(customer_gupshup.reload.unread_whatsapp_chat).to be_truthy
          end
        end
      end

      context 'when mobile request' do
        before do
          sign_out retailer_user.user
        end

        let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user_gupshup) }

        let(:header_email) { retailer_user_gupshup.email }
        let(:header_device) { retailer_user_gupshup.api_session_device }
        let(:header_token) { retailer_user_gupshup.api_session_token }

        describe 'when the retailer is gupshup integrated' do
          before do
            retailer_user_gupshup.generate_api_token!
            allow_any_instance_of(Notifications::Web::Messages).to receive(:notify_new_counter).and_return(true)
          end

          it 'sets the chat as unread' do
            # patch /api/v1/whatsapp_unread_chat/:id
            patch api_v1_set_chat_as_unread_path(customer_gupshup.id),
                  params: {
                    chat_service: 'whatsapp'
                  },
                  headers: { email: header_email, device: header_device, token: header_token }

            expect(response).to have_http_status(:ok)
            expect(customer_gupshup.reload.unread_whatsapp_chat).to be_truthy
          end
        end
      end
    end
  end

  describe 'POST #send_bulk_files' do
    let(:ok_net_response) { Net::HTTPOK.new(1.0, '200', 'OK') }
    let(:ok_body_response) do
      {
        status: 'submitted',
        messageId: 'ee4a68a0-1203-4c85-8dc3-49d0b3226a35'
      }.to_json
    end

    context 'when mobile request' do
      context 'when the retailer is gupshup integrated' do
        let(:header_email) { retailer_user_gupshup.email }
        let(:mobile_token) { create(:mobile_token, retailer_user: retailer_user_gupshup) }
        let(:header_device) { retailer_user_gupshup.api_session_device }
        let(:header_token) { retailer_user_gupshup.api_session_token }

        before do
          retailer_user_gupshup.generate_api_token!
          sign_out retailer_user.user
          sign_in retailer_user_gupshup.user
        end

        context 'when the message is sent without errors' do
          it 'successfully, returns a 200 status' do
            allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(ok_net_response)
            allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
            post api_v1_karix_send_bulk_files_path(customer_gupshup.id),
                 params: {
                   url: 'https://res.cloudinary.com/hs5vmn5xd/image/upload/v1601084668/fgloe3nljoxidzmcou6v.jpg',
                   template: false,
                   id: customer_gupshup.id
                 },
                 headers: { email: header_email, device: header_device, token: header_token }

            expect(response).to have_http_status(:ok)
            body = response.parsed_body
            expect(body['message']).to eq('Notificación enviada')
          end
        end

        context 'when params are missing' do
          it 'returns error when url is not sent' do
            post api_v1_karix_send_bulk_files_path(customer_gupshup.id),
                 params: {
                   template: false,
                   id: customer_gupshup.id
                 },
                 headers: { email: header_email, device: header_device, token: header_token }

            expect(response).to have_http_status(:bad_request)
            body = response.parsed_body
            expect(body['message']).to eq('Faltaron parámetros')
          end
        end
      end
    end

    context 'when local request' do
      let(:cloudinary_image_response) do
        {
          public_id: 'udbdbn7nv4xxupagiaxc',
          version: 1_585_073_676,
          signature: '5d01c78a3b9e3ecd7563ce0cfd72bb48a256ee64',
          width: 236,
          height: 203,
          format: 'jpeg',
          resource_type: 'image',
          created_at: '2020-03-24T18:14:36Z',
          tags: [],
          bytes: 17_804,
          type: 'upload',
          etag: 'ae455f6806cb00961ca947e120277fb3',
          placeholder: false,
          url: 'http://res.cloudinary.com/dhhrdm74a/image/upload/v1585073676/udbdbn7nv4xxupagiaxc.jpg',
          secure_url: 'https://res.cloudinary.com/dhhrdm74a/image/upload/v1585073676/udbdbn7nv4xxupagiaxc.jpg',
          original_filename: 'test_image'
        }.with_indifferent_access
      end

      let(:aws_s3_image_response) do
        {
          type: 'upload',
          url: 'https://mercately-bucket-dev.s3.amazonaws.com/udbdbn7nv4xxupagiaxc',
          secure_url: 'https://mercately-bucket-dev.s3.amazonaws.com/udbdbn7nv4xxupagiaxc',
          original_filename: 'test_image'
        }.with_indifferent_access
      end

      context 'when the retailer is gupshup integrated' do
        before do
          sign_in retailer_user_gupshup.user
        end

        context 'when the message is sent without errors' do
          it 'successfully, returns a 200 status' do
            allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(ok_net_response)
            allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
            allow_any_instance_of(Uploaders::S3Aws).to receive(:upload_file).and_return(aws_s3_image_response)
            post api_v1_karix_send_bulk_files_path(customer_gupshup.id),
                 params: {
                   file_data: [fixture_file_upload(Rails.root.join('spec/fixtures/files/profile.jpg'), 'image/jpeg')],
                   template: false,
                   id: customer_gupshup.id
                 }
            body = response.parsed_body
            expect(response).to have_http_status(:ok)
            expect(body['message']).to eq('Notificación enviada')
          end
        end
      end
    end
  end

  describe '#receive_whatsapp_qr' do
    context 'when update status message' do
      let!(:message) do
        create(
          :gupshup_whatsapp_message,
          :outbound,
          retailer: retailer,
          customer: customer1,
          status: 'sent',
          gupshup_message_id: '57157030-a856-11ed-9e3e-4bb940196bcf'
        )
      end

      context 'when reached' do
        it 'updates the message status' do
          post(
            '/whatsapp/webhook',
            params: {
              type: 'ack',
              product_id: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
              phone_id: retailer.qr_phone_id,
              data: [
                {
                  ackType: 'reached',
                  ackCode: 2,
                  chatId: '<EMAIL>',
                  msgId: '57157030-a856-11ed-9e3e-4bb940196bcf',
                  time: 1_675_932_430
                }
              ],
              whatsapp: {
                type: 'ack',
                product_id: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
                phone_id: retailer.qr_phone_id,
                data: [
                  {
                    ackType: 'reached',
                    ackCode: 3,
                    chatId: '<EMAIL>',
                    msgId: '57157030-a856-11ed-9e3e-4bb940196bcf',
                    time: 1_675_932_430
                  }
                ]
              }
            }
          )

          expect(message.reload.delivered?).to be true
        end
      end

      context 'when seen' do
        it 'updates the message status' do
          post(
            '/whatsapp/webhook',
            params: {
              type: 'ack',
              product_id: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
              phone_id: retailer.qr_phone_id,
              data: [
                {
                  ackType: 'seen',
                  ackCode: 3,
                  chatId: '<EMAIL>',
                  msgId: '57157030-a856-11ed-9e3e-4bb940196bcf',
                  time: 1_675_932_430
                }
              ],
              whatsapp: {
                type: 'ack',
                product_id: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
                phone_id: retailer.qr_phone_id,
                data: [
                  {
                    ackType: 'seen',
                    ackCode: 3,
                    chatId: '<EMAIL>',
                    msgId: '57157030-a856-11ed-9e3e-4bb940196bcf',
                    time: 1_675_932_430
                  }
                ]
              }
            }
          )

          expect(message.reload.read?).to be true
        end
      end
    end

    context 'when sent message by external' do
      let!(:qr_retailer) { create(:retailer, :qr_integrated, qr_phone_id: 26_931, qr_phone_number: '34633673284') }

      context 'when image, video, sticker, file, audio or document' do
        it 'saves the message' do
          expect do
            post(
              '/whatsapp/webhook',
              params: {
                product_id: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                phone_id: 26_931,
                message: {
                  type: 'image',
                  url: 'https://cdnydm.com/media/LmfAJTHCVJp0aVqyopwVAg.jpeg?size=402x400',
                  mime: 'image/jpeg',
                  filename: 'wa-media-true_584145223776@c.us_45542A5C03785491F6.jpeg',
                  caption: '2',
                  id: 'true_584145223776@c.us_45542A5C03785491F6',
                  _serialized: 'true_584145223776@c.us_45542A5C03785491F6',
                  fromMe: true,
                  statuses: [
                    {
                      status: 'delivered'
                    }
                  ]
                },
                user: {
                  id: '<EMAIL>',
                  name: '584145223776',
                  phone: '584145223776'
                },
                conversation: '<EMAIL>',
                conversation_name: 'John Doe',
                receiver: '34633673284',
                timestamp: 1_677_230_423,
                type: 'message',
                reply: 'https://api.maytapi.com/api/40bcdfa7-9560-4b21-8d6b-bedf8811ab75/26_931/sendMessage',
                productId: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                phoneId: 26_931,
                whatsapp: {
                  product_id: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                  phone_id: 26_931,
                  message: {
                    type: 'image',
                    url: 'https://cdnydm.com/media/LmfAJTHCVJp0aVqyopwVAg.jpeg?size=402x400',
                    mime: 'image/jpeg',
                    filename: 'wa-media-true_584145223776@c.us_45542A5C03785491F6.jpeg',
                    caption: '2',
                    id: 'true_584145223776@c.us_45542A5C03785491F6',
                    _serialized: 'true_584145223776@c.us_45542A5C03785491F6',
                    fromMe: true,
                    statuses: [
                      {
                        status: 'delivered'
                      }
                    ]
                  },
                  user: {
                    id: '<EMAIL>',
                    name: '584145223776',
                    phone: '584145223776'
                  },
                  conversation: '<EMAIL>',
                  conversation_name: 'John Doe',
                  receiver: '34633673284',
                  timestamp: 1_677_230_423,
                  type: 'message',
                  reply: 'https://api.maytapi.com/api/40bcdfa7-9560-4b21-8d6b-bedf8811ab75/26_931/sendMessage',
                  productId: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                  phoneId: 26_931
                }
              }
            )
          end.to change(GupshupWhatsappMessage, :count).by(1)
        end
      end

      context 'when location' do
        it 'saves the message' do
          expect do
            post(
              '/whatsapp/webhook',
              params: {
                product_id: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                phone_id: 26_931,
                message: {
                  type: 'location',
                  payload: '41.10956217404453,1.241214906496229',
                  id: 'true_584145223776@c.us_A92BB718A4E44976DFA99286612C2853',
                  _serialized: 'true_584145223776@c.us_A92BB718A4E44976DFA99286612C2853',
                  fromMe: true,
                  statuses: [
                    {
                      status: 'delivered'
                    }
                  ]
                },
                user: {
                  id: '<EMAIL>',
                  name: '584145223776',
                  phone: '584145223776'
                },
                conversation: '<EMAIL>',
                conversation_name: 'John Doe',
                receiver: '34633673284',
                timestamp: 1_677_237_659,
                type: 'message',
                reply: 'https://api.maytapi.com/api/40bcdfa7-9560-4b21-8d6b-bedf8811ab75/26_931/sendMessage',
                productId: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                phoneId: 26_931,
                whatsapp: {
                  product_id: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                  phone_id: 26_931,
                  message: {
                    type: 'location',
                    payload: '41.10956217404453,1.241214906496229',
                    id: 'true_584145223776@c.us_A92BB718A4E44976DFA99286612C2853',
                    _serialized: 'true_584145223776@c.us_A92BB718A4E44976DFA99286612C2853',
                    fromMe: true,
                    statuses: [
                      {
                        status: 'delivered'
                      }
                    ]
                  },
                  user: {
                    id: '<EMAIL>',
                    name: '584145223776',
                    phone: '584145223776'
                  },
                  conversation: '<EMAIL>',
                  conversation_name: 'John Doe',
                  receiver: '34633673284',
                  timestamp: 1_677_237_659,
                  type: 'message',
                  reply: 'https://api.maytapi.com/api/40bcdfa7-9560-4b21-8d6b-bedf8811ab75/26_931/sendMessage',
                  productId: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                  phoneId: 26_931
                }
              }
            )
          end.to change(GupshupWhatsappMessage, :count).by(1)
        end
      end

      context 'when text' do
        it 'saves the message' do
          expect do
            post(
              '/whatsapp/webhook',
              params: {
                product_id: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                phone_id: 26_931,
                message: {
                  type: 'text',
                  text: 'texto ejemplo',
                  id: 'true_584145223776@c.us_C10B2B88A5680B0579',
                  _serialized: 'true_584145223776@c.us_C10B2B88A5680B0579',
                  fromMe: true,
                  statuses: [
                    {
                      status: 'delivered'
                    }
                  ]
                },
                user: {
                  id: '<EMAIL>',
                  name: '584145223776',
                  phone: '584145223776'
                },
                conversation: '<EMAIL>',
                conversation_name: 'John Doe',
                receiver: '34633673284',
                timestamp: 1_677_246_341,
                type: 'message',
                reply: 'https://api.maytapi.com/api/40bcdfa7-9560-4b21-8d6b-bedf8811ab75/26_931/sendMessage',
                productId: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                phoneId: 26_931,
                whatsapp: {
                  product_id: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                  phone_id: 26_931,
                  message: {
                    type: 'text',
                    text: 'textoejemplo',
                    id: 'true_584145223776@c.us_C10B2B88A5680B0579',
                    _serialized: 'true_584145223776@c.us_C10B2B88A5680B0579',
                    fromMe: true,
                    statuses: [
                      {
                        status: 'delivered'
                      }
                    ]
                  },
                  user: {
                    id: '<EMAIL>',
                    name: '584145223776',
                    phone: '584145223776'
                  },
                  conversation: '<EMAIL>',
                  conversation_name: 'John Doe',
                  receiver: '34633673284',
                  timestamp: 1_677_246_341,
                  type: 'message',
                  reply: 'https://api.maytapi.com/api/40bcdfa7-9560-4b21-8d6b-bedf8811ab75/26_931/sendMessage',
                  productId: '40bcdfa7-9560-4b21-8d6b-bedf8811ab75',
                  phoneId: 26_931
                }
              }
            )
          end.to change(GupshupWhatsappMessage, :count).by(1)
        end
      end
    end
  end

  describe '#send_multiple_answers' do
    before do
      sign_out retailer_user.user
      sign_in retailer_user_gupshup.user
    end

    context 'when all goes well' do
      it 'successfully sends the fast answers' do
        allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_multiple_answers).and_return(true)
        post "/api/v1/send_multiple_whatsapp_answers/#{customer_gupshup.id}",
             params: {
               template: false,
               id: customer_gupshup.id
             }
        expect(response).to have_http_status(:ok)
        body = response.parsed_body
        expect(body['message']).to eq('Notificación enviada')
      end
    end

    context 'when something goes wrong' do
      it 'returns an error' do
        allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_multiple_answers).and_raise(StandardError)
        post "/api/v1/send_multiple_whatsapp_answers/#{customer_gupshup.id}",
             params: {
               template: false,
               id: customer_gupshup.id
             }
        body = response.parsed_body
        expect(response).to have_http_status(:bad_request)
        expect(body['message']).to eq('Faltaron parámetros')
      end
    end
  end
end
# rubocop:enable RSpec/ExampleLength
# rubocop:enable Layout/LineLength
