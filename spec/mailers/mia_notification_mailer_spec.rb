require 'rails_helper'

RSpec.describe MiaNotificationMailer do
  let(:retailer) { create(:retailer) }
  let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
  let(:mia_integration) { create(:mia_integration, retailer: retailer, mia_integration_type: mia_integration_type) }

  describe '#integration_confirm_email' do
    let(:mail) { described_class.integration_confirm_email(mia_integration) }

    it 'renders the headers' do
      expect(mail.subject).to eq('Nueva integración de IA')
      expect(mail.to).to eq([ENV.fetch('MIA_NOTIFICATION_EMAIL', nil)])
      expect(mail.from).to eq(['<EMAIL>'])
    end

    it 'assigns @mia_integration' do
      expect(mail.body.encoded).to include(retailer.name)
      expect(mail.body.encoded).to include("RetailerID: #{retailer.id}")
      expect(mail.body.encoded).to include("Service URL: #{mia_integration.service_url}")
    end
  end

  describe '#delete_integration_confirm_email' do
    let(:mail) { described_class.delete_integration_confirm_email(retailer, mia_integration.service_url) }

    before { mia_integration.destroy }

    it 'renders the headers' do
      expect(mail.subject).to eq('Integración de IA eliminada')
      expect(mail.to).to eq([ENV.fetch('MIA_NOTIFICATION_EMAIL', nil)])
      expect(mail.from).to eq(['<EMAIL>'])
    end

    it 'assigns @mia_integration' do
      expect(mail.body.encoded).to include(retailer.name)
      expect(mail.body.encoded).to include("RetailerID: #{retailer.id}")
      expect(mail.body.encoded).to include("Service URL: #{mia_integration.service_url}")
    end
  end
end
