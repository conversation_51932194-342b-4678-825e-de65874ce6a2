require 'rails_helper'

RSpec.describe Whatsapp::Inbound::Event do
  let(:retailer) { instance_double(<PERSON>tail<PERSON>) }

  describe 'when from is "gupshup"' do
    let(:from) { 'gupshup' }
    let(:service) { described_class.new(retailer, nil, from) }

    describe '#process_event!' do
      let(:params) { { 'type' => 'inbound', 'payload' => { 'id' => '1', 'status' => 'approved' } } }
      let(:converted_params) { { 'type' => 'inbound', 'payload' => { 'id' => '1', 'status' => 'approved' } } }
      let(:executor) { instance_double(Strategies::Whatsapp::Events::ApiExecutor) }

      before do
        allow(Strategies::Whatsapp::Events::ApiExecutor).to receive(:call).and_return(executor)
      end

      it 'calls the ApiExecutor with the correct parameters' do
        service.process_event!(params)
        expect(Strategies::Whatsapp::Events::ApiExecutor).to have_received(:call)
      end

      context 'when type is not inbound' do
        let(:params) { { 'type' => 'outbound', 'payload' => { 'id' => '1', 'status' => 'approved' } } }
        let(:converted_params) { { 'type' => 'outbound', 'payload' => { 'id' => '1', 'status' => 'approved' } } }

        it 'calls the ApiExecutor with qr_params as nil' do
          service.process_event!(params)
          expect(Strategies::Whatsapp::Events::ApiExecutor).to have_received(:call)
        end
      end

      context 'when params indicate outbound message' do
        let(:params) { { 'type' => 'message', 'direction' => 'outbound', 'payload' => { 'id' => '1' } } }

        it 'determines gsw_type as message-outbound' do
          allow(Rails.logger).to receive(:info)
          service.process_event!(params)
          expect(Strategies::Whatsapp::Events::ApiExecutor).to have_received(:call)
            .with('message-outbound', retailer: retailer, params: params, from: 'gupshup', qr_params: nil)
        end
      end

      context 'when logging params' do
        let(:params) { { 'type' => 'inbound', 'payload' => { 'id' => '1', 'status' => 'approved' } } }

        before do
          allow(Rails.logger).to receive(:info)
        end

        it 'logs the params and direction' do
          service.process_event!(params)

          expect(Rails.logger).to have_received(:info).with('*' * 100).twice
          expect(Rails.logger).to have_received(:info).with("PARAMS: #{params}")
          expect(Rails.logger).to have_received(:info).with('DIRECTION: inbound')
        end
      end

      context 'when type is inbound' do
        let(:params) { { 'type' => 'inbound', 'payload' => { 'id' => '1', 'status' => 'approved' } } }

        it 'passes params as qr_params' do
          allow(Rails.logger).to receive(:info)
          service.process_event!(params)
          expect(Strategies::Whatsapp::Events::ApiExecutor).to have_received(:call)
            .with('inbound', retailer: retailer, params: params, from: 'gupshup', qr_params: params)
        end
      end
    end
  end

  describe 'when from is not "gupshup"' do
    let(:from) { 'qr' }
    let(:service) { described_class.new(retailer, nil, from) }

    describe '#process_event!' do
      let(:params) { { 'type' => 'status', 'payload' => { 'id' => '1', 'status' => 'active' } } }
      let(:qr_executor) { instance_double(Strategies::Whatsapp::Events::QrExecutor) }

      before do
        allow(Strategies::Whatsapp::Events::QrExecutor).to receive(:call).and_return(qr_executor)
      end

      it 'calls the QrExecutor with the correct parameters' do
        service.process_event!(params)
        expect(Strategies::Whatsapp::Events::QrExecutor).to have_received(:call)
      end

      context 'when params indicate outbound message for QR' do
        let(:params) { { 'type' => 'message', 'direction' => 'outbound', 'payload' => { 'id' => '1' } } }

        it 'determines gsw_type as message-outbound for QR executor' do
          allow(Rails.logger).to receive(:info)
          service.process_event!(params)
          expect(Strategies::Whatsapp::Events::QrExecutor).to have_received(:call)
            .with('message-outbound', retailer: retailer, params: params, from: 'qr', qr_params: nil)
        end
      end

      context 'when logging params for QR' do
        let(:params) { { 'type' => 'status', 'payload' => { 'id' => '1', 'status' => 'active' } } }

        before do
          allow(Rails.logger).to receive(:info)
        end

        it 'logs the params and direction correctly' do
          service.process_event!(params)

          expect(Rails.logger).to have_received(:info).with('*' * 100).twice
          expect(Rails.logger).to have_received(:info).with("PARAMS: #{params}")
          expect(Rails.logger).to have_received(:info).with('DIRECTION: status')
        end
      end
    end
  end

  describe 'private methods' do
    let(:from) { 'gupshup' }
    let(:service) { described_class.new(retailer, nil, from) }

    describe '#determine_gsw_type' do
      context 'when params indicate outbound message' do
        let(:params) { { 'type' => 'message', 'direction' => 'outbound' } }

        it 'returns message-outbound' do
          result = service.send(:determine_gsw_type, params)
          expect(result).to eq('message-outbound')
        end
      end

      context 'when params do not indicate outbound message' do
        let(:params) { { 'type' => 'inbound', 'direction' => 'inbound' } }

        it 'returns the type from params' do
          result = service.send(:determine_gsw_type, params)
          expect(result).to eq('inbound')
        end
      end
    end

    describe '#outbound_message?' do
      context 'when type is message and direction is outbound' do
        let(:params) { { 'type' => 'message', 'direction' => 'outbound' } }

        it 'returns true' do
          result = service.send(:outbound_message?, params)
          expect(result).to be true
        end
      end

      context 'when type is not message' do
        let(:params) { { 'type' => 'status', 'direction' => 'outbound' } }

        it 'returns false' do
          result = service.send(:outbound_message?, params)
          expect(result).to be false
        end
      end

      context 'when direction is not outbound' do
        let(:params) { { 'type' => 'message', 'direction' => 'inbound' } }

        it 'returns false' do
          result = service.send(:outbound_message?, params)
          expect(result).to be false
        end
      end

      context 'when type is message but direction is nil' do
        let(:params) { { 'type' => 'message', 'direction' => nil } }

        it 'returns false' do
          result = service.send(:outbound_message?, params)
          expect(result).to be false
        end
      end
    end

    describe '#log_params' do
      before do
        allow(Rails.logger).to receive(:info)
      end

      it 'logs the asterisks, params and direction' do
        params = { 'type' => 'test', 'data' => 'value' }
        gsw_type = 'test-type'

        service.send(:log_params, params, gsw_type)

        expect(Rails.logger).to have_received(:info).with('*' * 100).twice
        expect(Rails.logger).to have_received(:info).with("PARAMS: #{params}")
        expect(Rails.logger).to have_received(:info).with("DIRECTION: #{gsw_type}")
      end
    end

    describe '#select_executor' do
      context 'when from is gupshup' do
        let(:service) { described_class.new(retailer, nil, 'gupshup') }

        it 'returns ApiExecutor' do
          result = service.send(:select_executor)
          expect(result).to eq(Strategies::Whatsapp::Events::ApiExecutor)
        end
      end

      context 'when from is not gupshup' do
        let(:service) { described_class.new(retailer, nil, 'qr') }

        it 'returns QrExecutor' do
          result = service.send(:select_executor)
          expect(result).to eq(Strategies::Whatsapp::Events::QrExecutor)
        end
      end
    end
  end
end
