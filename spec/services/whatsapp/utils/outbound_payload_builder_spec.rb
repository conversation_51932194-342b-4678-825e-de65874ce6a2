require 'rails_helper'

RSpec.describe Whatsapp::Utils::OutboundPayloadBuilder, type: :service do
  let(:retailer) { instance_double(Retailer, id: 123) }
  let(:customer) { instance_double(Customer, id: 456) }
  let(:timestamp) { 1_675_096_577_191 }
  let(:base_params) do
    {
      'timestamp' => timestamp,
      'payload' => {
        'id' => 'msg_123',
        'source' => '1234567890',
        'destination' => '0987654321',
        'type' => 'text',
        'payload' => {
          'text' => 'Hello World'
        }
      }
    }.with_indifferent_access
  end

  describe '#initialize' do
    it 'sets all attributes correctly with default from' do
      builder = described_class.new(retailer: retailer, customer: customer, params: base_params)

      expect(builder.retailer).to eq(retailer)
      expect(builder.customer).to eq(customer)
      expect(builder.params).to eq(base_params)
      expect(builder.from).to eq('gupshup')
    end

    it 'sets from parameter when provided' do
      builder = described_class.new(retailer: retailer, customer: customer, params: base_params, from: 'qr')

      expect(builder.from).to eq('qr')
    end
  end

  describe '#call' do
    let(:mock_time) { Time.zone.parse('2023-01-30 12:00:00') }

    before do
      allow(Whatsapp::Utils::CreationTimeGetter).to receive(:call).and_return(mock_time.to_i)
      allow(Time.zone).to receive(:now).and_return(mock_time)
    end

    context 'with text message' do
      let(:params) do
        {
          'timestamp' => timestamp,
          'payload' => {
            'id' => 'msg_123',
            'source' => '1234567890',
            'destination' => '0987654321',
            'type' => 'text',
            'payload' => {
              'text' => 'Hello World'
            }
          }
        }.with_indifferent_access
      end

      it 'builds basic payload structure for text message' do
        builder = described_class.new(retailer: retailer, customer: customer, params: params)
        result = builder.call

        expect(result[:retailer]).to eq(retailer)
        expect(result[:customer]).to eq(customer)
        expect(result[:whatsapp_message_id]).to eq('msg_123')
        expect(result[:status]).to eq('sent')
        expect(result[:direction]).to eq('outbound')
      end

      it 'builds message payload and timestamps correctly' do
        builder = described_class.new(retailer: retailer, customer: customer, params: params)
        result = builder.call

        expect(result[:message_payload]).to eq({
          type: 'text',
          text: 'Hello World'
        })
        expect(result[:delivered_at]).to eq(mock_time.to_i)
        expect(result[:sent_at]).to eq(timestamp)
        expect(result[:created_at]).to eq(mock_time.to_i)
      end

      it 'calls CreationTimeGetter with correct parameters' do
        builder = described_class.new(retailer: retailer, customer: customer, params: params, from: 'qr')
        builder.call

        expect(Whatsapp::Utils::CreationTimeGetter).to have_received(:call).with(params: params, from: 'qr')
      end
    end

    # Agrego todas las demás pruebas con with_indifferent_access
    context 'with media messages' do
      %w[image video audio file sticker].each do |media_type|
        context "with #{media_type} message" do
          let(:params) do
            {
              'timestamp' => timestamp,
              'payload' => {
                'id' => "#{media_type}_123",
                'type' => media_type,
                'payload' => {
                  'caption' => "#{media_type.capitalize} caption",
                  'url' => "https://example.com/#{media_type}.ext",
                  'filename' => "#{media_type}.ext",
                  'contentType' => "#{media_type}/ext"
                }
              }
            }.with_indifferent_access
          end

          it "builds basic payload for #{media_type} message" do
            builder = described_class.new(retailer: retailer, customer: customer, params: params)
            result = builder.call

            expect(result[:message_payload][:type]).to eq(media_type)
            expect(result[:message_payload][:url]).to eq("https://example.com/#{media_type}.ext")
            expect(result[:message_payload][:mime_type]).to eq("#{media_type}/ext")
          end

          it "includes correct metadata for #{media_type} message" do
            builder = described_class.new(retailer: retailer, customer: customer, params: params)
            result = builder.call

            if media_type != 'audio'
              expect(result[:message_payload][:caption]).to eq("#{media_type.capitalize} caption")
            end
            if %w[image file].include?(media_type)
              expect(result[:message_payload][:filename]).to eq("#{media_type}.ext")
            end
          end
        end
      end
    end

    context 'with location message' do
      let(:params) do
        {
          'timestamp' => timestamp,
          'payload' => {
            'id' => 'loc_123',
            'type' => 'location',
            'payload' => {
              'latitude' => 40.7128,
              'longitude' => -74.0060,
              'name' => 'New York City',
              'address' => 'NYC, NY, USA'
            }
          }
        }.with_indifferent_access
      end

      it 'builds payload for location message' do
        builder = described_class.new(retailer: retailer, customer: customer, params: params)
        result = builder.call

        expect(result[:message_payload]).to eq({
          type: 'location',
          latitude: 40.7128,
          longitude: -74.0060,
          name: 'New York City',
          address: 'NYC, NY, USA'
        })
      end
    end

    context 'with contact message' do
      let(:params) do
        {
          'timestamp' => timestamp,
          'payload' => {
            'id' => 'contact_123',
            'type' => 'contact',
            'payload' => {
              'contacts' => [
                {
                  'name' => { 'formatted_name' => 'John Doe' },
                  'phones' => [{ 'phone' => '+1234567890' }]
                }
              ]
            }
          }
        }.with_indifferent_access
      end

      it 'builds payload for contact message' do
        builder = described_class.new(retailer: retailer, customer: customer, params: params)
        result = builder.call

        expect(result[:message_payload][:type]).to eq('contact')
        expect(result[:message_payload][:payload][:payload][:contacts]).to be_an(Array)
      end

      it 'includes correct contact details' do
        builder = described_class.new(retailer: retailer, customer: customer, params: params)
        result = builder.call

        contact = result[:message_payload][:payload][:payload][:contacts].first
        expect(contact['name']['formatted_name']).to eq('John Doe')
        expect(contact['phones'].first['phone']).to eq('+1234567890')
      end
    end

    context 'with unknown message type' do
      let(:params) do
        {
          'timestamp' => timestamp,
          'payload' => {
            'id' => 'unknown_123',
            'type' => 'unknown_type',
            'payload' => {
              'custom_field' => 'custom_value'
            }
          }
        }.with_indifferent_access
      end

      it 'builds payload with default body extraction' do
        builder = described_class.new(retailer: retailer, customer: customer, params: params)
        result = builder.call

        expect(result[:message_payload]).to eq({
          'custom_field' => 'custom_value'
        })
      end
    end

    context 'with missing payload data' do
      let(:params) do
        {
          'timestamp' => timestamp,
          'payload' => {
            'id' => 'minimal_123',
            'type' => 'text'
          }
        }.with_indifferent_access
      end

      it 'handles missing nested payload gracefully' do
        builder = described_class.new(retailer: retailer, customer: customer, params: params)
        result = builder.call

        expect(result[:message_payload]).to eq({
          type: 'text'
        })
      end
    end

    context 'when compact removes nil values' do
      let(:params) do
        {
          'timestamp' => timestamp,
          'payload' => {
            'id' => 'compact_123',
            'type' => 'text',
            'payload' => {
              'text' => 'Hello'
            }
          }
        }.with_indifferent_access
      end

      it 'removes nil values from payload' do
        builder = described_class.new(retailer: retailer, customer: customer, params: params)
        result = builder.call

        # Verify that source and destination are not present when nil
        expect(result).not_to have_key(:source)
        expect(result).not_to have_key(:destination)

        # But other required fields are present
        expect(result[:whatsapp_message_id]).to eq('compact_123')
        expect(result[:status]).to eq('sent')
      end
    end

    context 'with partial data scenarios' do
      context 'when media data is partial' do
        let(:params) do
          {
            'timestamp' => timestamp,
            'payload' => {
              'id' => 'partial_media',
              'type' => 'image',
              'payload' => {
                'url' => 'https://example.com/image.jpg'
              }
            }
          }.with_indifferent_access
        end

        it 'handles partial media data correctly' do
          builder = described_class.new(retailer: retailer, customer: customer, params: params)
          result = builder.call

          expect(result[:message_payload]).to eq({
            type: 'image',
            url: 'https://example.com/image.jpg'
          })
        end
      end

      context 'when location data is partial' do
        let(:params) do
          {
            'timestamp' => timestamp,
            'payload' => {
              'id' => 'partial_location',
              'type' => 'location',
              'payload' => {
                'latitude' => 40.7128,
                'longitude' => -74.0060
              }
            }
          }.with_indifferent_access
        end

        it 'handles partial location data correctly' do
          builder = described_class.new(retailer: retailer, customer: customer, params: params)
          result = builder.call

          expect(result[:message_payload]).to eq({
            type: 'location',
            latitude: 40.7128,
            longitude: -74.0060
          })
        end
      end

      context 'when contacts array is empty' do
        let(:params) do
          {
            'timestamp' => timestamp,
            'payload' => {
              'id' => 'empty_contacts',
              'type' => 'contact',
              'payload' => {}
            }
          }.with_indifferent_access
        end

        it 'handles empty contacts array' do
          builder = described_class.new(retailer: retailer, customer: customer, params: params)
          result = builder.call

          expect(result[:message_payload]).to eq({
            type: 'contact',
            payload: {
              payload: {
                contacts: []
              }
            }
          })
        end
      end
    end
  end

  describe 'private methods' do
    let(:builder) { described_class.new(retailer: retailer, customer: customer, params: base_params) }

    describe '#extract_message_id' do
      it 'extracts message id from payload' do
        result = builder.send(:extract_message_id)
        expect(result).to eq('msg_123')
      end

      context 'when message id is missing' do
        let(:params_without_id) do
          {
            'payload' => {
              'source' => '1234567890'
            }
          }.with_indifferent_access
        end

        it 'returns nil when id is missing' do
          builder_without_id = described_class.new(retailer: retailer, customer: customer, params: params_without_id)
          result = builder_without_id.send(:extract_message_id)
          expect(result).to be_nil
        end
      end
    end

    describe '#extract_status' do
      it 'always returns sent' do
        result = builder.send(:extract_status)
        expect(result).to eq('sent')
      end
    end

    describe '#extract_message_body' do
      context 'with different message types' do
        %w[text image video audio file sticker location contact unknown].each do |message_type|
          it "handles #{message_type} message type" do
            params = { 'payload' => { 'type' => message_type } }.with_indifferent_access
            test_builder = described_class.new(retailer: retailer, customer: customer, params: params)

            expect { test_builder.send(:extract_message_body) }.not_to raise_error
          end
        end
      end
    end

    describe '#extract_text_body' do
      let(:text_params) do
        {
          'payload' => {
            'type' => 'text',
            'payload' => {
              'text' => 'Hello World'
            }
          }
        }.with_indifferent_access
      end

      it 'extracts text body correctly' do
        text_builder = described_class.new(retailer: retailer, customer: customer, params: text_params)
        result = text_builder.send(:extract_text_body)

        expect(result).to eq({
          type: 'text',
          text: 'Hello World'
        })
      end
    end

    describe '#extract_media_body' do
      let(:media_params) do
        {
          'payload' => {
            'type' => 'video',
            'payload' => {
              'caption' => 'Test caption',
              'url' => 'https://example.com/video.mp4',
              'filename' => 'video.mp4',
              'contentType' => 'video/mp4'
            }
          }
        }.with_indifferent_access
      end

      it 'extracts media body correctly' do
        media_builder = described_class.new(retailer: retailer, customer: customer, params: media_params)
        result = media_builder.send(:extract_media_body)

        expect(result).to eq({
          type: 'video',
          caption: 'Test caption',
          url: 'https://example.com/video.mp4',
          filename: 'video.mp4',
          mime_type: 'video/mp4'
        })
      end
    end

    describe '#extract_location_body' do
      let(:location_params) do
        {
          'payload' => {
            'type' => 'location',
            'payload' => {
              'latitude' => 40.7128,
              'longitude' => -74.0060,
              'name' => 'New York',
              'address' => 'NYC, NY'
            }
          }
        }.with_indifferent_access
      end

      it 'extracts location body correctly' do
        location_builder = described_class.new(retailer: retailer, customer: customer, params: location_params)
        result = location_builder.send(:extract_location_body)

        expect(result).to eq({
          type: 'location',
          latitude: 40.7128,
          longitude: -74.0060,
          name: 'New York',
          address: 'NYC, NY'
        })
      end
    end

    describe '#extract_contact_body' do
      let(:contact_params) do
        {
          'payload' => {
            'type' => 'contact',
            'payload' => {
              'contacts' => [{ 'name' => 'John Doe' }]
            }
          }
        }.with_indifferent_access
      end

      it 'extracts contact body correctly' do
        contact_builder = described_class.new(retailer: retailer, customer: customer, params: contact_params)
        result = contact_builder.send(:extract_contact_body)

        expect(result).to eq({
          type: 'contact',
          payload: {
            payload: {
              contacts: [{ 'name' => 'John Doe' }]
            }
          }
        })
      end
    end

    describe '#extract_default_body' do
      let(:default_params) do
        {
          'payload' => {
            'payload' => {
              'custom_field' => 'custom_value'
            }
          }
        }.with_indifferent_access
      end

      it 'extracts default body correctly' do
        default_builder = described_class.new(retailer: retailer, customer: customer, params: default_params)
        result = default_builder.send(:extract_default_body)

        expect(result).to eq({
          'custom_field' => 'custom_value'
        })
      end

      context 'when payload is missing' do
        let(:empty_params) { { 'payload' => {} }.with_indifferent_access }

        it 'returns empty hash when payload is missing' do
          empty_builder = described_class.new(retailer: retailer, customer: customer, params: empty_params)
          result = empty_builder.send(:extract_default_body)

          expect(result).to eq({})
        end
      end
    end
  end
end
