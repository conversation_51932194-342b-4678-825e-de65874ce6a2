require 'rails_helper'

RSpec.describe Whatsapp::Outbound::Msg do
  let(:retailer) { create(:retailer, :gupshup_integrated) }
  let(:customer) { create(:customer, retailer: retailer) }
  let(:retailer_user) { create(:retailer_user, retailer: retailer) }
  let(:net_response) { Net::HTTPOK.new(1.0, '200', 'OK') }

  let(:audio_params) do
    {
      file_data: fixture_file_upload(Rails.root.join('spec', 'fixtures', 'files', 'notification_tune.mp3')),
      type: 'audio',
      message_identifier: 'abc-123'
    }
  end

  let(:audio_files_params) do
    {
      file_data: [fixture_file_upload(Rails.root.join('spec', 'fixtures', 'files', 'notification_tune.mp3'))],
      type: 'audio',
      message_identifiers: ['abc-123']
    }
  end

  let(:audio_url_params) do
    {
      url: 'https://res.cloudinary.com/dhhrdm74a/video/upload/v1600269975/mubnkttmuxmbexcqqucz.mp4',
      type: 'audio'
    }
  end

  let(:pdf_url_params) do
    {
      url: 'https://res.cloudinary.com/dhhrdm74a/image/upload/v1600786470/7dgvVqKauCLTLQtWvWy1yEDb.pdf',
      type: 'file',
      content_type: 'application/pdf',
      file_name: 'Test Spec'
    }
  end

  let(:image_url_params_with_content_type) do
    {
      url: 'https://res.cloudinary.com/dhhrdm74a/image/upload/v1600715681/cEp879PjfNVgMaZ86vN9vAxD.png',
      type: 'file',
      content_type: 'image/png',
      message_identifiers: ['1dc-123']
    }
  end

  let(:image_urls_params_with_content_type) do
    {
      images_urls: ['https://res.cloudinary.com/dhhrdm74a/image/upload/v1600715681/cEp879PjfNVgMaZ86vN9vAxD.png'],
      type: 'file',
      content_type: 'image/png',
      message_identifiers: ['1dc-123']
    }
  end

  let(:image_url_params) do
    {
      url: 'https://res.cloudinary.com/dhhrdm74a/image/upload/v1600715681/cEp879PjfNVgMaZ86vN9vAxD.png',
      type: 'file'
    }
  end

  let(:cloudinary_audio_response) do
    {
      asset_id: 'c7fa2c0720915a5f998d7533ccb1b8d7',
      public_id: 'mubnkttmuxmbexcqqucz',
      version: 1_600_269_975,
      version_id: 'bc1914c017e198664eb695fbc5fc9eb4',
      signature: '7e017bb966eb6f9d9d65a68cc635d8c82bbe910a',
      width: 0,
      height: 0,
      format: 'webm',
      resource_type: 'video',
      created_at: '2020-09-16T15:26:15Z',
      tags: [],
      pages: 0,
      bytes: 31_121,
      type: 'upload',
      etag: '9af3be16f4811fa2eebb1f202de5dec8',
      placeholder: false,
      url: 'http://res.cloudinary.com/dhhrdm74a/video/upload/v1600269975/mubnkttmuxmbexcqqucz.webm',
      secure_url: 'https://res.cloudinary.com/dhhrdm74a/video/upload/v1600269975/mubnkttmuxmbexcqqucz.webm',
      audio: {
        codec: 'opus',
        frequency: 48_000,
        channels: 1,
        channel_layout: 'mono'
      },
      video: {},
      is_audio: true,
      duration: 5.04,
      original_filename: 'blob'
    }.with_indifferent_access
  end

  let(:aws_s3_audio_response) do
    {
      type: 'upload',
      url: 'https://mercately-bucket-dev.s3.amazonaws.com/mubnkttmuxmbexcqqucz',
      secure_url: 'https://mercately-bucket-dev.s3.amazonaws.com/mubnkttmuxmbexcqqucz',
      original_filename: 'blob'
    }.with_indifferent_access
  end

  let(:ok_body_response) do
    {
      status: 'submitted',
      messageId: 'c011a9c0-051d-4e01-a130-7b12903decb8'
    }.to_json
  end

  let(:template_text_params) do
    {
      template: true,
      message: 'Your Test number 12345 has been updated.',
      customer_id: customer.id,
      type: 'text'
    }
  end

  let(:text_msg_params) do
    {
      template: false,
      message: 'Test to send text message',
      customer_id: customer.id,
      type: 'text'
    }
  end

  let(:template_id_params) do
    {
      template: true,
      message: 'Your OTP for Test is abc. This is valid for Today.',
      customer_id: customer.id,
      type: 'text',
      template_params: %w[Test abc Today],
      gupshup_template_id: '997dd550-c8d8-4bf7-ad98-a5ac4844a1ed'
    }
  end

  let!(:wst) do
    create(:whatsapp_template, retailer: retailer, status: :accepted, name: 'Accepted',
                               gupshup_template_id: '997dd550-c8d8-4bf7-ad98-a5ac4844a1e5')
  end

  let(:template_id_message) do
    {
      template: true,
      message: 'Your OTP for Test is abc. This is valid for Today.',
      customer_id: customer.id,
      type: 'text',
      template_params: %w[Test abc Today],
      gupshup_template_id: '997dd550-c8d8-4bf7-ad98-a5ac4844a1e5'
    }
  end

  let(:note_params) do
    {
      message: 'Test note',
      customer_id: 2056,
      note: true,
      type: 'text',
      message_identifier: '1a0f5ff8-3643-465e-a6ff-e5acc45694a5'
    }
  end

  let(:maytapi_body_response) do
    request_object = HTTParty::Request.new Net::HTTP::Get, '/'
    response_object = Net::HTTPOK.new('1.1', 200, 'OK')
    parsed_response = -> { { 'success' => true, 'data' => { 'chatId' => '<EMAIL>', 'msgId' => '1dc-123' } } }

    HTTParty::Response.new(request_object, response_object, parsed_response)
  end

  let(:msg_instance) { described_class.new(retailer, customer) }
  let(:response) do
    instance_double(HTTParty::Response,
                    success?: true,
                    parsed_response: { 'success' => true },
                    code: 200)
  end
  let(:mia_params) { { message: 'Test message' } }

  describe 'class methods' do
    it { is_expected.to respond_to(:send_message) }
    it { is_expected.to respond_to(:send_gupshup_message) }
    it { is_expected.to respond_to(:create_note) }
    it { is_expected.to respond_to(:send_bulk_files) }
    it { is_expected.to respond_to(:send_multiple_answers) }
    it { is_expected.not_to respond_to(:update_params) }
    it { is_expected.not_to respond_to(:base_body) }
    it { is_expected.not_to respond_to(:list) }
    it { is_expected.not_to respond_to(:quick_reply) }
    it { is_expected.not_to respond_to(:template) }
    it { is_expected.not_to respond_to(:text) }
    it { is_expected.not_to respond_to(:image) }
    it { is_expected.not_to respond_to(:document) }
    it { is_expected.not_to respond_to(:media_template) }
    it { is_expected.not_to respond_to(:audio) }
    it { is_expected.not_to respond_to(:video) }
    it { is_expected.not_to respond_to(:location) }
    it { is_expected.not_to respond_to(:save_message) }
    it { is_expected.not_to respond_to(:send_message_request) }
    it { is_expected.not_to respond_to(:file) }
    it { is_expected.not_to respond_to(:get_resource_type) }
    it { is_expected.not_to respond_to(:get_resource_from_content_type) }
    it { is_expected.not_to respond_to(:check_type_on_url) }
    it { is_expected.not_to respond_to(:complete_template_params?) }
  end

  describe 'inheritance' do
    it { expect(described_class).to be < Whatsapp::Outbound::Base }
    it { is_expected.to respond_to(:get) }
    it { is_expected.to respond_to(:post) }
    it { is_expected.to respond_to(:post_form) }
  end

  describe '#send_message' do
    context 'when the message type is file' do
      context 'when the file is a PDF' do
        context 'when the PDF is sent as an URL' do
          context 'when the content_type is passed in parameters' do
            before do
              allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
              allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
            end

            it 'prepares the message body to send it to GupShup' do
              expect do
                response = described_class.new(retailer, customer).send_message(type: 'file', params:
                  pdf_url_params, retailer_user: retailer_user)

                gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
                expect(gs_message.message_payload['url']).to eq(pdf_url_params[:url])
              end.to change(GupshupWhatsappMessage, :count).by(1)
            end
          end
        end
      end

      context 'when the file is an image' do
        context 'when the image is sent as an URL' do
          context 'when the content_type is passed in parameters' do
            before do
              allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
              allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
            end

            it 'prepares the message body to send it to GupShup' do
              expect do
                response = described_class.new(retailer, customer).send_message(type: 'file', params:
                  image_url_params_with_content_type, retailer_user: retailer_user)

                gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
                expect(gs_message.message_payload['originalUrl']).to eq(image_url_params_with_content_type[:url])
              end.to change(GupshupWhatsappMessage, :count).by(1)
            end
          end

          context 'when the content_type is not passed in parameters' do
            before do
              allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
              allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
            end

            it 'prepares the message body to send it to GupShup' do
              expect do
                response = described_class.new(retailer, customer).send_message(type: 'file', params:
                  image_url_params, retailer_user: retailer_user)

                gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
                expect(gs_message.message_payload['originalUrl']).to eq(image_url_params[:url])
              end.to change(GupshupWhatsappMessage, :count).by(1)
            end
          end
        end
      end

      context 'when the file is an audio' do
        context 'when the audio is sent as a file object' do
          before do
            allow_any_instance_of(Uploaders::S3Aws).to receive(:upload_file).and_return(aws_s3_audio_response)
            allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
            allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
          end

          it 'uploads the audio to AWS S3 and prepares the message body to send it to GupShup' do
            expect do
              response = described_class.new(retailer, customer).send_message(type: 'file', params:
                audio_params, retailer_user: retailer_user)

              gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
              expect(gs_message.message_payload['url'].gsub('.aac', '')).to eq(aws_s3_audio_response['secure_url'])
            end.to change(GupshupWhatsappMessage, :count).by(1)
          end
        end

        context 'when the audio is sent as an URL' do
          before do
            allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
            allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
          end

          it 'prepares the message body to send it to GupShup' do
            expect do
              response = described_class.new(retailer, customer).send_message(type: 'file', params:
                audio_url_params, retailer_user: retailer_user)

              gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
              expect(gs_message.message_payload['url']).to eq(audio_url_params[:url].gsub('.mp4', '.aac'))
            end.to change(GupshupWhatsappMessage, :count).by(1)
          end
        end
      end
    end

    context 'when the message type is text' do
      context 'when it is a template' do
        context 'when it is sent through template text' do
          before do
            allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
            allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
          end

          it 'sends the text of the template' do
            expect do
              response = described_class.new(retailer, customer).send_message(type: 'template', params:
                template_text_params, retailer_user: retailer_user)

              gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
              expect(gs_message.message_payload['isHSM']).to eq('true')
              expect(gs_message.message_payload['type']).to eq('text')
              expect(gs_message.message_payload['text']).to eq('Your Test number 12345 has been updated.')
            end.to change(GupshupWhatsappMessage, :count).by(1)
          end
        end

        context 'when it is sent through template ID' do
          before do
            allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
            allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
          end

          it 'sends the ID of the template and the parameters' do
            expect do
              response = described_class.new(retailer, customer).send_message(type: 'template', params:
                template_id_params, retailer_user: retailer_user)

              gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
              expect(gs_message.message_payload['isHSM']).to eq('true')
              expect(gs_message.message_payload['type']).to eq('text')
              expect(gs_message.message_payload['text']).to eq('Your OTP for Test is abc. This is valid for Today.')
              expect(gs_message.message_payload['id']).to eq('997dd550-c8d8-4bf7-ad98-a5ac4844a1ed')
              expect(gs_message.message_payload['params']).to eq(%w[Test abc Today])
            end.to change(GupshupWhatsappMessage, :count).by(1)
          end

          it 'save the ID Template on GupshupWhatsappMessage' do
            expect do
              response = described_class.new(retailer, customer).send_message(type: 'template', params:
                template_id_message, retailer_user: retailer_user)

              gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
              expect(gs_message.template_id).to eq(wst.id)
            end.to change(GupshupWhatsappMessage, :count).by(1)
          end
        end
      end

      describe 'when it is a reply message' do
        let(:gwm) do
          create(:gupshup_whatsapp_message, :outbound, :sent, retailer: retailer,
                                                              customer: customer,
                                                              whatsapp_message_id: 'texttest3298')
        end

        let(:reply_text_params) do
          {
            message: 'Your reply message example.',
            customer_id: customer.id,
            type: 'text',
            reply_to: gwm.id
          }
        end

        let(:reply_body_response) do
          {
            status: 'submitted',
            messageId: 'c011a9c0-051d-4e01-a130-7b12903decb8',
            context: {
              msgId: gwm.id
            }
          }.to_json
        end

        before do
          allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
          allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(reply_body_response)
        end

        it 'returns msgId in context' do
          expect do
            response = described_class.new(retailer, customer).send_message(type: 'text', params:
              reply_text_params, retailer_user: retailer_user)

            gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
            expect(gs_message.message_payload['type']).to eq('text')
            expect(gs_message.message_payload['text']).to eq('Your reply message example.')
            expect(gs_message.message_payload['context']['msgId']).to eq(gwm.whatsapp_message_id)
          end.to change(GupshupWhatsappMessage, :count).by(1)
        end
      end

      context 'when it is not a template' do
        context 'when exist a business rule' do
          let!(:business_rule) do
            create(:business_rule, name: 'Incluir el nombre del agente al enviar mensaje',
                                   description: 'Incluye el nombre del agente al enviar',
                                   identifier: 'attach_agent_name_in_messages')
          end

          context 'when retailer is rule matching' do
            let!(:retailer_business_rule) do
              create(:retailer_business_rule, retailer: retailer, business_rule: business_rule)
            end

            before do
              allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
              allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
            end

            it 'attach agent name to text body message' do
              expect do
                response = described_class.new(retailer, customer).send_message(
                  type: 'text',
                  params: text_msg_params,
                  retailer_user: retailer_user
                )

                gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
                expect(gs_message.message_payload['text']).to match(/#{Regexp.escape(retailer_user.full_name)}/)
              end.to change(GupshupWhatsappMessage, :count).by(1)
            end
          end

          context 'when retailer is not matching rule' do
            before do
              allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
              allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
            end

            it 'attach agent name to text body message' do
              expect do
                response = described_class.new(retailer, customer).send_message(
                  type: 'text',
                  params: text_msg_params,
                  retailer_user: retailer_user
                )

                gs_message = GupshupWhatsappMessage.find_by(gupshup_message_id: response[:body]['messageId'])
                expect(gs_message.message_payload['text']).to match(/Test to send text message/)
              end.to change(GupshupWhatsappMessage, :count).by(1)
            end
          end
        end
      end
    end

    context 'when integration is qr' do
      subject(:base) { described_class.new(retailer, customer, retailer_user, 'maytapi') }

      let(:retailer) { create(:retailer, :qr_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }
      let(:retailer_user) { create(:retailer_user, retailer: retailer) }

      context 'when de message is an audio' do
        it 'uploads the audio to Cloudinary and prepares the message body to send it to maytapi' do
          allow_any_instance_of(Uploaders::S3Aws).to receive(:upload_file).and_return(aws_s3_audio_response)
          allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
          allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
          allow_any_instance_of(described_class).to receive(:send_message_to_maytapi).and_return(maytapi_body_response)

          expect do
            base.send_message(type: 'file', params: audio_params, retailer_user: retailer_user)
          end.to change(GupshupWhatsappMessage, :count).by(1)

          gs_message = GupshupWhatsappMessage.find_by(message_identifier: 'abc-123')
          expect(gs_message.message_payload['url']).to eq(aws_s3_audio_response['secure_url'])
        end
      end
    end
  end

  describe '#create_note' do
    context 'when is a gupshup note' do
      subject(:gupshup_note_subject) { described_class.new(retailer, customer, retailer_user, 'gupshup') }

      it 'saves a new GupshupWhatsappMessage as note' do
        expect do
          gupshup_note_subject.create_note(params: note_params, retailer_user: retailer_user, order_note: false)
        end.to change { GupshupWhatsappMessage.where(note: true).count }.by(1)
      end
    end

    context 'when sent_by_mia is present' do
      subject(:gupshup_note_subject) do
        described_class.new(retailer, customer, retailer_user, 'gupshup', sent_by_mia: true)
      end

      it 'saves a new GupshupWhatsappMessage as note' do
        note = nil
        expect do
          note = gupshup_note_subject.create_note(params: note_params, retailer_user: retailer_user, order_note: false,
                                                  sent_by_mia: true)
        end.to change { GupshupWhatsappMessage.where(note: true).count }.by(1)
        expect(note.sent_by_mia).to be(true)
      end
    end

    context 'when is a QR note' do
      subject(:qr_note_subject) { described_class.new(retailer, customer, retailer_user, 'qr') }

      it 'saves a new GupshupWhatsappMessage as note' do
        expect do
          qr_note_subject.create_note(params: note_params, retailer_user: retailer_user, order_note: false)
        end.to change { GupshupWhatsappMessage.where(note: true).count }.by(1)
      end
    end

    context 'when is a note with @integration nil' do
      subject(:note_subject) { described_class.new(retailer, customer, retailer_user) }

      it 'saves a new GupshupWhatsappMessage as note' do
        expect do
          note_subject.create_note(params: note_params, retailer_user: retailer_user, order_note: false)
        end.to change { GupshupWhatsappMessage.where(note: true).count }.by(1)
      end
    end

    context 'without retailer_user' do
      subject(:gupshup_note_subject) { described_class.new(retailer, customer, retailer_user, 'gupshup') }

      let(:create_note) { gupshup_note_subject.create_note(params: note_params, retailer_user: nil, order_note: false) }

      it { expect { create_note }.to change(GupshupWhatsappMessage, :count).by(1) }
    end
  end

  describe '#create_mia_internal_message' do
    let(:expected_message) do
      instance_double(GupshupWhatsappMessage,
                      id: 123,
                      customer_id: customer.id,
                      retailer: retailer,
                      customer: customer,
                      direction: 'outbound',
                      message_payload: { type: :text },
                      read?: false,
                      status: 'sent',
                      type: 'text',
                      created_at: Time.current,
                      updated_at: Time.current,
                      whatsapp_message_id: 'test-id',
                      channel: 'whatsapp',
                      source: retailer.public_phone_number,
                      destination: customer.phone_number(false),
                      retailer_user_id: retailer_user.id,
                      retailer_user: retailer_user,

                      sender_first_name: 'Test',
                      sender_last_name: 'User',
                      sender_email: '<EMAIL>',
                      note: nil,
                      order_note: nil,
                      order_summary: nil,
                      internal_url: nil,
                      reaction: nil,
                      sent_by_mia: true,
                      mia_flag: :human_help,
                      has_referral_media?: false,
                      has_referral?: false,
                      from_group: false,
                      gupshup_message_id: 'test-message-id',
                      message_identifier: 'test-message-id',
                      error_payload: nil)
    end
    let(:mia_flag) { :human_help }
    let(:expected_attributes) do
      {
        customer: customer,
        direction: 'outbound',
        source: retailer.public_phone_number,
        destination: customer.phone_number(false),
        channel: 'whatsapp',
        status: 'sent',
        retailer_user: retailer_user,
        message_payload: { type: :text },
        mia_flag: mia_flag,
        sent_by_mia: true,
        message_identifier: anything
      }
    end

    it 'creates a new internal message with mia flag' do
      expect { msg_instance.create_mia_internal_message(mia_flag) }
        .to change(GupshupWhatsappMessage, :count).by(1)

      verify_internal_message_attributes
    end

    def verify_internal_message_attributes
      message = GupshupWhatsappMessage.last
      expect(message.mia_flag).to eq('human_help')
      expect(message.sent_by_mia).to be true
      expect(message.direction).to eq('outbound')
      expect(message.status).to eq('sent')
      expect(message.channel).to eq('whatsapp')
      expect(message.message_payload).to eq({ 'type' => 'text' })
      expect(message.source).to eq(retailer.public_phone_number)
      expect(message.destination).to eq(customer.phone_number(false))
    end
  end

  describe '#send_bulk_files' do
    subject(:base) { described_class.new(retailer, customer, retailer_user, 'maytapi') }

    let(:retailer) { create(:retailer, :qr_integrated) }
    let(:customer) { create(:customer, retailer: retailer) }
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }

    before do
      allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
      allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
      allow_any_instance_of(described_class).to receive(:send_message_to_maytapi).and_return(maytapi_body_response)
    end

    context 'when the image is sent' do
      let(:options) { { type: 'file', params: image_url_params_with_content_type, retailer_user: retailer_user } }

      it 'prepares the message body to send it to maytapi' do
        expect { base.send_bulk_files(options) }.to change(GupshupWhatsappMessage, :count).by(1)
        gs_message = GupshupWhatsappMessage.find_by(message_identifier: '1dc-123')
        expect(gs_message.message_payload['originalUrl']).to eq(image_url_params_with_content_type[:url])
      end
    end

    context 'when the images are sent' do
      let(:options) { { type: 'file', params: image_urls_params_with_content_type, retailer_user: retailer_user } }

      it 'prepares the message body to send it to maytapi' do
        expect { base.send_bulk_files(options) }.to change(GupshupWhatsappMessage, :count).by(1)
        gs_message = GupshupWhatsappMessage.find_by(message_identifier: '1dc-123')
        expect(gs_message.message_payload['originalUrl']).to eq(image_urls_params_with_content_type[:images_urls][0])
      end
    end

    context 'when the file data is sent' do
      let(:options) { { type: 'file', params: audio_files_params, retailer_user: retailer_user } }

      before { allow_any_instance_of(Uploaders::S3Aws).to receive(:upload_file).and_return(aws_s3_audio_response) }

      it 'prepares the message body to send it to maytapi' do
        expect { base.send_bulk_files(options) }.to change(GupshupWhatsappMessage, :count).by(1)
      end
    end
  end

  describe '#send_multiple_answers' do
    let(:template) { create(:template, retailer: retailer) }
    let!(:additional_fast_answer1) do
      create(:additional_fast_answer, template: template, answer: 'First response', file_type: '')
    end
    let!(:additional_fast_answer2) do
      create(:additional_fast_answer, template: template, answer: 'Second response', file_type: 'audio')
    end

    context 'when using maytapi integration' do
      let(:maytapi_response) do
        request_object = HTTParty::Request.new Net::HTTP::Get, '/'
        response_object = Net::HTTPOK.new('1.1', 200, 'OK')
        parsed_response = -> { { 'success' => true, 'data' => { 'chatId' => '<EMAIL>', 'msgId' => '1dc-123' } } }
        HTTParty::Response.new(request_object, response_object, parsed_response)
      end

      let(:maytapi_params) do
        {
          template_id: template.id,
          type: 'text',
          message: 'Initial message',
          message_identifiers: Array.new(3) { SecureRandom.uuid }
        }
      end

      before do
        allow_any_instance_of(described_class).to receive(:send_answer) do
          create(:gupshup_whatsapp_message, customer: customer, retailer: retailer)
        end
      end

      it 'sends multiple messages through maytapi' do
        expect do
          described_class.new(retailer, customer, retailer_user, 'maytapi').send_multiple_answers(
            type: 'text',
            params: maytapi_params,
            retailer_user: retailer_user
          )
        end.to change(GupshupWhatsappMessage, :count).by(3)
      end
    end
  end

  describe '#save_sent_message' do
    let(:notifications) { instance_double(Notifications::Web::Messages) }
    let(:message_instance) { instance_double(GupshupWhatsappMessage) }
    let(:message_params) do
      {
        message: {
          id: 'msg-123',
          text: 'Test message'
        },
        payload: {
          type: 'text',
          text: 'Test message'
        }
      }
    end

    before do
      retailer.update(qr_phone_number: '+1234567890')
      allow(retailer).to receive(:qr_phone_number).and_return('+1234567890')
      allow(customer).to receive(:phone).and_return('+0987654321')
    end

    it 'creates a new message' do
      expect do
        described_class.new(retailer, customer).save_sent_message(
          message_params.with_indifferent_access,
          convert_params: false
        )
      end.to change(GupshupWhatsappMessage, :count).by(1)
    end

    it 'sets the correct source' do
      described_class.new(retailer, customer).save_sent_message(
        message_params.with_indifferent_access,
        convert_params: false
      )
      expect(GupshupWhatsappMessage.last.source).to eq(retailer.qr_phone_number)
    end

    it 'saves the message' do
      message = GupshupWhatsappMessage.last
      expect(retailer.gupshup_whatsapp_messages).to receive(:new)
        .and_return(message)
      expect(message).to receive(:save!)

      described_class.new(retailer, customer).save_sent_message(
        message_params.with_indifferent_access,
        convert_params: false
      )
    end

    it 'broadcasts the message' do
      message = GupshupWhatsappMessage.last
      allow(retailer.gupshup_whatsapp_messages).to receive(:new).and_return(message)
      allow(message).to receive(:save!)
      allow(Notifications::Web::Messages).to receive(:new).with(message).and_return(notifications)
      expect(notifications).to receive(:broadcast!)

      described_class.new(retailer, customer).save_sent_message(
        message_params.with_indifferent_access,
        convert_params: false
      )
    end
  end

  describe '#send_error_message' do
    it 'logs the error and sends it to Slack' do
      error = StandardError.new('Test error')
      expect(Rails.logger).to receive(:error).with(error)
      expect(SlackError).to receive(:send_error).with(error, kind_of(String))

      described_class.new(retailer, customer).send(:send_error_message, error)
    end
  end

  describe '#build_context' do
    it 'adds the msgId to the quoted hash' do
      quoted = { message: 'test' }
      msg_id = '12345'
      result = described_class.new(retailer, customer).send(:build_context, quoted, msg_id)
      expect(result[:gsId]).to eq(msg_id)
      expect(result[:message]).to eq('test')
    end
  end

  describe '#file' do
    subject(:msg_instance) { described_class.new(retailer, customer) }

    let(:retailer) do
      create(:retailer,
             gupshup_phone_number: '+1234567890',
             gupshup_src_name: 'TestSrc')
    end
    let(:customer) { create(:customer, retailer: retailer) }
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }

    let(:file_options) do
      {
        params: {
          type: 'file',
          file_data: file_data
        },
        retailer_user: retailer_user
      }
    end

    let(:file_data) do
      Rack::Test::UploadedFile.new(
        Rails.root.join('spec/fixtures/files/document.pdf'),
        'application/pdf'
      )
    end

    before do
      allow_any_instance_of(Uploaders::S3Aws).to receive(:upload_file).and_return(aws_s3_audio_response)
      allow_any_instance_of(Retailer).to receive(:attach_agent_name?).and_return(true)
    end

    context 'when file_data is provided' do
      it 'uploads the file and prepares the payload' do
        msg_instance.instance_variable_set(:@options, file_options)
        result = msg_instance.send(:file)
        expect(result).not_to be_nil
      end
    end
  end

  describe '#video' do
    it 'raises an error if file_url is missing' do
      expect do
        described_class.new(retailer, customer).send(:video, {})
      end.to raise_error(StandardError, 'Faltaron parámetros')
    end

    it 'prepares the video payload and body_string' do
      data = { file_url: 'https://example.com/video.mp4', file_caption: 'Test video' }
      result = described_class.new(retailer, customer).send(:video, data)
      expect(result[1]).to include('https://example.com/video.mp4')
    end
  end

  describe '#list' do
    let(:options) do
      {
        params: {
          list_title: 'Select',
          message: 'Choose an option',
          list: [{ title: 'Option 1', value: '1' }, { title: 'Option 2', value: '2' }]
        }
      }
    end

    it 'prepares a list message payload and body_string' do
      msg_instance = described_class.new(retailer, customer)
      msg_instance.instance_variable_set(:@options, options) # Usamos la variable let `options`

      result = msg_instance.send(:list) # Llamada al método privado
      expect(result[1]).to include('list')
    end
  end

  describe '#quick_reply' do
    let(:quick_reply_options) do
      {
        params: {
          message: 'Choose an option',
          list: [{ title: 'Yes', value: 'yes' }, { title: 'No', value: 'no' }]
        }
      }
    end

    it 'prepares a quick reply message payload and body_string' do
      msg_instance = described_class.new(retailer, customer)
      msg_instance.instance_variable_set(:@options, {
        params: {
          message: 'This is a quick reply',
          list: [{ title: 'Yes', value: 'yes' }, { title: 'No', value: 'no' }]
        }
      })

      result = msg_instance.send(:quick_reply) # Llamada al método privado
      expect(result).not_to be_nil
    end
  end

  describe '#update_params' do
    let(:additional_fast_answer) do
      instance_double(AdditionalFastAnswer,
                      file_type: 'image',
                      answer: 'Test image',
                      file_url: 'https://example.com/image.png')
    end
    let(:afa) do
      instance_double(
        AdditionalFastAnswer,
        file_type: 'image',
        answer: 'Test image',
        file_url: 'https://example.com/image.png'
      )
    end

    it 'updates params for image type' do
      msg_instance = described_class.new(retailer, customer)
      msg_instance.instance_variable_set(:@options, {
        params: {
          file_type: 'image',
          caption: 'Test Caption',
          message_identifiers: ['1dc-123']
        }
      })

      msg_instance.send(:update_params, afa, 0)

      expect(msg_instance.instance_variable_get(:@options)[:params][:content_type]).to eq('image')
    end
  end

  describe '#send_answer' do
    let(:type) { 'text' }

    context 'when integration is maytapi' do
      let(:integration) { 'maytapi' }
      let(:msg_instance) { described_class.new(retailer, customer, retailer_user, integration) }

      before do
        msg_instance.instance_variable_set(:@options, {})
        allow(msg_instance).to receive(:send_maytapi_message).and_return('message sent')
      end

      it 'sets the type in @options and calls send_maytapi_message' do
        expect(msg_instance).to receive(:send_maytapi_message)
        result = msg_instance.send_answer(type)
        expect(msg_instance.instance_variable_get(:@options)[:type]).to eq(type)
        expect(result).to eq('message sent')
      end
    end

    context 'when integration is not maytapi' do
      let(:integration) { 'gupshup' }
      let(:msg_instance) { described_class.new(retailer, customer, retailer_user, integration) }

      before do
        # Inicializa @options con retailer_user
        msg_instance.instance_variable_set(:@options, { retailer_user: retailer_user })
        allow(msg_instance).to receive(:send).with(type).and_return(%w[request_body message_body])
        allow(msg_instance).to receive(:send_message_request)
          .with('request_body')
          .and_return(double(read_body: '{"message": "success"}'))
        allow(msg_instance).to receive(:save_message).and_return(true)
      end

      it 'prepares the request body, sends the message, and saves it' do
        expect(msg_instance).to receive(:send).with(type).and_return(%w[request_body message_body])
        expect(msg_instance).to receive(:send_message_request).with('request_body')
        expect(msg_instance).to receive(:save_message)

        result = msg_instance.send_answer(type)
        expect(result).to be_truthy
      end
    end
  end

  describe '#catch_url' do
    let(:msg_instance) { described_class.new }

    before do
      msg_instance.instance_variable_set(:@retailer, retailer)

      stub_const('Whatsapp::Outbound::Msg::BSP_TEMPLATE_URL', 'http://localhost:8082/template/msg')
      stub_const('Whatsapp::Outbound::Msg::BSP_SM_URL', 'http://localhost:8082/msg')
      stub_const('Whatsapp::Outbound::Msg::TEMPLATE_URL', 'http://localhost:8081/template/msg')
      stub_const('Whatsapp::Outbound::Msg::SM_URL', 'http://localhost:8081/msg')
    end

    context 'when retailer is connected to BSP Mercately' do
      before do
        allow(retailer).to receive(:connect_bsp_mercately?).and_return(true)
      end

      context 'when is_template_with_id is true' do
        before { msg_instance.instance_variable_set(:@is_template_with_id, true) }

        it 'returns BSP_TEMPLATE_URL' do
          expect(msg_instance.send(:catch_url)).to eq('http://localhost:8082/template/msg')
        end
      end

      context 'when is_template_with_id is false' do
        before { msg_instance.instance_variable_set(:@is_template_with_id, false) }

        it 'returns BSP_SM_URL' do
          expect(msg_instance.send(:catch_url)).to eq('http://localhost:8082/msg')
        end
      end
    end

    context 'when retailer is not connected to BSP Mercately' do
      before do
        allow(retailer).to receive(:connect_bsp_mercately?).and_return(false)
      end

      context 'when is_template_with_id is true' do
        before { msg_instance.instance_variable_set(:@is_template_with_id, true) }

        it 'returns TEMPLATE_URL' do
          expect(msg_instance.send(:catch_url)).to eq('http://localhost:8081/template/msg')
        end
      end

      context 'when is_template_with_id is false' do
        before { msg_instance.instance_variable_set(:@is_template_with_id, false) }

        it 'returns SM_URL' do
          expect(msg_instance.send(:catch_url)).to eq('http://localhost:8081/msg')
        end
      end
    end
  end

  describe 'MIA chatbot activation' do
    let(:text_params) do
      {
        message: 'Test message without agent',
        type: 'text',
        message_identifier: SecureRandom.uuid
      }
    end

    let(:mia_net_response) { Net::HTTPAccepted.new(1.0, '202', 'Accepted') }
    let(:mia_integration) { create(:mia_integration, retailer: retailer, mia_integration_type: mia_integration_type) }
    let(:mia_integration_type) { create(:mia_integration_type, name: 'chatbot') }
    let(:mia_platform) { create(:mia_platform, name: 'Whatsapp') }

    before do
      create(:retailer_mia_platform, retailer: retailer, mia_platform: mia_platform)
      mia_integration
      retailer.update!(mia_chatbot_active: true)

      allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(mia_net_response)
      allow_any_instance_of(Net::HTTPAccepted).to receive(:read_body).and_return(ok_body_response)
    end

    describe '#activate_mia_chatbot_if_needed' do
      context 'when conditions are met for MIA activation' do
        context 'when message is sent without agent and is first outbound message' do
          it 'activates MIA chatbot for customer' do
            expect(customer.customer_mia_chatbot_for('whatsapp').active).to be false

            described_class.new(retailer, customer).send_message(type: 'text', params: text_params)

            expect(customer.customer_mia_chatbot_for('whatsapp').active).to be true
          end

          it 'logs the activation' do
            allow(Rails.logger).to receive(:info) # Allow other log calls
            expect(Rails.logger).to receive(:info).with(
              "MIA chatbot activated for customer #{customer.id} - first outbound message without agent"
            )

            described_class.new(retailer, customer).send_message(type: 'text', params: text_params)
          end
        end

        context 'when customer already has allow_mia_chatbot enabled' do
          before do
            customer.customer_mia_chatbot_for('whatsapp').update(active: true)
          end

          it 'does not change the allow_mia_chatbot status' do
            expect do
              described_class.new(retailer, customer).send_message(type: 'text', params: text_params)
            end.not_to(change { customer.customer_mia_chatbot_for('whatsapp').active })
          end
        end

        context 'when MIA service is not available' do
          let(:service) { described_class.new(retailer, customer) }

          before do
            allow(service).to receive_messages(
              should_activate_mia_chatbot?: true,
              mia_service_available?: false
            )
          end

          it 'does not activate MIA chatbot' do
            expect(customer).not_to receive(:update!)
            expect(Rails.logger).not_to receive(:info)

            service.send(:activate_mia_chatbot_if_needed, mia_net_response, double('message'))
          end
        end

        context 'when there is an error during activation' do
          let(:service) { described_class.new(retailer, customer) }
          let(:error_message) { 'Database connection failed' }

          before do
            allow(service).to receive_messages(
              should_activate_mia_chatbot?: true,
              mia_service_available?: true
            )
            # allow(customer).to receive(:update!).and_raise(StandardError, error_message)
            allow_any_instance_of(CustomerMiaChatbot).to receive(:update!).and_raise(StandardError, error_message)
          end

          it 'logs the error and backtrace' do
            expected_error_msg = "Error activating MIA chatbot for customer #{customer.id}: #{error_message}"
            expect(Rails.logger).to receive(:error).with(expected_error_msg)
            expect(Rails.logger).to receive(:error).with(anything) # backtrace

            service.send(:activate_mia_chatbot_if_needed, mia_net_response, double('message'))
          end

          it 'does not raise the error' do
            allow(Rails.logger).to receive(:error) # Silence logging for this test

            expect do
              service.send(:activate_mia_chatbot_if_needed, mia_net_response, double('message'))
            end.not_to raise_error
          end
        end
      end

      context 'when conditions are NOT met for MIA activation' do
        context 'when message is sent with an agent' do
          it 'does not activate MIA chatbot' do
            expect(customer.allow_mia_chatbot).to be_falsey

            described_class.new(retailer, customer).send_message(
              type: 'text',
              params: text_params,
              retailer_user: retailer_user
            )

            expect(customer.reload.allow_mia_chatbot).to be_falsey
          end
        end

        context 'when this is not the first outbound message' do
          before do
            # Create a previous outbound message
            create(:gupshup_whatsapp_message, :outbound,
                   customer: customer,
                   retailer: retailer,
                   note: false)
          end

          it 'does not activate MIA chatbot' do
            expect(customer.allow_mia_chatbot).to be_falsey

            described_class.new(retailer, customer).send_message(type: 'text', params: text_params)

            expect(customer.reload.allow_mia_chatbot).to be_falsey
          end
        end

        context 'when message sending fails' do
          let(:failed_net_response) { Net::HTTPBadRequest.new(1.0, '400', 'Bad Request') }

          before do
            allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(failed_net_response)
            allow_any_instance_of(Net::HTTPBadRequest).to receive(:read_body).and_return(
              { status: 'error', messageId: nil }.to_json
            )
          end

          it 'does not activate MIA chatbot' do
            expect(customer.allow_mia_chatbot).to be_falsey

            described_class.new(retailer, customer).send_message(type: 'text', params: text_params)

            expect(customer.reload.allow_mia_chatbot).to be_falsey
          end
        end
      end
    end

    describe '#should_activate_mia_chatbot?' do
      let(:service) { described_class.new(retailer, customer) }
      let(:successful_response) { double('response', code: '202') }
      let(:failed_response) { double('response', code: '400') }
      let(:message) { double('message') }

      context 'when all conditions are met' do
        before do
          service.instance_variable_set(:@options, { retailer_user: nil })
          allow(service).to receive(:should_activate_mia_for_outbound_message?).and_return(true)
        end

        it 'returns true' do
          expect(service.send(:should_activate_mia_chatbot?, successful_response, message)).to be_truthy
        end
      end

      context 'when response is not successful' do
        before do
          service.instance_variable_set(:@options, { retailer_user: nil })
          allow(service).to receive(:should_activate_mia_for_outbound_message?).and_return(true)
        end

        it 'returns false' do
          expect(service.send(:should_activate_mia_chatbot?, failed_response, message)).to be_falsey
        end
      end

      context 'when message is nil' do
        before do
          service.instance_variable_set(:@options, { retailer_user: nil })
          allow(service).to receive(:should_activate_mia_for_outbound_message?).and_return(true)
        end

        it 'returns false' do
          expect(service.send(:should_activate_mia_chatbot?, successful_response, nil)).to be_falsey
        end
      end

      context 'when this is not the first outbound message' do
        before do
          service.instance_variable_set(:@options, { retailer_user: nil })
          allow(service).to receive(:should_activate_mia_for_outbound_message?).and_return(false)
        end

        it 'returns false' do
          expect(service.send(:should_activate_mia_chatbot?, successful_response, message)).to be_falsey
        end
      end

      context 'when message is from automated chatbot flow' do
        before do
          service.instance_variable_set(:@options, {
            retailer_user: nil,
            params: { from_chatbot_flow: true }
          })
          allow(service).to receive(:should_activate_mia_for_outbound_message?).and_return(true)
        end

        it 'returns false' do
          expect(service.send(:should_activate_mia_chatbot?, successful_response, message)).to be_falsey
        end
      end

      context 'when response is nil' do
        before do
          service.instance_variable_set(:@options, { retailer_user: nil })
        end

        it 'returns false' do
          expect(service.send(:should_activate_mia_chatbot?, nil, message)).to be_falsey
        end
      end

      context 'when response code is not 202' do
        let(:bad_response) { double('response', code: '400') }

        before do
          service.instance_variable_set(:@options, { retailer_user: nil })
        end

        it 'returns false' do
          expect(service.send(:should_activate_mia_chatbot?, bad_response, message)).to be_falsey
        end
      end

      context 'when message is blank' do
        before do
          service.instance_variable_set(:@options, { retailer_user: nil })
        end

        it 'returns false for nil message' do
          expect(service.send(:should_activate_mia_chatbot?, successful_response, nil)).to be_falsey
        end

        it 'returns false for empty string message' do
          expect(service.send(:should_activate_mia_chatbot?, successful_response, '')).to be_falsey
        end
      end
    end

    describe '#should_activate_mia_for_outbound_message?' do
      let(:service) { described_class.new(retailer, customer) }

      context 'when customer has no previous outbound messages' do
        it 'returns true (will be first message)' do
          expect(service.send(:should_activate_mia_for_outbound_message?)).to be_truthy
        end
      end

      context 'when customer has one outbound message (will be second)' do
        before do
          create(:gupshup_whatsapp_message, :outbound,
                 customer: customer,
                 retailer: retailer,
                 note: false)
        end

        it 'returns true (total will be 1 after saving current message)' do
          expect(service.send(:should_activate_mia_for_outbound_message?)).to be_truthy
        end
      end

      context 'when customer has multiple outbound messages' do
        before do
          create_list(:gupshup_whatsapp_message, 2, :outbound,
                      customer: customer,
                      retailer: retailer,
                      note: false)
        end

        it 'returns false' do
          expect(service.send(:should_activate_mia_for_outbound_message?)).to be_falsey
        end
      end

      context 'when customer has outbound notes (should be excluded)' do
        before do
          create(:gupshup_whatsapp_message, :outbound,
                 customer: customer,
                 retailer: retailer,
                 note: true)
        end

        it 'returns true (notes are excluded)' do
          expect(service.send(:should_activate_mia_for_outbound_message?)).to be_truthy
        end
      end

      context 'with archived messages' do
        context 'when customer has no active messages but one archived message' do
          before do
            Messages::ArchiveGupshupWhatsappMessage.create!(
              id: 1,
              retailer_id: retailer.id,
              customer_id: customer.id,
              direction: 'outbound',
              note: false,
              status: 'sent',
              source: '123456789',
              destination: '987654321',
              channel: 'whatsapp',
              created_at: 1.day.ago,
              updated_at: 1.day.ago
            )
          end

          it 'returns true (total will be 1 after saving current message)' do
            expect(service.send(:should_activate_mia_for_outbound_message?)).to be_truthy
          end
        end

        context 'when customer has one active and one archived message' do
          before do
            create(:gupshup_whatsapp_message, :outbound,
                   customer: customer,
                   retailer: retailer,
                   note: false)
            Messages::ArchiveGupshupWhatsappMessage.create!(
              id: 1,
              retailer_id: retailer.id,
              customer_id: customer.id,
              direction: 'outbound',
              note: false,
              status: 'sent',
              source: '123456789',
              destination: '987654321',
              channel: 'whatsapp',
              created_at: 1.day.ago,
              updated_at: 1.day.ago
            )
          end

          it 'returns false (total count is 2)' do
            expect(service.send(:should_activate_mia_for_outbound_message?)).to be_falsey
          end
        end

        context 'when customer has archived notes (should be excluded)' do
          before do
            Messages::ArchiveGupshupWhatsappMessage.create!(
              id: 1,
              retailer_id: retailer.id,
              customer_id: customer.id,
              direction: 'outbound',
              note: true,
              status: 'sent',
              source: '123456789',
              destination: '987654321',
              channel: 'whatsapp',
              created_at: 1.day.ago,
              updated_at: 1.day.ago
            )
          end

          it 'returns true (archived notes are excluded)' do
            expect(service.send(:should_activate_mia_for_outbound_message?)).to be_truthy
          end
        end

        context 'when customer has multiple archived messages' do
          before do
            2.times do |i|
              Messages::ArchiveGupshupWhatsappMessage.create!(
                id: i + 1,
                retailer_id: retailer.id,
                customer_id: customer.id,
                direction: 'outbound',
                note: false,
                status: 'sent',
                source: '123456789',
                destination: '987654321',
                channel: 'whatsapp',
                created_at: (i + 1).days.ago,
                updated_at: (i + 1).days.ago
              )
            end
          end

          it 'returns false (total archived count is 2)' do
            expect(service.send(:should_activate_mia_for_outbound_message?)).to be_falsey
          end
        end
      end
    end

    describe '#automated_chatbot_flow_message?' do
      let(:service) { described_class.new(retailer, customer) }

      context 'when message is from chatbot flow' do
        before do
          service.instance_variable_set(:@options, { params: { from_chatbot_flow: true } })
        end

        it 'returns true' do
          expect(service.send(:automated_chatbot_flow_message?)).to be_truthy
        end
      end

      context 'when message is not from chatbot flow' do
        before do
          service.instance_variable_set(:@options, { params: { from_chatbot_flow: false } })
        end

        it 'returns false' do
          expect(service.send(:automated_chatbot_flow_message?)).to be_falsey
        end
      end

      context 'when from_chatbot_flow flag is not present' do
        before do
          service.instance_variable_set(:@options, { params: {} })
        end

        it 'returns false' do
          expect(service.send(:automated_chatbot_flow_message?)).to be_falsey
        end
      end

      context 'when params are not present' do
        before do
          service.instance_variable_set(:@options, {})
        end

        it 'returns false' do
          expect(service.send(:automated_chatbot_flow_message?)).to be_falsey
        end
      end
    end
  end

  describe 'referral methods integration with WhatsApp messages' do
    let(:retailer) { create(:retailer, :gupshup_integrated) }
    let(:customer) { create(:customer, retailer: retailer) }
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }

    describe 'when sending messages with referral data' do
      let(:referral_payload) do
        {
          'app' => 'TESTAPP',
          'timestamp' => 1748265346309,
          'version' => 2,
          'type' => 'message',
          'payload' => {
            'id' => 'wam.id_test_1748265346309',
            'source' => '573000000000',
            'type' => 'text',
            'payload' => { 'text' => '¡Hola! Quiero más información.' },
            'sender' => { 'phone' => '573000000000', 'name' => 'Test User' },
            'referral' => {
              'headline' => 'Test Headline',
              'body' => 'Test Body',
              'source_type' => 'ad',
              'source_id' => 'test_source_id',
              'media_type' => 'image',
              'image_url' => 'https://example.com/image.png'
            }
          }
        }
      end

      context 'when processing inbound message with referral' do
        it 'correctly identifies has_referral? as true' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: referral_payload)
          
          expect(message.has_referral?).to be true
        end

        it 'correctly identifies has_referral_media? as true for image referral' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: referral_payload)
          
          expect(message.has_referral_media?).to be true
        end

        it 'correctly returns referral_type_media as image' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: referral_payload)
          
          expect(message.referral_type_media).to eq('image')
        end

        it 'correctly returns inbound_sender_full_name' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: referral_payload)
          
          expect(message.inbound_sender_full_name).to eq('Test User')
        end

        it 'correctly returns inbound_sender_phone' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: referral_payload)
          
          expect(message.inbound_sender_phone).to eq('573000000000')
        end
      end

      context 'when processing inbound message with video referral' do
        let(:video_referral_payload) do
          {
            'payload' => {
              'referral' => {
                'headline' => 'Video Campaign',
                'media_type' => 'video',
                'video_url' => 'https://example.com/video.mp4',
                'video' => {
                  'id' => 'video_id_456'
                }
              }
            }
          }
        end

        it 'correctly identifies has_referral_media? as true for video referral' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: video_referral_payload)
          
          expect(message.has_referral_media?).to be true
        end

        it 'correctly returns referral_type_media as video' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: video_referral_payload)
          
          expect(message.referral_type_media).to eq('video')
        end

        it 'correctly returns referral_media_id for video' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: video_referral_payload)
          
          expect(message.referral_media_id).to eq('video_id_456')
        end
      end

      context 'when processing message without referral data' do
        let(:no_referral_payload) do
          {
            'payload' => {
              'type' => 'text',
              'payload' => { 'text' => 'Regular message without referral' }
            }
          }
        end

        it 'correctly identifies has_referral? as false' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: no_referral_payload)
          
          expect(message.has_referral?).to be false
        end

        it 'correctly identifies has_referral_media? as false' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: no_referral_payload)
          
          expect(message.has_referral_media?).to be false
        end

        it 'returns nil for referral_media_id when no referral data' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: retailer, 
                          customer: customer,
                          message_payload: no_referral_payload)
          
          expect(message.referral_media_id).to be_nil
        end
      end

      context 'when integration is qr and retailer does not connect qr mercately' do
        let(:qr_retailer) { create(:retailer, :gupshup_integrated, connect_qr_mercately: false) }
        
        it 'returns false for has_referral_media? even with referral data' do
          message = create(:gupshup_whatsapp_message, :inbound, 
                          retailer: qr_retailer, 
                          customer: customer,
                          integration: 'qr',
                          message_payload: referral_payload)
          
          expect(message.has_referral_media?).to be false
        end
      end

      context 'when creating MIA internal messages with referral context' do
        let(:msg_instance) { described_class.new(retailer, customer) }

        it 'creates internal message that responds correctly to referral methods' do
          message = msg_instance.create_mia_internal_message(:human_help)
          
          expect(message.has_referral?).to be false
          expect(message.has_referral_media?).to be false
        end
      end
    end
  end
end
