require 'rails_helper'

RSpec.describe ChatBots::V2::ProcessChatbot do
  subject(:my_class) { described_class.new(customer, chat_bot, origin_instance, text, media) }

  let(:retailer) { create(:retailer, :gupshup_integrated, welcome_message_sent: true) }
  let!(:customer) { create(:customer, :whatsapp, retailer: retailer, first_name: '<PERSON><PERSON>', last_name: '<PERSON><PERSON>') }
  let(:chat_bot) { create(:chat_bot, :v2, :for_whatsapp, retailer: retailer) }
  let(:chat_bot_option) { create(:chat_bot_option, :decision, chat_bot: chat_bot, text: 'main option') }
  let(:text) { 'Some text' }
  let(:media) { true }
  let(:message_process) { instance_double(ChatBots::V2::Message, send_message: true) }
  let(:send_answer) { instance_double(ChatBots::V2::Message, send_message: true) }
  let(:origin_instance) do
    instance_double(GupshupWhatsappMessage, class: GupshupWhatsappMessage, id: 1,
                                            last_message: self, before_last_message: self)
  end

  before { allow(ChatBots::V2::Message).to receive(:new).and_return(message_process) }

  it 'sets instance variables correctly' do
    expect(subject.instance_variable_get(:@customer)).to eq(customer)
    expect(subject.instance_variable_get(:@retailer)).to eq(customer.retailer)
    expect(subject.instance_variable_get(:@chat_bot)).to eq(chat_bot)
    expect(subject.instance_variable_get(:@origin_instance)).to eq(origin_instance)
    expect(subject.instance_variable_get(:@text)).to eq(text)
    expect(subject.instance_variable_get(:@media)).to eq(media)
    expect(subject.instance_variable_get(:@finish_bot)).to be_truthy
  end

  describe '#manage_chat_bot' do
    context 'when is a media' do
      context 'when customer has active_bot' do
        let!(:opt) do
          create(:chat_bot_option, chat_bot: chat_bot, option_type: :form, allow_media: true, answer_type: :anything)
        end
        let!(:child) { create(:chat_bot_option, chat_bot: chat_bot, text: 'First option', parent: opt) }

        context 'with manage_failed_attempts' do
          let(:chatbot) { create(:chat_bot, retailer: retailer) }
          let(:opt1) { create(:chat_bot_option, :decision, chat_bot: chatbot) }

          before do
            customer.chat_bot_option = opt1
            customer.active_bots.create(chat_bot_option: opt, platform: 'whatsapp')
          end

          context 'with reached_failed_attempts' do
            context 'when option does not have fail_path active' do
              before { subject.manage_chat_bot }

              context 'with is not_valid_media' do
                context 'with manage_failed_attempts' do
                  subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, text, media) }

                  let(:chat_bot) do
                    create(:chat_bot, :for_whatsapp, retailer: retailer)
                  end

                  let(:opt1) do
                    create(:chat_bot_option, :with_children, chat_bot: chat_bot, option_type: :decision,
                                                             allow_media: false, fail_path_active: false,
                                                             max_attempts: 100)
                  end

                  before do
                    customer.chat_bot_option = opt1
                    create(:customer_active_bot, customer: customer, chat_bot_option: opt1, platform: 'whatsapp')
                  end

                  context 'with reached_failed_attempts' do
                    context 'when option does not have fail_path active' do
                      it "updates the customer's failed_bot_attempts attribute" do
                        custom_class.manage_chat_bot

                        expect(customer.multiplatform_decorator.failed_bot_attempts_for(:whatsapp)).to eq(1)
                      end
                    end

                    context 'when customer failed_bot_attempts is less than option max_attempts' do
                      before { customer.active_bots.last.update_column(:failed_bot_attempts, 2) }

                      it "updates the customer's failed_bot_attempts attribute" do
                        opt1.update_columns(fail_path_active: true, max_attempts: 3)

                        ChatBots::FailedAttemptsUpdater.call(customer, platform: 'whatsapp')

                        expect(customer.reload.multiplatform_decorator.failed_bot_attempts_for('whatsapp')).to eq(3)
                      end
                    end

                    context 'when customer failed_bot_attempts plus 1 is equals option max_attempts' do
                      before do
                        opt1.update_columns(fail_path_active: true, max_attempts: 1)
                      end

                      let!(:good_option) { create(:chat_bot_option, :fake_option, chat_bot: chat_bot, parent: opt1) }
                      let!(:chil_option) { create(:chat_bot_option, :form, chat_bot: chat_bot, parent: fail_option) }

                      let!(:fail_option) do
                        create(:chat_bot_option, :fake_option, :failure, chat_bot: chat_bot, parent: opt1)
                      end

                      it 'goes to the failure path' do
                        custom_class.manage_chat_bot

                        expect(customer.reload.policy.active_bot_for?(platform: :whatsapp)).to be true
                        expect(customer.active_bots.last.chat_bot_option_id).to be(chil_option.id)
                      end
                    end
                  end
                end

                context 'when option failure_text is not present' do
                  it 'not execute send_message' do
                    expect_any_instance_of(described_class).not_to receive(:message_service)
                    subject.manage_chat_bot
                  end
                end
              end

              context 'when selected option is of type jump_to' do
                let(:form) { create(:chat_bot_option, :form, chat_bot: chat_bot) }
                let(:jump_to) { create(:chat_bot_option, :jump_to, chat_bot: chat_bot, parent: form2) }
                let!(:action) { create(:chat_bot_action, :jump_to_option, chat_bot_option: jump_to, jump_option: form) }

                context 'with option allowing media' do
                  let!(:form) { create(:chat_bot_option, :form, chat_bot: chat_bot) }
                  let!(:action) do
                    create(:chat_bot_action, :jump_to_option, chat_bot_option: jump_to, jump_option: form)
                  end

                  let(:form2) do
                    create(:chat_bot_option, :form, chat_bot: chat_bot, parent: form, answer_type: :anything)
                  end

                  let!(:jump_to) do
                    create(:chat_bot_option, :jump_to, chat_bot: chat_bot, parent: form2)
                  end

                  before do
                    customer.active_bots.delete_all
                  end

                  it 'sets the option id to the customer where bot jumped to' do
                    customer.active_bots.create(chat_bot_option: form2, platform: 'whatsapp')
                    my_class.manage_chat_bot

                    expect(customer.active_bots.last.chat_bot_option_id).to eq(form2.id)
                  end
                end

                context 'with option not allowing media' do
                  let!(:form) { create(:chat_bot_option, :form, chat_bot: chat_bot) }
                  let(:form2) { create(:chat_bot_option, :form, chat_bot: chat_bot, parent: form, answer_type: :text) }
                  let!(:action) do
                    create(:chat_bot_action, :jump_to_option, chat_bot_option: jump_to, jump_option: form)
                  end

                  let!(:jump_to) do
                    create(:chat_bot_option, :jump_to, chat_bot: chat_bot, parent: form2)
                  end

                  before do
                    customer.update(chat_bot_option_id: form2.id)
                    create(:customer_active_bot, customer: customer, chat_bot_option: form2, platform: 'whatsapp')
                    chat_bot.update(failed_attempts: 2)
                    my_class.manage_chat_bot
                  end

                  it 'does not set the option id to the customer where bot jumped to' do
                    form2.update(max_attempts: 2)
                    my_class.manage_chat_bot

                    expect(customer.active_bots.last.chat_bot_option_id).to eq(form2.id)
                    expect(customer.active_bots.last.failed_bot_attempts).to eq(2)
                  end

                  it { expect(customer.active_bots.last.chat_bot_option_id).to eq(form2.id) }
                  it { expect(customer.active_bots.last.failed_bot_attempts).to eq(1) }
                end
              end
            end
          end
        end

        context 'when current_option is form and is of type location' do
          let(:option) { create(:chat_bot_option, :form, chat_bot: chat_bot, answer_type: :location) }
          let(:origin_instance) do
            instance_double(
              GupshupWhatsappMessage,
              class: GupshupWhatsappMessage,
              before_last_message: self,
              id: 1,
              type: :location
            )
          end

          before do
            customer.update(chat_bot_option: option)
            ChatBots::CustomerFlowUpdater.call(customer, selected: option.id, platform: :whatsapp)
          end

          it 'does not process anything' do
            expect_any_instance_of(described_class).not_to receive(:update_customer_flow)
            expect_any_instance_of(described_class).to receive(:manage_failed_attempts).at_least(1)
            subject.manage_chat_bot
          end

          it 'process location' do
            expect_any_instance_of(described_class).to receive(:manage_location_forms)
            subject.manage_chat_bot
          end
        end
      end

      context 'when customer active_bot is false' do
        context 'with check_chat_bot_history' do
          context 'when the chat_bot is not present' do
            subject(:custom_class) { described_class.new(customer, nil, origin_instance, text, media) }

            it 'not call update_customer_flow method' do
              expect_any_instance_of(described_class).not_to receive(:update_customer_flow)
              custom_class.manage_chat_bot
            end
          end
        end
      end
    end

    context 'when is not media' do
      context 'when not_current_option is false' do
        subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, %w[text1 text2], false) }

        before { customer.chat_bot_option = chat_bot_option }

        it 'not process anything' do
          expect_any_instance_of(described_class).not_to receive(:update_customer_flow)
          subject.manage_chat_bot
        end
      end

      context 'when customer has active bot' do
        subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, 'Hello', false) }

        let(:customer) { create(:customer, :bot_active, retailer: retailer) }
        let(:option_type) { :form }
        let(:option) { create(:chat_bot_option, chat_bot: chat_bot, text: 'current opt', option_type: option_type) }

        before do
          ChatBots::CustomerFlowUpdater.call(customer, selected: option.id, platform: :whatsapp)
        end

        context 'when @input_option and @selected are true' do
          let!(:child_option) { create(:chat_bot_option, chat_bot: chat_bot, text: 'child 1', parent: option) }

          before { customer.update(chat_bot_option: option) }

          it { expect { subject.manage_chat_bot }.to change(customer.customer_bot_options, :count) }
        end

        context 'when chat_bot option_type is message' do
          let(:option_type) { :message }
          let!(:child_option) { create(:chat_bot_option, chat_bot: chat_bot, text: 'child 1', parent: option) }

          before { customer.update(chat_bot_option: option) }

          it 'does not process anything' do
            expect_any_instance_of(described_class).not_to receive(:update_customer_flow)
            subject.manage_chat_bot
          end
        end

        context 'when chat_bot option_type is decision' do
          let(:option_type) { :decision }
          let!(:child_option) { create(:chat_bot_option, chat_bot: chat_bot, text: 'child 1', parent: option) }

          before { customer.update(chat_bot_option: option) }

          it { expect { subject.manage_chat_bot }.not_to change(CustomerBotOption, :count) }
        end

        context 'when current_option has sublist' do
          let!(:opt_sublist1) { create(:option_sub_list, chat_bot_option: option, value_to_show: 'opt 1') }
          let!(:opt_sublist2) { create(:option_sub_list, chat_bot_option: option, value_to_show: 'opt 2') }

          before { customer.update(chat_bot_option: option) }

          context 'with match_sub_list_items false' do
            it 'does not process anything' do
              expect_any_instance_of(described_class).not_to receive(:update_customer_flow)
              subject.manage_chat_bot
            end
          end

          context 'with match_sub_list_items true' do
            subject(:custom_class1) { described_class.new(customer, chat_bot, origin_instance, ['1'], false) }

            let!(:child_option) { create(:chat_bot_option, chat_bot: chat_bot, text: 'child 1', parent: option) }

            it { expect { subject.manage_chat_bot }.to change(customer.customer_bot_options, :count).by(1) }
          end
        end

        context 'when current_option has not sublist' do
          let!(:child_option) { create(:chat_bot_option, chat_bot: chat_bot, text: 'child 1', parent: option) }

          before { customer.update(chat_bot_option: option) }

          it { expect { subject.manage_chat_bot }.to change(customer.customer_bot_options, :count).by(1) }
        end

        context 'when @sent_in_action' do
          subject(:custom_class1) { described_class.new(customer, chat_bot, instance, text, false) }

          let(:instance) { build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer) }
          let(:option) { create(:chat_bot_option, :form, text: 'node 1', parent: chat_bot_option) }
          let!(:child_option) { create(:chat_bot_option, text: 'child 1', parent: option) }
          let!(:action) { create(:chat_bot_action, :repeat_endpoint_option, chat_bot_option: option) }

          before { customer.update(chat_bot_option: option) }

          it { expect { subject.manage_chat_bot }.not_to change(customer, :chat_bot_option_id) }
        end

        context 'when selected option is of type jump_to' do
          let!(:option) { create(:chat_bot_option, chat_bot: chat_bot) }
          let!(:option2) { create(:chat_bot_option, :fake_option, chat_bot: chat_bot, parent: option) }
          let!(:option3) { create(:chat_bot_option, chat_bot: chat_bot, parent: option2) }
          let!(:option4) { create(:chat_bot_option, :fake_option, chat_bot: chat_bot, parent: option3, text: 'Hello') }
          let!(:jump_to) { create(:chat_bot_option, :jump_to, chat_bot: chat_bot, parent: option4) }
          let!(:action) { create(:chat_bot_action, :jump_to_option, chat_bot_option: jump_to, jump_option: option) }

          before { create(:customer_active_bot, customer: customer, chat_bot_option: jump_to, platform: 'whatsapp') }

          it 'sets option id to the customer where bot jumped to' do
            ChatBots::CustomerFlowUpdater.call(customer, selected: option.id, platform: :whatsapp)

            expect(ChatBots::CurrentOptionFinder.call(customer: customer, platform: :whatsapp).id).to eq(option.id)
          end
        end

        context 'when current_option is form and is of type location' do
          let(:option) { create(:chat_bot_option, :form, chat_bot: chat_bot, answer_type: :location) }

          before do
            customer.update(chat_bot_option: option)
            ChatBots::CustomerFlowUpdater.call(customer, selected: option.id, platform: :whatsapp)
          end

          it 'does not process anything' do
            expect_any_instance_of(described_class).not_to receive(:update_customer_flow)
            expect_any_instance_of(described_class).to receive(:manage_failed_attempts)
            subject.manage_chat_bot
          end
        end
      end

      context 'when customer has not active bot' do
        let(:opt1) do
          create(:chat_bot_option, :with_children, chat_bot: chat_bot, option_type: :form,
                                                   allow_media: false, text: 'current opt')
        end

        before do
          customer.active_bot = false
          customer.active_bots.delete_all
        end

        context 'with check_chat_bot_history is false' do
          subject(:custom_class) { described_class.new(customer, nil, origin_instance, text, false) }

          before { customer.update(chat_bot_option: chat_bot_option) }

          it 'does not process anything' do
            expect_any_instance_of(described_class).not_to receive(:update_customer_flow)
            subject.manage_chat_bot
          end
        end

        context 'when check_chat_bot_history is true' do
          subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, text, false) }

          let!(:chat_bot_customer) { create(:chat_bot_customer, chat_bot: chat_bot, customer: customer) }

          let(:customer) do
            create(:customer, :able_to_start_bots, retailer: retailer, chat_bot_option: chat_bot_option)
          end

          it { expect { subject.manage_chat_bot }.not_to change(customer, :active_bot) }

          it 'not process anything' do
            chat_bot.reactivate_after = nil
            customer.allow_start_bots = true
            ChatBots::CustomerFlowUpdater.call(customer, selected: opt1.id, platform: :whatsapp)

            custom_class.manage_chat_bot
            expect(customer.policy.active_bot_for?(platform: 'whatsapp')).to be_falsey
          end

          context 'with skip_this_options' do
            let(:chat_bot) { create(:chat_bot, :v2, :for_whatsapp, retailer: retailer, outbound_enabled: false) }
            let(:customer) { create(:customer, :able_to_start_bots, retailer: retailer, chat_bot_option: option) }
            let!(:option) { create(:chat_bot_option, chat_bot: chat_bot, option_type: :form, skip_option: true) }

            it { expect { subject.manage_chat_bot }.not_to change(customer, :chat_bot_option_id) }
            it { expect { subject.manage_chat_bot }.to change(CustomerActiveBot, :count) }
          end
        end
      end
    end
  end

  describe '#process_media' do
    context 'when customer has active_bot' do
      let(:customer) { create(:customer, :bot_active, retailer: retailer, chat_bot_option: chat_bot_option) }

      context 'when chat_bot_option option_type is message' do
        let(:chat_bot_option) { create(:chat_bot_option, :message, chat_bot: chat_bot, text: 'main option') }

        it { expect(subject.process_media).to be_nil }
      end
    end

    context 'when customer has not active_bot' do
      before do
        ChatBots::CustomerFlowUpdater.call(customer, selected: chat_bot_option.id, platform: :whatsapp)
      end

      context 'with check_chat_bot_history is false' do
        subject(:custom_class) { described_class.new(customer, nil, origin_instance, text, media) }

        it 'not call update_customer_flow method' do
          expect_any_instance_of(described_class).not_to receive(:update_customer_flow)

          custom_class.process_media
        end
      end

      context 'with check_chat_bot_history is true' do
        subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, text, false) }

        let!(:chat_bot_customer) { create(:chat_bot_customer, chat_bot: chat_bot, customer: customer) }

        it 'set active_bot on false' do
          chat_bot.reactivate_after = nil
          customer.allow_start_bots = true
          customer.chat_bot_option = chat_bot_option

          custom_class.process_media
          expect(customer.reload.active_bot).to be_falsey
        end
      end

      context 'with check_chat_bot_history is true and skip_this_options' do
        subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, text, false) }

        let!(:opt) { create(:chat_bot_option, chat_bot: chat_bot, option_type: :form, skip_option: true) }
        let!(:chat_bot_customer) { create(:chat_bot_customer, chat_bot: chat_bot, customer: customer) }

        it 'not process anything' do
          chat_bot.reactivate_after = nil
          customer.allow_start_bots = true
          ChatBots::CustomerFlowUpdater.call(customer, selected: opt.id, platform: :whatsapp)

          custom_class.process_media
          expect(ChatBots::CurrentOptionFinder.call(customer: customer, platform: :whatsapp).id).to eq(opt.id)
          expect(customer.policy.active_bot_for?(platform: 'whatsapp')).to be_truthy
        end
      end
    end
  end

  describe '#check_chat_bot_history' do
    subject(:my_class) { described_class.new(customer, chat_bot, message, text, media) }

    let(:text) { 'Hello, ChatBot!' }
    let(:media) { nil }
    let(:message) do
      create(:gupshup_whatsapp_message, :inbound, :created_now, customer: customer, retailer: retailer)
    end

    context 'when chat_bot is not present' do
      let(:chat_bot) { nil }

      it { expect(my_class.check_chat_bot_history).to be_falsey }
    end

    context 'when chat_bot is present' do
      let(:chat_bot) do
        create(:chat_bot, :for_whatsapp, retailer: retailer, reactivate_after: reactivate_after)
      end
      let!(:chat_bot_option) { create(:chat_bot_option, chat_bot: chat_bot) }
      let(:reactivate_after) { 0.5 }
      let(:created_at) { 31.minutes.ago }

      context 'with last_interaction' do
        let!(:chat_bot_customer) { create(:chat_bot_customer, :whatsapp, chat_bot: chat_bot, customer: customer) }

        context 'when time_to_reactivate is false and customer allow_start_bots_for is false' do
          let(:customer) { create(:customer, :whatsapp, :opted_in, retailer: retailer) }
          let(:reactivate_after) { nil }
          let!(:previous_message) do
            create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer, created_at: 1.hour.ago)
          end

          it { expect(my_class.check_chat_bot_history).to be_falsey }
        end

        context 'when time_to_reactivate is true' do
          let(:customer) { create(:customer, :whatsapp, :opted_in, retailer: retailer) }
          let!(:before_last_message) do
            create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer, created_at: created_at)
          end

          it { expect(my_class.check_chat_bot_history).to be_truthy }
          it { expect { my_class.check_chat_bot_history }.to change(ChatBotCustomer, :count).by(1) }
        end

        context 'when customer allow_start_bots_for is true' do
          let(:reactivate_after) { nil }
          let(:customer) { create(:customer, :whatsapp, :opted_in, :able_to_start_bots, retailer: retailer) }

          it { expect(my_class.check_chat_bot_history).to be_truthy }
          it { expect { my_class.check_chat_bot_history }.to change(ChatBotCustomer, :count).by(1) }
        end
      end

      context 'when activate_for_first_time is true' do
        it { expect(my_class.check_chat_bot_history).to be_truthy }
        it { expect { my_class.check_chat_bot_history }.to change(ChatBotCustomer, :count).by(1) }
      end

      context 'without last_interaction and activate_for_first_time is false' do
        let(:reactivate_after) { nil }
        let!(:before_last_message) do
          create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer, created_at: created_at)
        end

        it { expect(my_class.check_chat_bot_history).to be_falsey }
      end

      context 'with outbound enabled' do
        let(:chat_bot) { create(:chat_bot, :for_whatsapp, retailer: retailer, outbound_enabled: true) }

        context 'when outbound enabled but there is no previous outbound message' do
          it { expect(my_class.check_chat_bot_history).to be_truthy }
        end

        context 'when outbound enabled and there is previous outbound message' do
          let!(:inbound_message) do
            create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer)
          end
          let!(:outbound_message) do
            create(:gupshup_whatsapp_message, :outbound, customer: customer, retailer: retailer)
          end

          it { expect(my_class.check_chat_bot_history).to be_falsey }

          context 'when waiting time is 12 hours' do
            let(:chat_bot) do
              create(:chat_bot, :for_whatsapp, retailer: retailer, outbound_enabled: true, response_waiting_time_h: 12)
            end

            it { expect(my_class.check_chat_bot_history).to be_falsey }
          end
        end
      end
    end
  end

  describe '#not_current_option' do
    context 'when text is a string' do
      it 'return false' do
        expect(my_class.not_current_option?).to be_falsey
      end
    end

    context 'when text is an array and text is equal to customer.chat_bot_option_id' do
      subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, %w[text1 text2], media) }

      it 'return true' do
        customer.chat_bot_option = chat_bot_option
        expect(custom_class.not_current_option?).to be_truthy
      end
    end
  end

  describe '#preactivated flow' do
    let!(:opt) do
      create(:chat_bot_option, chat_bot: chat_bot, option_type: :form, allow_media: true, answer_type: :anything)
    end
    let!(:child) { create(:chat_bot_option, chat_bot: chat_bot, text: 'First option', parent: opt) }

    before do
      customer.active_bots.create(chat_bot_option: opt, platform: 'whatsapp', is_preactivated: true)
    end

    context 'when customer active bot is preactivated and is text' do
      subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, text, nil) }

      it {
        expect { custom_class.manage_chat_bot }.to change {
                                                     customer.active_bots.first.reload.is_preactivated
                                                   }.from(true).to(false)
      }
    end

    context 'when customer active bot is preactivated and is media' do
      subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, nil, media) }

      it {
        expect { custom_class.manage_chat_bot }.to change {
                                                     customer.active_bots.first.reload.is_preactivated
                                                   }.from(true).to(false)
      }
    end
  end

  describe '#update_customer_flow' do
    context 'when @selected is not present' do
      it 'does not update customer flow' do
        msg_spy = instance_spy(Customer)
        expect(msg_spy).not_to have_received(:update)

        my_class.update_customer_flow
      end
    end
  end

  describe '#activate_for_first_time' do
    context 'when only_new_customers of chat_bot is false' do
      let(:instance) { build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer) }

      it 'return false' do
        chat_bot.only_new_customers = false
        expect(my_class.activate_for_first_time?(instance.before_last_message)).to be(false)
      end
    end

    context 'when reactivate_after of chat_bot is nil' do
      let!(:msj) { create_list(:gupshup_whatsapp_message, 3, :inbound, customer: customer, retailer: retailer) }
      let(:instance) { build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer) }

      it 'return false' do
        chat_bot.only_new_customers = true
        chat_bot.reactivate_after = nil

        expect(my_class.activate_for_first_time?(instance.before_last_message)).to be(false)
      end
    end

    context 'when reactivate_after of chat_bot is not nil' do
      subject(:custom_class) { described_class.new(customer, chat_bot, instance, text, media) }

      let!(:msj) do
        create_list(:gupshup_whatsapp_message, 3, :inbound, customer: customer, retailer: retailer,
                                                            created_at: 2.hours.ago)
      end
      let(:instance) do
        create(:gupshup_whatsapp_message, :inbound, :created_now, customer: customer, retailer: retailer)
      end

      it 'return false' do
        chat_bot.only_new_customers = true
        chat_bot.reactivate_after = 1

        expect(custom_class.activate_for_first_time?(instance.before_last_message)).to be(true)
      end
    end
  end

  describe '#save_customer_option' do
    context 'when @selected is not present' do
      it { expect { my_class.save_customer_option }.not_to change(CustomerBotOption, :count) }
    end
  end

  describe '#execute_actions' do
    context 'when chat_bot_option is nil' do
      it 'returns nil' do
        expect(my_class.execute_actions(nil)).to be_nil
      end
    end

    context 'when chat_bot_option does not have actions' do
      let(:no_actions) { create(:chat_bot_option, chat_bot: chat_bot) }

      it 'returns nil' do
        expect(my_class.execute_actions(no_actions)).to be_nil
      end
    end

    context 'when chat_bot_option has chat_bot_actions' do
      context 'with action type is add_tag' do
        let!(:action) { create(:chat_bot_action, :add_tag, chat_bot_option: chat_bot_option) }

        before do
          allow(ChatBots::Actions::AddCustomerTag).to receive(:call).and_return(true)
        end

        it 'calls the service' do
          expect(ChatBots::Actions::AddCustomerTag).to receive(:call)
          my_class.execute_actions(chat_bot_option)
        end
      end

      context 'with action type is assign_agent' do
        let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
        let!(:action) do
          create(:chat_bot_action, :assign_agent, chat_bot_option: chat_bot_option, retailer_user: retailer_user)
        end

        before do
          allow(ChatBots::Actions::AssignCustomerAgent).to receive(:call).and_return(true)
        end

        it 'calls the service' do
          expect(ChatBots::Actions::AssignCustomerAgent).to receive(:call)
          my_class.execute_actions(chat_bot_option)
        end
      end

      context 'with action type is get_out_bot' do
        let!(:action) do
          create(:chat_bot_action, chat_bot_option: chat_bot_option)
        end

        before do
          allow(ChatBots::Actions::ExitBot).to receive(:call).and_return(true)
        end

        it 'calls the service' do
          expect(ChatBots::Actions::ExitBot).to receive(:call)
          my_class.execute_actions(chat_bot_option)
        end
      end

      context 'with action type is save_on_db' do
        subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, text, false) }

        let!(:opt1) { create(:chat_bot_option, text: 'node 1', parent: chat_bot_option, option_type: :decision) }
        let!(:action) do
          create(:chat_bot_action, :save_on_db, chat_bot_option: opt1, target_field: 'address')
        end

        before do
          allow(ChatBots::Actions::SaveCustomerData).to receive(:call).and_return(true)
        end

        it 'calls the service' do
          expect(ChatBots::Actions::SaveCustomerData).to receive(:call)
          custom_class.execute_actions(opt1)
        end
      end

      context 'with action type is assign_team' do
        let!(:team_assignment) { create(:team_assignment, retailer: retailer) }
        let!(:opt1) { create(:chat_bot_option, text: 'node 1', parent: chat_bot_option) }
        let!(:action) do
          create(:chat_bot_action, :assign_team, chat_bot_option: opt1, team_assignment: team_assignment)
        end

        before do
          allow(ChatBots::Actions::AssignCustomerTeam).to receive(:call).and_return(true)
        end

        it 'calls the service' do
          expect(ChatBots::Actions::AssignCustomerTeam).to receive(:call)
          my_class.execute_actions(opt1)
        end
      end

      context 'with action_type equals to jump_to_option' do
        context 'with an empty jump option' do
          let!(:opt1) { create(:chat_bot_option, text: 'node 1', parent: chat_bot_option) }
          let!(:action) { create(:chat_bot_action, :jump_to_option, chat_bot_option: opt1) }

          before do
            customer.update(chat_bot_option: opt1)
          end

          it 'return false' do
            my_class.execute_actions(opt1)

            expect(customer.chat_bot_option_id).to eq(opt1.id)
          end
        end

        context 'with a jump option' do
          let!(:opt1) { create(:chat_bot_option, text: 'node 1', parent: chat_bot_option) }
          let!(:opt2) { create(:chat_bot_option, text: 'node 2', parent: chat_bot_option) }
          let!(:action) { create(:chat_bot_action, :jump_to_option, chat_bot_option: opt1, jump_option: opt2) }

          before do
            ChatBots::CustomerFlowUpdater.call(customer, selected: opt1.id, platform: :whatsapp)
            allow(origin_instance).to receive(:send_answer).and_return(true)
          end

          it 'return false' do
            my_class.execute_actions(opt1)

            expect(ChatBots::CurrentOptionFinder.call(customer: customer, platform: :whatsapp).id).to eq(opt2.id)
          end
        end

        context 'with a skipped jump option' do
          let!(:opt1) { create(:chat_bot_option, text: 'node 1', parent: chat_bot_option) }
          let!(:opt2) { create(:chat_bot_option, :form, parent: chat_bot_option, text: 'node 2', skip_option: true) }
          let!(:opt3) { create(:chat_bot_option, text: 'node 3', parent: opt2) }
          let!(:action) { create(:chat_bot_action, :jump_to_option, chat_bot_option: opt1, jump_option: opt2) }
          let!(:action2) { create(:chat_bot_action, :save_on_db, chat_bot_option: opt2, target_field: 'first_name') }

          before do
            ChatBots::CustomerFlowUpdater.call(customer, selected: opt1.id, platform: :whatsapp)
            allow(origin_instance).to receive(:send_answer).and_return(true)
          end

          it 'return false' do
            my_class.execute_actions(opt1)

            expect(ChatBots::CurrentOptionFinder.call(customer: customer, platform: :whatsapp).id).to eq(opt3.id)
          end
        end
      end

      context 'when action type is ctx_notify' do
        let!(:action) do
          create(:chat_bot_action, :ctx_notify, chat_bot_option: chat_bot_option)
        end

        before do
          allow(ChatBots::Actions::NotifyGoalReached).to receive(:call).and_return(true)
        end

        it 'calls the service' do
          expect(ChatBots::Actions::NotifyGoalReached).to receive(:call)
          my_class.execute_actions(chat_bot_option)
        end
      end

      context 'with action type is create_deal' do
        let!(:opt1) { create(:chat_bot_option, text: 'node 1', parent: chat_bot_option) }
        let(:funnel) { create(:funnel, retailer: retailer) }
        let(:funnel_step) { create(:funnel_step, funnel: funnel) }

        let!(:action) do
          create(:chat_bot_action, :create_deal, chat_bot_option: opt1, funnel_step_id: funnel_step.id)
        end

        before do
          allow(ChatBots::Actions::CreateDeal).to receive(:call).and_return(true)
        end

        it 'calls the service' do
          expect(ChatBots::Actions::CreateDeal).to receive(:call)
          my_class.execute_actions(opt1)
        end
      end
    end
  end

  describe '#match_option' do
    context 'when find_by_position is not blank' do
      subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, '1', false) }

      let!(:opt1) { create(:chat_bot_option, chat_bot: chat_bot) }
      let!(:child1) { create(:chat_bot_option, chat_bot: chat_bot, text: 'opt 1', parent: opt1) }
      let!(:child2) { create(:chat_bot_option, chat_bot: chat_bot, text: 'opt 2', parent: opt1) }

      it 'return pick_option' do
        ChatBots::CustomerFlowUpdater.call(customer, selected: opt1.id, platform: :whatsapp)
        custom_class.manage_chat_bot
        expect(custom_class.match_option('1')).to eq(child1)
      end
    end

    context 'when find_by_position is not blank and indexes is one' do
      subject(:custom_class) { described_class.new(customer, chat_bot, origin_instance, '1', false) }

      let!(:opt1) { create(:chat_bot_option, chat_bot: chat_bot) }
      let!(:child1) { create(:chat_bot_option, chat_bot: chat_bot, text: 'new 1', parent: opt1) }
      let!(:child2) { create(:chat_bot_option, chat_bot: chat_bot, text: 'opt 2', parent: opt1) }

      it 'return pick_option' do
        ChatBots::CustomerFlowUpdater.call(customer, selected: opt1.id, platform: :whatsapp)
        custom_class.manage_chat_bot
        custom_class.match_option('opt')

        expect(custom_class.match_option('opt')).to eq(child2)
      end
    end
  end

  describe '#time_to_reactivate?' do
    let(:chat_bot) { create(:chat_bot, :for_whatsapp, retailer: retailer, reactivate_after: reactivate_after) }
    let!(:previous_message) do
      create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer, created_at: 1.hour.ago)
    end
    let(:origin_instance) do
      create(:gupshup_whatsapp_message, :inbound, :created_now, customer: customer, retailer: retailer)
    end

    context 'when chat_bot reactivate_after is nil' do
      let(:reactivate_after) { nil }

      it { expect(my_class.time_to_reactivate?).to be_falsey }
    end

    context 'when chat_bot reactivate_after is not nil' do
      let(:reactivate_after) { 0.5 }
      let!(:before_last_message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer, created_at: created_at)
      end

      before { allow(Customer::ChatBotDeactivator).to receive(:call) }

      context 'when before_last_message was sent after chatbot reactivate_after time' do
        let(:created_at) { 31.minutes.ago }

        it { expect(my_class.time_to_reactivate?).to be_truthy }

        it 'calls Customer::ChatBotDeactivator class' do
          my_class.time_to_reactivate?
          expect(Customer::ChatBotDeactivator).to have_received(:call)
        end
      end

      context 'when before_last_message was sent before chatbot reactivate_after time' do
        let(:created_at) { 29.minutes.ago }

        it { expect(my_class.time_to_reactivate?).to be_falsey }
      end
    end
  end

  describe '#manage_skip_option' do
    context 'when option is not present' do
      it 'deactivates chat bot and returns' do
        expect(Customer::ChatBotDeactivator).to receive(:call)

        my_class.manage_skip_option(nil)
      end
    end

    context 'when option is a jump_to option' do
      let!(:opt1) { create(:chat_bot_option, :jump_to, text: 'node 1', parent: chat_bot_option) }

      it 'executes actions and returns' do
        expect_any_instance_of(described_class).to receive(:execute_actions).with(opt1)

        my_class.manage_skip_option(opt1)
      end
    end

    context 'when option is a decision option or skip_option is false or not_existing_option_content?' do
      it 'updates customer, sends message and returns' do
        expect(ChatBots::CustomerFlowUpdater).to receive(:call)

        my_class.manage_skip_option(chat_bot_option)
      end
    end

    context 'when option is not a decision option and skip_option is true and has valid option content' do
      let!(:opt1) { create(:chat_bot_option, :form, text: 'node 1', parent: chat_bot_option, skip_option: true) }
      let!(:opt2) { create(:chat_bot_option, parent: opt1, text: 'node 2') }
      let!(:action) { create(:chat_bot_action, :save_on_db, chat_bot_option: opt1, target_field: 'first_name') }

      it 'calls manage_skip_option with the first active child option' do
        my_class.manage_skip_option(opt1)

        expect(ChatBots::CurrentOptionFinder.call(customer: customer, platform: :whatsapp).id).to eq(opt2.id)
      end
    end
  end

  describe '#skip_this_option?' do
    subject { described_class.new(customer, chat_bot, origin_instance, 'text', false) }

    let(:option) { double('Option', form?: form, skip_option: skip_option) }
    let(:sent_in_action) { '' }

    context 'when option is nil' do
      it { expect(my_class.skip_this_option?(nil)).to be_falsey }
    end

    context 'when option is not a form' do
      let(:form) { false }
      let(:skip_option) { true }

      it { expect(my_class.skip_this_option?(option)).to be_falsey }
    end

    context 'when option is a form but skip_option is false' do
      let(:form) { true }
      let(:skip_option) { false }

      it { expect(my_class.skip_this_option?(option)).to be_falsey }
    end

    context 'when option is a form, skip_option is true, and sent_in_action is blank' do
      let(:form) { true }
      let(:skip_option) { true }

      it { expect(my_class.skip_this_option?(option)).to be_truthy }
    end
  end

  describe '#manage_failed_attempts' do
    let(:process_chatbot) { described_class.new(customer, chat_bot, origin_instance, text, media) }
    let(:current_option) { create(:chat_bot_option, :decision, chat_bot: chat_bot) }

    context 'when failed attempts limit is reached' do
      let!(:failure_option) { create(:chat_bot_option, :failure, :fake_option, parent: current_option) }

      context 'when current_option has a failure option with a child' do
        let!(:fail_child) { create(:chat_bot_option, parent: failure_option) }

        before do
          allow(process_chatbot).to receive(:reached_failed_attempts).and_return(true)
          process_chatbot.instance_variable_set(:@current_option, current_option)
        end

        it 'returns the first failure option' do
          expect(process_chatbot.manage_failed_attempts).to eq(fail_child)
        end
      end

      context 'when current_option has a failure option without a child' do
        before do
          allow(process_chatbot).to receive(:reached_failed_attempts).and_return(true)
          allow(Customer::ChatBotDeactivator).to receive(:call).and_return(true)
          process_chatbot.instance_variable_set(:@current_option, current_option)
        end

        it 'calls to deactivate bot' do
          expect(Customer::ChatBotDeactivator).to receive(:call)
          expect(process_chatbot.manage_failed_attempts).to be_nil
        end
      end
    end

    context 'when failed attempts limit is not reached yet' do
      before do
        allow(process_chatbot).to receive(:reached_failed_attempts).and_return(false)
        allow(ChatBots::FailedAttemptsUpdater).to receive(:call).with(customer, platform: anything)
        process_chatbot.instance_variable_set(:@current_option, current_option)
      end

      it 'updates failed attempts' do
        expect(ChatBots::FailedAttemptsUpdater).to receive(:call).with(customer, platform: anything)
        process_chatbot.manage_failed_attempts
      end

      context 'when current_option does not have failure text' do
        it 'returns false' do
          expect(message_process).not_to receive(:send_message)
          expect(process_chatbot.manage_failed_attempts).to be_falsey
        end
      end

      context 'when current_option has a failure text' do
        before do
          current_option.update(failure_text: 'Hola')
        end

        it 'sends a message' do
          expect(message_process).to receive(:send_message)
          process_chatbot.manage_failed_attempts
        end
      end
    end
  end

  describe '#jump_to_chat_bot_option' do
    let(:process_chatbot) { described_class.new(customer, chat_bot, origin_instance, text, media) }

    before do
      allow(ChatBots::CustomerFlowUpdater).to receive(:call).and_return(true)
      allow(message_process).to receive(:send_message).and_return(true)
    end

    context 'when action does not have the jump option' do
      let(:action) { create(:chat_bot_action, :jump_to_option, chat_bot_option: chat_bot_option) }

      it 'returns nil' do
        expect(process_chatbot.jump_to_chat_bot_option(action, chat_bot_option)).to be_nil
      end
    end

    context 'when action has the jump option' do
      let(:jump) { create(:chat_bot_option, chat_bot: chat_bot) }
      let(:chat_bot_option) { create(:chat_bot_option, :jump_to, chat_bot: chat_bot, text: 'main option') }
      let(:action) { create(:chat_bot_action, :jump_to_option, chat_bot_option: chat_bot_option, jump_option: jump) }
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }
      let(:jump_option) { create(:chat_bot_option, :decision, chat_bot: chat_bot) }

      context 'when the step is skipped' do
        before do
          allow(process_chatbot).to receive_messages(skip_this_option?: true, manage_skip_option: false)
        end

        it 'returns nil' do
          expect(process_chatbot).to receive(:manage_skip_option)
          expect(process_chatbot.jump_to_chat_bot_option(action, chat_bot_option)).to be_nil
        end
      end

      context 'when the step is not skipped' do
        before do
          allow(process_chatbot).to receive(:skip_this_option?).and_return(false)
        end

        it 'calls the flow updater' do
          expect(ChatBots::CustomerFlowUpdater).to receive(:call)
          expect(message_process).to receive(:send_message)
          expect(process_chatbot).to receive(:execute_actions)

          process_chatbot.jump_to_chat_bot_option(action, chat_bot_option)
        end
      end

      context 'when it jumps to anothed option' do
        before do
          allow(action).to receive(:jump_option).and_return(jump_option)
          allow(process_chatbot).to receive(:skip_this_option?).and_return(false)
          allow(process_chatbot).to receive(:manage_skip_option)
          allow(ChatBots::CustomerFlowUpdater).to receive(:call)
          allow_any_instance_of(ChatBots::V2::Message).to receive(:send_message).and_return(true)
          allow(process_chatbot).to receive(:execute_actions)
        end

        context 'when jump_option is nil' do
          let(:jump_option) { nil }

          it 'does not call manage_skip_option' do
            process_chatbot.jump_to_chat_bot_option(action, option)
            expect(process_chatbot).not_to have_received(:manage_skip_option)
          end
        end

        context 'when jump_option should be skipped' do
          before do
            allow(process_chatbot).to receive(:skip_this_option?).and_return(true)
          end

          it 'calls manage_skip_option' do
            process_chatbot.jump_to_chat_bot_option(action, option)
            expect(process_chatbot).to have_received(:manage_skip_option).with(jump_option, option)
          end
        end

        context 'when jump_option should not be skipped nor has exit bot action' do
          it 'calls ChatBots::CustomerFlowUpdater' do
            process_chatbot.jump_to_chat_bot_option(action, option)
            expect(ChatBots::CustomerFlowUpdater).to have_received(:call)
          end

          it 'calls execute_actions' do
            process_chatbot.jump_to_chat_bot_option(action, option)
            expect(process_chatbot).to have_received(:execute_actions).with(jump_option)
          end
        end
      end
    end
  end

  describe '#execute_step' do
    let(:process_chatbot) { described_class.new(customer, chat_bot, origin_instance, text, media) }
    let(:selected_option) { create(:chat_bot_option, :jump_to, chat_bot: chat_bot) }
    let(:message_service) { instance_double(ChatBots::V2::Message, send_message: true, send_text: true) }

    before do
      allow(process_chatbot).to receive(:execute_actions)
      allow(process_chatbot).to receive(:message_service).and_return(message_service)
      allow(message_service).to receive(:send_message)
      allow(message_service).to receive(:send_text)
    end

    context 'when selected option is active_mia' do
      let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
      let!(:chatbot_integration) { create(:mia_integration, mia_integration_type:, retailer:) }
      let(:mia_platform) { create(:mia_platform) }
      let(:active_mia_option) { create(:chat_bot_option, :active_mia, chat_bot:) }

      before { process_chatbot.instance_variable_set(:@selected, active_mia_option) }

      context 'when retailer has mia chatbot active' do
        let(:retailer) do
          create(
            :retailer,
            :gupshup_integrated,
            welcome_message_sent: true,
            mia_chatbot_active: true,
            retailer_mia_platforms_attributes: [mia_platform_id: mia_platform.id]
          )
        end

        it 'enqueues job' do
          expect { process_chatbot.send(:execute_step) }
            .to have_enqueued_job(Mia::Chatbot::WhatsappJob)
            .with(customer.id, 'text', nil)
        end

        it do
          expect do
            process_chatbot.send(:execute_step)
          end.to change { customer.reload.customer_mia_chatbot_for('whatsapp').active }.from(false).to(true)
        end

        it { expect { process_chatbot.send(:execute_step) }.to change(CustomerMessageJob, :count) }
      end

      context 'when retailer has no mia chatbot active' do
        let(:retailer) { create(:retailer, :gupshup_integrated, welcome_message_sent: true) }

        it { expect { process_chatbot.send(:execute_step) }.not_to have_enqueued_job(Mia::Chatbot::WhatsappJob) }
        it { expect { process_chatbot.send(:execute_step) }.not_to change(customer, :allow_mia_chatbot) }
      end
    end

    context 'when selected option has jump_to attribute' do
      it 'executes actions for the selected option' do
        process_chatbot.instance_variable_set(:@selected, selected_option)

        process_chatbot.send(:execute_step)

        expect(process_chatbot).to have_received(:execute_actions).with(selected_option)
      end
    end

    context 'when selected option does not have jump_to attribute' do
      it 'does not execute actions for the selected option' do
        non_jump_option = create(:chat_bot_option, :form, chat_bot: chat_bot)
        process_chatbot.instance_variable_set(:@selected, non_jump_option)

        process_chatbot.send(:execute_step)

        expect(process_chatbot).not_to have_received(:execute_actions).with(non_jump_option)
      end
    end

    context 'when selected option is nil' do
      it 'does not execute actions for the selected option' do
        process_chatbot.instance_variable_set(:@selected, nil)

        process_chatbot.send(:execute_step)

        expect(process_chatbot).to have_received(:execute_actions).with(nil).once
      end
    end

    context 'when selected option should be skipped' do
      it 'manages skipping the selected option' do
        allow(process_chatbot).to receive(:skip_this_option?).and_return(true)
        allow(process_chatbot).to receive(:manage_skip_option)
        process_chatbot.instance_variable_set(:@selected, selected_option)

        process_chatbot.execute_step

        expect(process_chatbot).to have_received(:manage_skip_option).with(selected_option)
      end
    end

    context 'when selected option should not be skipped' do
      it 'does not manage skipping the selected option' do
        allow(process_chatbot).to receive(:skip_this_option?).and_return(false)
        allow(process_chatbot).to receive(:manage_skip_option)
        process_chatbot.instance_variable_set(:@selected, selected_option)

        process_chatbot.execute_step

        expect(process_chatbot).not_to have_received(:manage_skip_option)
      end
    end

    context 'when sent_in_action is false' do
      let(:selected_option) { create(:chat_bot_option, :decision, chat_bot: chat_bot) }

      it 'sends a message for the selected option' do
        process_chatbot.instance_variable_set(:@sent_in_action, false)
        process_chatbot.instance_variable_set(:@selected, selected_option)

        process_chatbot.execute_step

        expect(message_service).to have_received(:send_message)
      end
    end

    context 'when sent_in_action is true' do
      let(:selected_option) { create(:chat_bot_option, :decision, chat_bot: chat_bot) }

      it 'does not send a message for the selected option' do
        process_chatbot.instance_variable_set(:@sent_in_action, true)
        process_chatbot.instance_variable_set(:@selected, selected_option)

        process_chatbot.execute_step

        expect(message_service).not_to have_received(:send_message)
      end
    end

    context 'when selected option is a decision and current option is a form' do
      let(:current_option) { create(:chat_bot_option, :form, chat_bot: chat_bot) }
      let(:selected_option) { create(:chat_bot_option, :decision, chat_bot: chat_bot) }

      it 'executes actions for the selected option' do
        process_chatbot.instance_variable_set(:@selected, selected_option)
        process_chatbot.instance_variable_set(:@current_option, current_option)

        process_chatbot.execute_step

        expect(process_chatbot).to have_received(:execute_actions).with(selected_option).at_least(:once)
      end
    end

    context 'when selected option is not a decision or current option is not a form' do
      let(:current_option) { create(:chat_bot_option, :decision, chat_bot: chat_bot) }
      let(:selected_option) { create(:chat_bot_option, :message, chat_bot: chat_bot) }

      it 'executes actions for the selected option' do
        process_chatbot.instance_variable_set(:@selected, selected_option)
        process_chatbot.instance_variable_set(:@current_option, current_option)

        process_chatbot.execute_step

        expect(process_chatbot).to have_received(:execute_actions)
      end
    end
  end

  # rubocop:disable RSpec/ReceiveMessages
  describe '#activate_after_outbound?' do
    let(:origin_instance) { instance_double(GupshupWhatsappMessage, customer: customer) }

    before do
      # rubocop:disable RSpec/SubjectStub
      allow(subject).to receive(:outbound_stale_time_elapsed?).and_return(true)
      subject.instance_variable_set(:@chat_bot, chat_bot)
      subject.instance_variable_set(:@origin_instance, origin_instance)
      # rubocop:enable RSpec/SubjectStub
    end

    context 'when outbound is not enabled' do
      before do
        allow(chat_bot).to receive(:outbound_enabled).and_return(false)
      end

      it 'returns false' do
        expect(subject.send(:activate_after_outbound?)).to be(false)
      end
    end

    context 'when outbound is enabled but last outbound is nil' do
      before do
        allow(chat_bot).to receive(:outbound_enabled).and_return(true)
        allow(origin_instance).to receive(:last_outbound).and_return(nil)
      end

      it 'returns true' do
        expect(subject.send(:activate_after_outbound?)).to be(true)
      end
    end

    context 'when outbound is enabled, last outbound is not nil but response waiting time is nil' do
      before do
        allow(chat_bot).to receive(:outbound_enabled).and_return(true)
        allow(origin_instance).to receive(:last_outbound).and_return(true)
        allow(chat_bot).to receive(:response_waiting_time_h).and_return(nil)
      end

      it 'returns false' do
        expect(subject.send(:activate_after_outbound?)).to be(false)
      end
    end

    context 'when outbound is enabled, last outbound and response waiting time are not nil' do
      let!(:old_outbound_message) do
        create(:gupshup_whatsapp_message, :outbound, customer: customer, retailer: retailer, created_at: 2.hours.ago)
      end

      before do
        allow(chat_bot).to receive(:outbound_enabled).and_return(true)
        allow(chat_bot).to receive(:reactivate_after).and_return(1) # Add reactivate_after
        allow(origin_instance).to receive(:last_outbound).and_return(old_outbound_message)
        allow(chat_bot).to receive(:response_waiting_time_h).and_return(1)
      end

      it 'returns the result of outbound_stale_time_elapsed?' do
        expect(subject.send(:activate_after_outbound?)).to be(true)
      end
    end
  end
  # rubocop:enable RSpec/ReceiveMessages

  describe '#match_dynamic_list' do
    let(:ancestor_api) { instance_double(ChatBotOption, id: 1) }
    let(:response_data) do
      { 'data' => [{ 'title' => 'Option 1', 'value' => 'Value 1' }, { 'title' => 'Option 2', 'value' => 'Value 2' }] }
    end
    let(:response) { instance_double(CustomerBotResponse, response: response_data) }
    let(:form_options) do
      instance_double(ChatBotOptionForm, data_source: 'data', title_source: 'data.title', value_source: 'data.value')
    end
    let(:current_option) do
      instance_double(ChatBotOption, id: 1, closest_api_ancestor: ancestor_api, chat_bot_option_form: form_options)
    end

    before do
      subject.instance_variable_set(:@current_option, current_option)
      allow(customer.customer_bot_responses).to receive(:where).and_return([response])
      allow(response).to receive(:response).and_return(response_data)
      allow(current_option).to receive_messages(closest_api_ancestor: ancestor_api, chat_bot_option_form: form_options)
    end

    context 'when ancestor_api is nil' do
      before { allow(current_option).to receive(:closest_api_ancestor).and_return(nil) }

      it 'returns false' do
        expect(subject.match_dynamic_list('1')).to be_falsey
      end
    end

    context 'when response is blank' do
      before { allow(customer.customer_bot_responses).to receive(:where).and_return([]) }

      it 'returns false' do
        expect(subject.match_dynamic_list('1')).to be_falsey
      end
    end

    context 'when form_options is blank' do
      before { allow(current_option).to receive(:chat_bot_option_form).and_return(nil) }

      it 'returns false' do
        expect(subject.match_dynamic_list('1')).to be_falsey
      end
    end

    context 'when data is blank' do
      before { allow(response).to receive(:response).and_return({}) }

      it 'returns false' do
        expect(subject.match_dynamic_list('1')).to be_falsey
      end
    end

    context 'when text matches a position in the data' do
      it 'sets the selected value and returns the first active option' do
        allow(current_option).to receive(:first_active_option).and_return('active_option')
        expect(subject.match_dynamic_list('1')).to eq('active_option')
        expect(subject.instance_variable_get(:@selected_value)).to eq('Value 1')
      end
    end

    context 'when text matches a title or value in the data' do
      it 'sets the selected value and returns the first active option' do
        allow(current_option).to receive(:first_active_option).and_return('active_option')
        expect(subject.match_dynamic_list('Option 2')).to eq('active_option')
        expect(subject.instance_variable_get(:@selected_value)).to eq('Value 2')
      end
    end

    # rubocop:disable RSpec/SubjectStub
    context 'when text does not match any option' do
      it 'calls manage_failed_attempts and returns false' do
        allow(subject).to receive(:manage_failed_attempts)
        expect(subject.match_dynamic_list('Invalid')).to be_falsey
        expect(subject).to have_received(:manage_failed_attempts)
      end
    end
    # rubocop:enable RSpec/SubjectStub
  end

  describe '#execute_api_step' do
    let(:chat_bot_option) { create(:chat_bot_option, :api, chat_bot: chat_bot) }
    let(:api_options) { create(:chat_bot_option_api, chat_bot_option: chat_bot_option) }
    let(:api_response) { Interactor::Context.new(success: true) }
    let(:child_option) { instance_double(ChatBotOption, id: 1) }

    before do
      subject.instance_variable_set(:@selected, chat_bot_option)
      allow(chat_bot_option).to receive_messages(chat_bot_option_api: api_options, first_active_option: child_option)
      allow(ChatBots::ApiOption::Organizer).to receive(:call).and_return(api_response)
      allow(chat_bot_option).to receive_message_chain(:failure_option, :first_active_option).and_return(child_option)
      allow(origin_instance).to receive(:send_message_step)
    end

    context 'when @selected is blank and chat_bot_option is blank as well' do
      before do
        subject.instance_variable_set(:@selected, nil)
      end

      it 'does not call the API organizer' do
        subject.execute_api_step
        expect(ChatBots::ApiOption::Organizer).not_to have_received(:call)
      end
    end

    context 'when @selected is blank but chat_bot_option is present' do
      before do
        subject.instance_variable_set(:@selected, nil)
        allow(ChatBots::CustomerFlowUpdater).to receive(:call).and_return(true)
      end

      it 'calls the API organizer' do
        subject.execute_api_step(chat_bot_option)
        expect(ChatBots::ApiOption::Organizer).to have_received(:call)
      end
    end

    context 'when api_options is blank' do
      before { allow(chat_bot_option).to receive(:chat_bot_option_api).and_return(nil) }

      it 'does not call the API organizer' do
        subject.execute_api_step
        expect(ChatBots::ApiOption::Organizer).not_to have_received(:call)
      end
    end

    context 'when API response is successful' do
      let(:success) { true }

      before do
        allow(ChatBots::CustomerFlowUpdater).to receive(:call).and_return(success)
      end

      it 'sets @selected to the first active child option and sends the message step' do
        subject.execute_api_step
        expect(chat_bot_option).to have_received(:first_active_option)
        expect(origin_instance).to have_received(:send_message_step).with(child_option)
      end
    end

    context 'when API response is not successful' do
      before do
        allow_any_instance_of(Interactor::Context).to receive(:success?).and_return(false)
      end

      it 'sets @selected to the first active failure option and sends the message step' do
        subject.execute_api_step
        expect(origin_instance).to have_received(:send_message_step).with(child_option)
      end
    end

    # rubocop:disable RSpec/SubjectStub
    context 'when selected\'s child is blank' do
      before do
        allow(subject).to receive(:finish_bot!).and_return(true)
        allow(chat_bot_option).to receive(:first_active_option).and_return(nil)
      end

      it 'sets @selected to the first active failure option and sends the message step' do
        subject.execute_api_step
        expect(subject).to have_received(:finish_bot!)
      end
    end
    # rubocop:enable RSpec/SubjectStub
  end
end
