require 'rails_helper'

RSpec.describe ChatBots::V2::<PERSON><PERSON><PERSON><PERSON> do
  subject(:my_class) { described_class.new(customer, chat_bot_option, platform: platform) }

  let(:retailer) { create(:retailer) }
  let(:customer) { create(:customer, retailer: retailer, first_name: '<PERSON><PERSON>', last_name: '<PERSON><PERSON>') }
  let(:chat_bot) { create(:chat_bot, retailer: retailer) }
  let(:chat_bot_option) { create(:chat_bot_option, chat_bot: chat_bot, failure_text: 'failed message') }
  let(:platform) { :whatsapp }

  describe '#initialize' do
    it 'sets instance variables correctly when chat_bot_option is provided' do
      expect(my_class.instance_variable_get(:@customer)).to eq(customer)
      expect(my_class.instance_variable_get(:@retailer)).to eq(customer.retailer)
      expect(my_class.instance_variable_get(:@chat_bot_option)).to eq(chat_bot_option)
      expect(my_class.instance_variable_get(:@chat_bot)).to eq(chat_bot_option.chat_bot)
      expect(my_class.instance_variable_get(:@platform)).to eq(platform)
    end

    it 'sets instance variables correctly when chat_bot_option is nil' do
      my_class2 = described_class.new(customer, nil, platform: platform)

      expect(my_class2.instance_variable_get(:@customer)).to eq(customer)
      expect(my_class2.instance_variable_get(:@retailer)).to eq(customer.retailer)
      expect(my_class2.instance_variable_get(:@chat_bot_option)).to be_nil
      expect(my_class2.instance_variable_get(:@chat_bot)).to be_nil
      expect(my_class2.instance_variable_get(:@platform)).to eq(platform)
    end

    it 'sets platform to nil when not provided' do
      my_class3 = described_class.new(customer, chat_bot_option)

      expect(my_class3.instance_variable_get(:@platform)).to be_nil
    end
  end

  describe '#get_message' do
    describe 'if get_out, error_exit and failed_attempt params is false' do
      context 'when message is only text' do
        it 'return the same message' do
          chat_bot_option.answer = 'flat message'
          expect(my_class.get_message(false, false, false)).to eq('flat message')
        end
      end

      context 'when message has variables' do
        it 'return replaced message' do
          chat_bot_option.answer = 'Hello, {{first_name}} {{last_name}}'
          expect(my_class.get_message(false, false, false)).to eq('Hello, Jhon Doe')
        end
      end
    end

    describe 'failed_attempt params is true' do
      context 'when chatbot on_failed_attempt is send_attempt_message' do
        it 'return chatbot on_failed_attempt_message' do
          expect(my_class.get_message(false, false, true)).to eq('failed message')
        end
      end

      context 'when chatbot has not on_failed_attempt' do
        before do
          chat_bot_option.failure_text = nil
        end

        it 'return empty string' do
          expect(my_class.get_message(false, false, true)).to eq('')
        end
      end
    end

    describe 'get_out params is true and failed_attempt is false' do
      context 'when chatbot option has get out action and action has exit message' do
        let!(:chat_bot_action) do
          create(:chat_bot_action, chat_bot_option: chat_bot_option, action_type: :get_out_bot, exit_message: 'exit')
        end

        it 'return exit message of the action' do
          expect(my_class.get_message(true, false, false)).to eq('exit')
        end
      end

      context 'when chatbot option has get out action and action has not exit message' do
        let!(:chat_bot_action) do
          create(:chat_bot_action, chat_bot_option: chat_bot_option, action_type: :get_out_bot, exit_message: '')
        end

        it 'return empty string' do
          expect(my_class.get_message(true, false, false)).to eq('')
        end
      end

      context 'when chatbot option has not actions and chatbot has enabled goodbye message with goodbye message' do
        it 'return goodbye message' do
          chat_bot.enabled_goodbye_message = true
          chat_bot.goodbye_message = 'goodbye message'

          expect(my_class.get_message(true, false, false)).to eq('goodbye message')
        end
      end

      context 'when chatbot option has not actions and chatbot has not enabled goodbye message' do
        it 'return empty string' do
          chat_bot.enabled_goodbye_message = false

          expect(my_class.get_message(true, false, false)).to eq('')
        end
      end
    end

    context 'when error_exit params is true and get_out is false and failed_attempt is false' do
      it 'return chat bot error message' do
        chat_bot.error_message = 'error for failed attempts'

        my_class = described_class.new(customer, chat_bot_option, platform: platform)
        expect(my_class.get_message(false, true, false)).to eq('error for failed attempts')
      end
    end
  end

  describe '#replace_message_variables' do
    context 'when message has no variables' do
      it 'returns the original message' do
        message = 'Hello world!'
        expect(my_class.replace_message_variables(message)).to eq('Hello world!')
      end
    end

    context 'when message has variables' do
      before do
        allow(ChatBots::Options::VariableValueMapper).to receive(:call).and_return('John')
      end

      it 'replaces variables with their values' do
        message = 'Hello {{first_name}}!'
        expect(my_class.replace_message_variables(message)).to eq('Hello John!')
      end

      it 'handles multiple variables' do
        allow(ChatBots::Options::VariableValueMapper).to receive(:call).with(
          variable: 'first_name', customer: customer, platform: platform, api_options: nil
        ).and_return('John')
        allow(ChatBots::Options::VariableValueMapper).to receive(:call).with(
          variable: 'last_name', customer: customer, platform: platform, api_options: nil
        ).and_return('Doe')

        message = 'Hello {{first_name}} {{last_name}}!'
        expect(my_class.replace_message_variables(message)).to eq('Hello John Doe!')
      end

      it 'calls VariableValueMapper with correct parameters' do
        message = 'Hello {{first_name}}!'
        my_class.replace_message_variables(message)

        expect(ChatBots::Options::VariableValueMapper).to have_received(:call).with(
          variable: 'first_name', customer: customer, platform: platform, api_options: nil
        )
      end
    end

    context 'when chat_bot_option has closest_api_ancestor' do
      let(:api_option) { create(:chat_bot_option, :api, chat_bot: chat_bot) }
      let(:api_config) { create(:chat_bot_option_api, chat_bot_option: api_option) }

      before do
        allow(chat_bot_option).to receive(:closest_api_ancestor).and_return(api_option)
        allow(api_option).to receive(:chat_bot_option_api).and_return(api_config)
        allow(ChatBots::Options::VariableValueMapper).to receive(:call).and_return('John')
      end

      it 'passes api_options to VariableValueMapper' do
        message = 'Hello {{first_name}}!'
        my_class.replace_message_variables(message)

        expect(ChatBots::Options::VariableValueMapper).to have_received(:call).with(
          variable: 'first_name', customer: customer, platform: platform, api_options: api_config
        )
      end
    end
  end

  describe '#map_variable_value' do
    let(:api_options) { double('api_options') }

    it 'calls VariableValueMapper with correct parameters' do
      allow(ChatBots::Options::VariableValueMapper).to receive(:call).and_return('mapped_value')

      result = my_class.map_variable_value('test_variable', api_options)

      expect(ChatBots::Options::VariableValueMapper).to have_received(:call).with(
        variable: 'test_variable', customer: customer, platform: platform, api_options: api_options
      )
      expect(result).to eq('mapped_value')
    end
  end

  describe '#failed_attempt_message' do
    context 'when failure_text is present' do
      it 'returns the failure_text' do
        chat_bot_option.failure_text = 'Custom failure message'
        expect(my_class.failed_attempt_message).to eq('Custom failure message')
      end
    end

    context 'when failure_text is blank' do
      it 'returns empty string' do
        chat_bot_option.failure_text = nil
        expect(my_class.failed_attempt_message).to eq('')
      end
    end
  end

  describe '#get_out_message' do
    context 'when chat_bot_option has get_out_bot action with exit message' do
      let!(:action) do
        create(:chat_bot_action, :get_out_bot, chat_bot_option: chat_bot_option, exit_message: 'Custom exit')
      end

      it 'returns the action exit message' do
        expect(my_class.get_out_message).to eq('Custom exit')
      end
    end

    context 'when chat_bot_option has get_out_bot action without exit message' do
      let!(:action) { create(:chat_bot_action, :get_out_bot, chat_bot_option: chat_bot_option, exit_message: '') }

      context 'when chat_bot has enabled goodbye message' do
        before do
          chat_bot.update(enabled_goodbye_message: true, goodbye_message: 'Goodbye!')
        end

        it 'returns the chat_bot goodbye message' do
          expect(my_class.get_out_message).to eq('Goodbye!')
        end
      end

      context 'when chat_bot does not have enabled goodbye message' do
        before do
          chat_bot.update(enabled_goodbye_message: false, goodbye_message: 'Goodbye!')
        end

        it 'returns empty string' do
          expect(my_class.get_out_message).to eq('')
        end
      end
    end

    context 'when chat_bot_option has no get_out_bot action' do
      context 'when chat_bot has enabled goodbye message' do
        before do
          chat_bot.update(enabled_goodbye_message: true, goodbye_message: 'General goodbye')
        end

        it 'returns the chat_bot goodbye message' do
          expect(my_class.get_out_message).to eq('General goodbye')
        end
      end

      context 'when chat_bot does not have enabled goodbye message' do
        before do
          chat_bot.update(enabled_goodbye_message: false)
        end

        it 'returns empty string' do
          expect(my_class.get_out_message).to eq('')
        end
      end
    end
  end

  describe '#error_exit_message' do
    it 'returns the chat_bot error message' do
      chat_bot.update(error_message: 'System error occurred')
      expect(my_class.error_exit_message).to eq('System error occurred')
    end

    it 'returns nil when error_message is not set' do
      chat_bot.update(error_message: nil)
      expect(my_class.error_exit_message).to be_nil
    end
  end

  describe '#search_variable_value' do
    context 'when from is distinct to variable' do
      it 'return data of customer_related_data' do
        expect(my_class.search_variable_value('options', 'other')).to be_nil
      end
    end

    context 'when dinamic field not exist' do
      it 'return same value' do
        expect(my_class.search_variable_value('Hello, Hello!')).to eq('Hello, Hello!')
      end
    end

    context 'when dinamic field exist but not exist customer_related_data' do
      let!(:crf) { create(:customer_related_field, retailer: retailer, name: 'first_name') }

      it 'return empty string' do
        expect(my_class.search_variable_value('first_name')).to eq('')
      end
    end

    context 'when dinamic field exist and customer_related_data exist' do
      let!(:crf) { create(:customer_related_field, retailer: retailer, name: 'first_name') }
      let!(:crd) do
        create(:customer_related_datum, customer_related_field: crf, customer: customer, data: 'Customer data')
      end

      it 'return data of customer_related_data' do
        expect(my_class.search_variable_value('first_name')).to eq('Customer data')
      end
    end

    context 'when dinamic field is list type' do
      let!(:crf) do
        create(:customer_related_field, retailer: retailer, name: 'options', field_type: :list,
                                        list_options: [{ key: 'one', value: 'Label one' }])
      end
      let!(:crd) do
        create(:customer_related_datum, customer_related_field: crf, customer: customer, data: 'Label one')
      end

      it 'return data of list options' do
        expect(my_class.search_variable_value('options')).to eq('Label one')
      end
    end
  end

  describe '#append_text_options' do
    context 'when the chatbot option is the decision type but do not have children' do
      it 'return same message' do
        chat_bot_option.option_type = :decision
        expect(my_class.append_text_options('Hello, Hello!')).to eq('Hello, Hello!')
      end
    end

    context 'when the chatbot option is the form type but do not have children' do
      it 'return same message' do
        chat_bot_option.option_type = :form
        expect(my_class.append_text_options('Hello, Hello!')).to eq('Hello, Hello!')
      end
    end

    context 'when the chatbot option is the decision type and have childrens' do
      let!(:child1) { create(:chat_bot_option, chat_bot: chat_bot, text: 'First option', parent: chat_bot_option) }
      let!(:child2) { create(:chat_bot_option, chat_bot: chat_bot, text: 'Second option', parent: chat_bot_option) }
      let!(:child3) do
        create(:chat_bot_option, chat_bot: chat_bot, text: 'third opt', parent: chat_bot_option, option_deleted: true)
      end

      before do
        chat_bot_option.option_type = :decision
      end

      context 'when message is present' do
        it 'return same message' do
          expect(my_class.append_text_options('Hello, ')).to eq("Hello, \n\n1. First option\n2. Second option")
        end
      end

      context 'when message is empty' do
        it 'return same message' do
          expect(my_class.append_text_options('')).to eq("1. First option\n2. Second option")
        end
      end
    end

    context 'when the chatbot option is the form type and have option_sub_lists' do
      let!(:opt1) { create(:option_sub_list, chat_bot_option: chat_bot_option, value_to_show: 'First opt') }
      let!(:opt2) { create(:option_sub_list, chat_bot_option: chat_bot_option, value_to_show: 'Second opt') }

      before do
        chat_bot_option.option_type = :form
      end

      context 'when message is present' do
        it 'return same message' do
          expect(my_class.form_text_options('Hello, ')).to eq("Hello, \n\n1. First opt\n2. Second opt")
        end
      end

      context 'when message is empty' do
        it 'return same message' do
          expect(my_class.form_text_options('')).to eq("1. First opt\n2. Second opt")
        end
      end
    end

    context 'when the chatbot option is the message type' do
      before do
        chat_bot_option.option_type = :message
      end

      it 'does nothing' do
        expect_any_instance_of(described_class).not_to receive(:menu_text_options)
        expect_any_instance_of(described_class).not_to receive(:form_text_options)

        my_class.append_text_options('Hello')
      end
    end
  end

  describe '#form_text_options' do
    context 'when the chatbot option has not option sub lists' do
      it 'return same message' do
        expect(my_class.form_text_options('Hello, Hello!')).to eq('Hello, Hello!')
      end
    end

    context 'when the chatbot option has option_sub_list' do
      let!(:opt1) { create(:option_sub_list, chat_bot_option: chat_bot_option, value_to_show: 'First opt') }
      let!(:opt2) { create(:option_sub_list, chat_bot_option: chat_bot_option, value_to_show: 'Second opt') }

      context 'when message is present' do
        it 'return same message' do
          expect(my_class.form_text_options('Hello, ')).to eq("Hello, \n\n1. First opt\n2. Second opt")
        end
      end

      context 'when message is empty' do
        it 'return same message' do
          expect(my_class.form_text_options('')).to eq("1. First opt\n2. Second opt")
        end
      end
    end

    context 'when the chatbot option uses API for options' do
      let(:api_option) { create(:chat_bot_option, :api, chat_bot: chat_bot) }
      let(:form_option) { create(:chat_bot_option, :form, chat_bot: chat_bot) }
      let(:api_config) { create(:chat_bot_option_api, chat_bot_option: api_option) }
      let(:form_config) do
        double('ChatBotOptionForm',
               data_source: 'data',
               title_source: 'data.title',
               value_source: 'data.value')
      end
      let(:api_response_data) do
        {
          'data' => [
            { 'title' => 'First API Option', 'value' => 'value1' },
            { 'title' => 'Second API Option', 'value' => 'value2' }
          ]
        }
      end
      let(:api_response) do
        double('CustomerBotResponse').tap do |response|
          allow(response).to receive(:response).with(version: 2).and_return(api_response_data)
        end
      end

      before do
        allow(form_option).to receive_messages(
          use_api?: true,
          closest_api_ancestor: api_option,
          chat_bot_option_form: form_config
        )
        allow(CustomerBotResponse).to receive(:where).and_return([api_response])
      end

      it 'returns formatted options from API response' do
        subject_with_form = described_class.new(customer, form_option, platform: platform)
        result = subject_with_form.form_text_options('API Options:')

        expected = "API Options:\n\n1. First API Option\n2. Second API Option"
        expect(result).to eq(expected)
      end

      it 'handles empty message' do
        subject_with_form = described_class.new(customer, form_option, platform: platform)
        result = subject_with_form.form_text_options('')

        expected = "1. First API Option\n2. Second API Option"
        expect(result).to eq(expected)
      end

      it 'handles empty API response gracefully' do
        # Override the before block setup to return no responses
        allow(CustomerBotResponse).to receive(:where).and_return([])

        subject_with_form = described_class.new(customer, form_option, platform: platform)
        result = subject_with_form.form_text_options('Test:')
        expect(result).to eq('Test:')
      end

      it 'handles API response with empty data array' do
        # Create a response with empty data
        empty_response = double('EmptyResponse').tap do |response|
          allow(response).to receive(:response).with(version: 2).and_return({ 'data' => [] })
        end
        allow(CustomerBotResponse).to receive(:where).and_return([empty_response])

        subject_with_form = described_class.new(customer, form_option, platform: platform)
        result = subject_with_form.form_text_options('Test:')
        expect(result).to eq("Test:\n\n")
      end

      it 'logs error and returns original message on exception' do
        allow(Rails.logger).to receive(:error)
        allow(form_option).to receive(:closest_api_ancestor).and_raise(StandardError, 'Test error')

        subject_with_form = described_class.new(customer, form_option, platform: platform)
        result = subject_with_form.form_text_options('Original message')

        expect(Rails.logger).to have_received(:error).with('Error al armar la lista de opciones: Test error')
        expect(result).to eq('Original message')
      end
    end
  end

  describe '#append_buttons_options' do
    context 'when chatbot option is decision type' do
      before do
        chat_bot_option.option_type = :decision
      end

      context 'when chatbot option do not have active children' do
        it 'return empty options' do
          expect(my_class.append_buttons_options).to eq([])
        end
      end

      context 'when chatbot option have active children' do
        let!(:child1) { create(:chat_bot_option, chat_bot: chat_bot, text: 'First option', parent: chat_bot_option) }
        let!(:child2) { create(:chat_bot_option, chat_bot: chat_bot, text: 'Second option', parent: chat_bot_option) }
        let!(:child3) do
          create(:chat_bot_option, chat_bot: chat_bot, text: 'third opt', parent: chat_bot_option, option_deleted: true)
        end

        it 'return empty options' do
          expect(my_class.append_buttons_options.count).to eq(2)
        end
      end
    end

    context 'when chatbot option is form type' do
      before do
        chat_bot_option.option_type = :form
      end

      context 'when chatbot option do not have option_sub_list' do
        it 'return empty options' do
          expect(my_class.append_buttons_options).to eq([])
        end
      end

      context 'when chatbot option have active children' do
        let!(:opt1) { create(:option_sub_list, chat_bot_option: chat_bot_option, value_to_show: 'First opt') }
        let!(:opt2) { create(:option_sub_list, chat_bot_option: chat_bot_option, value_to_show: 'Second opt') }

        it 'return empty options' do
          expect(my_class.append_buttons_options.count).to eq(2)
        end
      end
    end

    context 'when chatbot option is form type with API' do
      let(:api_option) { create(:chat_bot_option, :api, chat_bot: chat_bot) }
      let(:form_option) { create(:chat_bot_option, :form, chat_bot: chat_bot, interactive: 'buttons') }
      let(:api_config) { create(:chat_bot_option_api, chat_bot_option: api_option) }
      let(:form_config) do
        double('ChatBotOptionForm',
               data_source: 'data',
               title_source: 'data.title',
               value_source: 'data.value')
      end
      let(:api_response_data) do
        {
          'data' => [
            { 'title' => 'First API Option', 'value' => 'value1' },
            { 'title' => 'Second API Option', 'value' => 'value2' },
            { 'title' => 'Third API Option', 'value' => 'value3' },
            { 'title' => 'Fourth API Option', 'value' => 'value4' }
          ]
        }
      end
      let(:api_response) do
        create(:customer_bot_response, customer: customer, chat_bot_option: api_option, response: api_response_data,
                                       platform: platform)
      end

      before do
        allow(form_option).to receive_messages(
          use_api?: true,
          closest_api_ancestor: api_option,
          chat_bot_option_form: form_config
        )
        allow(CustomerBotResponse).to receive(:where).and_return([api_response])
      end

      it 'returns button options from API response with filtering for buttons type' do
        subject_with_form = described_class.new(customer, form_option, platform: platform)
        result = subject_with_form.append_buttons_options

        expect(result).to be_an(Array)
        expect(result.size).to eq(3) # Limited to 3 for buttons type
        expect(result.first).to include(type: 'text', title: 'First API Option', postbackText: "1-#{form_option.id}")
      end

      it 'handles list interactive type' do
        form_option.update(interactive: 'list')
        subject_with_form = described_class.new(customer, form_option, platform: platform)
        result = subject_with_form.append_buttons_options

        expect(result.size).to eq(4) # Can show up to 10 for list type
      end

      it 'logs error and returns empty array on exception' do
        allow(Rails.logger).to receive(:error)
        allow(form_option).to receive(:closest_api_ancestor).and_raise(StandardError, 'Test error')

        subject_with_form = described_class.new(customer, form_option, platform: platform)
        result = subject_with_form.append_buttons_options

        expect(Rails.logger).to have_received(:error).with('Error al armar la lista de botones de opciones: Test error')
        expect(result).to eq([])
      end
    end

    context 'when the chatbot option is the message type' do
      before do
        chat_bot_option.option_type = :message
      end

      it 'does nothing' do
        expect_any_instance_of(described_class).not_to receive(:menu_button_options)
        expect_any_instance_of(described_class).not_to receive(:form_button_options)

        my_class.append_buttons_options
      end
    end

    describe '#filter_list_options' do
      let(:list_options) do
        [
          { title: 'Option 1' },
          { title: 'Option 2' },
          { title: 'Option 3' },
          { title: 'Option 4' },
          { title: 'Option 5' }
        ]
      end

      context 'when interactive is buttons' do
        before { chat_bot_option.update(interactive: 'buttons') }

        it 'returns first 3 options' do
          result = my_class.send(:filter_list_options, list_options)
          expect(result.size).to eq(3)
        end
      end

      context 'when interactive is list' do
        before { chat_bot_option.update(interactive: 'list') }

        it 'returns first 10 options (or all if less than 10)' do
          result = my_class.send(:filter_list_options, list_options)
          expect(result.size).to eq(5) # All 5 since it's less than 10
        end
      end

      context 'when interactive is text' do
        before { chat_bot_option.update(interactive: 'text') }

        it 'returns all options' do
          result = my_class.send(:filter_list_options, list_options)
          expect(result.size).to eq(5)
        end
      end

      context 'when list_options is not an array' do
        it 'returns empty array' do
          result = my_class.send(:filter_list_options, 'not an array')
          expect(result).to eq([])
        end
      end
    end
  end

  describe '#execute_option' do
    before do
      allow(ChatBots::V2::Message).to receive(:new).and_return(message_process)
      allow(ChatBots::V2::ProcessChatbot).to receive(:new).and_return(bot_process)
    end

    let!(:bot_process) do
      instance_double(
        ChatBots::V2::ProcessChatbot,
        manage_skip_option: true,
        execute_actions: nil,
        execute_active_mia_step: nil,
        mark_chat_as_resolved!: nil,
        finish_bot!: nil
      )
    end

    let!(:message_process) do
      instance_double(
        ChatBots::V2::Message,
        send_message: true
      )
    end

    let!(:origin_instance) { instance_double(GupshupWhatsappMessage, id: 123) }

    context 'when chat_bot_option is a form with skip_option' do
      subject { described_class.new(customer, option) }

      let!(:option) { create(:chat_bot_option, :form, skip_option: true) }

      it 'calls manage_skip_option' do
        subject.execute_option(origin_instance)

        expect(bot_process).to have_received(:manage_skip_option).with(option)
      end
    end

    context 'when chat_bot_option is a form with jump_to' do
      subject { described_class.new(customer, option) }

      let!(:option) { create(:chat_bot_option, :jump_to) }

      it 'calls execute_actions' do
        subject.execute_option(option)

        expect(bot_process).to have_received(:execute_actions).with(option)
      end
    end

    context 'when chat_bot_option is a active_mia' do
      subject { described_class.new(customer, option) }

      let!(:option) { create(:chat_bot_option, :active_mia) }

      it 'calls execute_actions' do
        subject.execute_option(option)

        expect(bot_process).to have_received(:execute_active_mia_step).with(option)
      end
    end

    context 'when chat_bot_option is an API option' do
      subject { described_class.new(customer, option, platform: platform) }

      let!(:option) { create(:chat_bot_option, :api) }
      let!(:api_config) { create(:chat_bot_option_api, chat_bot_option: option) }

      before do
        allow(bot_process).to receive(:api_options=)
        allow(bot_process).to receive(:execute_api_step)
      end

      it 'sets api_options and calls execute_api_step' do
        subject.execute_option(origin_instance)

        expect(bot_process).to have_received(:api_options=).with(api_config)
        expect(bot_process).to have_received(:execute_api_step).with(option)
      end
    end

    context 'when chat_bot_option is an API option without api config' do
      subject { described_class.new(customer, option, platform: platform) }

      let!(:option) { create(:chat_bot_option, :api) }
      let!(:bot_process) do
        instance_double(ChatBots::V2::ProcessChatbot).tap do |process|
          allow(ChatBots::V2::ProcessChatbot).to receive(:new).and_return(process)
          allow(process).to receive(:execute_api_step)
        end
      end

      before do
        allow(option).to receive(:chat_bot_option_api).and_return(nil)
      end

      it 'returns early without calling execute_api_step' do
        result = subject.execute_option(origin_instance)

        expect(result).to be_nil
        expect(bot_process).not_to have_received(:execute_api_step)
      end
    end

    context 'when chat_bot_option is a resolve_chat option' do
      subject { described_class.new(customer, option, platform: platform) }

      let!(:option) { create(:chat_bot_option, :resolve_chat) }

      before do
        allow(bot_process).to receive(:mark_chat_as_resolved!)
        allow(bot_process).to receive(:finish_bot!)
      end

      it 'calls mark_chat_as_resolved! and finish_bot!' do
        subject.execute_option(origin_instance)

        expect(bot_process).to have_received(:mark_chat_as_resolved!)
        expect(bot_process).to have_received(:finish_bot!)
      end
    end

    context 'when chat_bot_option is a message' do
      subject { described_class.new(customer, option) }

      let!(:option) { create(:chat_bot_option, :message) }

      it 'calls execute_actions' do
        subject.execute_option(option)

        expect(bot_process).to have_received(:execute_actions).with(option)
      end
    end

    context 'when chat_bot_option is a form' do
      subject { described_class.new(customer, option) }

      let!(:option) { create(:chat_bot_option, :form) }

      it 'calls send_message' do
        subject.execute_option(chat_bot_option)

        expect(message_process).to have_received(:send_message)
      end
    end

    context 'when chat_bot_option is a decision' do
      subject { described_class.new(customer, option) }

      let!(:option) { create(:chat_bot_option, :decision) }

      context 'when it does not have actions' do
        it 'calls send_message' do
          subject.execute_option(chat_bot_option)

          expect(message_process).to have_received(:send_message)
        end
      end

      context 'when it has actions' do
        context 'when it does not have get_out_bot action' do
          let(:add_tag) { create(:chat_bot_action, :add_tag) }

          let!(:bot_process) do
            instance_double(
              ChatBots::V2::ProcessChatbot,
              manage_skip_option: true,
              execute_actions: [add_tag]
            )
          end

          it 'calls send_message' do
            subject.execute_option(chat_bot_option)

            expect(message_process).to have_received(:send_message)
          end
        end

        context 'when it has get_out_bot action' do
          let(:add_tag) { create(:chat_bot_action, :add_tag) }
          let(:get_out_bot) { create(:chat_bot_action) }

          let!(:bot_process) do
            instance_double(
              ChatBots::V2::ProcessChatbot,
              manage_skip_option: true,
              execute_actions: [add_tag, get_out_bot]
            )
          end

          it 'does not call send_message' do
            subject.execute_option(chat_bot_option)

            expect(message_process).not_to have_received(:send_message)
          end
        end
      end
    end
  end

  describe '#form_and_location_option?' do
    let(:chat_bot_option) { create(:chat_bot_option, :form, :location, chat_bot: chat_bot) }

    it 'returns true if the option is a form and location' do
      expect(subject.send(:form_and_location_option?)).to be_truthy
    end

    it 'returns false if the option is not a form and location' do
      allow(chat_bot_option).to receive(:form?).and_return(false)

      expect(subject.send(:form_and_location_option?)).to be_falsey
    end
  end

  describe '#execute_api_option' do
    context 'when api_options is blank' do
      before do
        allow_any_instance_of(ChatBotOption).to receive(:chat_bot_option_api).and_return(nil)
      end

      let(:origin_instance) { instance_double(GupshupWhatsappMessage, id: 123) }

      it 'returns nil and does not call execute_api_step' do
        expect(subject.send(:execute_api_option, origin_instance)).to be_nil
      end
    end

    context 'when api_options is present' do
      let!(:chat_bot_option_api) { create(:chat_bot_option_api, chat_bot_option: chat_bot_option) }
      let(:origin_instance) { instance_double(GupshupWhatsappMessage, id: 123) }

      before do
        allow_any_instance_of(ChatBots::V2::ProcessChatbot).to receive(:api_options)
        allow_any_instance_of(ChatBots::V2::ProcessChatbot).to receive(:execute_api_step).and_return(true)
      end

      it 'returns nil and does not call execute_api_step' do
        expect(subject.send(:execute_api_option, origin_instance)).not_to be_nil
      end
    end
  end

  describe '#fetch_api_vars' do
    let(:api_option) { create(:chat_bot_option, :api, chat_bot: chat_bot) }
    let(:form_option) { create(:chat_bot_option, :form, chat_bot: chat_bot) }
    let(:api_config) { create(:chat_bot_option_api, chat_bot_option: api_option) }
    let(:form_config) do
      double('ChatBotOptionForm',
             data_source: 'data',
             title_source: 'data.title',
             value_source: 'data.value')
    end
    let(:api_response) do
      create(:customer_bot_response, customer: customer, chat_bot_option: api_option, platform: platform)
    end

    before do
      allow(form_option).to receive_messages(
        closest_api_ancestor: api_option,
        chat_bot_option_form: form_config
      )
      allow(CustomerBotResponse).to receive(:where).and_return([api_response])
    end

    it 'returns form_options and api_responses when all data is present' do
      subject_with_form = described_class.new(customer, form_option, platform: platform)
      result = subject_with_form.send(:fetch_api_vars)

      expect(result).to eq([form_config, [api_response]])
    end

    it 'raises ArgumentError when ancestor_api is blank' do
      allow(form_option).to receive(:closest_api_ancestor).and_return(nil)
      subject_with_form = described_class.new(customer, form_option, platform: platform)

      expect do
        subject_with_form.send(:fetch_api_vars)
      end.to raise_error(ArgumentError, 'No se encontró ancestro tipo api')
    end

    it 'raises ArgumentError when api_responses is blank' do
      allow(form_option).to receive(:closest_api_ancestor).and_return(nil)
      subject_with_form = described_class.new(customer, form_option, platform: platform)

      expect do
        subject_with_form.send(:fetch_api_vars)
      end.to raise_error(ArgumentError, 'No se encontró ancestro tipo api')
    end

    it 'raises ArgumentError when form_options is blank' do
      allow(form_option).to receive(:chat_bot_option_form).and_return(nil)
      subject_with_form = described_class.new(customer, form_option, platform: platform)

      expect do
        subject_with_form.send(:fetch_api_vars)
      end.to raise_error(ArgumentError,
                         'No se encontró configuración de api para pregunta')
    end
  end

  describe '#parse_form_list_keys' do
    let(:form_config) do
      double('FormConfig',
             data_source: 'response.data.items',
             title_source: 'response.data.items.name',
             value_source: 'response.data.items.id')
    end

    it 'parses form configuration keys correctly' do
      result = my_class.send(:parse_form_list_keys, form_config)

      expect(result).to eq([%w[response data items], 'name', 'id'])
    end
  end

  describe '#postback_text' do
    let(:item) { double('Item', position: 2) }

    it 'returns formatted postback text' do
      result = my_class.postback_text(item)
      expect(result).to eq("2-#{chat_bot_option.id}")
    end
  end

  describe '#fetch_related_field_value' do
    context 'when field is not a list type' do
      let(:field) { create(:customer_related_field, retailer: retailer, field_type: :string) }
      let!(:data) do
        create(:customer_related_datum, customer: customer, customer_related_field: field, data: 'test data')
      end

      it 'returns the raw data' do
        result = my_class.send(:fetch_related_field_value, field)
        expect(result).to eq('test data')
      end
    end

    context 'when field is a list type' do
      let(:field) do
        create(:customer_related_field,
               retailer: retailer,
               field_type: :list,
               list_options: [{ key: 'key1', value: 'Value 1' }])
      end
      let!(:data) { create(:customer_related_datum, customer: customer, customer_related_field: field, data: 'key1') }

      before do
        allow(field).to receive(:get_list_option_value).with('key1').and_return('Value 1')
      end

      it 'returns the list option value' do
        result = my_class.send(:fetch_related_field_value, field)
        expect(result).to eq('Value 1')
      end
    end

    context 'when no customer_related_data exists' do
      let(:field) { create(:customer_related_field, retailer: retailer, field_type: :string) }

      it 'returns empty string' do
        result = my_class.send(:fetch_related_field_value, field)
        expect(result).to eq('')
      end
    end
  end

  describe 'private option type checking methods' do
    describe '#form_and_skip_option?' do
      context 'when option is form and has skip_option true' do
        before { allow(chat_bot_option).to receive_messages(form?: true, skip_option: true) }

        it 'returns true' do
          expect(my_class.send(:form_and_skip_option?)).to be true
        end
      end

      context 'when option is not form' do
        before { allow(chat_bot_option).to receive_messages(form?: false, skip_option: true) }

        it 'returns false' do
          expect(my_class.send(:form_and_skip_option?)).to be false
        end
      end

      context 'when option is form but skip_option is false' do
        before { allow(chat_bot_option).to receive_messages(form?: true, skip_option: false) }

        it 'returns false' do
          expect(my_class.send(:form_and_skip_option?)).to be false
        end
      end
    end

    describe '#jump_to_or_message_option?' do
      context 'when option is jump_to' do
        before { allow(chat_bot_option).to receive_messages(jump_to?: true, message?: false) }

        it 'returns true' do
          expect(my_class.send(:jump_to_or_message_option?)).to be true
        end
      end

      context 'when option is message' do
        before { allow(chat_bot_option).to receive_messages(jump_to?: false, message?: true) }

        it 'returns true' do
          expect(my_class.send(:jump_to_or_message_option?)).to be true
        end
      end

      context 'when option is neither jump_to nor message' do
        before { allow(chat_bot_option).to receive_messages(jump_to?: false, message?: false) }

        it 'returns false' do
          expect(my_class.send(:jump_to_or_message_option?)).to be false
        end
      end
    end

    describe '#active_mia_option?' do
      context 'when option is active_mia' do
        before { allow(chat_bot_option).to receive(:active_mia?).and_return(true) }

        it 'returns true' do
          expect(my_class.send(:active_mia_option?)).to be true
        end
      end

      context 'when option is not active_mia' do
        before { allow(chat_bot_option).to receive(:active_mia?).and_return(false) }

        it 'returns false' do
          expect(my_class.send(:active_mia_option?)).to be false
        end
      end
    end

    describe '#api_option?' do
      context 'when option is api' do
        before { allow(chat_bot_option).to receive(:api?).and_return(true) }

        it 'returns true' do
          expect(my_class.send(:api_option?)).to be true
        end
      end

      context 'when option is not api' do
        before { allow(chat_bot_option).to receive(:api?).and_return(false) }

        it 'returns false' do
          expect(my_class.send(:api_option?)).to be false
        end
      end
    end

    describe '#resolve_chat_option?' do
      context 'when option is resolve_chat' do
        before { allow(chat_bot_option).to receive(:resolve_chat?).and_return(true) }

        it 'returns true' do
          expect(my_class.send(:resolve_chat_option?)).to be true
        end
      end

      context 'when option is not resolve_chat' do
        before { allow(chat_bot_option).to receive(:resolve_chat?).and_return(false) }

        it 'returns false' do
          expect(my_class.send(:resolve_chat_option?)).to be false
        end
      end
    end
  end

  describe 'service instantiation methods' do
    let(:origin_instance) { double('OriginInstance') }

    describe '#chatbot_processor_service' do
      it 'creates a new ProcessChatbot instance' do
        expect(ChatBots::V2::ProcessChatbot).to receive(:new).with(customer, chat_bot, origin_instance, nil, nil)

        my_class.send(:chatbot_processor_service, origin_instance)
      end
    end

    describe '#chatbots_message_service' do
      it 'creates a new Message instance' do
        expect(ChatBots::V2::Message).to receive(:new).with(customer, chat_bot_option, origin_instance)

        my_class.send(:chatbots_message_service, origin_instance)
      end
    end
  end
end
