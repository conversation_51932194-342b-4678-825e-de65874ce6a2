# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Campaigns::MessageEventProducer do
  # Usamos dobles simples en lugar de instance_double para evitar problemas de verificación
  subject(:producer) { described_class.new(producer: rdkafka_producer, logger: logger) }

  let(:campaign) { double('Campaign', id: 1) }
  let(:customer) { double('Customer', id: 2) }
  let(:message) { double('Message', id: 3, channel: 'whatsapp', status: 'sent') }
  let(:rdkafka_producer) { double('Rdkafka::Producer') }
  let(:rdkafka_config) { double('Rdkafka::Config', producer: rdkafka_producer) }
  let(:delivery_handle) { double('DeliveryHandle') }
  let(:delivery_report) { double('DeliveryReport', topic: 'topic', partition: 0, offset: 123) }
  let(:logger) { double('Logger') }

  before do
    # Configuración básica de los dobles
    allow(logger).to receive(:info)
    allow(logger).to receive(:error)
    allow(Time).to receive_message_chain(:current, :to_i).and_return(1_234_567_890)

    # Stub para Rdkafka::Config
    allow(Rdkafka::Config).to receive(:new).and_return(rdkafka_config)
    allow(rdkafka_config).to receive(:producer).and_return(rdkafka_producer)

    # Stub para el delivery_handle
    allow(delivery_handle).to receive(:wait).and_return(delivery_report)
    allow(rdkafka_producer).to receive(:produce).and_return(delivery_handle)
  end

  describe '#initialize' do
    context 'when producer is provided' do
      it 'uses the provided producer' do
        expect(producer.producer).to eq(rdkafka_producer)
      end
    end

    context 'when logger is provided' do
      it 'uses the provided logger' do
        expect(producer.logger).to eq(logger)
      end
    end

    context 'when logger is not provided' do
      before do
        allow(Rails).to receive(:logger).and_return(logger)
      end

      it 'uses Rails.logger' do
        new_producer = described_class.new(producer: rdkafka_producer)
        expect(new_producer.logger).to eq(logger)
      end
    end
  end

  describe '#produce_message_sent' do
    let(:expected_payload) do
      {
        event_type: 'campaign_message_sent_event',
        campaign_id: campaign.id,
        customer_id: customer.id,
        message_id: message.id,
        channel: message.channel,
        status: message.status,
        timestamp: 1_234_567_890
      }
    end

    it 'logs the event production' do
      expect(logger).to receive(:info).with("Producing message sent event for campaign #{campaign.id}, customer #{customer.id}, message #{message.id}")
      producer.produce_message_sent(campaign, customer, message)
    end

    it 'produces the event to Kafka' do
      expect(rdkafka_producer).to receive(:produce).with(
        topic: 'mercately_campaign_events',
        payload: expected_payload.to_json,
        key: campaign.id.to_s
      )
      producer.produce_message_sent(campaign, customer, message)
    end

    it 'returns true on success' do
      expect(producer.produce_message_sent(campaign, customer, message)).to be true
    end

    context 'when an error occurs' do
      before do
        allow(rdkafka_producer).to receive(:produce).and_raise(StandardError.new('Test error'))
      end

      it 'logs the error' do
        expect(logger).to receive(:error).with('Error producing message sent event: Test error')
        expect(logger).to receive(:error)
        producer.produce_message_sent(campaign, customer, message)
      end

      it 'returns false' do
        expect(producer.produce_message_sent(campaign, customer, message)).to be false
      end
    end
  end

  describe 'private methods' do
    describe '#build_event_payload' do
      it 'builds the correct payload' do
        expected_payload = {
          event_type: 'campaign_message_sent_event',
          campaign_id: campaign.id,
          customer_id: customer.id,
          message_id: message.id,
          channel: message.channel,
          status: message.status,
          timestamp: 1_234_567_890
        }

        expect(producer.send(:build_event_payload, campaign, customer, message)).to eq(expected_payload)
      end
    end

    describe '#log_event_production' do
      it 'logs the event production' do
        expect(logger).to receive(:info).with("Producing message sent event for campaign #{campaign.id}, customer #{customer.id}, message #{message.id}")
        producer.send(:log_event_production, campaign, customer, message)
      end
    end

    describe '#log_error' do
      let(:exception) { StandardError.new('Test error') }

      before do
        allow(exception).to receive(:backtrace).and_return(%w[line1 line2])
      end

      it 'logs the error message and backtrace' do
        expect(logger).to receive(:error).with('Error message: Test error')
        expect(logger).to receive(:error).with("line1\nline2")
        producer.send(:log_error, 'Error message', exception)
      end
    end

    describe '#partition_key' do
      it 'returns the campaign id as a string' do
        expect(producer.send(:partition_key, campaign, message)).to eq(campaign.id.to_s)
      end
    end
  end
end
