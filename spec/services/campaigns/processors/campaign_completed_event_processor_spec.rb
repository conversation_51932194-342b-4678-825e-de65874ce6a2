require 'rails_helper'

RSpec.describe Campaigns::Processors::CampaignCompletedEventProcessor do
  let(:retailer) { create(:retailer) }
  let(:campaign) { create(:campaign, retailer: retailer) }
  let(:payload) do
    {
      'event_type' => 'campaign_completed_event',
      'campaign_id' => campaign.id,
      'total_messages' => 10,
      'sent_messages' => 8,
      'failed_messages' => 2,
      'timestamp' => Time.current.to_i
    }
  end
  let(:processor) { described_class.new(payload) }
  let(:redis) { instance_double(Redis, set: true) }

  before do
    allow(Redis).to receive(:current).and_return(redis)
    allow(Rails.logger).to receive(:info)
    allow(Rails.logger).to receive(:error)
  end

  describe '#process' do
    context 'when the campaign exists' do
      before do
        allow(Campaign).to receive(:find).with(campaign.id).and_return(campaign)
        allow(campaign).to receive(:update).and_return(true)
      end

      it 'processes the event without errors' do
        expect { processor.process }.not_to raise_error
      end

      it 'updates the campaign status to sent' do
        expect(campaign).to receive(:update).with(status: :sent)
        processor.process
      end

      it 'logs the campaign statistics' do
        expect(Rails.logger).to receive(:info).with(
          /Estadísticas de campaña #{campaign.id}: total=10, sent=8, failed=2/
        )
        processor.process
      end

      it 'logs information about Redis counters cleanup' do
        expect(Rails.logger).to receive(:info).with(/Contadores de Redis mantenidos/)
        processor.process
      end

      it 'logs information about essential counters' do
        expect(Rails.logger).to receive(:info).with(/total \(para estadísticas\)/)
        expect(Rails.logger).to receive(:info).with(/pending_messages \(para control de flujo\)/)
        processor.process
      end

      context 'when no messages were sent' do
        let(:payload) do
          {
            'event_type' => 'campaign_completed_event',
            'campaign_id' => campaign.id,
            'total_messages' => 10,
            'sent_messages' => 0,
            'failed_messages' => 10,
            'timestamp' => Time.current.to_i
          }
        end

        it 'updates the campaign status to failed' do
          expect(campaign).to receive(:update).with(status: :failed, reason: :no_messages_sent)
          processor.process
        end
      end
    end

    context 'when the campaign does not exist' do
      before do
        allow(Campaign).to receive(:find).with(campaign.id).and_raise(ActiveRecord::RecordNotFound)
      end

      it 'logs an error and returns false' do
        expect(Rails.logger).to receive(:error).with(/Error al procesar evento campaign_completed_event/)
        expect(processor.process).to be false
      end
    end

    context 'when an error occurs during processing' do
      before do
        allow(Campaign).to receive(:find).with(campaign.id).and_raise(StandardError, 'Database error')
      end

      it 'logs the error and returns false' do
        expect(Rails.logger).to receive(:error).with(/Error al procesar evento campaign_completed_event: Database error/)
        expect(processor.process).to be false
      end
    end
  end

  describe '#initialize' do
    it 'sets the payload attributes correctly' do
      expect(processor.campaign_id).to eq(campaign.id)
      expect(processor.total_messages).to eq(10)
      expect(processor.sent_messages).to eq(8)
      expect(processor.failed_messages).to eq(2)
    end
  end
end
