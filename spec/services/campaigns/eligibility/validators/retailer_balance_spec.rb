require 'rails_helper'

RSpec.describe Campaigns::Eligibility::Validators::RetailerBalance do
  let(:retailer) { instance_double(<PERSON><PERSON><PERSON>, id: 123) }
  let(:campaign) { instance_double(Campaign, retailer: retailer, estimated_cost: 100.0) }
  let(:result) { instance_double(Campaigns::Eligibility::KafkaService::Result) }
  let(:validator) { described_class.new }

  before do
    allow(Rails.logger).to receive(:info)
    allow(result).to receive(:add_check)
  end

  describe '#validate' do
    context 'when retailer has BSP Mercately connection' do
      before do
        allow(retailer).to receive(:connect_bsp_mercately).and_return(true)
        allow(retailer).to receive(:ws_balance).and_return(50.0)
      end

      it 'sets has_balance to true' do
        validator.validate(campaign, result)

        expect(result).to have_received(:add_check).with(:has_balance, true)
      end

      it 'logs the check result' do
        validator.validate(campaign, result)

        expect(Rails.logger).to have_received(:info).with('🔍 Check has_balance: true')
      end
    end

    context 'when retailer has sufficient balance' do
      before do
        allow(retailer).to receive(:connect_bsp_mercately).and_return(false)
        allow(retailer).to receive(:ws_balance).and_return(150.0)
      end

      it 'sets has_balance to true' do
        validator.validate(campaign, result)

        expect(result).to have_received(:add_check).with(:has_balance, true)
      end
    end

    context 'when retailer has insufficient balance' do
      before do
        allow(retailer).to receive(:connect_bsp_mercately).and_return(false)
        allow(retailer).to receive(:ws_balance).and_return(50.0)
      end

      it 'sets has_balance to false' do
        validator.validate(campaign, result)

        expect(result).to have_received(:add_check).with(:has_balance, false)
      end

      it 'logs the check result' do
        validator.validate(campaign, result)

        expect(Rails.logger).to have_received(:info).with('🔍 Check has_balance: false')
      end
    end
  end
end
