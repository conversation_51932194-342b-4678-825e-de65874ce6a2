# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Campaigns::CampaignCompletionService do
  subject(:service) do
    described_class.new(
      campaign_id: campaign_id,
      event_producer: event_producer,
      logger: logger
    )
  end

  let(:retailer) { create(:retailer) }
  let(:campaign) { create(:campaign, retailer: retailer) }
  let(:campaign_id) { campaign.id }
  let(:event_producer) { instance_double(Rdkafka::Producer) }
  let(:logger) { instance_double(Logger, info: nil, error: nil) }

  describe '#complete_if_needed' do
    before do
      allow(Redis).to receive(:current).and_return(redis_mock)
      allow(Mercately::Kafka::ProducerPool).to receive(:get_producer).and_return(event_producer)
      allow(Mercately::Kafka::ProducerPool).to receive(:release_producer)
      allow(Campaign).to receive(:find_by).with(id: campaign_id).and_return(campaign)
    end

    let(:redis_mock) { instance_double(Redis) }

    context 'when there are pending messages' do
      before do
        allow(redis_mock).to receive(:get).with("campaign:#{campaign_id}:pending_messages").and_return('5')
      end

      it 'does not complete the campaign' do
        expect(service.complete_if_needed).to be_falsey
      end
    end

    context 'when there are no pending messages' do
      before do
        allow(redis_mock).to receive(:get).with("campaign:#{campaign_id}:pending_messages").and_return('0')
        allow(redis_mock).to receive(:get).with("campaign:#{campaign_id}:total").and_return('10')
        allow(campaign).to receive_messages(sent?: false, failed?: false, update: true)
        allow(event_producer).to receive(:produce).and_return(true)
      end

      it 'completes the campaign successfully' do
        expect(service.complete_if_needed).to be_truthy
      end

      it 'publishes a completion event' do
        expect(event_producer).to receive(:produce).with(
          topic: 'mercately_campaign_events',
          payload: anything,
          key: campaign_id.to_s
        )

        service.complete_if_needed
      end

      it 'updates the campaign status to sent' do
        expect(campaign).to receive(:update).with(status: :sent)
        service.complete_if_needed
      end
    end

    context 'when campaign is already completed' do
      before do
        allow(campaign).to receive(:sent?).and_return(true)
        # No necesitamos mock de Redis porque el método retorna antes de usarlo
      end

      it 'does not complete the campaign again' do
        expect(service.complete_if_needed).to be_falsey
      end
    end

    context 'when campaign is not found' do
      let(:campaign_id) { 999_999 }

      before do
        allow(Campaign).to receive(:find_by).with(id: campaign_id).and_return(nil)
      end

      it 'returns false and logs error' do
        expect(logger).to receive(:error).with("Campaign not found: #{campaign_id}")
        expect(service.complete_if_needed).to be_falsey
      end
    end
  end
end
