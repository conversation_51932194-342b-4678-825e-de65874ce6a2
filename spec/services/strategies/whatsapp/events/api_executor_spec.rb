require 'rails_helper'

RSpec.describe Strategies::Whatsapp::Events::ApiExecutor, type: :service do
  describe '#initialize' do
    it 'raises ArgumentError if strategy is blank' do
      expect { described_class.new(nil, retailer: 'Retailer', params: {}) }.to raise_error(ArgumentError)
    end

    it 'sets all attributes correctly' do
      retailer = double('Retailer')
      params = { test: 'value' }
      from = 'test_from'
      qr_params = { qr: 'test' }

      executor = described_class.new('message', retailer: retailer, params: params, from: from, qr_params: qr_params)

      expect(executor.strategy).to eq('message')
      expect(executor.retailer).to eq(retailer)
      expect(executor.params).to eq(params)
      expect(executor.from).to eq(from)
      expect(executor.qr_params).to eq(qr_params)
    end

    it 'sets default values for optional parameters' do
      retailer = double('Retailer')
      params = { test: 'value' }

      executor = described_class.new('message', retailer: retailer, params: params)

      expect(executor.strategy).to eq('message')
      expect(executor.retailer).to eq(retailer)
      expect(executor.params).to eq(params)
      expect(executor.from).to be_nil
      expect(executor.qr_params).to be_nil
    end
  end

  describe '#call' do
    context 'when select_strategy is nil' do
      it 'does not call any strategy' do
        executor = described_class.new('invalid_strategy', retailer: 'Retailer', params: {})
        expect(executor.call).to be_nil
      end
    end

    context 'when retailer is blank' do
      it 'does not call any strategy' do
        executor = described_class.new('message', retailer: '', params: {})
        expect(executor.call).to be_nil
      end
    end

    context 'when params is blank' do
      it 'does not call any strategy' do
        executor = described_class.new('message', retailer: 'Retailer', params: {})
        allow(executor).to receive(:select_strategy).and_return(double('StrategyClass', call: true))
        expect(executor.call).to be_nil
      end
    end

    context 'when all conditions are met' do
      it 'calls the selected strategy' do
        executor = described_class.new('message', retailer: 'Retailer', params: { key: 'value' })
        mock_strategy = double('StrategyClass')
        allow(executor).to receive(:select_strategy).and_return(mock_strategy)
        expect(mock_strategy).to receive(:call)
          .with(retailer: 'Retailer', params: { key: 'value' }, from: nil, qr_params: nil)
        executor.call
      end
    end

    context 'when strategy is message-outbound' do
      let(:retailer) { double('Retailer') }
      let(:params) { { message: 'test' } }
      let(:from) { 'test_from' }
      let(:qr_params) { { qr: 'test' } }

      it 'selects OutboundStrategy' do
        executor = described_class.new('message-outbound', retailer: retailer, params: params, from: from,
                                                           qr_params: qr_params)

        expect(executor.send(:select_strategy)).to eq(Whatsapp::Events::OutboundStrategy)
      end

      it 'calls OutboundStrategy with correct parameters' do
        mock_outbound_strategy = double('OutboundStrategy')
        allow(Whatsapp::Events::OutboundStrategy).to receive(:call).and_return(mock_outbound_strategy)

        executor = described_class.new('message-outbound', retailer: retailer, params: params, from: from,
                                                           qr_params: qr_params)

        expect(Whatsapp::Events::OutboundStrategy).to receive(:call)
          .with(retailer: retailer, params: params, from: from, qr_params: qr_params)

        executor.call
      end
    end
  end

  describe '#select_strategy' do
    let(:executor) { described_class.new('message', retailer: 'Retailer', params: { key: 'value' }) }

    it 'returns InboundStrategy for message' do
      executor.instance_variable_set(:@strategy, 'message')
      expect(executor.send(:select_strategy)).to eq(Whatsapp::Events::InboundStrategy)
    end

    it 'returns OutboundStrategy for message-outbound' do
      executor.instance_variable_set(:@strategy, 'message-outbound')
      expect(executor.send(:select_strategy)).to eq(Whatsapp::Events::OutboundStrategy)
    end

    it 'returns InboundStrategy for quick_reply' do
      executor.instance_variable_set(:@strategy, 'quick_reply')
      expect(executor.send(:select_strategy)).to eq(Whatsapp::Events::InboundStrategy)
    end

    it 'returns ApiMessageStrategy for message-event' do
      executor.instance_variable_set(:@strategy, 'message-event')
      expect(executor.send(:select_strategy)).to eq(Whatsapp::Events::ApiMessageStrategy)
    end

    it 'returns BillingStrategy for billing-event' do
      executor.instance_variable_set(:@strategy, 'billing-event')
      expect(executor.send(:select_strategy)).to eq(Whatsapp::Events::BillingStrategy)
    end

    it 'returns TemplateStrategy for template-event' do
      executor.instance_variable_set(:@strategy, 'template-event')
      expect(executor.send(:select_strategy)).to eq(Whatsapp::Events::TemplateStrategy)
    end

    it 'returns nil for invalid strategy' do
      executor.instance_variable_set(:@strategy, 'invalid_strategy')
      expect(executor.send(:select_strategy)).to be_nil
    end
  end
end
