require 'rails_helper'

RSpec.describe Mia::Chatbot::Messenger, type: :service do
  let(:retailer) { create(:retailer, :fb_integrated) }
  let(:customer) { create(:customer, retailer:, allow_mia_chatbot: true) }
  let(:message) { create(:facebook_message, customer:, text: 'Hola', file_type: nil) }
  let(:service) { described_class.new(message) }

  describe '#call' do
    context 'when message type is invalid' do
      before { allow(service).to receive(:inbound_type).and_return('unsupported') }

      it 'does not enqueue job' do
        expect(Mia::Chatbot::MessengerJob).not_to receive(:perform_later)
        service.call
      end
    end

    context 'when message already enqueued' do
      before do
        CustomerMessageJob.create!(platform: 'messenger', customer_id: customer.id, platform_message_id: message.id)
      end

      it 'does not enqueue again' do
        expect(Mia::Chatbot::MessengerJob).not_to receive(:perform_later)
        service.call
      end
    end

    context 'when message is a valid text and not enqueued' do
      it 'enqueues MessengerJob for text message' do
        expect do
          service.call
        end.to have_enqueued_job(Mia::Chatbot::MessengerJob)
          .with(customer.id, 'text', message.reply_to)
      end
    end

    context 'when message is media (image)' do
      let(:message) do
        create(:facebook_message, customer:, url: 'https://example.com/image.jpg',
                                  file_type: 'image', filename: 'image.jpg')
      end

      it 'enqueues MessengerJob for media message with media_params' do
        expect(Mia::Chatbot::MessengerJob).to receive(:perform_later)
          .with(customer.id, 'image', message.reply_to, { url: message.url, file_name: message.filename })

        described_class.new(message).call
      end
    end

    context 'when mia chatbot is inactive but message is older than 24h' do
      let(:message) { create(:facebook_message, customer:, text: 'Hola') }

      before do
        customer.update!(allow_mia_chatbot: false)
        create(:facebook_message, customer:, created_at: 25.hours.ago)
      end

      it 'activates the chatbot and enqueues job' do
        expect do
          described_class.new(message).call
        end.to change { customer.customer_mia_chatbot_for('messenger').active }.from(false).to(true)
          .and have_enqueued_job(Mia::Chatbot::MessengerJob)
      end
    end

    context 'when message is within 24h and chatbot is inactive' do
      before do
        customer.update!(allow_mia_chatbot: false)
        create(:facebook_message, customer:, created_at: 10.minutes.ago)
      end

      it 'does not enqueue the job' do
        expect do
          described_class.new(message).call
        end.not_to have_enqueued_job(Mia::Chatbot::MessengerJob)
      end
    end
  end

  describe '#inbound_type' do
    it 'returns text if no url' do
      expect(service.send(:inbound_type)).to eq('text')
    end

    it 'returns file_type if url present' do
      message.update!(url: 'https://example.com/file.mp4', file_type: 'video')
      expect(service.send(:inbound_type)).to eq('video')
    end
  end

  describe '#inbound_message' do
    it 'returns message text if text' do
      expect(service.send(:inbound_message)).to eq('Hola')
    end

    it 'returns message url if media' do
      message.update!(file_type: 'image', url: 'https://example.com/img.jpg')
      expect(service.send(:inbound_message)).to eq('https://example.com/img.jpg')
    end
  end

  describe '#message_enqueued?' do
    it 'returns true if platform_message_id matches message.id' do
      CustomerMessageJob.create!(platform: 'messenger', customer_id: customer.id, platform_message_id: message.id)
      expect(service.send(:message_enqueued?)).to be true
    end

    it 'returns false otherwise' do
      expect(service.send(:message_enqueued?)).to be false
    end
  end

  describe '#last_message' do
    it 'returns the second to last facebook message' do
      create(:facebook_message, customer:, created_at: 2.hours.ago)
      second_msg = create(:facebook_message, customer:, created_at: 1.hour.ago)
      message.update!(created_at: Time.current)

      result = service.send(:last_message)
      expect(result).to eq(second_msg)
    end
  end
end
