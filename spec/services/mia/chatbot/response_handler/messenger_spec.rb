require 'rails_helper'

RSpec.describe Mia::Chatbot::ResponseHandler::Messenger do
  subject(:handler) { described_class.new(mia_response: mia_response, customer: customer) }

  let(:retailer) { create(:retailer, :fb_integrated) }
  let(:customer) { create(:customer, retailer:) }
  let(:mia_response) do
    OpenStruct.new(
      status: 200,
      body: {
        'flag' => 'message',
        'response' => [{ 'text' => 'Hola Messenger!' }],
        'sources' => { 'source' => 'test_source' }
      },
      customer_id: customer.id,
      platform: 'messenger'
    )
  end

  describe '#call' do
    it 'sends a text message via Messenger and saves the source' do
      expect do
        handler.call
      end.to change(FacebookMessage, :count).by(1)
        .and change(MessageMiaResponseSourcesData, :count).by(1)

      message = FacebookMessage.last
      expect(message.text).to eq('Hola Messenger!')
      expect(message.sent_by_mia).to be true
    end
  end
end
