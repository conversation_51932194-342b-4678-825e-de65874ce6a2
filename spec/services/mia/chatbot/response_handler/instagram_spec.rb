require 'rails_helper'

RSpec.describe Mia::Chatbot::ResponseHandler::Instagram do
  subject(:handler) { described_class.new(mia_response: mia_response, customer: customer) }

  let(:retailer) { create(:retailer, :ig_integrated) }
  let(:customer) { create(:customer, retailer:) }
  let(:mia_response) do
    OpenStruct.new(
      status: 200,
      body: {
        'flag' => 'message',
        'response' => [{ 'text' => 'Hola desde MIA!' }],
        'sources' => { 'source' => 'test_source' }
      },
      customer_id: customer.id,
      platform: 'instagram'
    )
  end

  describe '#call' do
    it 'sends a text message and saves the source' do
      expect do
        handler.call
      end.to change(InstagramMessage, :count).by(1)
        .and change(MessageMiaResponseSourcesData, :count).by(1)

      message = InstagramMessage.last
      expect(message.text).to eq('Hola desde MIA!')
      expect(message.sent_by_mia).to be true
    end
  end

  describe '#call with file message' do
    subject(:handler_with_file) do
      described_class.new(mia_response: mia_response_with_file, customer: customer)
    end

    let(:mia_response_with_file) do
      OpenStruct.new(
        status: 200,
        body: {
          'flag' => 'message',
          'response' => [
            {
              'text' => 'Aquí tienes un archivo',
              'references' => [
                {
                  'url' => 'https://cdn.mia.ai/archivo.pdf',
                  'content_type' => 'application/pdf'
                }
              ]
            }
          ],
          'sources' => { 'source' => 'test_source' }
        },
        customer_id: customer.id,
        platform: 'instagram'
      )
    end

    it 'envía un archivo y guarda la fuente' do
      expect do
        handler_with_file.call
      end.to change(InstagramMessage, :count).by(2)
        .and change(MessageMiaResponseSourcesData, :count).by(1)

      message = InstagramMessage.last
      expect(message.file_type).to eq('file')
      expect(message.filename).to eq('archivo.pdf')
      expect(message.sent_by_mia).to be true
    end
  end

  describe '#process_outbound_message' do
    let(:body_with_invalid_flag) do
      {
        'flag' => 'invalid_flag',
        'response' => ['Mensaje inválido']
      }
    end

    it 'logs error and does not process any type of message if the flag is invalid' do
      handler = described_class.new(mia_response: OpenStruct.new(body: body_with_invalid_flag, status: 200),
                                    customer: customer)

      expect(Rails.logger).to receive(:error)
        .with("There was an error trying to get Mia responses: Flag 'invalid_flag' not supported")

      expect(handler).not_to receive(:process_messages)
      expect(handler).not_to receive(:process_buy_intention)
      expect(handler).not_to receive(:process_human_help)

      handler.send(:process_outbound_message, body_with_invalid_flag)
    end
  end

  describe '#get_file_type' do
    it 'returns image when content_type includes image' do
      result = handler.send(:get_file_type, 'image/png')
      expect(result).to eq('image')
    end

    it 'returns audio when content_type includes audio' do
      result = handler.send(:get_file_type, 'audio/mpeg')
      expect(result).to eq('audio')
    end

    it 'returns video when content_type includes video' do
      result = handler.send(:get_file_type, 'video/mp4')
      expect(result).to eq('video')
    end

    it 'returns file when content_type is unknown' do
      result = handler.send(:get_file_type, 'application/pdf')
      expect(result).to eq('file')
    end
  end
end
