require 'rails_helper'

RSpec.describe Mia::Chatbot::ResponseHandler::Whatsapp do
  let(:retailer) { create(:retailer) }
  let(:customer) { create(:customer, retailer:) }

  let(:whatsapp_service_double) do
    instance_double(Whatsapp::Outbound::Msg, send_message: { message: OpenStruct.new(id: 123) })
  end

  before do
    allow(Whatsapp::Outbound::Msg).to receive(:new).and_return(whatsapp_service_double)
    allow(whatsapp_service_double).to receive(:create_mia_internal_message)
    allow(whatsapp_service_double).to receive(:create_note)
  end

  describe '#call' do
    context 'when status is 200 and flag is :message' do
      let(:mia_response) do
        OpenStruct.new(
          status: 200,
          body: {
            'flag' => 'message',
            'response' => [{ 'text' => 'Hola, ¿cómo estás?' }],
            'sources' => { source: 'openai' }
          },
          customer_id: customer.id,
          platform: 'whatsapp'
        )
      end

      it 'sends a text message' do
        expect(whatsapp_service_double).to receive(:send_message)
          .with(type: 'text', params: { type: 'text', message: '<PERSON><PERSON>, ¿cómo estás?' })
        described_class.new(mia_response:).call
      end

      it 'saves the sources' do
        expect do
          described_class.new(mia_response:).call
        end.to change(MessageMiaResponseSourcesData, :count).by(1)
      end
    end

    context 'when status is 200 and flag is :buy' do
      let(:mia_response) do
        OpenStruct.new(
          status: 200,
          body: {
            'flag' => 'buy',
            'response' => 'Gracias por tu compra',
            'summary' => 'Cliente interesado en comprar'
          },
          customer_id: customer.id,
          platform: 'whatsapp'
        )
      end

      it 'sends a buy intention message and updates customer_mia_chatbot' do
        chatbot = create(:customer_mia_chatbot, customer:, platform: 'whatsapp', active: true)

        expect(whatsapp_service_double).to receive(:send_message)
          .with(type: 'text', params: { type: 'text', message: 'Gracias por tu compra' })
        expect(whatsapp_service_double).to receive(:create_mia_internal_message).with('buy')
        expect(whatsapp_service_double).to receive(:create_note)

        described_class.new(mia_response:).call

        expect(chatbot.reload.active).to be false
        expect(chatbot.buy_intention).to be true
      end
    end

    context 'when status is 202 and flag is :help' do
      let(:mia_response) do
        OpenStruct.new(
          status: 202,
          body: {
            'flag' => 'help',
            'response' => 'Te transferimos a un agente',
            'summary' => 'Cliente necesita ayuda humana'
          },
          customer_id: customer.id,
          platform: 'whatsapp'
        )
      end

      it 'sends help message and updates customer_mia_chatbot' do
        chatbot = create(:customer_mia_chatbot, customer:, platform: 'whatsapp', active: true)

        expect(whatsapp_service_double).to receive(:send_message)
          .with(type: 'text', params: { type: 'text', message: 'Te transferimos a un agente' })
        expect(whatsapp_service_double).to receive(:create_mia_internal_message).with('human_help')
        expect(whatsapp_service_double).to receive(:create_note)

        described_class.new(mia_response:).call

        expect(chatbot.reload.active).to be false
        expect(chatbot.human_help).to be true
      end
    end

    context 'when mia_response has unsupported flag' do
      let(:mia_response) do
        OpenStruct.new(
          status: 200,
          body: {
            'flag' => 'unknown_flag',
            'response' => ['text' => 'irrelevant']
          },
          customer_id: customer.id,
          platform: 'whatsapp'
        )
      end

      it 'logs an error and does not crash' do
        expect(Rails.logger).to receive(:error)
          .with("There was an error trying to get Mia responses: Flag 'unknown_flag' not supported")
        described_class.new(mia_response:).call
      end
    end

    context 'when mia_response has status != 200/202' do
      let(:mia_response) do
        OpenStruct.new(
          status: 500,
          body: { error: 'Internal error' },
          customer_id: customer.id,
          platform: 'whatsapp'
        )
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error)
          .with('There was an error trying to get Mia responses: {:error=>"Internal error"}')
        described_class.new(mia_response:).call
      end
    end

    context 'when customer is nil' do
      let(:mia_response) do
        OpenStruct.new(
          status: 200,
          body: { 'flag' => 'message', 'response' => [] },
          customer_id: nil,
          platform: 'whatsapp'
        )
      end

      it 'does not process the response' do
        expect(described_class.new(mia_response:).call).to be_nil
      end
    end
  end

  describe '#whatsapp_service' do
    subject(:handler) do
      described_class.new(mia_response: mia_response).tap do |instance|
        instance.instance_variable_set(:@customer, customer)
        instance.instance_variable_set(:@retailer, retailer)
      end
    end

    let(:mia_response) do
      OpenStruct.new(customer_id: customer.id, status: 200, body: { 'flag' => 'message', 'response' => [] })
    end

    context 'when the retailer is integrated with gupshup' do
      before { allow(retailer).to receive(:gupshup_integrated?).and_return(true) }

      it 'creates an instance of Whatsapp::Outbound::Msg with provider gupshup' do
        expect(Whatsapp::Outbound::Msg).to receive(:new)
          .with(retailer, customer, nil, 'gupshup', true)
          .and_return(double)

        handler.send(:whatsapp_service)
      end
    end

    context 'when the retailer is NOT integrated with gupshup' do
      before { allow(retailer).to receive(:gupshup_integrated?).and_return(false) }

      it 'creates an instance of Whatsapp::Outbound::Msg with provider maytapi' do
        expect(Whatsapp::Outbound::Msg).to receive(:new)
          .with(retailer, customer, nil, 'maytapi', true)
          .and_return(double)

        handler.send(:whatsapp_service)
      end
    end

    context 'when @whatsapp_service has already been cached' do
      let(:cached_service) { double('WhatsappService') }

      before do
        handler.instance_variable_set(:@whatsapp_service, cached_service)
      end

      it 'returns the cached instance' do
        expect(handler.send(:whatsapp_service)).to eq(cached_service)
      end
    end
  end
end
