require 'rails_helper'

RSpec.describe Customer::NotifiableUpdate, type: :service do
  describe '#call' do
    let(:retailer) { create(:retailer) }
    let!(:customer) { create(:customer, retailer: retailer) }
    let!(:user1) { create(:retailer_user, retailer: retailer) }
    let!(:user2) { create(:retailer_user, retailer: retailer) }

    let(:redis_mock) { instance_double(Redis, publish: true) }

    before do
      allow(Redis).to receive(:new).and_return(redis_mock)
    end

    it 'publishes a message for each active user' do
      service = described_class.new(customer)

      expect(redis_mock).to receive(:publish).with(
        'update_customer_chat_info',
        {
          retailer_id: retailer.id,
          customer_id: customer.id,
          room: user1.id,
          status: true
        }.to_json
      )

      expect(redis_mock).to receive(:publish).with(
        'update_customer_chat_info',
        {
          retailer_id: retailer.id,
          customer_id: customer.id,
          room: user2.id,
          status: true
        }.to_json
      )

      service.call
    end

    it 'rescues and logs errors without stopping iteration' do
      allow(redis_mock).to receive(:publish).and_raise(StandardError.new('Redis error'))
      expect(Rails.logger).to receive(:error).twice.with(/Error al notificar al usuario/)

      expect { described_class.new(customer).call }.not_to raise_error
    end
  end
end
