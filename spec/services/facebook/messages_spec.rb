require 'rails_helper'
require 'vcr'

RSpec.describe Facebook::Messages, :vcr do
  subject(:messages_service) { described_class.new(facebook_retailer) }

  let!(:retailer) { create(:retailer) }
  let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
  let!(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }

  let!(:customer) do
    create(:customer, :messenger, retailer: facebook_retailer.retailer, psid: message_data_save['sender']['id'])
  end

  let(:message_id) { 'm_5zQXER3IeTxCOXaHhNOhD0OUElOLpk3ODw-8fGJl54d3iTker4IqLmXsMnseIWB5jfv61RUW9yCCJdOcqegr2w' }

  let(:message_data_save) do
    {
      sender: {
        id: '2701618273252603'
      },
      recipient: {
        id: '101072371445783'
      },
      timestamp: 158_471_290_274_7,
      message: {
        mid: 'm_KODXgRl3Z3jMnhcco43SmkOUElOLpk3ODw-8fGJl54fOaz40lxlvbAxoWPFIKA4cNc_0DXcN4qN9F6muDspxQA',
        text: 'Buenos días'
      }
    }.with_indifferent_access
  end

  let(:response_message_read) { { recipient_id: '2701618273252603' }.with_indifferent_access }

  let(:image_response) do
    {
      sender: {
        id: '2701618273252603'
      },
      recipient: {
        id: '101072371445783'
      },
      timestamp: 158_471_290_274_7,
      message: {
        mid: 'm_nSu6JMgG3WAPSk8VnAZGkUOUElOLpk3ODw-8fGJl54ceV2t3gs0-kBbFNPIKzcmYG1Fs3Nu5TUK0jlwdfBSp-g',
        attachments: [
          {
            type: 'image',
            payload: {
              url: 'https://scontent.xx.fbcdn.net/v/t1.15752-9/90441567_200152011413011_5011659860094222336_n.png?' \
                   '_nc_cat=104&_nc_sid=b96e70&_nc_ohc=eUTiF2BmlOIAX-3TbsS&_nc_ad=z-m&_nc_cid=0&_nc_ht=' \
                   'scontent.xx&oh=593765a0372736abb1a05f7964b4221c&oe=5EFBA946'
            }
          }
        ]
      }
    }.with_indifferent_access
  end

  let(:location_response) do
    {
      sender: {
        id: '2701618273252603'
      },
      recipient: {
        id: '101072371445783'
      },
      timestamp: 158_471_290_274_7,
      message: {
        mid: 'm_RLWdGrKwoIB9ZZwkROgzaEOUElOLpk3ODw-8fGJl54c6pGmhwTRWjyilAy5GIyEX9VL9p3xOjfMgm8RC-gS23g',
        attachments: [
          {
            title: 'Maracay',
            url: 'https://l.facebook.com/l.php?u=https%3A%2F%2Fwww.bing.com%2Fmaps%2Fdefault.' \
                 'aspx%3Fv%3D2%26pc%3DFACEBK%26mid%3D8100%26where1%3DMaracay%26FORM%3DFBKPL1%26mkt%3Des-MX&h=' \
                 'AT3jp3ZMvmgmJPdyHSlEEmkNzpQTYwU65ByZ6jRX8j99cGpYjkFuvk3BQItwHxuMk0QzmnpT0iyqP-TngYz9NofrlbHerzu' \
                 '89UurEeBOjDyZapFbknMBLQonuUI-CBvgnBN4lYkfYylE&s=1',
            type: 'location',
            payload: {
              coordinates: {
                lat: 10.2399,
                long: -67.6036
              }
            }
          }
        ]
      }
    }.with_indifferent_access
  end

  let(:send_attachment_response) do
    {
      recipient_id: '1008372609250235',
      message_id: 'm_AG5Hz2Uq7tuwNEhXfYYKj8mJEM_QPpz5jdCK48PnKAjSdjfipqxqMvK8ma6AC8fplwlqLP_5cgXIbu7I3rBN0P'
    }.with_indifferent_access
  end

  let(:get_started_button_response) do
    {
      sender: {
        id: '2701618273252603'
      },
      recipient: {
        id: '101072371445783'
      },
      timestamp: 1_615_219_411_827,
      postback: {
        title: 'Get Started',
        payload: 'Iniciar chat'
      }
    }.with_indifferent_access
  end

  let(:message_form_webhook) do
    {
      sender: {
        id: '2701618273252603'
      },
      recipient: {
        id: '101072371445783'
      },
      timestamp: 1_683_127_165_379,
      postback: {
        title: 'Start Chatting',
        payload: 'USER-DEFINED-PAYLOAD',
        mid: 'm_nlv6QAZW1Uf_rE-otMOKhaIzGpLCj-e99KDaI_JAw4VEIfgcdW0FnCJNo6FV0YReiwy-O-hMiPr3AfNTdp9fng'
      }
    }.with_indifferent_access
  end

  let(:message_without_postback) do
    {
      sender: {
        id: '2701618273252603'
      },
      recipient: {
        id: '101072371445783'
      },
      timestamp: 1_683_127_165_379,
      postback: {}
    }.with_indifferent_access
  end

  let(:message_without_title) do
    {
      sender: {
        id: '2701618273252603'
      },
      recipient: {
        id: '101072371445783'
      },
      timestamp: 1_683_127_165_379,
      postback: {
        title: nil,
        payload: 'USER-DEFINED-PAYLOAD'
      }
    }.with_indifferent_access
  end

  let(:video_response_msn) do
    {
      message: '',
      attachments: {
        data: [
          {
            id: '935236408019556',
            mime_type: 'video/mp4',
            name: 'video-45a70c30-745a-437b-9d42-9f07b15c7901-1706209826.mp4',
            size: 229_451,
            video_data: {
              width: 352,
              height: 640,
              length: 4,
              video_type: 1,
              url: 'https://scontent.fmyc2-1.fna.fbcdn.net/v/t42.3356-2/388936622_6169508353149748_2604780901517007053_n.mp4?_nc_cat=106&ccb=1-7&_nc_sid=4f86bc&_nc_ohc=pY6XYFDn6kMAX_v7v2U&_nc_ht=scontent.fmyc2-1.fna&edm=AKVc4UAEAAAA&oh=03_AdRzyMvKOuhpXmN2skIdVjzgYolnDeCX3FIjLd23Vnlh_Q&oe=65B475E3&dl=1',
              preview_url: 'https://scontent.fmyc2-1.fna.fbcdn.net/v/t15.3394-10/358132627_6721640464564606_1057514963248854002_n.jpg?_nc_cat=102&ccb=1-7&_nc_sid=0e5130&_nc_ohc=xtaSA113PocAX-8Vs4m&_nc_ht=scontent.fmyc2-1.fna&edm=AKVc4UAEAAAA&oh=03_AdR0G_F23-4e0dF_EnmL0_gG4uHjHDgl1nx512saccFBlw&oe=65B84F28',
              rotation: 0
            }
          }
        ]
      },
      id: 'm_xik0QwgJDtrb3VooUMSI6zmCqkDfrjSd0lw5F1zcmUPmqS3Ma78sWAHF2XHGsi_hsFI0WHMzrjXUn9MY8Za4Tw'
    }.with_indifferent_access
  end

  describe '#initialize' do
    context 'when type is messenger' do
      it 'sets the type, facebook_retailer, and klass instance variables' do
        message = described_class.new(facebook_retailer)
        expect(message.instance_variable_get(:@type)).to eq('messenger')
        expect(message.instance_variable_get(:@facebook_retailer)).to eq(facebook_retailer)
        expect(message.instance_variable_get(:@klass)).to be(FacebookMessage)
      end
    end

    context 'when type is instagram' do
      it 'sets the type, facebook_retailer, and klass instance variables' do
        message = described_class.new(facebook_retailer, 'instagram')
        expect(message.instance_variable_get(:@type)).to eq('instagram')
        expect(message.instance_variable_get(:@facebook_retailer)).to eq(facebook_retailer)
        expect(message.instance_variable_get(:@klass)).to eq(InstagramMessage)
      end
    end
  end

  describe '#save' do
    let(:customers_service) { Facebook::Customers.new(facebook_retailer) }

    before do
      allow(Facebook::Customers).to receive(:new).and_return(customers_service)
      allow_any_instance_of(Facebook::Customers).to receive(:import).and_return(customer)
    end

    context 'when is a text message' do
      it 'saves the text' do
        expect { messages_service.save(message_data_save) }.to change(FacebookMessage, :count).by(1)
        expect(FacebookMessage.last.text).to eq('Buenos días')
      end
    end

    context 'when is a referral ad with a photo_url' do
      let(:referral_message_with_photo) do
        {
          sender: { id: '2701618273252603' },
          recipient: { id: '101072371445783' },
          timestamp: 158_471_290_274_7,
          message: {
            mid: 'm_KODXgRl3Z3jMnhcco43SmkOUElOLpk3ODw-8fGJl54fOaz40lxlvbAxoWPFIKA4cNc_0DXcN4qN9F6muDspxQA',
            text: 'Buenos días',
            referral: {
              ads_context_data: {
                photo_url: 'https://sampleimage.com/photo.jpg'
              }
            }
          }
        }.with_indifferent_access
      end

      before do
        allow_any_instance_of(described_class).to receive(:sniff_mime_type).and_return('image')
        allow_any_instance_of(described_class)
          .to receive(:get_fallback_media_url)
          .and_return(['https://sampleimage.com/photo.jpg', 'image'])
      end

      it 'saves the message with photo_url and file_type image' do
        expect { messages_service.save(referral_message_with_photo) }.to change(FacebookMessage, :count).by(1)
        last_message = FacebookMessage.last
        expect(last_message.text).to eq('Buenos días')
        expect(last_message.url).to eq('https://sampleimage.com/photo.jpg')
        expect(last_message.file_type).to eq('image')
      end
    end

    context 'when is a referral ad with a video_url' do
      let(:referral_message_with_video) do
        {
          sender: { id: '2701618273252603' },
          recipient: { id: '101072371445783' },
          timestamp: 158_471_290_274_7,
          message: {
            mid: 'm_KODXgRl3Z3jMnhcco43SmkOUElOLpk3ODw-8fGJl54fOaz40lxlvbAxoWPFIKA4cNc_0DXcN4qN9F6muDspxQA',
            text: 'Buenos días',
            referral: {
              ads_context_data: {
                video_url: 'https://samplevideo.com/video.mp4'
              }
            }
          }
        }.with_indifferent_access
      end

      before do
        allow_any_instance_of(described_class).to receive(:sniff_mime_type).and_return('video')
        allow_any_instance_of(described_class)
          .to receive(:get_fallback_media_url)
          .and_return(['https://samplevideo.com/video.mp4', 'video'])
      end

      it 'saves the message with video_url and file_type video' do
        expect { messages_service.save(referral_message_with_video) }.to change(FacebookMessage, :count).by(1)
        last_message = FacebookMessage.last
        expect(last_message.text).to eq('Buenos días')
        expect(last_message.url).to eq('https://samplevideo.com/video.mp4')
        expect(last_message.file_type).to eq('video')
      end
    end

    context 'when is a location message' do
      it 'saves the location' do
        expect { messages_service.save(location_response) }.to change(FacebookMessage, :count).by(1)
        expect(FacebookMessage.last.file_type).to eq('location')
      end
    end

    context 'when is any other type of message' do
      before do
        allow(UploadResourcesJob).to receive(:perform_later).and_return(true)
      end

      it 'saves the message' do
        expect { messages_service.save(image_response) }.to change(FacebookMessage, :count).by(1)
        expect(FacebookMessage.last.file_type).to eq('image')
      end
    end
  end

  describe '#import_delivered' do
    let(:facebook_message) { create(:facebook_message, facebook_retailer: facebook_retailer, mid: message_id) }

    it 'updates the message sent from Mercately' do
      expect(facebook_message.file_type).to be_nil

      VCR.use_cassette('facebook/messages/message_imported') do
        messages_service.import_delivered(facebook_message.mid, customer.psid)
        expect(facebook_message.reload.file_type).not_to be_nil
      end
    end
  end

  describe '#send_attachment' do
    context 'when file_data is present' do
      before do
        allow(Connection).to receive(:post_form_request)
          .and_return(instance_double(Faraday::Response, status: 200, body: send_attachment_response.to_json))
      end

      it 'sends an attachment to Messenger' do
        file = fixture_file_upload(Rails.root.join('spec/fixtures/files/profile.jpg'), 'image/jpeg')

        response = messages_service.send_attachment(customer.psid, file.path, 'profile.jpg', nil, nil, nil, {}, nil)
        expect(response['message_id']).not_to be_nil
      end
    end

    context 'when url is present' do
      before do
        allow(Connection).to receive(:post_form_request)
          .and_return(instance_double(Faraday::Response, status: 200,
                                                         body: send_attachment_response.to_json))
      end

      it 'sends an attachment to Messenger' do
        response = messages_service.send_attachment(customer.psid, nil, nil, 'https://www.images.com/image.jpg',
                                                    'image', nil, {}, nil)

        expect(response['message_id']).not_to be_nil
      end
    end
  end

  describe '#mark_read' do
    before do
      create_list(:facebook_message, 2, facebook_retailer: facebook_retailer, customer:
        customer, sent_by_retailer: true)
    end

    it 'sets as read all the messages sent by the retailer' do
      expect(customer.facebook_messages.retailer_unread.count).to eq(2)
      messages_service.mark_read(customer.psid)
      expect(customer.facebook_messages.retailer_unread.count).to eq(0)
    end
  end

  describe '#send_read_action' do
    before do
      allow(Connection).to receive(:post_request)
        .and_return(response_message_read)
    end

    it 'sets as read the message sent by the customer in messenger' do
      expect(messages_service.send_read_action(customer.psid, 'mark_seen')).to eq(response_message_read)
    end
  end

  describe '#save_postback_interaction' do
    let(:customers_service) { Facebook::Customers.new(facebook_retailer) }

    before do
      allow(Facebook::Customers).to receive(:new).and_return(customers_service)
      allow_any_instance_of(Facebook::Customers).to receive(:import).and_return(customer)
    end

    it 'saves get started button payload as a text message' do
      expect do
        messages_service.save_postback_interaction(get_started_button_response)
      end.to change(FacebookMessage, :count).by(1)

      expect(FacebookMessage.last.text).to eq('Get Started')
    end

    it 'saves form buttons payload as a text message' do
      expect do
        messages_service.save_postback_interaction(message_form_webhook)
      end.to change(FacebookMessage, :count).by(1)

      expect(FacebookMessage.last.text).to eq('Start Chatting')
    end

    context 'when postback is empty' do
      it 'saves the message' do
        expect do
          messages_service.save_postback_interaction(message_without_postback)
        end.to change(FacebookMessage, :count).by(1)

        expect(FacebookMessage.last.text).to be_nil
      end
    end

    context 'when postback title is empty' do
      it 'saves the message' do
        expect do
          messages_service.save_postback_interaction(message_without_title)
        end.to change(FacebookMessage, :count).by(1)

        expect(FacebookMessage.last.text).to be_nil
      end
    end
  end

  describe '#prepare_message' do
    let(:to) { customer.psid }
    let(:message) { 'Hello, world!' }
    let(:payload) { nil }
    let(:comment_id) { '123456789' }

    context 'when payload is nil' do
      context 'when it is not sent to a comment' do
        it 'returns a plain message' do
          expected_body = {
            recipient: {
              id: to
            },
            message: {
              text: message
            }
          }.to_json
          expect(messages_service.send('prepare_message', to, message, payload, nil)).to eq(expected_body)
        end
      end

      context 'when it is sent to a comment' do
        it 'returns a plain message' do
          expected_body = {
            recipient: {
              comment_id: comment_id
            },
            message: {
              text: message
            }
          }.to_json
          expect(messages_service.send('prepare_message', to, message, payload, comment_id)).to eq(expected_body)
        end
      end
    end

    context 'when payload is present' do
      context 'when 3 or less options' do
        let(:payload) do
          {
            message: 'Pick a option:',
            list: [
              { title: 'Option 1', postbackText: 'Option 1 selected' },
              { title: 'Option 2', postbackText: 'Option 2 selected' }
            ]
          }.with_indifferent_access
        end
        let(:expected_body) do
          {
            recipient: {
              id: to
            },
            message: {
              attachment: {
                type: 'template',
                payload: {
                  template_type: 'button',
                  text: 'Pick a option:',
                  buttons: [
                    { type: 'postback', title: 'Option 1', payload: 'Option 1 selected' },
                    { type: 'postback', title: 'Option 2', payload: 'Option 2 selected' }
                  ]
                }
              }
            }
          }.to_json
        end

        it 'returns an interactive buttons message' do
          expect(messages_service.send('prepare_message', to, message, payload, nil)).to eq(expected_body)
        end
      end

      context 'when more than 3 options' do
        let(:payload) do
          {
            message: 'Pick a option:',
            list: [
              { title: 'Option 1', postbackText: 'Option 1 selected' },
              { title: 'Option 2', postbackText: 'Option 2 selected' },
              { title: 'Option 3', postbackText: 'Option 3 selected' },
              { title: 'Option 4', postbackText: 'Option 4 selected' }
            ]
          }.with_indifferent_access
        end
        let(:expected_body) do
          {
            recipient: {
              id: to
            },
            message: {
              text: 'Pick a option:',
              quick_replies: [
                { content_type: 'text', title: 'Option 1', payload: 'Option 1 selected' },
                { content_type: 'text', title: 'Option 2', payload: 'Option 2 selected' },
                { content_type: 'text', title: 'Option 3', payload: 'Option 3 selected' },
                { content_type: 'text', title: 'Option 4', payload: 'Option 4 selected' }
              ]
            }
          }.to_json
        end

        it 'returns an interactive list message' do
          expect(messages_service.send('prepare_message', to, message, payload, nil)).to eq(expected_body)
        end
      end
    end
  end

  describe '#add_human_agent_tag' do
    let(:body) { { recipient: { id: customer.psid }, message: { text: 'Inbound text' } } }
    let!(:fb_message) do
      create(:facebook_message, facebook_retailer: facebook_retailer,
                                customer: customer,
                                text: 'Inbound text',
                                sent_by_retailer: false,
                                created_at: 2.days.ago)
    end

    it 'adds the human agent tag to the message' do
      messages_service.instance_variable_set(:@type, 'messenger')
      expect(messages_service.send('add_human_agent_tag', customer.psid, body))
        .to eq(body.merge({ messaging_type: 'MESSAGE_TAG', tag: 'HUMAN_AGENT' }))
    end

    it 'does not add the human agent tag to the message' do
      messages_service.instance_variable_set(:@type, 'instagram')
      expect(messages_service.send('add_human_agent_tag', customer.psid, body)).to eq(body)
    end
  end

  describe '#prepare_attachment' do
    let(:to) { customer.psid }
    let(:filename) { 'image.png' }
    let(:url) { 'https://example.com/image.png' }
    let(:file_type) { 'image' }
    let(:file_content_type) { 'image/png' }
    let(:payload) { {} }
    let(:comment_id) { '123456789' }

    context 'when file_path is not present and file_url is present' do
      context 'when it is not sent to a comment' do
        let(:expected_body) do
          {
            recipient: {
              id: to
            },
            message: {
              attachment: {
                type: file_type,
                payload: {
                  url: url
                }
              }
            }
          }
        end

        it 'returns a message with a URL' do
          expect(
            messages_service.send('prepare_attachment', to, nil, filename, url, file_type, file_content_type, payload)
          ).to eq(expected_body)
        end
      end

      context 'when it is sent to a comment' do
        let(:expected_body) do
          {
            recipient: {
              comment_id: comment_id
            },
            message: {
              attachment: {
                type: file_type,
                payload: {
                  url: url
                }
              }
            }
          }
        end

        it 'returns a message with a URL' do
          expect(
            messages_service.send('prepare_attachment', to, nil, filename, url, file_type, file_content_type, payload,
                                  comment_id)
          ).to eq(expected_body)
        end
      end
    end

    context 'when payload is present' do
      context 'when it is not sent to a comment' do
        let(:payload) do
          {
            list: [
              { title: 'Option 1', postbackText: 'Option 1 selected' },
              { title: 'Option 2', postbackText: 'Option 2 selected' }
            ]
          }.with_indifferent_access
        end
        let(:expected_body) do
          {
            recipient: {
              id: to
            },
            message: {
              attachment: {
                type: file_type,
                payload: {
                  url: url
                }
              },
              quick_replies: JSON.dump([{ content_type: 'text', title: 'Option 1', payload: 'Option 1 selected' },
                                        { content_type: 'text', title: 'Option 2', payload: 'Option 2 selected' }])
            }
          }
        end

        it 'returns a message with an attachment list' do
          expect(
            messages_service.send('prepare_attachment', to, nil, filename, url, file_type, file_content_type, payload)
          ).to eq(expected_body)
        end
      end

      context 'when it is sent to a comment' do
        let(:payload) do
          {
            list: [
              { title: 'Option 1', postbackText: 'Option 1 selected' },
              { title: 'Option 2', postbackText: 'Option 2 selected' }
            ]
          }.with_indifferent_access
        end
        let(:expected_body) do
          {
            recipient: {
              comment_id: comment_id
            },
            message: {
              attachment: {
                type: file_type,
                payload: {
                  url: url
                }
              },
              quick_replies: JSON.dump([{ content_type: 'text', title: 'Option 1', payload: 'Option 1 selected' },
                                        { content_type: 'text', title: 'Option 2', payload: 'Option 2 selected' }])
            }
          }
        end

        it 'returns a message with an attachment list' do
          expect(
            messages_service.send('prepare_attachment', to, nil, filename, url, file_type, file_content_type, payload,
                                  comment_id)
          ).to eq(expected_body)
        end
      end
    end
  end

  describe '#save_delivered' do
    context 'when it is a video message' do
      it 'saves a new video messenger message' do
        expect do
          messages_service.save_delivered(video_response_msn, customer.psid)
        end.to change(FacebookMessage, :count).by(1)

        last_message = FacebookMessage.last
        expect(last_message.file_type).to include('video')
      end
    end
  end

  describe '#check_content_type' do
    it 'returns nil' do
      expect(messages_service.send(:check_content_type, nil)).to be_nil
    end

    context 'when it is a video' do
      it 'returns video' do
        expect(messages_service.send(:check_content_type, 'video/mp4')).to eq('video')
      end
    end

    context 'when it is an image' do
      it 'returns image' do
        expect(messages_service.send(:check_content_type, 'image/png')).to eq('image')
      end
    end

    context 'when it is an audio' do
      it 'returns audio' do
        expect(messages_service.send(:check_content_type, 'audio/mp3')).to eq('audio')
      end
    end

    context 'when it is a PDF' do
      it 'returns file' do
        expect(messages_service.send(:check_content_type, 'application/pdf')).to eq('file')
      end
    end
  end

  describe '#grab_url' do
    context 'when it is a video response' do
      it 'returns the video url' do
        attachment = video_response_msn['attachments']&.[]('data')&.[](0)
        file_type = attachment&.[]('mime_type')
        url = attachment&.[]('video_data')&.[]('url')

        expect(messages_service.send(:grab_url, video_response_msn, file_type)).to eq(url)
      end
    end
  end

  describe '#sniff_mime_type' do
    let(:facebook_retailer) { create(:facebook_retailer) }
    let(:messages_service) { described_class.new(facebook_retailer) }

    it 'returns the correct mime type for a valid URL' do
      url = 'https://example.com/image.jpg'
      allow(Net::HTTP).to receive(:get_response).and_return(double(content_type: 'image/jpeg'))
      expect(messages_service.send(:sniff_mime_type, url)).to eq('image')
    end

    it 'returns "unknown" for an invalid URL' do
      url = 'https://invalid-url.com'
      allow(Net::HTTP).to receive(:get_response).and_raise(Errno::ENOENT)
      expect(messages_service.send(:sniff_mime_type, url)).to eq('unknown')
    end
  end
end
