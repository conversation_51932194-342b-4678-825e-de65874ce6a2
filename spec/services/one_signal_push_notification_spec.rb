require 'rails_helper'

RSpec.describe OneSignalPushNotification do
  let(:emails) { ['<EMAIL>', '<EMAIL>'] }
  let(:body) { 'Test message body' }
  let(:customer_id) { 1 }
  let(:channel) { 'push' }
  let(:additional_data) { { extra_key: 'extra_value' } }
  let(:notification_service) { described_class.new(emails, body, customer_id, channel, additional_data) }

  describe '#initialize' do
    it 'initializes with valid parameters' do
      expect(notification_service.instance_variable_get(:@emails)).to eq(emails)
      expect(notification_service.instance_variable_get(:@body)).to eq(body)
      expect(notification_service.instance_variable_get(:@customer_id)).to eq(customer_id)
      expect(notification_service.instance_variable_get(:@channel)).to eq(channel)
      expect(notification_service.instance_variable_get(:@additional_data)).to eq(additional_data)
    end

    context 'with invalid email parameters' do
      it 'handles nil emails gracefully' do
        service = described_class.new(nil, body, customer_id, channel)
        expect(service.instance_variable_get(:@emails)).to eq([])
      end

      it 'handles empty array of emails gracefully' do
        service = described_class.new([], body, customer_id, channel)
        expect(service.instance_variable_get(:@emails)).to eq([])
      end

      it 'handles array with nil values gracefully' do
        service = described_class.new(['<EMAIL>', nil, '<EMAIL>'], body, customer_id, channel)
        expect(service.instance_variable_get(:@emails)).to eq(['<EMAIL>', '<EMAIL>'])
      end
    end
  end

  describe '.send_sales_push' do
    let(:order) do
      {
        customer: { full_name: 'John Doe' },
        total: 100,
        web_id: 123
      }
    end

    before do
      allow(ENV).to receive(:[]).and_return(nil)
      allow(ENV).to receive(:[]).with('ONE_SIGNAL_APP_ID').and_return('test_app_id')
    end

    it 'sends a sales push notification' do
      expected_params = {
        'app_id' => 'test_app_id',
        'headings' => { 'en' => '¡Tienes un nuevo pedido! 🤑' },
        'contents' => { 'en' => "Cliente: John Doe.\nTotal: 100" },
        'data' => {
          'type' => 'navigate',
          'screen' => 'Order',
          'screenParamKey' => 'orderId',
          'screenParamValue' => 123
        },
        'priority' => 10,
        'content_available' => true,
        'mutable_content' => true,
        'channel_for_external_user_ids' => 'push',
        'include_external_user_ids' => emails
      }

      expect(described_class).to receive(:send_request).with(expected_params)
      described_class.send_sales_push(emails, order)
    end

    context 'when hide_prices is true' do
      it 'sends a sales push notification without total' do # rubocop:disable RSpec/ExampleLength
        expected_params = {
          'app_id' => 'test_app_id',
          'headings' => { 'en' => '¡Tienes un nuevo pedido! 🤑' },
          'contents' => { 'en' => 'Cliente: John Doe.' },
          'data' => {
            'type' => 'navigate',
            'screen' => 'Order',
            'screenParamKey' => 'orderId',
            'screenParamValue' => 123
          },
          'priority' => 10,
          'content_available' => true,
          'mutable_content' => true,
          'channel_for_external_user_ids' => 'push',
          'include_external_user_ids' => emails
        }

        expect(described_class).to receive(:send_request).with(expected_params)
        described_class.send_sales_push(emails, order, true)
      end
    end
  end

  describe '.send_qr_disconnection_push' do
    let(:reconnect_url) { 'https://example.com/reconnect' }

    before do
      allow(ENV).to receive(:[]).and_return(nil)
      allow(ENV).to receive(:[]).with('ONE_SIGNAL_APP_ID').and_return('test_app_id')
    end

    it 'sends a QR disconnection push notification' do
      expected_params = {
        'app_id' => 'test_app_id',
        'headings' => { 'en' => '¡Conexión de Whatsapp!' },
        'contents' => { 'en' => '⚠️ Parece que perdiste la conexión de WhatsApp, haz click aquí y escanea tu código QR para reconectarte.' },
        'data' => {},
        'url' => reconnect_url,
        'priority' => 10,
        'content_available' => true,
        'mutable_content' => true,
        'channel_for_external_user_ids' => 'push',
        'include_external_user_ids' => emails
      }

      expect(described_class).to receive(:send_request).with(expected_params)
      described_class.send_qr_disconnection_push(emails, reconnect_url)
    end
  end

  describe '.send_message' do
    let(:title) { 'Test Title' }
    let(:message) { 'Test Message' }

    before do
      allow(ENV).to receive(:[]).and_return(nil)
      allow(ENV).to receive(:[]).with('ONE_SIGNAL_APP_ID').and_return('test_app_id')
    end

    it 'sends a message push notification' do
      expected_params = {
        'app_id' => 'test_app_id',
        'headings' => { 'en' => message },
        'contents' => { 'en' => message },
        'data' => {
          type: 'message',
          title: title,
          body: message
        },
        'priority' => 10,
        'content_available' => true,
        'mutable_content' => true,
        'channel_for_external_user_ids' => 'push',
        'include_external_user_ids' => emails
      }

      expect(described_class).to receive(:send_request).with(expected_params)
      described_class.send_message(emails, title, message)
    end
  end

  describe '#send_messages' do
    let(:retailer_id) { 123 }
    let(:customer) {
      double('Customer', notification_info: 'Customer Info', present?: true, retailer_id: retailer_id)
    }

    before do
      allow(ENV).to receive(:[]).and_return(nil)
      allow(ENV).to receive(:[]).with('ONE_SIGNAL_APP_ID').and_return('test_app_id')
      allow(Customer).to receive(:find).with(customer_id).and_return(customer)
    end

    it 'sends messages when customer is present' do
      expected_params = {
        'app_id' => 'test_app_id',
        'headings' => { 'en' => 'Customer Info' },
        'contents' => { 'en' => body },
        'data' => {
          type: 'message',
          channel: channel,
          customer_id: customer_id,
          title: 'Customer Info',
          body: body,
          extra_key: 'extra_value',
          retailer_id: retailer_id
        },
        'priority' => 10,
        'content_available' => true,
        'mutable_content' => true,
        'channel_for_external_user_ids' => 'push',
        'include_external_user_ids' => emails,
        'android_channel_id' => '055762dd-3694-43fd-953f-25af3977b7d1',
        'android_group' => customer_id.to_s
      }

      expect(described_class).to receive(:send_request).with(expected_params)
      notification_service.send_messages
    end

    it 'does not send messages when customer is not found' do
      allow(Customer).to receive(:find).with(customer_id).and_return(nil)
      expect(described_class).not_to receive(:send_request)
      notification_service.send_messages
    end
  end

  describe '.send_financial_results_push' do
    let(:data) do
      {
        new_customers: 10,
        recurring_customers: 20,
        total_sales: 1000
      }
    end

    before do
      allow(ENV).to receive(:[]).and_return(nil)
      allow(ENV).to receive(:[]).with('ONE_SIGNAL_APP_ID').and_return('test_app_id')
      allow(Time).to receive(:now).and_return(Time.new(2024, 1, 2))
    end

    it 'sends financial results push notification' do
      expected_body = "Clientes nuevos: 10\nClientes recurrentes: 20\nTotal de ventas: 1000"
      expected_params = {
        'app_id' => 'test_app_id',
        'headings' => { 'en' => 'Tus resultados del 01-01-2024' },
        'contents' => { 'en' => expected_body },
        'data' => {},
        'priority' => 10,
        'content_available' => true,
        'mutable_content' => true,
        'channel_for_external_user_ids' => 'push',
        'include_external_user_ids' => emails
      }

      expect(described_class).to receive(:send_request).with(expected_params)
      described_class.send_financial_results_push(emails, data)
    end
  end
end
