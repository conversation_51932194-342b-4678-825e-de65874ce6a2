require 'rails_helper'

RSpec.describe Retailers::MyBusinessController do
  let(:retailer) { create(:retailer, catalog_slug: 'test-slug') }
  let(:user) { create(:user) }
  let(:retailer_user) { create(:retailer_user, retailer: retailer, user: user) }

  before do
    sign_in user
    allow(controller).to receive(:current_retailer).and_return(retailer)
  end

  describe 'GET #edit' do
    it 'renders edit template' do
      get :edit, params: { slug: retailer.slug }
      expect(response).to render_template(:edit)
    end
  end

  describe 'PUT #update_general_information' do
    context 'with valid parameters' do
      context 'when slug remains the same' do
        let(:valid_params) do
          {
            slug: retailer.slug,
            retailer: {
              name: retailer.name,
              description: 'Updated description',
              catalog_slug: 'updated-test-slug'
            }
          }
        end

        before do
          request.env['HTTP_REFERER'] = retailers_my_account_path(retailer)
        end

        it 'redirects back' do
          put :update_general_information, params: valid_params
          expect(response).to redirect_to(retailers_my_account_path(retailer))
        end

        it 'calls set_jwt_cookie' do
          # Verificar que se llama al método set_jwt_cookie
          expect(controller).to receive(:set_jwt_cookie).with(user)
          put :update_general_information, params: valid_params
        end
      end
    end
  end

  describe 'GET #locations' do
    it 'assigns retailer addresses' do
      address = create(:retailer_address, retailer: retailer)
      get :locations, params: { slug: retailer.slug }
      expect(assigns(:addresses)).to include(address)
      expect(assigns(:new_address)).to be_a_new(RetailerAddress)
    end
  end

  describe 'POST #create_address' do
    context 'with invalid parameters' do
      let(:invalid_params) do
        {
          slug: retailer.slug,
          retailer_address: {
            address: '',
            retailer_id: retailer.id
          }
        }
      end

      it 'renders locations with error messages' do
        post :create_address, params: invalid_params
        expect(response).to render_template(:locations)
        expect(assigns(:address).errors).to be_present
      end
    end
  end

  describe 'GET #schedules' do
    it 'creates default schedules when none exist' do
      get :schedules, params: { slug: retailer.slug }
      expect(assigns(:schedules).size).to eq(7)
      expect(assigns(:exists)).to be false
    end

    it 'uses existing schedules when present' do
      create(:retailer_schedule, retailer: retailer, weekday: 0)
      get :schedules, params: { slug: retailer.slug }
      expect(assigns(:exists)).to be true
    end
  end

  describe 'GET #shipping' do
    it 'renders shipping template' do
      get :shipping, params: { slug: retailer.slug }
      expect(response).to render_template(:shipping)
    end
  end

  describe 'GET #domain' do
    it 'renders domain template' do
      get :domain, params: { slug: retailer.slug }
      expect(response).to render_template(:domain)
    end
  end

  describe 'POST #set_domain' do
    let(:cert_content) { "-----BEGIN CERTIFICATE-----\nMIIDvTCCAqWgAwIBAgIUfSXZ\n-----END CERTIFICATE-----\n" }
    let(:key_content) { "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w\n-----END PRIVATE KEY-----\n" }

    before do
      Dir.mkdir('spec/support') unless File.directory?('spec/support')
      Dir.mkdir('spec/support/files') unless File.directory?('spec/support/files')

      File.write('spec/support/files/test.crt', cert_content)
      File.write('spec/support/files/test.key', key_content)
    end

    after do
      FileUtils.rm_rf('spec/support/files')
    end

    context 'with valid parameters' do
      let(:certificate) { Rack::Test::UploadedFile.new(Rails.root.join('spec/support/files/test.crt')) }
      let(:key_file) { Rack::Test::UploadedFile.new(Rails.root.join('spec/support/files/test.key')) }

      let(:valid_params) do
        {
          slug: retailer.slug,
          retailer: {
            domain: 'test.com',
            certificate_file: certificate,
            key_file: key_file
          }
        }
      end

      before do
        allow(Retailer::DomainSetter).to receive(:call).and_return(true)
      end

      it 'sets domain and redirects with success message' do
        post :set_domain, params: valid_params
        expect(response).to redirect_to(business_domain_path(retailer))
        expect(flash[:notice]).to eq(I18n.t('retailer.profile.my_business.edit.domain.success'))
      end
    end

    context 'with invalid parameters' do
      let(:invalid_params) do
        {
          slug: retailer.slug,
          retailer: {
            domain: '', # parámetro vacío
            certificate_file: nil,
            key_file: nil
          }
        }
      end

      # rubocop:disable RSpec/PendingWithoutReason
      xit 'shows error message' do
        post :set_domain, params: invalid_params
        expect(controller).to render_template('retailers/my_business/domain')
        expect(flash.now[:notice]).to eq('Debe llenar todos los campos')
      end
      # rubocop:enable RSpec/PendingWithoutReason
    end
  end
end
