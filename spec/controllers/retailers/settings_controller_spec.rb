require 'rails_helper'

# rubocop:disable Rails/ResponseParsedBody
RSpec.describe Retailers::SettingsController do
  let(:retailer) { create(:retailer) }
  let(:retailer_user) { create(:retailer_user, retailer: retailer, first_name: '', last_name: '') }
  let(:admin_user) { create(:retailer_user, :admin, retailer: retailer_user.retailer) }

  before do
    sign_in retailer_user.user
    retailer.payment_plan.update(max_agents: 3)
  end

  describe '#team' do
    let!(:second_retailer_user) { create(:retailer_user, :supervisor, retailer: retailer_user.retailer) }

    it 'redirects to root' do
      # Crear un nuevo administrador antes de actualizar
      create(:retailer_user, :admin, retailer: retailer_user.retailer) # new_admin_user

      retailer_user.update!(retailer_admin: false)

      get :team, params: { slug: retailer_user.retailer.slug }
      expect(response).to redirect_to(root_path(retailer_user.retailer.slug))
    end
  end

  describe 'PUT #update' do
    let(:t1) { create(:team, retailer: retailer, name: 'My Team', color: '#FFF') }

    describe 'successfully' do
      context 'when params has team_retailer_users_attributes' do
        it 'returns team updated and added team_retailer_user' do
          put :update, params: {
            slug: retailer_user.retailer.slug, user: retailer_user.id,
            retailer_user: {
              first_name: 'First Name updated', last_name: 'Last Name updated',
              team_retailer_users_attributes: [{ team_id: t1.id }]
            }
          }

          body = JSON.parse(response.body)
          expect(response).to have_http_status(:ok)
          expect(body['user']['team_retailer_users'].count).to eq(1)
        end
      end

      context 'when params has team_retailer_users_attributes with _destroy attribute in true' do
        let!(:tru) { create(:team_retailer_user, team_id: t1.id, retailer_user_id: retailer_user.id) }

        it 'returns team updated and removed team_retailer_user' do
          put :update, params: {
            slug: retailer_user.retailer.slug, user: retailer_user.id,
            retailer_user: {
              first_name: 'First Name updated', last_name: 'Last Name updated',
              team_retailer_users_attributes: [{ id: tru.id, team_id: t1.id, _destroy: true }]
            }
          }

          body = JSON.parse(response.body)
          expect(response).to have_http_status(:ok)
          expect(body['user']['team_retailer_users'].count).to eq(0)
        end
      end

      context 'when retailer_user is superior (admin or supervisor)' do
        it 'does not remove managed_agents' do
          retailer_user.update!(retailer_admin: true, retailer_supervisor: false, managed_agents: [1, 2, 3])

          put :update, params: {
            slug: retailer_user.retailer.slug, user: retailer_user.id,
            retailer_user: { first_name: 'First Name updated', last_name: 'Last Name updated' }
          }

          body = JSON.parse(response.body)
          expect(response).to have_http_status(:ok)
          expect(body['user']['managed_agents']).to eq([1, 2, 3])
        end
      end

      context 'when retailer_user is not superior' do
        it 'removes managed_agents' do
          create(:retailer_user, :admin, retailer: retailer_user.retailer)

          retailer_user.update!(retailer_admin: false, retailer_supervisor: false, managed_agents: [1, 2, 3])

          put :update, params: {
            slug: retailer_user.retailer.slug, user: retailer_user.id,
            retailer_user: { first_name: 'Updated Name', last_name: 'Updated Last Name' }
          }

          retailer_user.reload
          expect(retailer_user.managed_agents).to be_empty
        end
      end
    end

    describe 'unsuccessfully' do
      let(:retailer_user_params) do
        {
          first_name: 'First Name updated',
          last_name: 'Last Name updated',
          team_retailer_users_attributes: [{ team_id: t1.id }]
        }
      end
      let(:params) { { slug: retailer.slug, user: retailer_user.id, retailer_user: retailer_user_params } }
      let(:put_request) { put :update, params: params }

      before { allow_any_instance_of(RetailerUser).to receive(:update).and_return(false) }

      it { expect { put_request }.not_to(change { retailer_user.reload.first_name }) }

      describe 'and also' do
        before { put_request }

        it { expect(response).to have_http_status(:unprocessable_entity) }
      end
    end
  end

  describe '#invite_team_member' do
    context 'with ROLES_ACTIVE env var' do
      let(:role) { retailer.roles.first }
      let(:retailer_user_params) do
        { email: email, first_name: 'John', last_name: 'Wick', role_id: role.id }
      end
      let(:params) { { slug: retailer.slug, retailer_user: retailer_user_params } }
      let(:post_request) { post :invite_team_member, params: params }

      before do
        allow(ENV).to receive(:fetch).and_call_original
        allow(ENV).to receive(:fetch).with('ROLES_ACTIVE').and_return(true)
      end

      context 'when user does not exist' do
        let(:email) { '<EMAIL>' }

        it { expect { post_request }.to change(RetailerUser, :count).by(1) }
        it { expect { post_request }.to change(User, :count).by(1) }

        describe 'and also' do
          before { post_request }

          it { expect(response).to have_http_status(:ok) }
        end
      end

      context 'when user exists' do
        let(:another_retailer) { create(:retailer) }
        let!(:another_retailer_user) { create(:retailer_user, retailer: another_retailer) }
        let(:email) { another_retailer_user.email }

        it { expect { post_request }.to change(RetailerUser, :count).by(1) }
        it { expect { post_request }.not_to change(User, :count) }
        it { expect { post_request }.to change { retailer.retailer_users.count }.by(1) }

        describe 'and also' do
          before { post_request }

          it { expect(response).to have_http_status(:ok) }
        end
      end
    end

    context 'without ROLES_ACTIVE env var' do
      before do
        allow(ENV).to receive(:fetch).and_call_original
        allow(ENV).to receive(:fetch).with('ROLES_ACTIVE', false).and_return(false)
      end

      let(:agent_invited) { { email: '<EMAIL>', first_name: 'John', last_name: 'Wick' } }

      context 'when user does not exist' do
        it 'creates a new retailer user as agent with first name and last name' do
          expect do
            post :invite_team_member, params: { slug: retailer_user.retailer.slug, retailer_user: agent_invited }
          end.to change(RetailerUser, :count).by(1).and change(User, :count).by(1)

          expect(User.find_by(email: '<EMAIL>').retailer_admin).to be(false)
        end

        it 'creates a new retailer user as admin with first name and last name' do
          admin_invited = agent_invited.merge(retailer_admin: true)

          expect do
            post :invite_team_member, params: { slug: retailer_user.retailer.slug, retailer_user: admin_invited }
          end.to change(RetailerUser, :count).by(1)
          user = User.find_by(email: '<EMAIL>')
          expect(user.retailer_admin).to be(true)
          expect(user.retailer_supervisor).to be(false)
        end

        it 'creates a new retailer user as supervisor with first name and last name' do
          supervisor_invited = agent_invited.merge(retailer_supervisor: true)

          expect do
            post :invite_team_member, params: { slug: retailer_user.retailer.slug, retailer_user: supervisor_invited }
          end.to change(RetailerUser, :count).by(1).and change(User, :count).by(1)

          user = User.find_by(email: supervisor_invited[:email])
          expect(user.retailer_supervisor).to be(true)
          expect(user.retailer_admin).to be(false)
        end

        it 'returns internal server error when Exception raised' do
          allow_any_instance_of(User).to receive(:invite!).and_return(Exception)

          post :invite_team_member, params: { slug: retailer_user.retailer.slug, retailer_user: agent_invited }

          expect(response).to have_http_status(:internal_server_error)
          expect(response.content_type).to eq('application/json; charset=utf-8')
          expect(JSON.parse(response.body)).to include('message' => be_empty)
        end

        it 'shows a notice with error if email already exists' do
          create(:retailer_user, retailer: retailer_user.retailer, email: '<EMAIL>')

          post :invite_team_member, params: { slug: retailer_user.retailer.slug, retailer_user: agent_invited }

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response.content_type).to eq('application/json; charset=utf-8')
          expect(JSON.parse(response.body)).to include('message' => 'Correo ya está asignado a alguien más')
        end
      end

      context 'when user exists' do
        let(:another_retailer) { create(:retailer) }
        let!(:another_retailer_user) { create(:retailer_user, retailer: another_retailer) }
        let(:email) { another_retailer_user.email }
        let(:retailer_user_params) { { email: email, first_name: 'John', last_name: 'Wick', retailer_admin: true } }
        let(:params) { { slug: retailer.slug, retailer_user: retailer_user_params } }
        let(:post_request) { post :invite_team_member, params: params }

        it { expect { post_request }.to change(RetailerUser, :count).by(1) }
        it { expect { post_request }.not_to change(User, :count) }
        it { expect { post_request }.to change { retailer.retailer_users.count }.by(1) }

        describe 'and also' do
          before { post_request }

          it { expect(response).to have_http_status(:ok) }
        end
      end
    end

    context 'when see_phone_numbers is present' do
      agent_params = { first_name: 'John', last_name: 'Wick'}
      
      it 'creates a new retailer user as agent with first name, last name and see_phone_numbers is false' do
        agent_params[:email] = '<EMAIL>'
        agent_params[:see_phone_numbers] = false

        
        post :invite_team_member, params: { slug: retailer.slug, retailer_user: agent_params }

        json_response = JSON.parse(response.body)
        created_user = User.find_by(email: '<EMAIL>')
        
        expect(created_user).not_to be_nil
        expect(created_user.retailer_users.first.see_phone_numbers).to be(false)
        expect(json_response['user']['see_phone_numbers']).to be(false)
        expect(response).to have_http_status(:ok)
      end

      it 'creates a new retailer user as agent with first name, last name and see_phone_numbers is true' do
        agent_params[:email] = '<EMAIL>'
        agent_params[:see_phone_numbers] = true
        
        post :invite_team_member, params: { slug: retailer.slug, retailer_user: agent_params }

        json_response = JSON.parse(response.body)
        created_user = User.find_by(email: '<EMAIL>')
        
        expect(created_user).not_to be_nil
        expect(created_user.retailer_users.first.see_phone_numbers).to be(true)
        expect(json_response['user']['see_phone_numbers']).to be(true)
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'PUT #renew_invitation' do
    let(:retailer_user2) { create(:retailer_user, retailer: retailer) }

    context 'when retailer user doesn\'t have a pending invitation' do
      it 'returns message error' do
        put :renew_invitation, params: { slug: retailer.slug, retailer_user: { email: retailer_user2.email } }
        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:not_found)
        expect(json_response['message']).to match(/Usuario no tiene una invitación pendiente/)
      end
    end

    context 'when retailer user doesn\'t exist' do
      it 'returns message error' do
        put :renew_invitation, params: { slug: retailer.slug, retailer_user: { email: '<EMAIL>' } }
        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:not_found)
        expect(json_response['message']).to match(/Usuario no existe/)
      end
    end

    context 'when retailer user has a pending invitation' do
      let!(:retailer_user2) { create(:retailer_user, retailer: retailer, invitation_token: Devise.friendly_token) }

      it 'returns url to accept invitation' do
        put :renew_invitation, params: { slug: retailer.slug, retailer_user: { email: retailer_user2.email } }
        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(json_response['url']).to match(%r{\A(http|https)://\S+\z})
      end
    end
  end

  describe '#set_admin_team_member' do
    it 'sets the member as admin' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: false,
                                      retailer_supervisor: false)
      post :set_admin_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(member.reload.retailer_admin).to be(true)
    end

    it 'sets the supervisor as admin' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_supervisor: true)
      post :set_admin_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(member.reload.retailer_admin).to be(true)
      expect(member.reload.retailer_supervisor).to be(false)
    end

    it 'redirects to dashboard if an exception occurs' do
      allow_any_instance_of(RetailerUser).to receive(:update_columns).and_return(false)
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: false)
      post :set_admin_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(response).to redirect_to(retailers_dashboard_path(retailer_user.retailer.slug))
      expect(flash[:notice]).to be_present
      expect(flash[:notice]).to match(/Error al actualizar usuario..*/)
    end
  end

  describe '#set_agent_team_member' do
    it 'sets the admin as agent' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: true)
      post :set_agent_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(member.reload.retailer_admin).to be(false)
      expect(member.reload.retailer_supervisor).to be(false)
    end

    it 'sets the supervisor as agent' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_supervisor: true)
      post :set_agent_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(member.reload.retailer_admin).to be(false)
      expect(member.reload.retailer_supervisor).to be(false)
    end

    it 'redirects to dashboard if an exception occurs' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: true)
      allow_any_instance_of(RetailerUser).to receive(:update_columns).and_return(false)
      post :set_agent_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(response).to redirect_to(retailers_dashboard_path(retailer_user.retailer.slug))
      expect(flash[:notice]).to be_present
      expect(flash[:notice]).to match(/Error al actualizar usuario..*/)
    end
  end

  describe '#set_supervisor_team_member' do
    it 'sets the admin as supervisor' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: true)
      post :set_supervisor_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(member.reload.retailer_admin).to be(false)
      expect(member.reload.retailer_supervisor).to be(true)
    end

    it 'sets the agent as supervisor' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: false,
                                      retailer_supervisor: false)
      post :set_supervisor_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(member.reload.retailer_admin).to be(false)
      expect(member.reload.retailer_supervisor).to be(true)
    end

    it 'cannot set a supervisor when an error is raised' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: false,
                                      retailer_supervisor: false)
      allow_any_instance_of(RetailerUser).to receive(:update_columns).and_return(false)
      post :set_supervisor_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(response).to redirect_to(retailers_dashboard_path(retailer_user.retailer.slug))
      expect(flash[:notice]).to be_present
      expect(flash[:notice]).to eq('Error al actualizar usuario.')
    end

    it 'redirects to dashboard if an exception occurs' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: true)
      allow_any_instance_of(RetailerUser).to receive(:update_columns).and_return(false)
      post :set_agent_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(response).to redirect_to(retailers_dashboard_path(retailer_user.retailer.slug))
      expect(flash[:notice]).to be_present
      expect(flash[:notice]).to match(/Error al actualizar usuario..*/)
    end
  end

  describe '#reinvite_team_member' do
    it 'reinvites the member' do
      days_ago = 2.days.ago
      member = create(:retailer_user, retailer: retailer_user.retailer, invitation_sent_at: days_ago)
      post :reinvite_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(member.user.reload.invitation_sent_at).not_to eq(days_ago)
    end

    it 'redirects to dashboard if member is not found' do
      post :reinvite_team_member, params: { slug: retailer_user.retailer.slug, user: '<EMAIL>' }
      expect(response).to have_http_status(:not_found)
      expect(JSON.parse(response.body)['message']).to eq('Error al invitar usuario.')
    end
  end

  describe '#remove_team_member' do
    it 'removes member from team' do
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: false)
      member.generate_api_token!
      post :remove_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(member.reload.removed_from_team).to be(true)
      expect(member.user.reload.api_session_token).to be_nil
    end

    it 'redirects to dashboard if an exception occurs' do
      allow_any_instance_of(RetailerUser).to receive(:update_column).and_return(false)
      member = create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: false)
      post :remove_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
      expect(response).to redirect_to(retailers_dashboard_path(retailer_user.retailer.slug))
      expect(flash[:notice]).to be_present
      expect(flash[:notice]).to match(/Error al remover usuario..*/)
    end

    context 'when the agent belongs to an assignment team' do
      let(:member) { create(:retailer_user, retailer: retailer_user.retailer) }
      let(:team_assignment) { create(:team_assignment, retailer: retailer_user.retailer) }
      let!(:agent_team) { create(:agent_team, :activated, retailer_user: member, team_assignment: team_assignment) }

      it 'disables the agent from the team' do
        expect(agent_team.active).to be true
        post :remove_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
        expect(agent_team.reload.active).to be false
      end
    end
  end

  describe '#reactive_team_member' do
    context 'when the retailer has not reached the limit of agents' do
      it 'reactives member from team' do
        member = create(:retailer_user, retailer: retailer, retailer_admin: false, removed_from_team: true)
        post :reactive_team_member, params: { slug: retailer_user.retailer.slug, user: member.id }
        expect(member.reload.removed_from_team).to be(false)
      end
    end

    context 'when the retailer has reached the limit of agents' do
      let(:member1) { create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: false) }
      let(:member2) do
        create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: false, removed_from_team: true)
      end
      let(:member) { create(:retailer_user, retailer: retailer_user.retailer, retailer_admin: false) }

      it 'redirects to dashboard if an exception occurs' do
        member1
        member2
        member

        post :reactive_team_member, params: { slug: retailer_user.retailer.slug, user: member2.id }
        expect(response).to redirect_to(retailers_dashboard_path(retailer_user.retailer.slug))
        expect(flash[:notice]).to be_present
        expect(flash[:notice]).to match(/Límite máximo de agentes alcanzado/)
      end
    end
  end

  describe '#reassign' do
    let(:new_retailer_user) { create(:retailer_user, retailer: retailer_user.retailer) }

    before do
      create_list(:customer, 2, retailer: retailer, retailer_user_id: retailer_user.id)
    end

    it 'reassigns the chats to the new retailer user' do
      post :reassign, params: { slug: retailer.slug, user: retailer_user.id, new_agent: new_retailer_user.id }
      expect(Customer.where(retailer_user_id: retailer_user.id).count).to eq(0)
      expect(Customer.where(retailer_user_id: new_retailer_user.id).count).to eq(2)
    end
  end

  describe 'DELETE #destroy' do
    context 'when delete_agent returns true' do
      before do
        allow_any_instance_of(RetailerUser).to receive(:delete_agent).and_return(true)
      end

      it 'returns status code 200 and success message' do
        delete :destroy, params: { user: retailer_user.id, slug: retailer.slug }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['message']).to eq('Usuario eliminado con éxito.')
      end
    end

    context 'when delete_agent returns false' do
      let(:new_agent) { create(:retailer_user, retailer: retailer) }

      before do
        allow_any_instance_of(RetailerUser).to receive(:delete_agent).with(new_agent.id.to_s).and_return(false)
      end

      it 'returns status code 422 and error message' do
        delete :destroy, params: { new_agent: new_agent.id, user: retailer_user.id, slug: retailer.slug }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['message']).to eq('Error eliminando usuario')
      end
    end

    context 'when agent removed is removed from everywhere' do
      context 'when agent belongs to a team assignment' do
        let!(:agent_team) { create(:agent_team, retailer_user: retailer_user) }

        it 'calls remove_agent on AgentTeam' do
          allow(AgentTeam).to receive(:remove_agent).with(retailer_user.id).and_return(true)
          delete :destroy, params: { user: retailer_user.id, slug: retailer.slug }
          expect(AgentTeam).to have_received(:remove_agent).with(retailer_user.id)
        end

        it 'removes agent from the team assignment' do
          delete :destroy, params: { user: retailer_user.id, slug: retailer.slug }
          expect(AgentTeam.where(retailer_user: retailer_user)).to be_empty
        end
      end

      context 'when agent belongs to a team' do
        let(:team) { create(:team, retailer: retailer) }
        let!(:team_retailer_user) { create(:team_retailer_user, team: team, retailer_user: retailer_user) }

        it 'calls remove_agent on TeamRetailerUser' do
          allow(TeamRetailerUser).to receive(:remove_agent).with(retailer_user.id).and_return(true)
          delete :destroy, params: { user: retailer_user.id, slug: retailer.slug }
          expect(TeamRetailerUser).to have_received(:remove_agent).with(retailer_user.id)
        end

        it 'removes agent from the team' do
          delete :destroy, params: { user: retailer_user.id, slug: retailer.slug }
          expect(TeamRetailerUser.where(retailer_user: retailer_user)).to be_empty
        end
      end

      context 'when agent has assigned deals' do
        let!(:deals) { create_list(:deal, 5, retailer_user: retailer_user) }
        let(:fixed_time) { Time.parse('2025-06-19 10:00:00 UTC') }
        let(:token) { JwtService.encode({ issuer: ENV.fetch('JWT_ISSUER', 'mercately').freeze }) }
        let(:headers) { { 'Authorization' => "Bearer #{token}" } }
        let(:url) { "#{ENV.fetch('DEAL_ENGINE_BASE_URL', nil)}/deals/remove_agent_from_deals" }
        let(:body) { { deal: { retailer_user_id: retailer_user.id } }.to_json }
        let(:connection) { instance_double(Faraday::Connection) }
        let(:response) { instance_double(Faraday::Response, success?: true) }

        before do
          # Mock time to ensure consistent JWT tokens
          allow(Time).to receive(:current).and_return(fixed_time)
          allow(Connection).to receive(:prepare_connection).with(url).and_return(connection)
          allow(Connection).to receive(:put_request).with(connection, body, headers).and_return(response)
          allow(Rails.logger).to receive(:info)
          allow(Rails.logger).to receive(:error)
        end

        it 'calls remove_agent_from_deals_engine' do
          delete :destroy, params: { user: retailer_user.id, slug: retailer.slug }
          expect(Connection).to have_received(:prepare_connection).with(url).at_least(:once)
          expect(Connection).to have_received(:put_request).with(connection, body, headers).at_least(:once)
          expect(Rails.logger).to have_received(:info)
            .with("Successfully removed relationships from deals for agent #{retailer_user.id}").at_least(:once)
        end
      end

      context 'when agent has assigned chat bot actions' do
        let(:chat_bot) { create(:chat_bot, retailer: retailer) }
        let(:chat_bot_option) { create(:chat_bot_option, chat_bot: chat_bot) }
        let!(:chat_bot_actions) do
          create_list(:chat_bot_action, 5, :assign_agent, retailer_user: retailer_user,
                                                          chat_bot_option: chat_bot_option)
        end

        it 'calls remove_agent on ChatBotAction' do
          allow(ChatBotAction).to receive(:remove_agent).with(retailer_user.id).and_return(true)
          delete :destroy, params: { user: retailer_user.id, slug: retailer.slug }
          expect(ChatBotAction).to have_received(:remove_agent).with(retailer_user.id)
        end

        it 'removes the chat bot actions' do
          delete :destroy, params: { user: retailer_user.id, slug: retailer.slug }
          expect(ChatBotAction.where(retailer_user: retailer_user)).to be_empty
        end
      end
    end
  end
end
# rubocop:enable Rails/ResponseParsedBody
