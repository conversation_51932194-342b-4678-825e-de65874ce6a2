require 'rails_helper'

class DummyController
  include PlansControllerConcern
end

RSpec.describe PlansControllerConcern do
  let(:dummy) { DummyController.new }

  describe '#format_plans' do
    it 'returns plans grouped by billing_cycle with serialized items' do
      pt1 = double('PricingTier', billing_cycle: 'monthly')
      pt2 = double('PricingTier', billing_cycle: 'yearly')
      allow(PricingTier).to receive_message_chain(:where, :active, :select, :distinct, :each).and_yield(pt1).and_yield(pt2)

      monthly_tier = double('PricingTier', billing_cycle: 'monthly')
      yearly_tier = double('PricingTier', billing_cycle: 'yearly')
      allow(PricingTier).to receive_message_chain(:active, :where, :order, :includes).and_return([monthly_tier], [yearly_tier])

      allow(dummy).to receive(:serialize_plan).with(monthly_tier).and_return({ id: 1, period: 'monthly' })
      allow(dummy).to receive(:serialize_plan).with(yearly_tier).and_return({ id: 2, period: 'yearly' })

      result = dummy.format_plans
      expect(result).to eq([
        { period: 'monthly', items: [{ id: 1, period: 'monthly' }] },
        { period: 'yearly', items: [{ id: 2, period: 'yearly' }] }
      ])
    end
  end

  describe '#format_chargebee_plans' do
    it 'returns plans grouped by period with items from plans_by_period' do
      plans = double('plans')
      chargebee = double('chargebee')
      allow(dummy).to receive(:periods).and_return([
        { name: 'monthly', period_unit: 'month', period: 1 },
        { name: 'yearly', period_unit: 'year', period: 1 }
      ])
      allow(dummy).to receive(:plans_by_period).with(plans, { name: 'monthly', period_unit: 'month', period: 1 }, chargebee).and_return([:plan1])
      allow(dummy).to receive(:plans_by_period).with(plans, { name: 'yearly', period_unit: 'year', period: 1 }, chargebee).and_return([:plan2])

      result = dummy.format_chargebee_plans(plans, chargebee)
      expect(result).to eq([
        { period: 'monthly', items: [:plan1] },
        { period: 'yearly', items: [:plan2] }
      ])
    end
  end

  describe '#format_addons' do
    it 'serializes each addon with the payment plan' do
      payment_plan = double('PaymentPlan')
      tier1 = double('Tier1')
      tier2 = double('Tier2')
      allow(dummy).to receive(:serialize_addon).with(tier1, payment_plan).and_return({ id: 1 })
      allow(dummy).to receive(:serialize_addon).with(tier2, payment_plan).and_return({ id: 2 })

      result = dummy.format_addons(payment_plan, [tier1, tier2])
      expect(result).to eq([{ id: 1 }, { id: 2 }])
    end
  end

  describe '#format_chargebee_addons' do
    it 'serializes each chargebee addon' do
      chargebee = double('chargebee')
      addon1 = double('Addon1')
      addon2 = double('Addon2')
      allow(dummy).to receive(:serialize_chargebee_addon).with(addon1, chargebee).and_return({ id: 1 })
      allow(dummy).to receive(:serialize_chargebee_addon).with(addon2, chargebee).and_return({ id: 2 })

      result = dummy.format_chargebee_addons([addon1, addon2], chargebee)
      expect(result).to eq([{ id: 1 }, { id: 2 }])
    end
  end

  describe '#format_tiers' do
    it 'returns an empty array if tiers is blank' do
      expect(dummy.format_tiers(nil)).to eq([])
      expect(dummy.format_tiers([])).to eq([])
    end

    it 'returns formatted tiers' do
      tier1 = double('Tier', starting_unit: 1, ending_unit: 10, price: 5.5)
      tier2 = double('Tier', starting_unit: 11, ending_unit: 20, price: 10)
      result = dummy.format_tiers([tier1, tier2])
      expect(result).to eq([
        { starting_unit: 1, ending_unit: 10, price: 5.5 },
        { starting_unit: 11, ending_unit: 20, price: 10.0 }
      ])
    end
  end

  describe '#format_entitlements' do
    it 'returns serialized entitlements for the given tier_id' do
      entitlement1 = double('Entitlement', id: 1, identifier: 'feat1', quantity: 2)
      entitlement2 = double('Entitlement', id: 2, identifier: 'feat2', quantity: 5)
      pricing_tier = double('PricingTier', entitlements: [entitlement1, entitlement2])
      allow(PricingTier).to receive(:find).with(123).and_return(pricing_tier)
      allow(dummy).to receive(:serialize_entitlement).with(entitlement1).and_return({ id: 1, feature_id: 'feat1', value: 2 })
      allow(dummy).to receive(:serialize_entitlement).with(entitlement2).and_return({ id: 2, feature_id: 'feat2', value: 5 })

      result = dummy.format_entitlements(123)
      expect(result).to eq([
        { id: 1, feature_id: 'feat1', value: 2 },
        { id: 2, feature_id: 'feat2', value: 5 }
      ])
    end
  end

  describe '#periods' do
    it 'returns the correct periods array' do
      expect(dummy.periods).to eq([
        { name: 'monthly', period_unit: 'month', period: 1 },
        { name: 'quarterly', period_unit: 'month', period: 3 },
        { name: 'yearly', period_unit: 'year', period: 1 }
      ])
    end
  end

  describe '#match_charges' do
    let(:retailer) { double('Retailer') }
    let(:payment_plan) { double('PaymentPlan') }
    let(:params) do
      {
        subscription: {
          charges: [
            { id: 'charge1', selected: true },
            { id: 'charge2', selected: false },
            { id: 'charge3', selected: true }
          ]
        }
      }
    end

    it 'does nothing if no charges are selected' do
      params_no_selected = { subscription: { charges: [{ id: 'charge1', selected: false }] } }
      expect(retailer).not_to receive(:payment_plan)
      expect(dummy.match_charges(retailer, params_no_selected)).to be_nil
    end

    it 'sets allowed charges and saves payment_plan' do
      allowed_charges = [
        { id: 'charge1', internal_column: 'foo' },
        { id: 'charge3', internal_column: 'bar' }
      ]
      expect(retailer).to receive(:payment_plan).and_return(payment_plan).at_least(:once)
      expect(payment_plan).to receive(:allowed_charges).and_return(allowed_charges).at_least(:once)
      expect(payment_plan).to receive(:send).with('foo=', true)
      expect(payment_plan).to receive(:send).with('bar=', true)
      expect(payment_plan).to receive(:save)
      dummy.match_charges(retailer, params)
    end
  end

  describe '#set_payment_start_date' do
    let(:subscription) { double('Subscription', created_at: 1_700_000_000) }
    let(:payment_plan) { double('PaymentPlan') }
    let(:retailer) { double('Retailer', first_payment_date: nil) }

    before do
      allow(payment_plan).to receive(:retailer).and_return(retailer)
      allow(payment_plan).to receive(:payment_start_date).and_return(nil)
      allow(payment_plan).to receive(:update)
      allow(retailer).to receive(:update)
    end

    it 'returns early if already_have_payments? is true' do
      allow(dummy).to receive(:already_have_payments?).with(payment_plan).and_return(true)
      expect(payment_plan).not_to receive(:update)
      expect(retailer).not_to receive(:update)
      dummy.set_payment_start_date(subscription, payment_plan)
    end

    it 'sets first_payment_date and payment_start_date if not present' do
      allow(dummy).to receive(:already_have_payments?).with(payment_plan).and_return(false)
      allow(retailer).to receive(:first_payment_date).and_return(nil)
      allow(payment_plan).to receive(:payment_start_date).and_return(nil)
      expect(retailer).to receive(:update).with(first_payment_date: Time.at(subscription.created_at))
      expect(payment_plan).to receive(:update).with(payment_start_date: Time.at(subscription.created_at))
      dummy.set_payment_start_date(subscription, payment_plan)
    end

    it 'does not update first_payment_date if already present' do
      allow(dummy).to receive(:already_have_payments?).with(payment_plan).and_return(false)
      allow(retailer).to receive(:first_payment_date).and_return(Time.now)
      allow(payment_plan).to receive(:payment_start_date).and_return(nil)
      expect(retailer).not_to receive(:update)
      expect(payment_plan).to receive(:update)
      dummy.set_payment_start_date(subscription, payment_plan)
    end

    it 'does not update payment_start_date if already present' do
      allow(dummy).to receive(:already_have_payments?).with(payment_plan).and_return(false)
      allow(retailer).to receive(:first_payment_date).and_return(nil)
      allow(payment_plan).to receive(:payment_start_date).and_return(Time.now)
      expect(payment_plan).not_to receive(:update)
      dummy.set_payment_start_date(subscription, payment_plan)
    end

    it 'rescues StandardError and uses Time.now' do
      allow(dummy).to receive(:already_have_payments?).with(payment_plan).and_return(false)
      allow(subscription).to receive(:created_at).and_raise(StandardError)
      allow(retailer).to receive(:first_payment_date).and_return(nil)
      allow(payment_plan).to receive(:payment_start_date).and_return(nil)
      expect(retailer).to receive(:update).with(first_payment_date: kind_of(Time))
      expect(payment_plan).to receive(:update).with(payment_start_date: kind_of(Time))
      dummy.set_payment_start_date(subscription, payment_plan)
    end
  end

  describe '#save_acquired_addons' do
    let(:retailer) { double('Retailer') }
    let(:payment_plan) { double('PaymentPlan') }
    let(:plans_utils) { double('PlansUtils') }
    let(:params) do
      {
        subscription: {
          addons: ['addon1'],
          charges: ['charge1']
        }
      }
    end

    before do
      allow(retailer).to receive(:payment_plan).and_return(payment_plan)
      allow(dummy).to receive(:plans_utils).and_return(plans_utils)
    end

    it 'calls save_addons and save_charges on plans_utils' do
      expect(plans_utils).to receive(:save_addons).with(payment_plan, ['addon1'])
      expect(plans_utils).to receive(:save_charges).with(payment_plan, ['charge1'])
      dummy.save_acquired_addons(retailer, params)
    end
  end

  describe '#calculate_estimates' do
    let(:payment_plan) { double('PaymentPlan') }
    let(:current_tier) { double('PricingTier', id: 1, month_interval: 1) }
    let(:new_tier) { double('PricingTier', id: 2, month_interval: 1) }
    let(:params) { { new_subscription_item_id: 'new_id' } }
    let(:plans_utils) { double('PlansUtils') }

    before do
      allow(payment_plan).to receive(:pricing_tier).and_return(current_tier)
      allow(PricingTier).to receive(:find_by).with(identifier: 'new_id').and_return(new_tier)
      allow(dummy).to receive(:plans_utils).and_return(plans_utils)
    end

    it 'calls upgrade_to_same_cycle if new_tier has same month_interval' do
      expect(plans_utils).to receive(:upgrade_to_same_cycle).with(payment_plan, new_tier, current_tier, params).and_return('result')
      expect(dummy.calculate_estimates(payment_plan, params)).to eq('result')
    end

    it 'calls upgrade_to_different_cycle if new_tier has different month_interval' do
      allow(new_tier).to receive(:month_interval).and_return(2)
      expect(plans_utils).to receive(:upgrade_to_different_cycle).with(payment_plan, new_tier, current_tier, params).and_return('diff_result')
      expect(dummy.calculate_estimates(payment_plan, params)).to eq('diff_result')
    end

    it 'calls addon_estimates if new_tier and current_tier are the same' do
      allow(new_tier).to receive(:id).and_return(1)
      expect(plans_utils).to receive(:addon_estimates).with(payment_plan, params, []).and_return(['addon_est'])
      allow(payment_plan).to receive(:pricing_tier).and_return(new_tier)
      result = dummy.calculate_estimates(payment_plan, params)
      expect(result).to eq({
        unbilled_charge_estimates: ['addon_est'],
        credit_note_estimates: [],
        subscription_estimate: {}
      })
    end
  end

  describe '#chargebee' do
    it 'returns a new Chargebee::Api instance' do
      cb = double('Chargebee::Api')
      stub_const('Chargebee::Api', Class.new)
      expect(dummy.send(:chargebee)).to be_a(Chargebee::Api)
    end
  end

  describe '#already_have_payments?' do
    let(:payment_plan) { double('PaymentPlan') }
    let(:retailer) { double('Retailer') }

    before do
      allow(payment_plan).to receive(:retailer).and_return(retailer)
    end

    it 'returns true if chargebee_subscription_id is present' do
      allow(payment_plan).to receive(:chargebee_subscription_id).and_return('sub_id')
      expect(dummy.send(:already_have_payments?, payment_plan)).to be true
    end

    it 'returns true if valid_paymentez_transaction? is true' do
      allow(payment_plan).to receive(:chargebee_subscription_id).and_return(nil)
      allow(dummy).to receive(:valid_paymentez_transaction?).with(retailer).and_return(true)
      allow(dummy).to receive(:valid_stripe_transaction?).with(retailer).and_return(false)
      expect(dummy.send(:already_have_payments?, payment_plan)).to be true
    end

    it 'returns true if valid_stripe_transaction? is true' do
      allow(payment_plan).to receive(:chargebee_subscription_id).and_return(nil)
      allow(dummy).to receive(:valid_paymentez_transaction?).with(retailer).and_return(false)
      allow(dummy).to receive(:valid_stripe_transaction?).with(retailer).and_return(true)
      expect(dummy.send(:already_have_payments?, payment_plan)).to be true
    end

    it 'returns false if none are present/true' do
      allow(payment_plan).to receive(:chargebee_subscription_id).and_return(nil)
      allow(dummy).to receive(:valid_paymentez_transaction?).with(retailer).and_return(false)
      allow(dummy).to receive(:valid_stripe_transaction?).with(retailer).and_return(false)
      expect(dummy.send(:already_have_payments?, payment_plan)).to be false
    end
  end

  describe '#valid_paymentez_transaction?' do
    let(:retailer) { double('Retailer') }
    let(:relation) { double('Relation') }

    it 'returns true if more than one successful paymentez transaction' do
      allow(retailer).to receive_message_chain(:paymentez_transactions, :where, :count).and_return(2)
      expect(dummy.send(:valid_paymentez_transaction?, retailer)).to be true
    end

    it 'returns false if one or fewer successful paymentez transaction' do
      allow(retailer).to receive_message_chain(:paymentez_transactions, :where, :count).and_return(1)
      expect(dummy.send(:valid_paymentez_transaction?, retailer)).to be false
    end
  end

  describe '#valid_stripe_transaction?' do
    let(:retailer) { double('Retailer') }

    it 'returns true if more than one successful stripe transaction' do
      allow(retailer).to receive_message_chain(:stripe_transactions, :success, :count).and_return(2)
      expect(dummy.send(:valid_stripe_transaction?, retailer)).to be true
    end

    it 'returns false if one or fewer successful stripe transaction' do
      allow(retailer).to receive_message_chain(:stripe_transactions, :success, :count).and_return(1)
      expect(dummy.send(:valid_stripe_transaction?, retailer)).to be false
    end
  end

  describe '#plans_by_period' do
    let(:plan1) { double('Plan', period: 1, period_unit: 'month') }
    let(:plan2) { double('Plan', period: 3, period_unit: 'month') }
    let(:plan3) { double('Plan', period: 1, period_unit: 'year') }
    let(:chargebee) { double('Chargebee') }
    let(:period) { { period: 1, period_unit: 'month' } }

    it 'returns only plans matching the period and period_unit, serialized' do
      allow(dummy).to receive(:serialize_chargebee_plan).with(plan1, chargebee).and_return({ id: 1 })
      plans = [plan1, plan2, plan3]
      result = dummy.send(:plans_by_period, plans, period, chargebee)
      expect(result).to eq([{ id: 1 }])
    end

    it 'returns empty array if no plans match' do
      plans = [plan2, plan3]
      result = dummy.send(:plans_by_period, plans, period, chargebee)
      expect(result).to eq([])
    end
  end

  describe '#serialize_chargebee_plan' do
    it 'serializes a chargebee plan with metadata and item_description' do
      plan = double(
        'Plan',
        id: 1,
        item_id: 'item_1',
        price: 100,
        name: 'Pro',
        external_name: 'Pro External',
        currency_code: 'USD',
        description: 'desc'
      )
      chargebee = double('Chargebee')
      expect(chargebee).to receive(:item_metadata).with('item_1').and_return({ foo: 'bar' })
      expect(chargebee).to receive(:item_description).with('item_1').and_return('desc from cb')

      result = dummy.send(:serialize_chargebee_plan, plan, chargebee)
      expect(result).to eq(
        id: 1,
        item_id: 'item_1',
        price: 100,
        name: 'Pro',
        external_name: 'Pro External',
        currency_code: 'USD',
        description: 'desc',
        metadata: { foo: 'bar' },
        item_description: 'desc from cb'
      )
    end
  end

  describe '#serialize_plan' do
    it 'serializes a plan tier' do
      plan = double(
        'Plan',
        id: 2,
        identifier: 'plan_2',
        name: 'Basic',
        description: 'desc',
        metadata: '{"foo":"bar"}'
      )
      tier = double(
        'Tier',
        identifier: 'tier_2',
        price: 50,
        priceable: plan,
        id: 22,
        contains_ai?: true
      )
      result = dummy.send(:serialize_plan, tier)
      expect(result).to eq(
        id: 'tier_2',
        plan_id: 2,
        item_id: 'plan_2',
        price: 50.0,
        name: 'Basic',
        external_name: 'Basic',
        currency_code: 'USD',
        description: 'desc',
        metadata: { key_values: { "foo" => "bar" } },
        item_description: 'desc',
        tier_id: 22,
        has_ai: true
      )
    end
  end

  describe '#serialize_addon' do
    it 'serializes an addon tier with acquired status' do
      addon = double(
        'Addon',
        identifier: 'addon_1',
        name: 'AddonName',
        description: 'addon desc'
      )
      tier = double(
        'Tier',
        identifier: 'tier_3',
        price: 25,
        priceable: addon,
        pricing_model: 'flat',
        id: 33
      )
      payment_plan = double('PaymentPlan')
      expect(tier).to receive(:acquired?).with(payment_plan).and_return(true)
      result = dummy.send(:serialize_addon, tier, payment_plan)
      expect(result).to eq(
        id: 'tier_3',
        item_id: 'addon_1',
        price: 25.0,
        name: 'AddonName',
        external_name: 'AddonName',
        currency_code: 'USD',
        description: 'addon desc',
        pricing_model: 'flat',
        tiers: [],
        item_description: 'addon desc',
        tier_id: 33,
        acquired: true
      )
    end
  end

  describe '#serialize_chargebee_addon' do
    it 'serializes a chargebee addon with metadata and item_description' do
      addon = double(
        'Addon',
        id: 10,
        price: 99.5,
        name: 'AddonName',
        item_id: 'addon_item_1',
        external_name: 'Addon External',
        currency_code: 'USD',
        description: 'addon desc',
        pricing_model: 'flat',
        tiers: [:tier1, :tier2]
      )
      chargebee = double('Chargebee')
      expect(chargebee).to receive(:item_metadata).with('addon_item_1').and_return({ foo: 'bar' })
      expect(chargebee).to receive(:item_description).with('addon_item_1').and_return('desc from cb')
      expect(dummy).to receive(:format_tiers).with([:tier1, :tier2]).and_return([{ t: 1 }, { t: 2 }])

      result = dummy.send(:serialize_chargebee_addon, addon, chargebee)
      expect(result).to eq(
        id: 10,
        price: 99.5,
        name: 'AddonName',
        item_id: 'addon_item_1',
        external_name: 'Addon External',
        currency_code: 'USD',
        description: 'addon desc',
        pricing_model: 'flat',
        tiers: [{ t: 1 }, { t: 2 }],
        metadata: { foo: 'bar' },
        item_description: 'desc from cb'
      )
    end
  end

  describe '#serialize_entitlement' do
    it 'serializes an entitlement' do
      entitlement = double('Entitlement', id: 5, identifier: 'feature_x', quantity: 3)
      result = dummy.send(:serialize_entitlement, entitlement)
      expect(result).to eq(
        id: 5,
        feature_id: 'feature_x',
        value: 3
      )
    end
  end

  describe '#plans_utils' do
    it 'returns a Plans::Utils instance and memoizes it' do
      utils = double('Plans::Utils')
      expect(Plans::Utils).to receive(:new).once.and_return(utils)
      expect(dummy.send(:plans_utils)).to eq(utils)
      expect(dummy.send(:plans_utils)).to eq(utils) # memoized
    end
  end
end
