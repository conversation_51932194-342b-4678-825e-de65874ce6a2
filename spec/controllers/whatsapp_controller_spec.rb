require 'rails_helper'

RSpec.describe WhatsappController, type: :request do
  let(:sandbox) do
    {
      app: 'Fransur',
      timestamp: 167_509_389_763_9,
      version: 2,
      type: 'user-event',
      payload: {
        phone: 'callbackSetPhone',
        type: 'sandbox-start'
      },
      whatsapp: {
        app: 'Fransur',
        timestamp: 167_509_389_763_9,
        version: 2,
        type: 'user-event',
        payload: {
          phone: 'callbackSetPhone',
          type: 'sandbox-start'
        }
      }
    }.with_indifferent_access
  end

  let(:no_type) do
    {
      app: 'Fransur',
      timestamp: 167_509_389_763_9,
      version: 2,
      type: 'user-event',
      payload: {
        phone: 'callbackSetPhone',
        type: 'sandbox-start'
      },
      whatsapp: {
        app: 'Fransur',
        timestamp: 167_509_389_763_9,
        version: 2,
        type: 'user-event',
        payload: {
          phone: 'callbackSetPhone'
        }
      }
    }.with_indifferent_access
  end

  let(:inbound) do
    {
      app: 'Fransur',
      timestamp: 167_509_657_719_1,
      version: 2,
      type: 'message',
      payload: {
        id: 'ABEGWEFFIjd2Ago-sEG0omaKo6qc',
        source: '584145221100',
        type: 'text',
        payload: {
          text: 'Hola'
        },
        sender: {
          phone: '584145221100',
          name: 'Elvis Olivar',
          country_code: '58',
          dial_code: '4145221100'
        }
      },
      whatsapp: {
        app: 'Fransur',
        timestamp: 167_509_657_719_1,
        version: 2,
        type: 'message',
        payload: {
          id: 'ABEGWEFFIjd2Ago-sEG0omaKo6qc',
          source: '584145221100',
          type: 'text',
          payload: {
            text: 'Hola'
          },
          sender: {
            phone: '584145221100',
            name: 'Elvis Olivar',
            country_code: '58',
            dial_code: '4145221100'
          }
        }
      }
    }.with_indifferent_access
  end

  let(:inbound_no_app) do
    {
      app: 'Example',
      timestamp: 167_509_657_719_1,
      version: 2,
      type: 'message',
      payload: {
        id: 'ABEGWEFFIjd2Ago-sEG0omaKo6qc',
        source: '584145221100',
        type: 'text',
        payload: {
          text: 'Hola'
        },
        sender: {
          phone: '584145221100',
          name: 'Elvis Olivar',
          country_code: '58',
          dial_code: '4145221100'
        }
      },
      whatsapp: {
        app: 'Example',
        timestamp: 167_509_657_719_1,
        version: 2,
        type: 'message',
        payload: {
          id: 'ABEGWEFFIjd2Ago-sEG0omaKo6qc',
          source: '584145221100',
          type: 'text',
          payload: {
            text: 'Hola'
          },
          sender: {
            phone: '584145221100',
            name: 'Elvis Olivar',
            country_code: '58',
            dial_code: '4145221100'
          }
        }
      }
    }.with_indifferent_access
  end

  let(:failed_event) do
    {
      app: 'Fransur',
      timestamp: 167_510_183_809_9,
      version: 2,
      type: 'message-event',
      payload: {
        id: 'a863bce2-d549-4f44-9f59-54c29de04070',
        type: 'failed',
        destination: '584120541100',
        payload: {
          code: 1013,
          reason: 'User is not valid:Recipient is not a valid WhatsApp user'
        }
      },
      whatsapp: {
        app: 'Fransur',
        timestamp: 167_510_183_809_9,
        version: 2,
        type: 'message-event',
        payload: {
          id: 'a863bce2-d549-4f44-9f59-54c29de04070',
          type: 'failed',
          destination: '584120541100',
          payload: {
            code: 1013,
            reason: 'User is not valid:Recipient is not a valid WhatsApp user'
          }
        }
      }
    }.with_indifferent_access
  end

  let(:qr_inbound_no_phone) do
    {
      product_id: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
      phone_id: 452_36,
      message: {
        type: 'text',
        text: 'Prueba 1',
        id: 'false_584245951100@c.us_BA9D1E23B54DE57591033E022351E0F8',
        _serialized: 'false_584245951100@c.us_BA9D1E23B54DE57591033E022351E0F8',
        fromMe: false
      },
      user: {
        id: '<EMAIL>',
        name: 'Ejemplo',
        phone: '584245951100'
      },
      conversation: '<EMAIL>',
      conversation_name: 'Ejemplo',
      receiver: '584145221100',
      timestamp: 167_354_850_5,
      type: 'message',
      reply: 'https://api.maytapi.com/api/12da6053-e2b1-4c75-9865-0f3ddeca0161/452_36/sendMessage',
      productId: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
      phoneId: 452_36,
      whatsapp: {
        product_id: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
        phone_id: 452_36,
        message: {
          type: 'text',
          text: 'Prueba 1',
          id: 'false_584245951100@c.us_BA9D1E23B54DE57591033E022351E0F8',
          _serialized: 'false_584245951100@c.us_BA9D1E23B54DE57591033E022351E0F8',
          fromMe: false
        },
        user: {
          id: '<EMAIL>',
          name: 'Ejemplo',
          phone: '584245951100'
        },
        conversation: '<EMAIL>',
        conversation_name: 'Ejemplo',
        receiver: '584145221100',
        timestamp: 167_354_850_5,
        type: 'message',
        reply: 'https://api.maytapi.com/api/12da6053-e2b1-4c75-9865-0f3ddeca0161/452_36/sendMessage',
        productId: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
        phoneId: 452_36
      }
    }.with_indifferent_access
  end

  let(:qr_inbound) do
    {
      product_id: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
      phone_id: 123_45,
      message: {
        type: 'text',
        text: 'Peurba 1',
        id: 'false_584245951100@c.us_BA9D1E23B54DE57591033E022351E0F8',
        _serialized: 'false_584245951100@c.us_BA9D1E23B54DE57591033E022351E0F8',
        fromMe: false
      },
      user: {
        id: '<EMAIL>',
        name: 'Ejemplo',
        phone: '584245951100'
      },
      conversation: '<EMAIL>',
      conversation_name: 'Ejemplo',
      receiver: '584145221100',
      timestamp: 167_354_850_5,
      type: 'message',
      reply: 'https://api.maytapi.com/api/12da6053-e2b1-4c75-9865-0f3ddeca0161/123_45/sendMessage',
      productId: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
      phoneId: 123_45,
      whatsapp: {
        product_id: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
        phone_id: 123_45,
        message: {
          type: 'text',
          text: 'Peurba 1',
          id: 'false_584245951100@c.us_BA9D1E23B54DE57591033E022351E0F8',
          _serialized: 'false_584245951100@c.us_BA9D1E23B54DE57591033E022351E0F8',
          fromMe: false
        },
        user: {
          id: '<EMAIL>',
          name: 'Ejemplo',
          phone: '584245951100'
        },
        conversation: '<EMAIL>',
        conversation_name: 'Ejemplo',
        receiver: '584145221100',
        timestamp: 167_354_850_5,
        type: 'message',
        reply: 'https://api.maytapi.com/api/12da6053-e2b1-4c75-9865-0f3ddeca0161/123_45/sendMessage',
        productId: '12da6053-e2b1-4c75-9865-0f3ddeca0161',
        phoneId: 123_45
      }
    }
  end

  let(:connected_status) do
    {
      success: true,
      status: {
        loggedIn: true,
        number: '123456789012'
      },
      number: '123456789012'
    }.with_indifferent_access
  end

  let(:logout_response) do
    {
      success: true
    }.with_indifferent_access
  end

  let(:qr_outbound_error) do
    {
      type: 'error',
      product_id: '9316a97a-472c-4f23-b300-08798a39515a',
      phone_id: 12_345,
      code: 'E01',
      message: 'The contact cannot be found!',
      data: {
        type: 'text',
        to_number: '<EMAIL>',
        message: 'Estimado/a estudiante Información sobre la siguiente materia del técnico en Administración.',
        id: 'MyString'
      },
      phoneId: 12_345,
      whatsapp: {
        type: 'error',
        product_id: '9316a97a-472c-4f23-b300-08798a39515a',
        phone_id: 12_345,
        code: 'E01',
        message: 'The contact cannot be found!',
        data: {
          type: 'text',
          to_number: '<EMAIL>',
          message: 'Estimado/a estudiante Información sobre la siguiente materia del técnico en Administración.',
          id: 'MyString'
        },
        phoneId: 12_345
      }
    }
  end

  let(:ack_message) do
    {
      data: [
        {
          msgId: '64da74d9868200b398d4137a',
          messageId: 'true_593961371400@c.us_3EB0C7154D8E554BFAF6C7',
          ackCode: 2,
          ackType: 'delivered'
        }
      ],
      type: 'ack',
      whatsapp: {
        data: [
          {
            msgId: '64da74d9868200b398d4137a',
            messageId: 'true_593961371400@c.us_3EB0C7154D8E554BFAF6C7',
            ackCode: 2,
            ackType: 'delivered'
          }
        ],
        type: 'ack'
      }
    }
  end

  describe 'POST #receive_whatsapp_api' do
    it 'establishes the connection with the app' do
      post '/gupshup/ws', params: sandbox

      expect(response).to have_http_status(:ok)
    end

    it 'returns 500 error when none type is specified' do
      post '/gupshup/ws', params: no_type

      expect(response).to have_http_status(:ok)
    end

    context 'when the app is not found' do
      let!(:retailer) { create(:retailer, :gupshup_integrated, gupshup_src_name: 'Fransur') }

      it 'send SlackError Whatsapp app not found' do
        allow(SlackError).to receive(:send_error)

        post '/gupshup/ws', params: inbound_no_app
        sleep(1)

        expect(SlackError).to have_received(:send_error).with(hash_including(message: /Whatsapp app not fo/), '', true)
      end
    end

    context 'when a sent message fails' do
      let!(:retailer) { create(:retailer, :gupshup_integrated, gupshup_src_name: 'Fransur') }
      let(:message_inbound) { instance_double(Whatsapp::Inbound::Event) }

      before do
        allow(SlackError).to receive(:send_error).and_return(true)
        allow(Whatsapp::Inbound::Event).to receive(:new).with(anything, anything, anything)
          .and_return(message_inbound)
        allow(Whatsapp::Utils::FailedMessageProcessor).to receive(:call).with(anything).and_return(true)
      end

      it 'returns 200 success' do
        post '/gupshup/ws', params: failed_event

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when it is an inbound message' do
      let!(:retailer) { create(:retailer, :gupshup_integrated, gupshup_src_name: 'Fransur') }
      let(:message_inbound) { instance_double(Whatsapp::Inbound::Event) }

      before do
        allow(SlackError).to receive(:send_error).and_return(true)
        allow(Whatsapp::Inbound::Message).to receive(:new).with(anything, anything, anything)
          .and_return(message_inbound)
        allow(message_inbound).to receive(:process_event!).with(anything).and_return(true)
      end

      it 'returns 200 success' do
        post '/gupshup/ws', params: inbound

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when it is an inbound message with video' do
      let(:retailer) { create(:retailer, :gupshup_integrated, gupshup_src_name: 'Fransur') }
      let(:customer) { create(:customer, phone: '584145221100') }

      let(:video_payload) do
        {
          app: retailer.gupshup_src_name,
          timestamp: 167_509_657_719_1,
          version: 2,
          type: 'message',
          payload: {
            id: 'ABEGVzAVQGIUAhAol1gnDhNo7k9BkMRGJ17o',
            source: '573015406214',
            type: 'video',
            payload: {
              url: 'https://filemanager.gupshup.io/wa/e738a62e-0a63-4163-8078-d0413050daf9/wa/media/b8ee898c-6839-4b97-a97c-6b973113a226?download=false',
              contentType: 'video/mp4',
              urlExpiry: 1_737_918_547_696
            },
            sender: {
              phone: '573015406214',
              name: 'Jesús Martínez',
              country_code: '57',
              dial_code: '3015406214'
            }
          },
          whatsapp: {
            app: retailer.gupshup_src_name,
            timestamp: 167_509_657_719_1,
            version: 2,
            type: 'message',
            payload: {
              id: 'ABEGVzAVQGIUAhAol1gnDhNo7k9BkMRGJ17o',
              source: '573015406214',
              type: 'video',
              payload: {
                url: 'https://filemanager.gupshup.io/wa/e738a62e-0a63-4163-8078-d0413050daf9/wa/media/b8ee898c-6839-4b97-a97c-6b973113a226?download=false',
                contentType: 'video/mp4',
                urlExpiry: 1_737_918_547_696
              },
              sender: {
                phone: '573015406214',
                name: 'Jesús Martínez',
                country_code: '57',
                dial_code: '3015406214'
              }
            }
          }
        }.with_indifferent_access
      end

      before do
        allow(Whatsapp::Helpers::Customers).to receive(:save_customer).and_return(customer)
      end

      it 'creates a new GupshupWhatsappMessage' do
        expect { post '/gupshup/ws', params: video_payload }.to change(GupshupWhatsappMessage, :count).by(1)
      end

      it 'creates a new GupshupWhatsappMessage with the correct attributes' do
        post '/gupshup/ws', params: video_payload

        gwm = GupshupWhatsappMessage.last
        expect(gwm.retailer_id).to eq(retailer.id)
        expect(gwm.customer_id).to eq(customer.id)
        expect(gwm.whatsapp_message_id).to eq('ABEGVzAVQGIUAhAol1gnDhNo7k9BkMRGJ17o')
        expect(gwm.gupshup_message_id).to eq('ABEGVzAVQGIUAhAol1gnDhNo7k9BkMRGJ17o')
        expect(gwm.status).to eq('delivered')
        expect(gwm.direction).to eq('inbound')
      end

      it 'enqueues Whatsapp::UploadResourcesJob' do
        expect { post '/gupshup/ws', params: video_payload }.to have_enqueued_job(Whatsapp::UploadResourcesJob)
      end
    end

    context 'when an exception is raised' do
      let!(:retailer) { create(:retailer, :gupshup_integrated, gupshup_src_name: 'Fransur') }
      let(:mock_error) { StandardError.new('Test error message') }

      before do
        allow(Rails.logger).to receive(:error)
        allow(Rails.logger).to receive(:info)
        allow(mock_error).to receive(:backtrace).and_return(%w[line1 line2 line3 line4 line5 line6
                                                               line7 line8 line9 line10 line11 line12])
        allow(Whatsapp::Inbound::Event).to receive(:new).and_raise(mock_error)
      end

      it 'logs the error and backtrace' do
        post '/gupshup/ws', params: inbound

        expect(Rails.logger).to have_received(:error).with(mock_error)
        expect(Rails.logger).to have_received(:info).with(%w[line1 line2 line3 line4 line5 line6
                                                             line7 line8 line9 line10])
      end

      it 'does not raise the exception' do
        expect { post '/gupshup/ws', params: inbound }.not_to raise_error
      end
    end
  end

  describe 'POST #receive_whatsapp_qr' do
    it 'establishes the connection with WhatsApp instance' do
      post '/whatsapp/webhook'

      expect(response).to have_http_status(:ok)
    end

    context 'when the WhatsApp instance is not found' do
      let!(:retailer) { create(:retailer, :qr_integrated) }

      it 'returns 404 error' do
        allow(SlackError).to receive(:send_error).and_return(true)

        post '/whatsapp/webhook', params: qr_inbound_no_phone

        body = response.parsed_body
        expect(response).to have_http_status(:not_found)
        expect(body['message']).to include('Phone id not found')
      end
    end

    context 'when it is a text inbound message' do
      let!(:retailer) { create(:retailer, :qr_integrated, qr_phone_id: '12345') }
      let(:message_inbound) { instance_double(Whatsapp::Inbound::Message) }

      before do
        allow(SlackError).to receive(:send_error).and_return(true)
        allow(Whatsapp::Inbound::Message).to receive(:new).with(anything, anything, anything)
          .and_return(message_inbound)
        allow(message_inbound).to receive(:process_event!).with(anything).and_return(true)
      end

      it 'returns 200 success' do
        post '/whatsapp/webhook', params: qr_inbound

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when qr_code was loaded' do
      let!(:retailer) { create(:retailer, qr_phone_id: '123') }

      before do
        allow(Connection).to receive(:get_request).and_return(connected_status)
      end

      it 'update retailer with phone number' do
        post whatsapp_webhook_path, params: {
          whatsapp: {
            phone_id: '123',
            type: 'status',
            status: 'active'
          }
        }

        expect(retailer.reload.qr_phone_number).to eq('123456789012')
      end
    end

    context 'when message returns error' do
      let!(:retailer) { create(:retailer, :qr_integrated, qr_phone_id: '12345') }
      let!(:gupshup_whatsapp_message) { create(:gupshup_whatsapp_message, retailer: retailer) }

      before do
        allow(Connection).to receive(:get_request).and_return(connected_status)
      end

      it 'updates the status and error_payload' do
        post whatsapp_webhook_path, params: qr_outbound_error

        gwm = GupshupWhatsappMessage.last
        expect(gwm.status).to eq 'error'
        expect(gwm.error_payload['payload']['payload']['reason']).to eq 'The contact cannot be found!'
        expect(gwm.error_payload['payload']['payload']['code']).to eq 'E01'
      end
    end

    context 'when idle' do
      let!(:retailer) { create(:retailer, :qr_integrated, qr_phone_id: '123') }

      before do
        allow(Connection).to receive(:get_request).and_return(logout_response)
        allow(Slack::Notifier).to receive(:new).and_return(Slack::Notifier.new(''))
        allow_any_instance_of(Slack::Notifier).to receive(:ping).and_return(true)
      end

      it 'update retailer with phone number' do
        post whatsapp_webhook_path, params: {
          whatsapp: {
            phone_id: '123',
            type: 'status',
            status: 'idle'
          }
        }

        retailer.reload
        expect(retailer.qr_phone_number).to be_nil
      end
    end

    context 'when logout' do
      let!(:retailer) { create(:retailer, :qr_integrated, qr_phone_id: '123') }

      before do
        allow(Connection).to receive(:get_request).and_return(logout_response)
        allow(Slack::Notifier).to receive(:new).and_return(Slack::Notifier.new(''))
        allow_any_instance_of(Slack::Notifier).to receive(:ping).and_return(true)
      end

      it 'update retailer with phone number' do
        post whatsapp_webhook_path, params: {
          whatsapp: {
            phone_id: '123',
            type: 'status',
            status: 'qr-screen'
          }
        }

        retailer.reload
        expect(retailer.qr_phone_number).to be_nil
      end
    end

    context 'when ack type delivered not update gupshup_message_id' do
      let(:retailer) { create(:retailer, connect_qr_mercately: false) }
      let!(:msg) do
        create(:gupshup_whatsapp_message, gupshup_message_id: '64da74d9868200b398d4137a', retailer: retailer)
      end

      before do
        allow(Connection).to receive(:get_request).and_return(connected_status)
      end

      it 'not update message with messageId' do
        post whatsapp_webhook_path, params: ack_message
        expect(response).to have_http_status(:ok)
        expect(msg.reload.gupshup_message_id).to eq('64da74d9868200b398d4137a')
      end
    end

    context 'when ack type delivered update gupshup_message_id' do
      let!(:msg) do
        create(:gupshup_whatsapp_message, gupshup_message_id: '64da74d9868200b398d4137a')
      end

      before do
        allow(Connection).to receive(:get_request).and_return(connected_status)
      end

      it 'update message with messageId' do
        post whatsapp_webhook_path, params: ack_message
        expect(response).to have_http_status(:ok)
        expect(msg.reload.gupshup_message_id).to eq('64da74d9868200b398d4137a')
      end
    end

    context 'when an exception is raised during QR processing' do
      let!(:retailer) { create(:retailer, :qr_integrated, qr_phone_id: '12345') }
      let(:mock_error) { StandardError.new('QR processing error') }

      before do
        allow(Whatsapp::Inbound::Event).to receive(:new).and_raise(mock_error)
      end

      it 'raises the exception (no rescue block in this method)' do
        expect { post '/whatsapp/webhook', params: qr_inbound }.to raise_error(StandardError, 'QR processing error')
      end
    end
  end

  describe 'POST #receive_whatsapp_api_code' do
    context 'when the integration is successful' do
      before do
        allow(Whatsapp::Integration::Configure).to receive(:call).and_return(true)
      end

      it 'redirects to the root path with a success notice' do
        get '/whatsapp/callback', params: { some_param: 'value' }

        expect(response).to redirect_to(root_path)
        follow_redirect!
        expect(flash[:notice]).to eq('Integración exitosa')
      end
    end

    context 'when an error occurs during integration' do
      before do
        allow(Whatsapp::Integration::Configure).to receive(:call).and_raise(StandardError, 'Integration failed')
        allow(Rails.logger).to receive(:error)
      end

      it 'logs the error and redirects to the root path with a failure notice' do
        get '/whatsapp/callback', params: { some_param: 'value' }

        expect(Rails.logger).to have_received(:error).with(instance_of(StandardError))
        expect(response).to redirect_to(root_path)
        follow_redirect!
        expect(flash[:notice]).to eq('Integración fallida')
      end
    end
  end
end
