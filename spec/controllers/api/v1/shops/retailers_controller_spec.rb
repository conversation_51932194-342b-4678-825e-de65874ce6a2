require 'rails_helper'

RSpec.describe Api::V1::Shops::RetailersController, type: :request do
  let!(:retailer) { create(:retailer) }
  let(:retailer_user) { create(:retailer_user, :admin, retailer: retailer) }
  let!(:mia_integration_type) { create(:mia_integration_type, :guides) }
  let!(:mia_integration) { create(:mia_integration, retailer:, mia_integration_type:) }
  let(:headers) do
    {
      'RETAILER-KEY' => retailer.unique_key,
      'MERCATELY-AUTH-TOKEN' => ENV.fetch('MERCATELY_AUTH_TOKEN', nil),
      'CONTENT_TYPE' => 'application/json'
    }
  end
  let(:params) do
    {
      web_id: 'abc',
      subtotal: 10,
      tax_amount: 15,
      shipping_value: 0,
      channel: 'other',
      total: 10,
      delivery_method: 'send_order',
      address: 'Av. 123',
      city: 'Rio',
      origin: 'Catalog',
      state: 'State',
      zip_code: '123',
      created_at: '',
      customer: {
        web_id: 'CUS-123',
        id_in_shops: '1',
        full_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        phone: '+5932245678'
      },
      agent_id: '',
      agent_name: '',
      items: [],
      custom_answers: []
    }
  end

  before do
    allow_any_instance_of(described_class).to receive(:current_retailer).and_return(retailer)
    allow(OrderMailer).to receive_message_chain(:sale_notification, :deliver_later)
    allow(OneSignalPushNotification).to receive(:send_sales_push)
    allow_any_instance_of(described_class).to receive(:create_shop_order).and_return(true)
  end

  describe 'POST /send_order_notification' do
    it 'sends notifications and returns success' do
      post '/api/v1/shops/retailers/send_order_notification', params: params, headers: headers, as: :json

      expect(response).to have_http_status(:ok)
      expect(response.parsed_body['message']).to eq('Notificación enviada con exito')
      expect(OneSignalPushNotification).to have_received(:send_sales_push).once
    end
  end

  describe 'POST /save_order_data' do
    it 'calls save_shop_order and returns success' do
      post '/api/v1/shops/retailers/save_order_data', params: params, headers: headers, as: :json

      expect(response).to have_http_status(:ok)
      expect(response.parsed_body['message']).to eq('Orden guardada con exito')
    end
  end

  describe 'GET /mia_integration' do
    context 'when integration exists' do
      it 'returns the integration data' do
        get '/api/v1/shops/retailers/mia_integration', params: { mia_integration_type: 'guides' }, headers: headers

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body).to eq({ 'openai_key' => 'openai_key', 'public_key' => nil, 'service_url' => nil })
      end
    end

    context 'when integration does not exist' do
      before do
        allow(retailer).to receive(:mia_integration).and_return(nil)
      end

      it 'returns not found message' do
        get '/api/v1/shops/retailers/mia_integration', params: { mia_integration_type: 'missing' }, headers: headers

        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body['message']).to eq('No se encontró la integración')
      end
    end
  end
end
