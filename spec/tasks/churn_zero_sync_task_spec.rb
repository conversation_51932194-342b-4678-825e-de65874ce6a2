require 'rails_helper'
require 'rake'

RSpec.describe 'churn_zero_sync', type: :task do
  before do
    MercatelyChurnzeroApi.configure do |config|
      config.api_key = 'mock_app_key'
      config.base_url = 'https://example.com'
    end
    stub_request(:post, /#{MercatelyChurnzeroApi.configuration.base_url}/)
      .to_return(status: 200, body: '', headers: {})
    task.reenable
  end

  describe 'sync_retailers' do
    let(:task) { Rake::Task['churn_zero_sync:sync_retailers'] }

    context 'when are free and inactive retailers' do
      let(:retailers) { create_list(:retailer, 10) }
      let(:free_retailer) { create(:retailer) }
      let(:inactive_retailer) { create(:retailer) }

      before do
        PaymentPlan.where(retailer: retailers).update_all(plan: :pro)
        free_retailer.payment_plan.update(plan: :free)
        free_retailer.payment_plan.update(status: :inactive)
      end

      it 'does not sync free or inactive retailers' do
        expect do
          task.invoke
        end.to change(Retailer.where.not(last_sync_with_churnzero_date: nil), :count).by(10)
        expect(free_retailer.last_sync_with_churnzero_date).to be_nil
        expect(free_retailer.last_sync_with_churnzero_date).to be_nil
      end
    end

    context 'when retailer is valid to sync' do
      let(:retailer) { create(:retailer, gupshup_connection_date: 1.day.ago, first_payment_date: 5.days.ago) }
      let(:payment_plan) { retailer.payment_plan }
      let(:top_ups) { create_list(:top_up, 3, amount: 10, retailer: retailer) }

      before do
        top_ups
        payment_plan.update(plan: :pro, price: 300, month_interval: 3, status: :active, payment_start_date: 35.days.ago)
      end

      it 'calculates months from start to end date' do
        task.invoke
        expect(WebMock).to(have_requested(:post, /#{MercatelyChurnzeroApi.configuration.base_url}/)
              .with do |request|
                body = JSON.parse(request.body).first
                if body.present?
                  expect(body['attr_DaysUntilConnection']).to eq(4)
                  expect(body['attr_TotalBalanceSpent']).to eq(30)
                  expect(body['attr_AverageTicket']).to eq('115.0')
                end
              end)
      end
    end

    context 'when retailers is inactive but has last_sync_with_churnzero_date' do
      let(:gt_last_sync) { 10.minutes.from_now }
      let(:lt_last_sync) { 10.minutes.ago }
      let!(:gt_retailer) { create(:retailer, last_sync_with_churnzero_date: 2.minutes.ago) }
      let!(:lt_retailer) { create(:retailer, last_sync_with_churnzero_date: 2.minutes.ago) }

      before do
        gt_retailer.payment_plan.update(updated_at: gt_last_sync, plan: :free)
        lt_retailer.payment_plan.update(updated_at: lt_last_sync, plan: :free)
      end

      it 'syncs retailers with last_sync_with_churnzero_date < payment_plans.updated_at' do
        task.invoke
        expect(lt_retailer.reload.last_sync_with_churnzero_date).to be_within(1.minute).of(2.minutes.ago)
        expect(gt_retailer.reload.last_sync_with_churnzero_date).to be_within(1.minute).of(DateTime.now)
      end

      context 'when plan has ended' do
        let(:payment_plan_params) do
          { price: 90, month_interval: 3, payment_start_date: Time.new(2023, 1, 1), end_date: Time.new(2023, 2, 6) }
        end

        before do
          lt_retailer.payment_plan.update(payment_plan_params)
        end

        it 'calculates months from start to end date' do
          task.invoke
          expect(WebMock).to(have_requested(:post, /#{MercatelyChurnzeroApi.configuration.base_url}/)
                .with do |request|
                  retailer = JSON.parse(request.body).find { |r| r['accountExternalId'] == lt_retailer.id }
                  if retailer
                    expect(retailer['attr_LTM']).to eq(2)
                    expect(retailer['attr_LTV']).to eq('60.0')
                  end
                end)
        end
      end

      context 'when plan is active' do
        before do
          start_date = Time.now - (99 * 60 * 60 * 24)
          lt_retailer.payment_plan.update(price: 90, month_interval: 3, payment_start_date: start_date, end_date: nil)
        end

        it 'calculates months from start to current date' do
          task.invoke
          expect(WebMock).to(have_requested(:post, /#{MercatelyChurnzeroApi.configuration.base_url}/)
                .with do |request|
                  retailer = JSON.parse(request.body).find { |r| r['accountExternalId'] == lt_retailer.id }
                  if retailer
                    expect(retailer['attr_LTM']).to eq(4)
                    expect(retailer['attr_LTV']).to eq('120.0')
                  end
                end)
        end
      end
    end

    context 'when retailer does not have last_sync_with_churnzero_date' do
      let(:retailer) { create(:retailer) }
      let(:free_retailer) { create(:retailer) }
      let!(:contact) { create(:retailer_user, retailer: retailer) }
      let!(:free_contact) { create(:retailer_user, retailer: free_retailer) }

      before do
        retailer.payment_plan.update(plan: :pro)
        free_retailer.payment_plan.update(plan: :free)
      end

      it 'does not send contacts from retailers that have not been synchronized' do
        task.invoke
        expect(contact.reload.last_sync_with_churnzero_at).to be_within(1.minute).of(DateTime.now)
        expect(free_contact.reload.last_sync_with_churnzero_at).to be_nil
      end

      it 'does not send removed contacts' do
        RetailerUser.update_all(removed_from_team: true)
        task.invoke
        expect(contact.reload.last_sync_with_churnzero_at).to be_nil
        expect(free_contact.reload.last_sync_with_churnzero_at).to be_nil
      end
    end
  end

  describe 'sync_retailers_weekly' do
    let(:task) { Rake::Task['churn_zero_sync:sync_retailers_weekly'] }

    context 'when retailer is valid to sync' do
      let(:retailer) { create(:retailer, gupshup_connection_date: 1.day.ago, first_payment_date: 5.days.ago) }
      let(:payment_plan) { retailer.payment_plan }
      let!(:contact_groups) { create_list(:contact_group, 3, :with_customers, retailer: retailer) }
      let!(:sent_campaigns) { create_list(:campaign, 3, retailer: retailer) }
      let!(:tags) { create_list(:tag, 3, retailer: retailer) }

      before do
        allow(ActiveRecord::Base.connection).to receive(:reconnect!)
        payment_plan.update(plan: :pro, price: 300, month_interval: 3, status: :active, payment_start_date: 35.days.ago)
      end

      it 'sends correct total contact groups and tags' do
        task.invoke
        expect(WebMock).to(have_requested(:post, /#{MercatelyChurnzeroApi.configuration.base_url}/)
              .with do |request|
                body = JSON.parse(request.body).first
                if body.present?
                  expect(body['attr_TotalContactGroups']).to eq(6)
                  expect(body['attr_TotalTags']).to eq(3)
                end
              end)
      end

      it 'sends correct contact groups and tags created in last 30 days' do
        task.invoke
        expect(WebMock).to(have_requested(:post, /#{MercatelyChurnzeroApi.configuration.base_url}/)
              .with do |request|
                body = JSON.parse(request.body).first
                if body.present?
                  expect(body['attr_ContactGroupsCreatedLast30Days']).to eq(6)
                  expect(body['attr_TotalTagsCreatedLast30Days']).to eq(3)
                  expect(body['attr_TotalUsedTagsLast30Days']).to eq(0)
                end
              end)
      end
    end
  end
end
