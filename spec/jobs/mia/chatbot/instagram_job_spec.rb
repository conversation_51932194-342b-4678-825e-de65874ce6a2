require 'rails_helper'

RSpec.describe Mia::Chatbot::InstagramJob do
  describe '#perform' do
    let(:retailer) { create(:retailer, :ig_integrated, welcome_message_sent: true) }
    let(:customer) { create(:customer, allow_mia_chatbot:, retailer:, human_help:, buy_intention:) }
    let(:allow_mia_chatbot) { true }
    let(:human_help) { false }
    let(:buy_intention) { false }
    let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
    let!(:customer_mia_chatbot) do
      create(:customer_mia_chatbot, platform: 'instagram', active: true, customer:, human_help:, buy_intention:)
    end
    let(:sources) do
      {
        guides: [
          {
            llm_query: 'Opciones de entrega',
            type: 'text',
            id: 'g123123a',
            title: 'Guia de opciones de entrega',
            result: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod'
          }
        ],
        products: []
      }
    end

    before do
      allow(ActiveRecord::Base.connection).to receive(:reconnect!)
      create(:mia_integration, :with_public_key, mia_integration_type:, retailer:)
    end

    context 'when message is processed' do
      let(:success) { true }

      before do
        allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
          .to receive(:send_message)
          .and_return(double(status:, body:, success?: success))

        allow_any_instance_of(Redis).to receive(:get).with("instagram_messages_#{customer.id}").and_return('hola')
        allow_any_instance_of(Redis).to receive(:del).with("instagram_messages_#{customer.id}")
      end

      context 'when inbound_type is text' do
        subject(:job) { described_class.perform_now(customer.id, 'text', nil) }

        context 'with referenced message' do
          let(:status) { 200 }
          let(:body) { { 'response' => [{ 'text' => 'AI response with reference' }], 'flag' => 'message' } }
          let(:referenced_message_id) { 'ref123' }
          let(:referenced_message) do
            {
              type: 'text',
              content: 'This is a referenced message'
            }
          end
          let(:referenced_msg) do
            instance_double(InstagramMessage,
                            mid: referenced_message_id,
                            text: 'This is a referenced message')
          end

          before do
            allow(InstagramMessage).to receive(:find_by)
              .with(mid: referenced_message_id)
              .and_return(referenced_msg)
            allow_any_instance_of(described_class).to receive_messages(
              type: 'text'
            )
            allow_any_instance_of(InstagramMessage).to receive(:mark_unread_flag)
            msg = InstagramMessage.new(mid: referenced_message_id, text: 'This is a referenced message',
                                       facebook_retailer: retailer.facebook_retailer, customer:)
            msg.save(validate: false)
          end

          it 'includes the referenced message in the request' do
            expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .with(hash_including(
                      question: hash_including(
                        referenced_message: referenced_message
                      )
                    ))

            described_class.perform_now(customer.id, 'text', referenced_message_id, {})
          end
        end

        context 'when mia response status is 200' do
          let(:status) { 200 }

          context 'with message flag' do
            context 'with text only' do
              let(:body) { { 'response' => [{ 'text' => 'AI response' }], 'flag' => 'message' } }
              let(:human_help) { true }

              context 'with customer_context_message' do
                let(:value) do
                  [
                    { type: 'text', content: { text: 'esta es una prueba', source: 'outbound' } },
                    { type: 'text', content: { text: 'hola', source: 'outbound' } }
                  ]
                end
                let!(:customer_context_message) do
                  create(:customer_context_message, customer_id: customer.id, platform: 'instagram', value:)
                end

                it { expect { subject }.to change { customer_context_message.reload.value }.to([]) }
              end

              it { expect { subject }.to(change { customer_mia_chatbot.reload.human_help }) }

              it 'sends text outbound message' do
                expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
                  .to receive(:send_message)
                  .with(hash_including(:question, :context))

                expect { subject }.to change(InstagramMessage, :count).by(1)
                expect(customer.reload.customer_mia_chatbot_for('instagram').human_help).to be_falsy
              end
            end

            context 'with single reference and short text' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Short caption',
                    'char_count' => 100,
                    'references' => [{
                      'content_type' => 'image/jpeg',
                      'url' => 'http://example.com/image.jpg'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends single message with caption' do
                expect_chatbot_message
                subject
              end

              context 'with sources' do
                let(:body) do
                  {
                    'response' => [{
                      'text' => 'Short caption',
                      'char_count' => 100,
                      'references' => [{
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image.jpg'
                      }]
                    }],
                    'flag' => 'message',
                    'sources' => sources
                  }
                end

                let(:msg_response) do
                  { code: 200, body: {}, message: instance_double(InstagramMessage, id: 1) }
                end

                it { expect { subject }.to change(MessageMiaResponseSourcesData, :count) }
              end
            end

            context 'with multiple references or long text' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Long text message',
                    'char_count' => 2000,
                    'references' => [
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image1.jpg'
                      },
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image2.jpg'
                      }
                    ]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends text and references separately' do
                expect_chatbot_message
                subject
              end

              context 'with sources' do
                let(:body) do
                  {
                    'response' => [{
                      'text' => 'Long text message',
                      'char_count' => 2000,
                      'references' => [
                        {
                          'content_type' => 'image/jpeg',
                          'url' => 'http://example.com/image1.jpg'
                        },
                        {
                          'content_type' => 'image/jpeg',
                          'url' => 'http://example.com/image2.jpg'
                        }
                      ]
                    }],
                    'flag' => 'message',
                    'sources' => sources
                  }
                end

                let(:msg_response) do
                  { code: 200, body: {}, message: instance_double(InstagramMessage, id: 1) }
                end

                it { expect { subject }.to change(MessageMiaResponseSourcesData, :count) }
              end
            end

            context 'with text reference' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Some text',
                    'references' => [{
                      'content_type' => 'text/plain',
                      'url' => 'http://example.com/text.txt'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends text message from reference' do
                expect_any_instance_of(Mia::Chatbot::ResponseHandler::Instagram)
                  .to receive(:send_reference)
                  .with({ 'content_type' => 'text/plain', 'url' => 'http://example.com/text.txt' }, 'Some text')
                  .and_call_original

                subject
              end
            end

            context 'with message length calculation' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Test message',
                    'char_count' => nil,
                    'references' => [{
                      'content_type' => 'image/jpeg',
                      'url' => 'http://example.com/image.jpg'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'calculates message length from text when char_count is nil' do
                expect_any_instance_of(Mia::Chatbot::ResponseHandler::Instagram)
                  .to receive(:send_reference)
                  .with({ 'content_type' => 'image/jpeg', 'url' => 'http://example.com/image.jpg' }, 'Test message')
                  .and_call_original

                subject
              end
            end

            context 'with text reference in multiple messages' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Long text message',
                    'char_count' => 2000,
                    'references' => [
                      {
                        'content_type' => 'text/plain',
                        'url' => 'http://example.com/text1.txt'
                      },
                      {
                        'content_type' => 'text/plain',
                        'url' => 'http://example.com/text2.txt'
                      }
                    ]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends multiple text messages from references' do
                expect_any_instance_of(Mia::Chatbot::ResponseHandler::Instagram)
                  .to receive(:send_reference)
                  .with({ 'content_type' => 'text/plain', 'url' => 'http://example.com/text1.txt' })
                  .and_call_original

                expect_any_instance_of(Mia::Chatbot::ResponseHandler::Instagram)
                  .to receive(:send_reference)
                  .with({ 'content_type' => 'text/plain', 'url' => 'http://example.com/text2.txt' })
                  .and_call_original

                subject
              end
            end

            context 'with message length edge cases' do
              let(:body) do
                {
                  'response' => [{
                    'text' => nil,
                    'char_count' => nil,
                    'references' => [{
                      'content_type' => 'image/jpeg',
                      'url' => 'http://example.com/image.jpg'
                    }]
                  }],
                  'flag' => 'message'
                }
              end
            end

            context 'with invalid flag' do
              let(:body) { { 'response' => [], 'flag' => 'invalid_flag' } }
              let(:status) { 200 }

              it 'handles invalid flag without error' do
                expect(Rails.logger).to receive(:error)
                  .with("There was an error trying to get Mia responses: Flag 'invalid_flag' not supported")

                subject
              end
            end

            context 'with multiple references and empty text' do
              let(:body) do
                {
                  'response' => [{
                    'text' => '',
                    'char_count' => 0,
                    'references' => [
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image1.jpg'
                      },
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image2.jpg'
                      }
                    ]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends only references when text is empty' do
                subject
              end
            end

            context 'when mia response has nil flag' do
              let(:status) { 200 }
              let(:body) { { 'response' => [{ 'text' => 'AI response' }] } }

              it 'handles nil flag without error' do
                expect(Rails.logger).to receive(:error)
                  .with("There was an error trying to get Mia responses: Flag '' not supported")

                subject
              end
            end
          end
        end

        context 'when mia response status is 202' do
          let(:status) { 202 }

          context 'when mia response has help flag' do
            let(:help_summary) do
              'El usuario ha solicitado asistencia humana. Anteriormente, ha mostrado interés en la ' \
                'compra de bicicletas eléctricas, específicamente la Bicicleta Eléctrica tres Puestos. ' \
                'Necesita ser transferido a un asesor humano para obtener más ayuda y posiblemente ' \
                'finalizar el proceso de compra. Asegúrate de proporcionar asistencia personalizada y ' \
                'responder cualquier pregunta adicional que el usuario pueda tener.'
            end

            let(:body) do
              {
                'flag' => 'help',
                'response' => 'Hemos pasado tu inquietud a un agente, en breve te contestaremos',
                'summary' => help_summary
              }
            end

            it { expect { subject }.to change { customer_mia_chatbot.reload.active }.to(false) }
            it { expect { subject }.to change { customer_mia_chatbot.reload.human_help }.to(true) }
          end
        end

        context 'when mia response status is an error' do
          let(:success) { false }
          let(:status) { 500 }
          let(:body) { 'Something went wrong' }

          it 'logs the error message' do
            expect(Rails.logger).to receive(:error)
              .with('There was an error trying to get Mia responses: Something went wrong')

            subject
          end
        end
      end

      context 'when inbound_type is audio' do
        subject(:job) { described_class.perform_now(customer.id, 'audio', nil, media_params) }

        let(:url) { 'https://example.com/audio.aac' }
        let(:media_params) { { url: url } }

        context 'when url is not blank' do
          let(:status) { 200 }
          let(:body) { { 'response' => [{ 'text' => 'AI response about audio' }], 'flag' => 'message' } }
          let(:chatbot_response) { double(status:, body:, success?: true) }

          before do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)
          end

          it 'processes the audio message with the direct URL' do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)

            expect { subject }.not_to raise_error
          end
        end
      end

      context 'when inbound_type is image' do
        subject(:job) { described_class.perform_now(customer.id, 'image', nil, media_params) }

        let(:url) { 'https://example.com/image.jpg' }
        let(:caption) { 'Test image caption' }
        let(:media_params) { { url: url, caption: caption } }

        context 'when url is not blank' do
          let(:status) { 200 }
          let(:body) { { 'response' => [{ 'text' => 'AI response about image' }], 'flag' => 'message' } }
          let(:chatbot_response) { double(status:, body:, success?: true) }

          before do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)
          end

          it 'processes the image message with the URL and caption' do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)

            expect { subject }.not_to raise_error
          end
        end
      end

      context 'when inbound_type is file' do
        subject(:job) { described_class.perform_now(customer.id, 'file', nil, media_params) }

        let(:url) { 'https://example.com/document.pdf' }
        let(:caption) { 'Test file caption' }
        let(:file_name) { 'document.pdf' }
        let(:media_params) { { url: url, caption: caption, file_name: file_name } }

        context 'when url is not blank' do
          let(:status) { 200 }
          let(:body) { { 'response' => [{ 'text' => 'AI response about file' }], 'flag' => 'message' } }
          let(:chatbot_response) { double(status:, body:, success?: true) }

          before do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)
          end

          it 'processes the file message with the URL, caption and file_name' do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)

            expect { subject }.not_to raise_error
          end
        end
      end
    end

    context 'when message is not processed' do
      context 'when customer does not exist' do
        subject(:job) { described_class.perform_now(0, 'text', nil) }

        it 'does not call MercatelyMiaApi::Chatbot and send message' do
          expect_any_instance_of(MercatelyMiaApi::V2::Chatbot).not_to receive(:send_message)
          subject
        end
      end
    end

    describe '#referenced_message_payload' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer) }

      before do
        job.instance_variable_set(:@customer, customer)
      end

      context 'when referenced message ID is provided and type is text' do
        let(:referenced_message_id) { 123 }
        let(:referenced_msg) do
          instance_double(InstagramMessage,
                          mid: referenced_message_id,
                          text: 'Hello',
                          file_type: 'image')
        end
        let(:expected_payload) { { type: 'text', content: 'Hello' } }

        before do
          job.instance_variable_set(:@referenced_message_id, referenced_message_id)
          allow(job).to receive_messages(
            referenced_message: referenced_msg,
            type: 'text',
            build_text_message: expected_payload
          )
        end

        it 'finds the message by ID and builds the text payload' do
          result = job.send(:referenced_message_payload)
          expect(result).to eq(expected_payload)
          expect(job.instance_variable_get(:@referenced_message_payload)).to eq(expected_payload)
        end

        it 'memoizes the result' do
          first_result = job.send(:referenced_message_payload)

          expect(job).not_to receive(:build_text_message)

          second_result = job.send(:referenced_message_payload)

          expect(first_result).to eq(expected_payload)
          expect(second_result).to eq(expected_payload)
          expect(first_result).to equal(second_result)
        end
      end

      context 'when referenced message ID is provided and type is image' do
        let(:referenced_message_id) { 456 }
        let(:referenced_msg) do
          instance_double(InstagramMessage,
                          mid: referenced_message_id,
                          url: 'http://example.com/image.jpg',
                          file_type: 'image')
        end
        let(:expected_media_payload) { { type: 'image', media_url: 'http://example.com/image.jpg' } }

        before do
          job.instance_variable_set(:@referenced_message_id, referenced_message_id)
          allow(job).to receive_messages(
            referenced_message: referenced_msg,
            type: 'image',
            build_media_message: expected_media_payload
          )
        end

        it 'finds the message by ID and builds the media payload' do
          result = job.send(:referenced_message_payload)
          expect(result).to eq(expected_media_payload)
          expect(job.instance_variable_get(:@referenced_message_payload)).to eq(expected_media_payload)
        end

        it 'memoizes the result' do
          first_result = job.send(:referenced_message_payload)

          expect(job).not_to receive(:build_media_message)

          second_result = job.send(:referenced_message_payload)

          expect(first_result).to eq(expected_media_payload)
          expect(second_result).to eq(expected_media_payload)
          expect(first_result).to equal(second_result)
        end
      end

      context 'when referenced message does not exist' do
        before do
          job.instance_variable_set(:@referenced_message_id, nil)
          allow(job).to receive(:referenced_message).and_return(nil)
        end

        it 'returns nil when referenced_message is nil' do
          allow(job).to receive(:type).and_raise(NoMethodError)

          expect { job.send(:referenced_message_payload) }.to raise_error(NoMethodError)
        end
      end
    end

    describe '#process_inbound_message' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer, retailer:) }
      let(:retailer) { create(:retailer) }
      let(:chatbot_client) { instance_double(MercatelyMiaApi::V2::Chatbot) }
      let(:send_message_params) { { question: { type: 'text', content: 'Hello' } } }
      let(:response) { double('response') }

      before do
        job.instance_variable_set(:@customer, customer)
        allow(job).to receive_messages(chatbot_client: chatbot_client, send_message_params: send_message_params)
      end

      it 'sends the message to the chatbot client and returns the response' do
        expect(chatbot_client).to receive(:send_message).with(send_message_params).and_return(response)

        result = job.send(:process_inbound_message)

        expect(result).to eq(response)
      end
    end

    describe '#send_message_params' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer, retailer:) }
      let(:retailer) { create(:retailer) }

      before do
        job.instance_variable_set(:@customer_id, customer.id)
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@inbound_type, 'text')
        job.instance_variable_set(:@message_value, 'Hello')
      end

      it 'returns cached params if already defined' do
        cached_params = { cached: true }
        job.instance_variable_set(:@send_message_params, cached_params)

        expect(job.send(:send_message_params)).to eq(cached_params)
      end

      it 'includes referenced message when present' do
        referenced_message = { type: 'text', content: 'Referenced content' }
        allow(job).to receive_messages(
          referenced_message_payload: referenced_message,
          type: 'text'
        )
        job.instance_variable_set(:@referenced_message_id, 'ref123')

        params = job.send(:send_message_params)

        expect(params[:question]).to include(referenced_message: referenced_message)
      end

      it 'uses text builder as fallback for unknown message type' do
        job.instance_variable_set(:@inbound_type, 'unknown_type')
        allow(Rails.logger).to receive(:warn)

        params = job.send(:send_message_params)

        expect(params[:question][:type]).to eq('text')
      end
    end

    describe '#message_value' do
      let(:job) { described_class.new }
      let(:inbound_message) { 'Hello world' }
      let(:url) { 'https://example.com/file.jpg' }
      let(:media_params) { { url: url } }

      context 'when inbound_type is text' do
        before do
          job.instance_variable_set(:@inbound_type, 'text')
          job.instance_variable_set(:@message_value, nil)
          allow(job).to receive(:inbound_message).and_return(inbound_message)
        end

        it 'returns the inbound_message' do
          expect(job.send(:message_value)).to eq(inbound_message)
        end
      end

      context 'when inbound_type is not text' do
        before do
          job.instance_variable_set(:@inbound_type, 'image')
          job.instance_variable_set(:@message_value, nil)
          job.instance_variable_set(:@media_params, media_params)
        end

        it 'returns the url from media_params' do
          expect(job.send(:message_value)).to eq(url)
        end
      end

      context 'when @message_value is already set' do
        let(:cached_value) { 'cached value' }

        before do
          job.instance_variable_set(:@message_value, cached_value)
        end

        it 'returns the cached value' do
          expect(job.send(:message_value)).to eq(cached_value)
        end
      end
    end

    describe 'message builders' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer, retailer:) }
      let(:retailer) { create(:retailer) }
      let(:url) { 'https://example.com/file.jpg' }
      let(:caption) { 'Test caption' }
      let(:file_name) { 'file.jpg' }

      before do
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@url, url)
        job.instance_variable_set(:@caption, caption)
        job.instance_variable_set(:@file_name, file_name)
        job.instance_variable_set(:@inbound_type, 'text')
        allow(job).to receive(:message_value).and_return(url)
      end

      describe '#message_builder' do
        it 'returns media_builder for image type' do
          job.instance_variable_set(:@inbound_type, 'image')
          expect(job).to receive(:media_builder)
          job.send(:message_builder)
        end

        it 'returns media_builder for file type' do
          job.instance_variable_set(:@inbound_type, 'file')
          expect(job).to receive(:media_builder)
          job.send(:message_builder)
        end

        it 'returns media_builder for audio type' do
          job.instance_variable_set(:@inbound_type, 'audio')
          expect(job).to receive(:media_builder)
          job.send(:message_builder)
        end

        it 'returns text_builder for text type' do
          job.instance_variable_set(:@inbound_type, 'text')
          expect(job).to receive(:text_builder)
          job.send(:message_builder)
        end
      end

      describe '#text_builder' do
        it 'returns a hash with text type and content' do
          allow(job).to receive(:message_value).and_return('Hello world')

          result = job.send(:text_builder)

          expect(result).to eq({
            type: 'text',
            content: 'Hello world'
          })
        end
      end
    end
  end

  describe 'private methods' do
    let(:job) { described_class.new }
    let(:retailer) { create(:retailer, :ig_integrated) }
    let(:customer) { create(:customer, retailer:) }
    let(:referenced_message_id) { 'mid_123' }
    let(:referenced_message) do
      create(:instagram_message, customer:, mid: referenced_message_id, text: 'Hola mundo')
    end

    before do
      job.instance_variable_set(:@customer, customer)
      job.instance_variable_set(:@referenced_message_id, referenced_message_id)
      allow(job).to receive(:referenced_message).and_return(referenced_message)
    end

    describe '#referenced_message' do
      it 'returns the referenced InstagramMessage by mid' do
        job.remove_instance_variable(:@referenced_message) if job.instance_variable_defined?(:@referenced_message)
        result = job.send(:referenced_message)
        expect(result).to eq(referenced_message)
      end
    end

    describe '#type' do
      context 'when message has no url' do
        before { referenced_message.update!(url: nil) }

        it 'returns text as type' do
          expect(job.send(:type)).to eq('text')
        end
      end

      context 'when message has url and file_type' do
        before { referenced_message.update!(url: 'http://example.com/image.jpg', file_type: 'image') }

        it 'returns the file_type as type' do
          expect(job.send(:type)).to eq('image')
        end
      end
    end

    describe '#build_text_message' do
      it 'builds a hash with type and text content' do
        referenced_message.update!(text: 'Mensaje de prueba')
        expect(job.send(:build_text_message)).to eq({
          type: 'text',
          content: 'Mensaje de prueba'
        })
      end
    end

    describe '#build_media_message' do
      before do
        referenced_message.update!(
          url: 'http://example.com/file.pdf',
          file_type: 'file',
          filename: 'file.pdf'
        )
      end

      it 'builds a hash with media_url, file_name and type' do
        expect(job.send(:build_media_message)).to eq({
          type: 'file',
          media_url: 'http://example.com/file.pdf',
          file_name: 'file.pdf'
        })
      end
    end
  end

  private

    def expect_chatbot_message
      expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
        .to receive(:send_message)
        .with(hash_including(:question, :context))
    end
end
