require 'rails_helper'

RSpec.describe Mia::Chatbot::Messenger<PERSON><PERSON> do
  describe '#perform' do
    let(:retailer) { create(:retailer, :fb_integrated, welcome_message_sent: true) }
    let(:customer) { create(:customer, allow_mia_chatbot:, retailer:, human_help:, buy_intention:) }
    let(:allow_mia_chatbot) { true }
    let(:human_help) { false }
    let(:buy_intention) { false }
    let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
    let!(:customer_mia_chatbot) do
      create(:customer_mia_chatbot, platform: 'messenger', active: true, customer:, human_help:, buy_intention:)
    end
    let(:sources) do
      {
        guides: [
          {
            llm_query: 'Opciones de entrega',
            type: 'text',
            id: 'g123123a',
            title: 'Guia de opciones de entrega',
            result: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod'
          }
        ],
        products: []
      }
    end

    before do
      allow(ActiveRecord::Base.connection).to receive(:reconnect!)
      create(:mia_integration, :with_public_key, mia_integration_type:, retailer:)
    end

    context 'when message is processed' do
      let(:success) { true }

      before do
        allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
          .to receive(:send_message)
          .and_return(double(status:, body:, success?: success))

        allow_any_instance_of(Redis).to receive(:get).with("messenger_messages_#{customer.id}").and_return('hola')
        allow_any_instance_of(Redis).to receive(:del).with("messenger_messages_#{customer.id}")
      end

      context 'when inbound_type is text' do
        subject(:job) { described_class.perform_now(customer.id, 'text', nil) }

        context 'with referenced message' do
          let(:status) { 200 }
          let(:body) { { 'response' => [{ 'text' => 'AI response with reference' }], 'flag' => 'message' } }
          let(:referenced_message_id) { 'ref123' }
          let(:referenced_message) do
            {
              type: 'text',
              content: 'This is a referenced message'
            }
          end
          let(:referenced_msg) do
            instance_double(FacebookMessage,
                            mid: referenced_message_id,
                            text: 'This is a referenced message')
          end

          before do
            allow(FacebookMessage).to receive(:find_by)
              .with(mid: referenced_message_id)
              .and_return(referenced_msg)
            allow_any_instance_of(described_class).to receive_messages(
              type: 'text'
            )
            allow_any_instance_of(FacebookMessage).to receive(:mark_unread_flag)
            msg = FacebookMessage.new(mid: referenced_message_id, text: 'This is a referenced message',
                                      facebook_retailer: retailer.facebook_retailer, customer:)
            msg.save(validate: false)
          end

          it 'includes the referenced message in the request' do
            expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .with(hash_including(
                      question: hash_including(
                        referenced_message: referenced_message
                      )
                    ))

            described_class.perform_now(customer.id, 'text', referenced_message_id, {})
          end
        end

        context 'when mia response status is 200' do
          let(:status) { 200 }

          context 'with message flag' do
            context 'with text only' do
              let(:body) { { 'response' => [{ 'text' => 'AI response' }], 'flag' => 'message' } }
              let(:human_help) { true }

              it { expect { subject }.to(change { customer_mia_chatbot.reload.human_help }) }

              it 'sends text outbound message' do
                expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
                  .to receive(:send_message)
                  .with(hash_including(:question, :context))

                expect { subject }.to change(FacebookMessage, :count).by(1)
                expect(customer.reload.customer_mia_chatbot_for('messenger').human_help).to be_falsy
              end
            end

            context 'with single reference and short text' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Short caption',
                    'char_count' => 100,
                    'references' => [{
                      'content_type' => 'image/jpeg',
                      'url' => 'http://example.com/image.jpg'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends single message with caption' do
                expect_chatbot_message
                subject
              end
            end

            context 'with multiple references or long text' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Long text message',
                    'char_count' => 2000,
                    'references' => [
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image1.jpg'
                      },
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image2.jpg'
                      }
                    ]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends text and references separately' do
                expect_chatbot_message
                subject
              end
            end

            context 'with text reference' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Some text',
                    'references' => [{
                      'content_type' => 'text/plain',
                      'url' => 'http://example.com/text.txt'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends text message from reference' do
                expect_any_instance_of(Mia::Chatbot::ResponseHandler::Messenger)
                  .to receive(:send_reference)
                  .with({ 'content_type' => 'text/plain', 'url' => 'http://example.com/text.txt' }, 'Some text')
                  .and_call_original

                subject
              end
            end

            context 'with message length calculation' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Test message',
                    'char_count' => nil,
                    'references' => [{
                      'content_type' => 'image/jpeg',
                      'url' => 'http://example.com/image.jpg'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'calculates message length from text when char_count is nil' do
                expect_any_instance_of(Mia::Chatbot::ResponseHandler::Messenger)
                  .to receive(:send_reference)
                  .with({ 'content_type' => 'image/jpeg', 'url' => 'http://example.com/image.jpg' }, 'Test message')
                  .and_call_original

                subject
              end
            end

            context 'when valid_customer_and_message? returns false' do
              let(:body) { nil }

              before do
                allow_any_instance_of(described_class).to receive(:valid_customer_and_message?).and_return(false)
              end

              it 'does not send message' do
                expect_any_instance_of(MercatelyMiaApi::V2::Chatbot).not_to receive(:send_message)
                described_class.perform_now(customer.id, 'text', nil)
              end
            end

            context 'with text reference in multiple messages' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Long text message',
                    'char_count' => 2000,
                    'references' => [
                      {
                        'content_type' => 'text/plain',
                        'url' => 'http://example.com/text1.txt'
                      },
                      {
                        'content_type' => 'text/plain',
                        'url' => 'http://example.com/text2.txt'
                      }
                    ]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends multiple text messages from references' do
                expect_any_instance_of(Mia::Chatbot::ResponseHandler::Messenger)
                  .to receive(:send_reference)
                  .with({ 'content_type' => 'text/plain', 'url' => 'http://example.com/text1.txt' })
                  .and_call_original

                expect_any_instance_of(Mia::Chatbot::ResponseHandler::Messenger)
                  .to receive(:send_reference)
                  .with({ 'content_type' => 'text/plain', 'url' => 'http://example.com/text2.txt' })
                  .and_call_original

                subject
              end
            end

            context 'with message length edge cases' do
              let(:body) do
                {
                  'response' => [{
                    'text' => nil,
                    'char_count' => nil,
                    'references' => [{
                      'content_type' => 'image/jpeg',
                      'url' => 'http://example.com/image.jpg'
                    }]
                  }],
                  'flag' => 'message'
                }
              end
            end

            context 'with invalid flag' do
              let(:body) { { 'response' => [], 'flag' => 'invalid_flag' } }
              let(:status) { 200 }

              it 'handles invalid flag without error' do
                expect(Rails.logger).to receive(:error)
                  .with("There was an error trying to get Mia responses: Flag 'invalid_flag' not supported")

                subject
              end
            end

            context 'with multiple references and empty text' do
              let(:body) do
                {
                  'response' => [{
                    'text' => '',
                    'char_count' => 0,
                    'references' => [
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image1.jpg'
                      },
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image2.jpg'
                      }
                    ]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends only references when text is empty' do
                subject
              end
            end

            context 'when mia response has nil flag' do
              let(:status) { 200 }
              let(:body) { { 'response' => [{ 'text' => 'AI response' }] } }

              it 'handles nil flag without error' do
                expect(Rails.logger).to receive(:error)
                  .with("There was an error trying to get Mia responses: Flag '' not supported")

                subject
              end
            end
          end
        end
      end
    end

    private

      def expect_chatbot_message
        expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
          .to receive(:send_message)
          .with(hash_including(:question, :context))
      end
  end
end
