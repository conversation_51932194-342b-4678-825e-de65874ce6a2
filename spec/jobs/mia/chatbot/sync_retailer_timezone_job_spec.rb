require 'rails_helper'

RSpec.describe Mia::Chatbot::SyncRetailerTimezoneJob do
  describe '#perform' do
    subject(:job) { described_class.perform_now(retailer_id) }

    let(:retailer) { create(:retailer) }
    let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }

    before { create(:mia_integration, :with_public_key, mia_integration_type:, retailer:) }

    context 'when retailer exists' do
      let(:retailer_id) { retailer.id }

      context 'without raise an error' do
        let(:result) { double(status:, body:, success?: success) }

        before do
          allow_any_instance_of(MercatelyMiaApi::V2::Chatbot).to receive(:update_timezone).and_return(result)
        end

        context 'when result is success' do
          let(:status) { 200 }
          let(:success) { true }
          let(:body) { { 'response' => 'Timezone updated successfully' } }

          context 'with timezone' do
            let(:timezone) { 'America/Bogota' }
            let(:retailer) { create(:retailer, timezone:) }

            it 'calls update_timezone' do
              expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
                .to receive(:update_timezone)
                .with(hash_including(timezone: timezone))
              subject
            end

            it { expect { subject }.not_to have_enqueued_job(described_class) }
          end

          context 'without timezone' do
            it 'calls update_timezone' do
              expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
                .to receive(:update_timezone)
                .with(hash_including(timezone: 'America/Guayaquil'))
              subject
            end

            it { expect { subject }.not_to have_enqueued_job(described_class) }
          end
        end

        context 'when result is not success' do
          let(:status) { 404 }
          let(:success) { false }
          let(:body) { { error: 'Not Found' } }

          context 'when update_timezone fails' do
            it 'calls update_timezone and raises an error' do
              expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
                .to receive(:update_timezone)
                .with(hash_including(:timezone))
                .and_return(result)

              subject
            end

            it { expect { subject }.to have_enqueued_job(described_class) }
          end
        end
      end

      context 'with raise an error and last attempt' do
        subject(:job) { described_class.perform_now(retailer_id, 3) }

        context 'when update_timezone raises an error' do
          before do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot).to receive(:update_timezone).and_raise(StandardError)
          end

          it 'calls update_timezone and raises an error' do
            expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:update_timezone)
              .with(hash_including(:timezone))
              .and_raise
            subject
          end

          it { expect { subject }.not_to have_enqueued_job(described_class) }
        end
      end
    end

    context 'when retailer does not exist' do
      let(:retailer_id) { 0 }

      it 'does not call update_timezone or update_feature_flag' do
        expect_any_instance_of(MercatelyMiaApi::V2::Chatbot).not_to receive(:update_timezone)
        subject
      end
    end
  end
end
