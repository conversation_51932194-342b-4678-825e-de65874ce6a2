require 'rails_helper'

RSpec.describe Mia::Chatbot::WhatsappJob do
  describe '#perform' do
    let(:retailer) { create(:retailer, :gupshup_integrated, welcome_message_sent: true) }
    let(:customer) { create(:customer, allow_mia_chatbot:, retailer:, human_help:, buy_intention:) }
    let(:allow_mia_chatbot) { true }
    let(:human_help) { false }
    let(:buy_intention) { false }
    let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
    let!(:customer_mia_chatbot) do
      create(:customer_mia_chatbot, platform: 'whatsapp', active: true, customer:, human_help:, buy_intention:)
    end
    let(:sources) do
      {
        guides: [
          {
            llm_query: 'Opciones de entrega',
            type: 'text',
            id: 'g123123a',
            title: 'Guia de opciones de entrega',
            result: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod'
          }
        ],
        products: []
      }
    end

    before do
      allow(ActiveRecord::Base.connection).to receive(:reconnect!)
      create(:mia_integration, :with_public_key, mia_integration_type:, retailer:)
    end

    context 'when message is processed' do
      let(:success) { true }

      before do
        allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
          .to receive(:send_message)
          .and_return(double(status:, body:, success?: success))

        allow_any_instance_of(Redis).to receive(:get).with("whatsapp_messages_#{customer.id}").and_return('hola')
        allow_any_instance_of(Redis).to receive(:del).with("whatsapp_messages_#{customer.id}")
      end

      context 'when inbound_type is text' do
        subject(:job) { described_class.perform_now(customer.id, 'text', nil) }

        context 'with referenced message' do
          let(:status) { 200 }
          let(:body) { { 'response' => [{ 'text' => 'AI response with reference' }], 'flag' => 'message' } }
          let(:referenced_message_id) { 'ref123' }
          let(:referenced_message) do
            {
              type: 'text',
              content: 'This is a referenced message'
            }
          end
          let(:referenced_msg) do
            instance_double(GupshupWhatsappMessage,
                            whatsapp_message_id: referenced_message_id,
                            message_payload: { 'text' => 'This is a referenced message' },
                            type: 'text')
          end

          before do
            allow(GupshupWhatsappMessage).to receive(:find_by)
              .with(whatsapp_message_id: referenced_message_id)
              .and_return(referenced_msg)
            allow_any_instance_of(described_class).to receive_messages(
              referenced_message: referenced_msg,
              type: 'text',
              referenced_message_payload: referenced_message
            )
          end

          it 'includes the referenced message in the request' do
            expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .with(hash_including(
                      question: hash_including(
                        referenced_message: referenced_message
                      )
                    ))

            described_class.perform_now(customer.id, 'text', {}, referenced_message_id)
          end
        end

        context 'when mia response status is 200' do
          let(:status) { 200 }

          context 'with message flag' do
            context 'with text only' do
              let(:body) { { 'response' => [{ 'text' => 'AI response' }], 'flag' => 'message' } }
              let(:human_help) { true }

              context 'with customer_context_message' do
                let(:value) do
                  [
                    { type: 'text', content: { text: 'esta es una prueba', source: 'outbound' } },
                    { type: 'text', content: { text: 'hola', source: 'outbound' } }
                  ]
                end
                let!(:customer_context_message) do
                  create(:customer_context_message, customer_id: customer.id, platform: 'whatsapp', value:)
                end

                it { expect { subject }.to change { customer_context_message.reload.value }.to([]) }
              end

              context 'with sources' do
                let(:body) do
                  {
                    'response' => [
                      { 'text' => 'AI response' },
                      { 'text' => 'AI response 2' }
                    ],
                    'flag' => 'message',
                    'sources' => sources
                  }
                end

                let(:msg_response) do
                  { code: 200, body: {}, message: instance_double(GupshupWhatsappMessage, id: 1) }
                end

                before do
                  allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message).and_return(msg_response)
                end

                it { expect { subject }.to change(MessageMiaResponseSourcesData, :count) }
              end

              it { expect { subject }.to(change { customer_mia_chatbot.reload.human_help }) }

              it 'sends text outbound message' do
                expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
                  .to receive(:send_message)
                  .with(hash_including(:question, :context))

                expect_any_instance_of(Whatsapp::Outbound::Msg)
                  .to receive(:send_message)
                  .with(type: 'text', params: { type: 'text', message: 'AI response' })

                subject
                # expect(customer.reload.customer_mia_chatbot_for('whatsapp').human_help).to be_falsy
              end
            end

            context 'with single reference and short text' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Short caption',
                    'char_count' => 100,
                    'references' => [{
                      'content_type' => 'image/jpeg',
                      'url' => 'http://example.com/image.jpg'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends single message with caption' do
                expect_chatbot_message
                expect_single_file_message_with_caption
                subject
              end

              context 'with sources' do
                let(:body) do
                  {
                    'response' => [{
                      'text' => 'Short caption',
                      'char_count' => 100,
                      'references' => [{
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image.jpg'
                      }]
                    }],
                    'flag' => 'message',
                    'sources' => sources
                  }
                end

                let(:msg_response) do
                  { code: 200, body: {}, message: instance_double(GupshupWhatsappMessage, id: 1) }
                end

                before do
                  allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message).and_return(msg_response)
                end

                it { expect { subject }.to change(MessageMiaResponseSourcesData, :count) }
              end
            end

            context 'with multiple references or long text' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Long text message',
                    'char_count' => 2000,
                    'references' => [
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image1.jpg'
                      },
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image2.jpg'
                      }
                    ]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends text and references separately' do
                expect_chatbot_message
                expect_text_and_multiple_references
                subject
              end

              context 'with sources' do
                let(:body) do
                  {
                    'response' => [{
                      'text' => 'Long text message',
                      'char_count' => 2000,
                      'references' => [
                        {
                          'content_type' => 'image/jpeg',
                          'url' => 'http://example.com/image1.jpg'
                        },
                        {
                          'content_type' => 'image/jpeg',
                          'url' => 'http://example.com/image2.jpg'
                        }
                      ]
                    }],
                    'flag' => 'message',
                    'sources' => sources
                  }
                end

                let(:msg_response) do
                  { code: 200, body: {}, message: instance_double(GupshupWhatsappMessage, id: 1) }
                end

                before do
                  allow_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message).and_return(msg_response)
                end

                it { expect { subject }.to change(MessageMiaResponseSourcesData, :count) }
              end
            end

            context 'with text only and no references' do
              let(:body) { { 'response' => [{ 'text' => 'Simple text' }], 'flag' => 'message' } }

              it 'sends text message' do
                expect_any_instance_of(Whatsapp::Outbound::Msg)
                  .to receive(:send_message)
                  .with(type: 'text', params: { type: 'text', message: 'Simple text' })

                subject
              end
            end

            context 'with text reference' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Some text',
                    'references' => [{
                      'content_type' => 'text/plain',
                      'url' => 'http://example.com/text.txt'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends text message from reference' do
                expect_any_instance_of(Whatsapp::Outbound::Msg)
                  .to receive(:send_message)
                  .with(type: 'text', params: { type: 'text', message: 'http://example.com/text.txt' })

                subject
              end
            end

            context 'with message length calculation' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Test message',
                    'char_count' => nil,
                    'references' => [{
                      'content_type' => 'image/jpeg',
                      'url' => 'http://example.com/image.jpg'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'calculates message length from text when char_count is nil' do
                expect_any_instance_of(Whatsapp::Outbound::Msg)
                  .to receive(:send_message)
                  .with(type: 'file', params: hash_including(
                    caption: 'Test message'
                  ))

                subject
              end
            end

            context 'with empty message response' do
              let(:body) { { 'response' => [{}], 'flag' => 'message' } }

              it 'handles empty message without error' do
                expect_any_instance_of(Whatsapp::Outbound::Msg).not_to receive(:send_message)
                subject
              end
            end

            context 'with text reference in multiple messages' do
              let(:body) do
                {
                  'response' => [{
                    'text' => 'Long text message',
                    'char_count' => 2000,
                    'references' => [
                      {
                        'content_type' => 'text/plain',
                        'url' => 'http://example.com/text1.txt'
                      },
                      {
                        'content_type' => 'text/plain',
                        'url' => 'http://example.com/text2.txt'
                      }
                    ]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends multiple text messages from references' do
                expect_any_instance_of(Whatsapp::Outbound::Msg)
                  .to receive(:send_message)
                  .with(type: 'text', params: { type: 'text', message: 'Long text message' })

                expect_any_instance_of(Whatsapp::Outbound::Msg)
                  .to receive(:send_message)
                  .with(type: 'text', params: { type: 'text', message: 'http://example.com/text1.txt' })

                expect_any_instance_of(Whatsapp::Outbound::Msg)
                  .to receive(:send_message)
                  .with(type: 'text', params: { type: 'text', message: 'http://example.com/text2.txt' })

                subject
              end
            end

            context 'with message length edge cases' do
              let(:body) do
                {
                  'response' => [{
                    'text' => nil,
                    'char_count' => nil,
                    'references' => [{
                      'content_type' => 'image/jpeg',
                      'url' => 'http://example.com/image.jpg'
                    }]
                  }],
                  'flag' => 'message'
                }
              end

              it 'handles nil text and char_count' do
                expect_any_instance_of(Whatsapp::Outbound::Msg)
                  .to receive(:send_message)
                  .with(type: 'file', params: {
                    type: 'file',
                    caption: nil,
                    url: 'http://example.com/image.jpg',
                    content_type: 'image/jpeg',
                    file_name: 'image.jpg'
                  })

                subject
              end
            end

            context 'with invalid flag' do
              let(:body) { { 'response' => [], 'flag' => 'invalid_flag' } }
              let(:status) { 200 }

              it 'handles invalid flag without error' do
                expect(Rails.logger).to receive(:error)
                  .with("There was an error trying to get Mia responses: Flag 'invalid_flag' not supported")

                subject
              end
            end

            context 'with multiple references and empty text' do
              let(:body) do
                {
                  'response' => [{
                    'text' => '',
                    'char_count' => 0,
                    'references' => [
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image1.jpg'
                      },
                      {
                        'content_type' => 'image/jpeg',
                        'url' => 'http://example.com/image2.jpg'
                      }
                    ]
                  }],
                  'flag' => 'message'
                }
              end

              it 'sends only references when text is empty' do
                expect_no_text_message
                expect_image_messages
                subject
              end
            end

            context 'when mia response has nil flag' do
              let(:status) { 200 }
              let(:body) { { 'response' => [{ 'text' => 'AI response' }] } }

              it 'handles nil flag without error' do
                expect(Rails.logger).to receive(:error)
                  .with("There was an error trying to get Mia responses: Flag '' not supported")

                subject
              end
            end
          end
        end

        context 'when mia response status is 202' do
          let(:status) { 202 }

          context 'when mia response has help flag' do
            let(:help_summary) do
              'El usuario ha solicitado asistencia humana. Anteriormente, ha mostrado interés en la ' \
                'compra de bicicletas eléctricas, específicamente la Bicicleta Eléctrica tres Puestos. ' \
                'Necesita ser transferido a un asesor humano para obtener más ayuda y posiblemente ' \
                'finalizar el proceso de compra. Asegúrate de proporcionar asistencia personalizada y ' \
                'responder cualquier pregunta adicional que el usuario pueda tener.'
            end

            let(:body) do
              {
                'flag' => 'help',
                'response' => 'Hemos pasado tu inquietud a un agente, en breve te contestaremos',
                'summary' => help_summary
              }
            end

            it { expect { subject }.to change { customer_mia_chatbot.reload.active }.to(false) }
            it { expect { subject }.to change { customer_mia_chatbot.reload.human_help }.to(true) }

            it 'sends the default message and deactivates mia chatbot' do
              expect_chatbot_message
              expect_any_instance_of(Whatsapp::Outbound::Msg).to receive(:send_message)
                .with(type: 'text', params: { type: 'text', message: body['response'] })

              expect_any_instance_of(Whatsapp::Outbound::Msg).to receive(:create_mia_internal_message)
                .with('human_help')

              expect_any_instance_of(Whatsapp::Outbound::Msg).to receive(:create_note)
                .with(params: { message: body['summary'], message_identifier: anything },
                      retailer_user: nil, order_note: false, sent_by_mia: true)

              subject
            end
          end

          context 'when mia response has buy flag' do
            let(:buy_summary) do
              'El usuario ha expresado su intención de compra. Actualmente, hemos proporcionado ' \
                'información sobre varios productos, incluyendo bicicletas eléctricas como la ' \
                'Bicicleta Eléctrica tres Puestos, que tiene un precio de $1,899,000 USD y ' \
                'características como un motor de 350 watts, batería de litio extraíble 48V 10 Ah, ' \
                'y capacidad de recorrer hasta 27 km con una carga. También se han mostrado imágenes ' \
                'de los productos disponibles. El usuario está listo para ser transferido a un asesor ' \
                'humano para continuar con el proceso de compra.'
            end

            let(:body) do
              {
                'flag' => 'buy',
                'response' => 'Hemos pasado tu inquietud a un agente, en breve te contestaremos',
                'summary' => buy_summary
              }
            end

            it { expect { subject }.to change { customer_mia_chatbot.reload.active }.to(false) }
            it { expect { subject }.to change { customer_mia_chatbot.reload.buy_intention }.to(true) }

            it 'creates internal message and updates customer flags' do
              expect_any_instance_of(Whatsapp::Outbound::Msg)
                .to receive(:send_message)
                .with(type: 'text', params: { type: 'text', message: body['response'] })

              expect_any_instance_of(Whatsapp::Outbound::Msg)
                .to receive(:create_mia_internal_message)
                .with('buy')

              expect_any_instance_of(Whatsapp::Outbound::Msg).to receive(:create_note)
                .with(params: { message: body['summary'], message_identifier: anything },
                      retailer_user: nil, order_note: false, sent_by_mia: true)

              subject
            end
          end
        end

        context 'when mia response status is an error' do
          let(:success) { false }
          let(:status) { 500 }
          let(:body) { 'Something went wrong' }

          it 'logs the error message' do
            expect(Rails.logger).to receive(:error)
              .with('There was an error trying to get Mia responses: Something went wrong')

            subject
          end
        end
      end

      context 'when inbound_type is audio' do
        subject(:job) { described_class.perform_now(customer.id, 'audio', nil, media_params) }

        let(:url) { 'https://example.com/audio.aac' }
        let(:media_params) { { url: url } }

        before { allow_any_instance_of(Retailer).to receive(:gupshup_integrated?).and_return(false) }

        context 'when url is not blank' do
          let(:status) { 200 }
          let(:body) { { 'response' => [{ 'text' => 'AI response about audio' }], 'flag' => 'message' } }
          let(:chatbot_response) { double(status:, body:, success?: true) }

          before do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)
          end

          it 'processes the audio message with the direct URL' do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)

            allow_any_instance_of(Whatsapp::Outbound::Msg)
              .to receive(:send_message)
              .and_return(true)

            expect { subject }.not_to raise_error
          end
        end
      end

      context 'when inbound_type is image' do
        subject(:job) { described_class.perform_now(customer.id, 'image', nil, media_params) }

        let(:url) { 'https://example.com/image.jpg' }
        let(:caption) { 'Test image caption' }
        let(:media_params) { { url: url, caption: caption } }

        before do
          allow_any_instance_of(Retailer).to receive(:gupshup_integrated?).and_return(false)
        end

        context 'when url is not blank' do
          let(:status) { 200 }
          let(:body) { { 'response' => [{ 'text' => 'AI response about image' }], 'flag' => 'message' } }
          let(:chatbot_response) { double(status:, body:, success?: true) }

          before do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)
          end

          it 'processes the image message with the URL and caption' do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)

            allow_any_instance_of(Whatsapp::Outbound::Msg)
              .to receive(:send_message)
              .and_return(true)

            expect { subject }.not_to raise_error
          end
        end
      end

      context 'when inbound_type is file' do
        subject(:job) { described_class.perform_now(customer.id, 'file', nil, media_params) }

        let(:url) { 'https://example.com/document.pdf' }
        let(:caption) { 'Test file caption' }
        let(:file_name) { 'document.pdf' }
        let(:media_params) { { url: url, caption: caption, file_name: file_name } }

        before do
          allow_any_instance_of(Retailer).to receive(:gupshup_integrated?).and_return(false)
        end

        context 'when url is not blank' do
          let(:status) { 200 }
          let(:body) { { 'response' => [{ 'text' => 'AI response about file' }], 'flag' => 'message' } }
          let(:chatbot_response) { double(status:, body:, success?: true) }

          before do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)
          end

          it 'processes the file message with the URL, caption and file_name' do
            allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
              .to receive(:send_message)
              .and_return(chatbot_response)

            allow_any_instance_of(Whatsapp::Outbound::Msg)
              .to receive(:send_message)
              .and_return(true)

            expect { subject }.not_to raise_error
          end
        end
      end
    end

    context 'when message is not processed' do
      context 'when customer does not exist' do
        subject(:job) { described_class.perform_now(0, 'text', nil) }

        it 'does not call MercatelyMiaApi::Chatbot and send message' do
          expect_any_instance_of(MercatelyMiaApi::V2::Chatbot).not_to receive(:send_message)
          expect_any_instance_of(Whatsapp::Outbound::Msg).not_to receive(:send_message)
          subject
        end
      end

      context 'when inbound_type is text and inbound_message is blank' do
        subject(:job) { described_class.perform_now(customer.id, 'text', nil) }

        before do
          allow_any_instance_of(Redis).to receive(:get).with("whatsapp_messages_#{customer.id}").and_return(nil)
        end

        it 'does not call MercatelyMiaApi::Chatbot and send message' do
          expect_any_instance_of(MercatelyMiaApi::V2::Chatbot).not_to receive(:send_message)
          expect_any_instance_of(Whatsapp::Outbound::Msg).not_to receive(:send_message)
          subject
        end
      end
    end

    context 'when customer flags need updating' do
      subject(:job) { described_class.perform_now(customer.id, 'text', nil) }

      let(:status) { 200 }
      let(:body) { { 'response' => [{ 'text' => 'AI response' }], 'flag' => 'message' } }
      let(:chatbot_response) { double(status:, body:, success?: true) }

      before do
        allow_any_instance_of(Redis).to receive(:get)
          .with("whatsapp_messages_#{customer.id}")
          .and_return('hola')
        allow_any_instance_of(Redis).to receive(:get)
          .with("whatsapp_referenced_message_#{customer.id}")
          .and_return(nil)
        allow_any_instance_of(Redis).to receive(:del)
          .with("whatsapp_messages_#{customer.id}")
        allow_any_instance_of(Redis).to receive(:del)
          .with("whatsapp_referenced_message_#{customer.id}")

        allow_any_instance_of(MercatelyMiaApi::V2::Chatbot)
          .to receive(:send_message)
          .and_return(chatbot_response)
      end

      context 'when customer has human_help flag' do
        let(:human_help) { true }
        let(:buy_intention) { false }

        it 'updates human_help flag' do
          expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
            .to receive(:send_message)
            .with(hash_including(:question, :context))

          expect_any_instance_of(Whatsapp::Outbound::Msg)
            .to receive(:send_message)
            .with(type: 'text', params: { type: 'text', message: 'AI response' })

          subject
          customer_mia_chatbot.reload
          expect(customer_mia_chatbot.human_help).to be_falsy
          expect(customer_mia_chatbot.buy_intention).to be_falsy
        end
      end

      context 'when customer has buy_intention flag' do
        let(:human_help) { false }
        let(:buy_intention) { true }

        it 'updates buy_intention flag' do
          expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
            .to receive(:send_message)
            .with(hash_including(:question, :context))

          expect_any_instance_of(Whatsapp::Outbound::Msg)
            .to receive(:send_message)
            .with(type: 'text', params: { type: 'text', message: 'AI response' })

          subject
          customer_mia_chatbot.reload
          expect(customer_mia_chatbot.human_help).to be_falsy
          expect(customer_mia_chatbot.buy_intention).to be_falsy
        end
      end

      context 'when customer has both flags' do
        let(:human_help) { true }
        let(:buy_intention) { true }

        it 'updates both flags' do
          expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
            .to receive(:send_message)
            .with(hash_including(:question, :context))

          expect_any_instance_of(Whatsapp::Outbound::Msg)
            .to receive(:send_message)
            .with(type: 'text', params: { type: 'text', message: 'AI response' })

          subject
          customer_mia_chatbot.reload
          expect(customer_mia_chatbot.human_help).to be_falsy
          expect(customer_mia_chatbot.buy_intention).to be_falsy
        end
      end
    end

    describe '#build_text_message' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer) }
      let(:referenced_message_id) { 'ref123' }
      let(:referenced_msg) do
        instance_double(GupshupWhatsappMessage,
                        message_payload: { 'text' => 'Hello' },
                        type: 'text',
                        get_media_caption: nil)
      end

      before do
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@referenced_message_id, referenced_message_id)
        allow(job).to receive_messages(
          referenced_message: referenced_msg,
          message_payload: { 'text' => 'Hello' },
          type: 'text'
        )
      end

      it 'returns the correct payload for text message' do
        result = job.send(:build_text_message)
        expect(result).to eq({ type: 'text', content: 'Hello' })
      end
    end

    describe '#build_media_message' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer) }
      let(:referenced_message_id) { 'ref123' }
      let(:referenced_msg) do
        instance_double(GupshupWhatsappMessage,
                        message_payload: { 'originalUrl' => 'http://example.com/image.jpg' },
                        type: 'image',
                        get_media_caption: nil)
      end

      before do
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@referenced_message_id, referenced_message_id)
        allow(job).to receive_messages(
          referenced_message: referenced_msg,
          message_payload: { 'originalUrl' => 'http://example.com/image.jpg' },
          type: 'image'
        )
      end

      it 'returns the correct payload for image message' do
        result = job.send(:build_media_message)
        expect(result).to eq({
          type: 'image',
          media_url: 'http://example.com/image.jpg',
          caption: nil
        })
      end

      context 'when type is audio' do
        let(:referenced_msg) do
          instance_double(GupshupWhatsappMessage,
                          message_payload: { 'originalUrl' => 'http://example.com/audio.ogg' },
                          type: 'audio',
                          get_media_caption: nil)
        end

        before do
          allow(job).to receive_messages(
            referenced_message: referenced_msg,
            message_payload: { 'originalUrl' => 'http://example.com/audio.ogg' },
            type: 'audio'
          )
        end

        it 'returns payload without caption or file_name for audio' do
          result = job.send(:build_media_message)
          expect(result).to eq({
            type: 'audio',
            media_url: 'http://example.com/audio.ogg'
          })
        end
      end

      context 'when type is file' do
        let(:referenced_msg) do
          instance_double(GupshupWhatsappMessage,
                          message_payload: { 'originalUrl' => 'http://example.com/document.pdf' },
                          type: 'file',
                          get_media_caption: 'Document caption')
        end

        before do
          allow(job).to receive_messages(
            referenced_message: referenced_msg,
            message_payload: { 'originalUrl' => 'http://example.com/document.pdf' },
            type: 'file',
            extract_file_name: 'document.pdf'
          )
        end

        it 'returns payload with caption and file_name for file' do
          result = job.send(:build_media_message)
          expect(result).to eq({
            type: 'file',
            media_url: 'http://example.com/document.pdf',
            caption: 'Document caption',
            file_name: 'document.pdf'
          })
        end
      end
    end

    describe '#extract_media_url' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer) }
      let(:referenced_message_id) { 'ref123' }

      before do
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@referenced_message_id, referenced_message_id)
      end

      context 'when originalUrl is present' do
        before do
          allow(job).to receive(:message_payload).and_return({ 'originalUrl' => 'http://example.com/file.jpg' })
        end

        it 'returns the originalUrl' do
          result = job.send(:extract_media_url)
          expect(result).to eq('http://example.com/file.jpg')
        end
      end

      context 'when payload.url is present' do
        before do
          allow(job).to receive(:message_payload)
            .and_return({ 'payload' => { 'url' => 'http://example.com/nested.jpg' } })
        end

        it 'returns the nested url' do
          result = job.send(:extract_media_url)
          expect(result).to eq('http://example.com/nested.jpg')
        end
      end

      context 'when direct url is present' do
        before do
          allow(job).to receive(:message_payload).and_return({ 'url' => 'http://example.com/direct.jpg' })
        end

        it 'returns the direct url' do
          result = job.send(:extract_media_url)
          expect(result).to eq('http://example.com/direct.jpg')
        end
      end
    end

    describe '#extract_file_name' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer) }
      let(:referenced_message_id) { 'ref123' }

      before do
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@referenced_message_id, referenced_message_id)
      end

      context 'when payload.name is present' do
        before do
          allow(job).to receive(:message_payload).and_return({ 'payload' => { 'name' => 'document.pdf' } })
        end

        it 'returns the nested name' do
          result = job.send(:extract_file_name)
          expect(result).to eq('document.pdf')
        end
      end

      context 'when payload.filename is present' do
        before do
          allow(job).to receive(:message_payload).and_return({ 'payload' => { 'filename' => 'file.docx' } })
        end

        it 'returns the nested filename' do
          result = job.send(:extract_file_name)
          expect(result).to eq('file.docx')
        end
      end

      context 'when direct name is present' do
        before do
          allow(job).to receive(:message_payload).and_return({ 'name' => 'direct.txt' })
        end

        it 'returns the direct name' do
          result = job.send(:extract_file_name)
          expect(result).to eq('direct.txt')
        end
      end

      context 'when direct filename is present' do
        before do
          allow(job).to receive(:message_payload).and_return({ 'filename' => 'direct_file.txt' })
        end

        it 'returns the direct filename' do
          result = job.send(:extract_file_name)
          expect(result).to eq('direct_file.txt')
        end
      end
    end

    describe '#type' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer) }
      let(:referenced_message_id) { 'ref123' }
      let(:referenced_msg) { instance_double(GupshupWhatsappMessage, type: 'image') }

      before do
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@referenced_message_id, referenced_message_id)
        allow(job).to receive(:referenced_message).and_return(referenced_msg)
      end

      it 'returns the type from referenced_message' do
        result = job.send(:type)
        expect(result).to eq('image')
      end

      it 'memoizes the result' do
        first_result = job.send(:type)

        allow(referenced_msg).to receive(:type).and_return('audio')

        second_result = job.send(:type)

        expect(first_result).to eq('image')
        expect(second_result).to eq(first_result)
      end
    end

    describe '#message_payload' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer) }
      let(:referenced_message_id) { 'ref123' }
      let(:referenced_msg) do
        instance_double(GupshupWhatsappMessage,
                        message_payload: {
                          'payload' => { 'text' => 'Hello from payload' }
                        })
      end

      before do
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@referenced_message_id, referenced_message_id)
        allow(job).to receive(:referenced_message).and_return(referenced_msg)
      end

      it 'returns the message payload' do
        result = job.send(:message_payload)
        expect(result).to eq({ 'text' => 'Hello from payload' })
      end

      it 'memoizes the result' do
        first_result = job.send(:message_payload)

        allow(referenced_msg).to receive(:message_payload).and_return({ 'different' => 'value' })
        second_result = job.send(:message_payload)

        expect(first_result).to eq({ 'text' => 'Hello from payload' })
        expect(second_result).to eq(first_result)
      end

      context 'when payload is not nested' do
        let(:referenced_msg) do
          instance_double(GupshupWhatsappMessage,
                          message_payload: { 'text' => 'Direct text' })
        end

        it 'falls back to the direct message_payload' do
          result = job.send(:message_payload)
          expect(result).to eq({ 'text' => 'Direct text' })
        end
      end
    end

    describe '#referenced_message' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer) }
      let(:referenced_message_id) { 'ref123' }
      let(:gupshup_messages) { double('gupshup_messages') }
      let(:referenced_msg) { instance_double(GupshupWhatsappMessage) }

      before do
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@referenced_message_id, referenced_message_id)
        allow(customer).to receive(:gupshup_whatsapp_messages).and_return(gupshup_messages)
      end

      it 'finds the message by whatsapp_message_id' do
        expect(gupshup_messages).to receive(:find_by)
          .with(whatsapp_message_id: referenced_message_id)
          .and_return(referenced_msg)

        result = job.send(:referenced_message)
        expect(result).to eq(referenced_msg)
      end

      it 'memoizes the result' do
        expect(gupshup_messages).to receive(:find_by)
          .once
          .with(whatsapp_message_id: referenced_message_id)
          .and_return(referenced_msg)

        first_result = job.send(:referenced_message)
        second_result = job.send(:referenced_message)

        expect(first_result).to eq(referenced_msg)
        expect(second_result).to eq(referenced_msg)
        expect(first_result).to equal(second_result)
      end
    end

    describe '#referenced_message_payload' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer) }

      before do
        job.instance_variable_set(:@customer, customer)
      end

      context 'when referenced message ID is provided and type is text' do
        let(:referenced_message_id) { 123 }
        let(:referenced_msg) do
          instance_double(GupshupWhatsappMessage,
                          whatsapp_message_id: referenced_message_id,
                          message_payload: { 'text' => 'Hello' },
                          type: 'text',
                          get_media_caption: nil)
        end
        let(:expected_payload) { { type: 'text', content: 'Hello' } }

        before do
          job.instance_variable_set(:@referenced_message_id, referenced_message_id)
          allow(job).to receive_messages(
            referenced_message: referenced_msg,
            type: 'text',
            build_text_message: expected_payload
          )
        end

        it 'finds the message by ID and builds the text payload' do
          result = job.send(:referenced_message_payload)
          expect(result).to eq(expected_payload)
          expect(job.instance_variable_get(:@referenced_message_payload)).to eq(expected_payload)
        end

        it 'memoizes the result' do
          first_result = job.send(:referenced_message_payload)

          expect(job).not_to receive(:build_text_message)

          second_result = job.send(:referenced_message_payload)

          expect(first_result).to eq(expected_payload)
          expect(second_result).to eq(expected_payload)
          expect(first_result).to equal(second_result)
        end
      end

      context 'when referenced message ID is provided and type is image' do
        let(:referenced_message_id) { 456 }
        let(:referenced_msg) do
          instance_double(GupshupWhatsappMessage,
                          whatsapp_message_id: referenced_message_id,
                          message_payload: { 'originalUrl' => 'http://example.com/image.jpg' },
                          type: 'image',
                          get_media_caption: nil)
        end
        let(:expected_media_payload) { { type: 'image', media_url: 'http://example.com/image.jpg' } }

        before do
          job.instance_variable_set(:@referenced_message_id, referenced_message_id)
          allow(job).to receive_messages(
            referenced_message: referenced_msg,
            type: 'image',
            build_media_message: expected_media_payload
          )
        end

        it 'finds the message by ID and builds the media payload' do
          result = job.send(:referenced_message_payload)
          expect(result).to eq(expected_media_payload)
          expect(job.instance_variable_get(:@referenced_message_payload)).to eq(expected_media_payload)
        end

        it 'memoizes the result' do
          first_result = job.send(:referenced_message_payload)

          expect(job).not_to receive(:build_media_message)

          second_result = job.send(:referenced_message_payload)

          expect(first_result).to eq(expected_media_payload)
          expect(second_result).to eq(expected_media_payload)
          expect(first_result).to equal(second_result)
        end
      end

      context 'when referenced message does not exist' do
        before do
          job.instance_variable_set(:@referenced_message_id, nil)
          allow(job).to receive(:referenced_message).and_return(nil)
        end

        it 'returns nil when referenced_message is nil' do
          allow(job).to receive(:type).and_raise(NoMethodError)

          expect { job.send(:referenced_message_payload) }.to raise_error(NoMethodError)
        end
      end
    end

    describe '#process_inbound_message' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer, retailer:) }
      let(:retailer) { create(:retailer) }
      let(:chatbot_client) { instance_double(MercatelyMiaApi::V2::Chatbot) }
      let(:send_message_params) { { question: { type: 'text', content: 'Hello' } } }
      let(:response) { double('response') }

      before do
        job.instance_variable_set(:@customer, customer)
        allow(job).to receive_messages(chatbot_client: chatbot_client, send_message_params: send_message_params)
      end

      it 'sends the message to the chatbot client and returns the response' do
        expect(chatbot_client).to receive(:send_message).with(send_message_params).and_return(response)

        result = job.send(:process_inbound_message)

        expect(result).to eq(response)
      end
    end

    describe '#send_message_params' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer, retailer:) }
      let(:retailer) { create(:retailer) }

      before do
        job.instance_variable_set(:@customer_id, customer.id)
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@inbound_type, 'text')
        job.instance_variable_set(:@message_value, 'Hello')
      end

      it 'returns cached params if already defined' do
        cached_params = { cached: true }
        job.instance_variable_set(:@send_message_params, cached_params)

        expect(job.send(:send_message_params)).to eq(cached_params)
      end

      it 'builds and returns params for text message' do
        params = job.send(:send_message_params)

        expect(params).to include(
          customer_id: customer.id.to_s,
          phone_number: customer.phone_number
        )
        expect(params[:question]).to include(
          type: 'text',
          content: 'Hello'
        )
      end

      it 'includes referenced message when present' do
        referenced_message = { type: 'text', content: 'Referenced content' }
        allow(job).to receive_messages(
          referenced_message_payload: referenced_message,
          type: 'text'
        )
        job.instance_variable_set(:@referenced_message_id, 'ref123')

        params = job.send(:send_message_params)

        expect(params[:question]).to include(referenced_message: referenced_message)
      end

      it 'uses text builder as fallback for unknown message type' do
        job.instance_variable_set(:@inbound_type, 'unknown_type')
        allow(Rails.logger).to receive(:warn)

        params = job.send(:send_message_params)

        expect(params[:question][:type]).to eq('text')
      end
    end

    describe '#message_value' do
      let(:job) { described_class.new }
      let(:inbound_message) { 'Hello world' }
      let(:url) { 'https://example.com/file.jpg' }
      let(:media_params) { { url: url } }

      context 'when inbound_type is text' do
        before do
          job.instance_variable_set(:@inbound_type, 'text')
          job.instance_variable_set(:@message_value, nil)
          allow(job).to receive(:inbound_message).and_return(inbound_message)
        end

        it 'returns the inbound_message' do
          expect(job.send(:message_value)).to eq(inbound_message)
        end
      end

      context 'when inbound_type is not text' do
        before do
          job.instance_variable_set(:@inbound_type, 'image')
          job.instance_variable_set(:@message_value, nil)
          job.instance_variable_set(:@media_params, media_params)
        end

        it 'returns the url from media_params' do
          expect(job.send(:message_value)).to eq(url)
        end
      end

      context 'when @message_value is already set' do
        let(:cached_value) { 'cached value' }

        before do
          job.instance_variable_set(:@message_value, cached_value)
        end

        it 'returns the cached value' do
          expect(job.send(:message_value)).to eq(cached_value)
        end
      end
    end

    describe 'message builders' do
      let(:job) { described_class.new }
      let(:customer) { create(:customer, retailer:) }
      let(:retailer) { create(:retailer) }
      let(:url) { 'https://example.com/file.jpg' }
      let(:caption) { 'Test caption' }
      let(:file_name) { 'file.jpg' }

      before do
        job.instance_variable_set(:@customer, customer)
        job.instance_variable_set(:@url, url)
        job.instance_variable_set(:@caption, caption)
        job.instance_variable_set(:@file_name, file_name)
        job.instance_variable_set(:@inbound_type, 'text')
        allow(job).to receive(:message_value).and_return(url)
      end

      describe '#message_builder' do
        it 'returns media_builder for image type' do
          job.instance_variable_set(:@inbound_type, 'image')
          expect(job).to receive(:media_builder)
          job.send(:message_builder)
        end

        it 'returns media_builder for file type' do
          job.instance_variable_set(:@inbound_type, 'file')
          expect(job).to receive(:media_builder)
          job.send(:message_builder)
        end

        it 'returns media_builder for audio type' do
          job.instance_variable_set(:@inbound_type, 'audio')
          expect(job).to receive(:media_builder)
          job.send(:message_builder)
        end

        it 'returns text_builder for text type' do
          job.instance_variable_set(:@inbound_type, 'text')
          expect(job).to receive(:text_builder)
          job.send(:message_builder)
        end
      end

      describe '#media_builder' do
        before do
          job.instance_variable_set(:@media_params, { caption: caption, file_name: file_name })
        end

        it 'returns a hash with media properties for image type' do
          job.instance_variable_set(:@inbound_type, 'image')

          result = job.send(:media_builder)

          expect(result).to eq({
            type: 'image',
            media_url: url,
            caption: caption,
            file_name: file_name
          }.compact)
        end

        it 'returns a hash with media properties for file type' do
          job.instance_variable_set(:@inbound_type, 'file')

          result = job.send(:media_builder)

          expect(result).to eq({
            type: 'file',
            media_url: url,
            caption: caption,
            file_name: file_name
          }.compact)
        end

        it 'returns a hash with media properties for audio type' do
          job.instance_variable_set(:@inbound_type, 'audio')

          result = job.send(:media_builder)

          expect(result).to eq({
            type: 'audio',
            media_url: url,
            caption: caption,
            file_name: file_name
          }.compact)
        end
      end

      describe '#text_builder' do
        it 'returns a hash with text type and content' do
          allow(job).to receive(:message_value).and_return('Hello world')

          result = job.send(:text_builder)

          expect(result).to eq({
            type: 'text',
            content: 'Hello world'
          })
        end
      end
    end

    private

      def expect_no_text_message
        expect_any_instance_of(Whatsapp::Outbound::Msg).not_to receive(:send_message).with(
          type: 'text',
          params: hash_including(type: 'text')
        )
      end

      def expect_image_messages
        expect_any_instance_of(Whatsapp::Outbound::Msg)
          .to receive(:send_message)
          .with(
            type: 'file',
            params: {
              type: 'file',
              caption: '',
              url: 'http://example.com/image1.jpg',
              content_type: 'image/jpeg',
              file_name: 'image1.jpg'
            }
          )

        expect_any_instance_of(Whatsapp::Outbound::Msg)
          .to receive(:send_message)
          .with(
            type: 'file',
            params: {
              type: 'file',
              caption: '',
              url: 'http://example.com/image2.jpg',
              content_type: 'image/jpeg',
              file_name: 'image2.jpg'
            }
          )
      end

      def expect_chatbot_message
        expect_any_instance_of(MercatelyMiaApi::V2::Chatbot)
          .to receive(:send_message)
          .with(hash_including(:question, :context))
      end

      def expect_single_file_message_with_caption
        expect_any_instance_of(Whatsapp::Outbound::Msg)
          .to receive(:send_message)
          .with(
            type: 'file',
            params: {
              type: 'file',
              caption: 'Short caption',
              url: 'http://example.com/image.jpg',
              content_type: 'image/jpeg',
              file_name: 'image.jpg'
            }
          )
      end

      def expect_text_and_multiple_references
        expect_any_instance_of(Whatsapp::Outbound::Msg)
          .to receive(:send_message)
          .with(type: 'text', params: { type: 'text', message: 'Long text message' })

        expect_any_instance_of(Whatsapp::Outbound::Msg)
          .to receive(:send_message)
          .with(
            type: 'file',
            params: {
              type: 'file',
              caption: '',
              url: 'http://example.com/image1.jpg',
              content_type: 'image/jpeg',
              file_name: 'image1.jpg'
            }
          )

        expect_any_instance_of(Whatsapp::Outbound::Msg)
          .to receive(:send_message)
          .with(
            type: 'file',
            params: {
              type: 'file',
              caption: '',
              url: 'http://example.com/image2.jpg',
              content_type: 'image/jpeg',
              file_name: 'image2.jpg'
            }
          )
      end
  end
end
