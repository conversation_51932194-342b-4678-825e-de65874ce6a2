require 'rails_helper'

RSpec.describe Mia::DeleteRemoteMiaIntegrationFileJob do
  describe '#perform' do
    let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
    let(:integration) { create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type) }
    let(:ssh_session) { double('ssh_session') }

    describe 'successfully' do
      before do
        allow(ssh_session).to receive(:exec!)
        allow(Net::SSH).to receive(:start).and_yield(ssh_session)

        described_class.perform_now(integration.mia_integration_type.name, integration.retailer.slug)
      end

      it { expect(ssh_session).to have_received(:exec!) }
    end

    describe 'raises an error' do
      before do
        allow(Net::SSH).to receive(:start).and_raise(Net::SSH::AuthenticationFailed)
        allow(SlackError).to receive(:send_error)

        described_class.perform_now(integration.mia_integration_type.name, integration.retailer.slug)
      end

      it { expect(SlackError).to have_received(:send_error).at_least(:once) }
    end
  end
end
