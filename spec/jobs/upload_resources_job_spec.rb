require 'rails_helper'

RSpec.describe UploadResourcesJob do
  describe '#perform_later' do
    let(:retailer) { create(:retailer) }
    let(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }
    let!(:retailer_user) { create(:retailer_user, :admin, retailer: retailer) }
    let(:customer) { create(:customer, retailer: retailer) }
    let(:agent) { create(:retailer_user, :agent, retailer: retailer) }
    let!(:agent_customer) { create(:agent_customer, customer: customer, retailer_user: agent) }

    let(:facebook_message) do
      create(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer)
    end

    let(:instagram_message) do
      create(:instagram_message, :inbound, facebook_retailer: facebook_retailer, customer: customer)
    end

    context 'when is facebook platform' do
      it 'enques the job' do
        ActiveJob::Base.queue_adapter = :test
        expect { described_class.perform_later(facebook_message.id, 'messenger') }.to have_enqueued_job
      end
    end

    context 'when is instagram platform' do
      it 'enques the job' do
        ActiveJob::Base.queue_adapter = :test
        expect { described_class.perform_later(instagram_message.id, 'instagram') }.to have_enqueued_job
      end
    end

    context 'when facebook platform' do
      let(:facebook_message2) do
        build(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer,
                                           url: 'https://test.com/fb_imagetest.jpg',
                                           file_type: 'image')
      end

      let(:facebook_message3) do
        build(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer,
                                           url: 'https://test.com/fb_imagetest.jpg',
                                           file_type: 'image')
      end

      context 'when upload file is success' do
        let(:s3_instance) { instance_double(Uploaders::S3Aws) }

        before do
          allow(URI).to receive(:open).and_return(true)
          allow(Uploaders::S3Aws).to receive(:new).with(anything).and_return(s3_instance)
          allow(s3_instance).to receive(:upload_file).and_return(
            {
              'type' => 'upload',
              'secure_url' => 'https://dc2p3rqc7bvrd.cloudfront.net/test.jpg',
              'original_filename' => 'test.jpg'
            }
          )
        end

        it 'sets internal_url after create fb message' do
          facebook_message2.save
          described_class.perform_now(facebook_message2.id, 'facebook')
          expect(facebook_message2.reload.internal_url).not_to be_nil
        end
      end

      context 'when upload fails' do
        let(:s3_instance) { instance_double(Uploaders::S3Aws) }

        before do
          allow(URI).to receive(:open).and_return(true)
          allow(SlackError).to receive(:send_error).and_return(true)
          allow(Uploaders::S3Aws).to receive(:new).with(anything).and_return(s3_instance)
          allow(s3_instance).to receive(:upload_file).with(anything, anything)
        end

        it 'retry 3 times max' do
          n = 0
          facebook_message3.save(validate: false)
          allow(s3_instance).to receive(:upload_file).with(anything, custom_filename: anything,
                                                                     bucket: anything, cloudfront: anything) { n += 1 }
          described_class.perform_now(facebook_message3.id, 'messenger')
          expect(n).to eq(3).or eq(6)
        end
      end

      describe '#parse_string_io_files with content_type method' do
        let(:msg) { build(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer, url: 'https://test.com/fb_imagetest.jpg') }
        let(:platform) { 'messenger' }
        let(:open_media) do
          double('OpenMedia', read: 'fake data', content_type: 'image/gif').tap do |obj|
            allow(obj).to receive(:respond_to?).with(:content_type).and_return(true)
          end
        end
        let(:job) { described_class.new }

        before do
          allow(job).to receive(:upload_to_s3)
        end

        it 'uses open_media.content_type when available' do
          expect(job).to receive(:upload_to_s3) do |tempfile, filename, msg_arg, platform_arg|
            expect(tempfile.content_type).to eq('image/gif')
            expect(filename).to eq("fb_media_#{msg.id}_#{msg.facebook_retailer_id}")
            expect(msg_arg).to eq(msg)
            expect(platform_arg).to eq(platform)
          end
          job.send(:parse_string_io_files, "fb_media_#{msg.id}_#{msg.facebook_retailer_id}", open_media, msg, platform)
        end
      end

      describe '#parse_string_io_files with content-type from meta' do
        let(:msg) { build(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer, url: 'https://test.com/fb_imagetest.jpg') }
        let(:platform) { 'messenger' }
        let(:open_media) do
          double('OpenMedia', read: 'fake data').tap do |obj|
            allow(obj).to receive(:respond_to?).with(:content_type).and_return(false)
            allow(obj).to receive(:respond_to?).with(:meta).and_return(true)
            allow(obj).to receive(:meta).and_return({ 'content-type' => 'image/png' })
          end
        end
        let(:job) { described_class.new }

        before do
          allow(job).to receive(:upload_to_s3)
        end

        it 'uses open_media.meta["content-type"] when content_type is not available' do
          expect(job).to receive(:upload_to_s3) do |tempfile, filename, msg_arg, platform_arg|
            expect(tempfile.content_type).to eq('image/png')
            expect(filename).to eq("fb_media_#{msg.id}_#{msg.facebook_retailer_id}")
            expect(msg_arg).to eq(msg)
            expect(platform_arg).to eq(platform)
          end
          job.send(:parse_string_io_files, "fb_media_#{msg.id}_#{msg.facebook_retailer_id}", open_media, msg, platform)
        end
      end

      describe '#parse_string_io_files with fetch_content_type (Net::HTTP)' do
        let(:msg) { build(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer, url: 'https://test.com/fb_imagetest.jpg') }
        let(:platform) { 'messenger' }
        let(:open_media) do
          double('OpenMedia', read: 'fake data').tap do |obj|
            allow(obj).to receive(:respond_to?).with(:content_type).and_return(false)
            allow(obj).to receive(:respond_to?).with(:meta).and_return(false)
          end
        end
        let(:job) { described_class.new }

        before do
          allow(job).to receive(:upload_to_s3)
          fake_http = double('HTTP')
          fake_head_response = double('HTTPResponse')
          allow(fake_http).to receive(:head).and_return(fake_head_response)
          allow(fake_head_response).to receive(:[]).with('Content-Type').and_return('image/webp')
          allow(Net::HTTP).to receive(:start).and_yield(fake_http)
        end

        it 'uses fetch_content_type with Net::HTTP when other content type methods are missing' do
          expect(job).to receive(:upload_to_s3) do |tempfile, filename, msg_arg, platform_arg|
            expect(tempfile.respond_to?(:content_type)).to be true
            expect(tempfile.method(:content_type).call).to eq('image/webp')
            expect(filename).to eq("fb_media_#{msg.id}_#{msg.facebook_retailer_id}")
            expect(msg_arg).to eq(msg)
            expect(platform_arg).to eq(platform)
          end
          job.send(:parse_string_io_files, "fb_media_#{msg.id}_#{msg.facebook_retailer_id}", open_media, msg, platform)
        end
      end
    end

    context 'when instagram platform' do
      let(:ig_message2) do
        build(:instagram_message, :inbound, facebook_retailer: facebook_retailer, customer: customer,
                                            url: 'https://test.com/fb_imagetest.jpg',
                                            file_type: 'image')
      end

      let(:ig_message3) do
        build(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer,
                                           url: 'https://test.com/fb_imagetest.jpg', file_type: 'image',
                                           internal_url: 'https://test.com/fb_imagetest.jpg')
      end

      let(:s3_instance) { instance_double(Uploaders::S3Aws) }

      before do
        allow(URI).to receive(:open).and_return(true)
        allow(Uploaders::S3Aws).to receive(:new).with(anything).and_return(s3_instance)
        allow(s3_instance).to receive(:upload_file).and_return(
          {
            'type' => 'upload',
            'secure_url' => 'https://dc2p3rqc7bvrd.cloudfront.net/test.jpg',
            'original_filename' => 'test.jpg'
          }
        )
      end

      context 'when internal_url is nil' do
        it 'sets internal_url after create ig message' do
          ig_message2.save
          described_class.perform_now(ig_message2.id, 'instagram')
          expect(ig_message2.reload.internal_url).not_to be_nil
        end
      end

      context 'when internal_url is already set' do
        it 'does not set internal_url after create ig message' do
          ig_message3.save
          described_class.perform_now(ig_message3.id, 'messenger')
          expect(s3_instance).not_to have_received(:upload_file)
        end
      end
    end
  end
end
