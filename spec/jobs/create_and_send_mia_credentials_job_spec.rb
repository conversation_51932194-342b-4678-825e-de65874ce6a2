require 'rails_helper'

RSpec.describe CreateAndSendMiaCredentialsJob do
  describe '#perform' do
    context 'when integration exists' do
      let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
      let(:integration) { create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type) }

      let(:ssh_session) { double('ssh_session') }
      let(:scp_session) { double('scp_session') }

      describe 'successfully' do
        before do
          allow(ssh_session).to receive(:scp).and_return(scp_session)
          allow(scp_session).to receive(:upload!)
          allow(Net::SSH).to receive(:start).and_yield(ssh_session)

          described_class.perform_now(integration.id)
        end

        it { expect(scp_session).to have_received(:upload!) }
      end

      describe 'raises an error' do
        before do
          allow(Net::SSH).to receive(:start).and_raise(Net::SSH::AuthenticationFailed)
          allow(SlackError).to receive(:send_error)

          described_class.perform_now(integration.id)
        end

        it { expect(SlackError).to have_received(:send_error).at_least(:once) }
      end
    end

    context 'when integration does not exist' do
      it { expect(described_class.perform_now(0)).to be_nil }
    end
  end
end
