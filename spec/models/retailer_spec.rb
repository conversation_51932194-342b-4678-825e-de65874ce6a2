require 'rails_helper'
require 'bcrypt'

# rubocop:disable Rails/Date
RSpec.describe Retailer, :enable_engine_callback do
  subject(:retailer_qr_integrated) { build(:retailer, :qr_integrated, :with_admin, payment_plan: payment_plan) }

  let(:payment_plan) { build(:payment_plan) }
  let(:retailer) { build(:retailer, payment_plan: payment_plan) }

  describe 'enums' do
    it { is_expected.to define_enum_for(:id_type).with_values(cedula: 0, pasaporte: 1, ruc: 2, rut: 3, otro: 4) }

    it 'defines enums for mia_products_synced' do
      expect(subject).to define_enum_for(:mia_products_synced).with_values(not_synced: 0, pending: 1, complete: 2,
                                                                           sync_failed: 3).with_prefix(true)
    end

    it 'defines enums for hubspot_match' do
      expect(subject)
        .to define_enum_for(:hubspot_match).with_values(phone_or_email: 0, phone: 1, email: 2).with_prefix(true)
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:partner).optional(true) }

    it { is_expected.to have_one(:meli_retailer).dependent(:destroy) }
    it { is_expected.to have_one(:mp_retailer).dependent(:destroy) }
    it { is_expected.to have_one(:stripe_retailer).dependent(:destroy) }
    it { is_expected.to have_one(:retailer_user).dependent(:destroy) }
    it { is_expected.to have_one(:facebook_retailer).dependent(:destroy) }
    it { is_expected.to have_one(:facebook_catalog).dependent(:destroy) }
    it { is_expected.to have_one(:payment_plan).dependent(:destroy) }
    it { is_expected.to have_one(:retailer_bill_detail).dependent(:destroy) }
    it { is_expected.to have_one(:partner_config).dependent(:destroy) }

    it { is_expected.to have_many(:products).dependent(:destroy) }
    it { is_expected.to have_many(:customers).dependent(:destroy) }
    it { is_expected.to have_many(:customer_related_data).through(:customers) }
    it { is_expected.to have_many(:retailer_users).dependent(:destroy) }
    it { is_expected.to have_many(:retailer_addresses).dependent(:destroy) }
    it { is_expected.to have_many(:retailer_schedules).dependent(:destroy) }
    it { is_expected.to have_many(:templates).dependent(:destroy) }
    it { is_expected.to have_many(:gupshup_whatsapp_messages).dependent(:destroy) }
    it { is_expected.to have_many(:automatic_answers).dependent(:destroy) }
    it { is_expected.to have_many(:payment_methods).dependent(:destroy) }
    it { is_expected.to have_many(:paymentez_credit_cards).dependent(:destroy) }
    it { is_expected.to have_many(:paymentez_transactions) }
    it { is_expected.to have_many(:stripe_transactions) }
    it { is_expected.to have_many(:funnels) }
    it { is_expected.to have_many(:deals) }
    it { is_expected.to have_many(:whatsapp_templates).dependent(:destroy) }
    it { is_expected.to have_many(:top_ups).dependent(:destroy) }
    it { is_expected.to have_many(:tags).dependent(:destroy) }
    it { is_expected.to have_many(:sales_channels).dependent(:destroy) }
    it { is_expected.to have_many(:chat_bots).dependent(:destroy) }
    it { is_expected.to have_many(:team_assignments).dependent(:destroy) }
    it { is_expected.to have_many(:chat_bot_customers).through(:customers) }
    it { is_expected.to have_many(:customer_related_fields) }
    it { is_expected.to have_many(:calendar_events).dependent(:destroy) }
    it { is_expected.to have_many(:reminders).dependent(:destroy) }
    it { is_expected.to have_many(:contact_groups).dependent(:destroy) }
    it { is_expected.to have_many(:campaigns).dependent(:destroy) }
    it { is_expected.to have_many(:hubspot_fields) }
    it { is_expected.to have_many(:customer_hubspot_fields) }
    it { is_expected.to have_many(:plan_cancellations) }
    it { is_expected.to have_many(:retailer_average_response_times) }
    it { is_expected.to have_many(:retailer_unfinished_message_blocks) }
    it { is_expected.to have_many(:retailer_whatsapp_conversations) }
    it { is_expected.to have_many(:message_blocks) }
    it { is_expected.to have_many(:retailer_most_used_tags) }
    it { is_expected.to have_many(:retailer_conversations) }
    it { is_expected.to have_many(:retailer_amount_messages) }
    it { is_expected.to have_many(:retailer_business_rules).dependent(:destroy) }
    it { is_expected.to have_many(:retailer_business_rule_data).dependent(:destroy) }
    it { is_expected.to have_many(:business_rules).through(:retailer_business_rules) }
    it { is_expected.to have_many(:retailer_amount_messages_by_hours) }
    it { is_expected.to have_many(:hubspot_owners) }
    it { is_expected.to have_many(:agent_hubspot_owners) }
    it { is_expected.to have_many(:quotes) }
    it { is_expected.to have_many(:customer_fb_posts) }
    it { is_expected.to have_many(:teams).dependent(:destroy) }
    it { is_expected.to have_many(:deal_automations) }
    it { is_expected.to have_many(:retailer_customers).dependent(:destroy) }
    it { is_expected.to have_many(:template_stats).through(:whatsapp_templates) }
    it { is_expected.to have_one(:shopify_shop) }
    it { is_expected.to have_one(:retailer_onboarding).dependent(:destroy) }
    it { is_expected.to have_many(:funnel_exports) }
    it { is_expected.to have_many(:shop_orders) }
    it { is_expected.to have_many(:customer_exports).dependent(:destroy) }
    it { is_expected.to have_many(:retailer_mia_platforms) }
    it { is_expected.to have_many(:mia_platforms).through(:retailer_mia_platforms) }
    it { is_expected.to have_many(:import_contacts_loggers).dependent(:destroy) }
    it { is_expected.to have_many(:widget_configs) }

    it 'has many customer_event_categories' do
      expect(subject)
        .to have_many(:customer_event_categories).class_name('Crm::CustomerEventCategory').dependent(:destroy)
    end

    it { is_expected.to have_many(:automations) }
    it { is_expected.to have_many(:scheduled_automations) }
    it { is_expected.to have_many(:mia_integrations).dependent(:destroy) }
    it { is_expected.to have_many(:funnel_deal_histories).dependent(:destroy) }
    it { is_expected.to have_one(:ctx_service).dependent(:destroy) }
    it { is_expected.to have_many(:integration_message_data) }
    it { is_expected.to have_many(:integration_messages) }
    it { is_expected.to have_many(:users).through(:retailer_users) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:currency) }
    it { is_expected.to validate_uniqueness_of(:slug) }
    it { is_expected.to validate_uniqueness_of(:domain).allow_blank }
    it { is_expected.to validate_length_of(:catalog_slug).is_at_most(25) }
    it { is_expected.to validate_length_of(:description).is_at_most(140) }
    it { is_expected.to validate_inclusion_of(:mia_chatbot_active).in_array([true, false]) }
    it { is_expected.to validate_inclusion_of(:mia_products_sync).in_array([true, false]) }

    %i[facebook_url instagram_url twitter_url whatsapp_url tiktok_url].each do |url_field|
      it { is_expected.to allow_value('https://example.com').for(url_field) }
      it { is_expected.not_to allow_value('invalid_url').for(url_field) }
    end
    context 'when the catalog_slug is not empty' do
      before { subject.catalog_slug = retailer.slug }

      it { is_expected.to validate_uniqueness_of(:catalog_slug).case_insensitive }
    end

    context 'when the catalog_slug is empty' do
      before { subject.catalog_slug = '' }

      it { is_expected.not_to validate_uniqueness_of(:catalog_slug) }
    end

    context 'when catalog_slug has more than 25 characters' do
      before { subject.catalog_slug = 'a' * 26 }

      it { is_expected.not_to be_valid }
    end

    context 'when description has more than 140 characters' do
      before { subject.description = 'a' * 141 }

      it { is_expected.not_to be_valid }
    end

    context 'when Facebook URL format is invalid' do
      before { subject.facebook_url = 'invalid_url' }

      it { is_expected.not_to be_valid }
    end

    context 'when Instagram URL format is invalid' do
      before { subject.instagram_url = 'invalid_url' }

      it { is_expected.not_to be_valid }
    end

    context 'when Twitter URL format is invalid' do
      before { subject.twitter_url = 'invalid_url' }

      it { is_expected.not_to be_valid }
    end

    context 'when WhatsApp URL format is invalid' do
      before { subject.whatsapp_url = 'invalid_url' }

      it { is_expected.not_to be_valid }
    end

    context 'when TikTok URL format is invalid' do
      before { subject.tiktok_url = 'invalid_url' }

      it { is_expected.not_to be_valid }
    end

    context 'when Facebook URL format is valid' do
      before { subject.facebook_url = 'https://www.facebook.com' }

      it { is_expected.to be_valid }
    end

    context 'when Instagram URL format is valid' do
      before { subject.instagram_url = 'https://instagram.com' }

      it { is_expected.to be_valid }
    end

    context 'when Twitter URL format is valid' do
      before { subject.twitter_url = 'https://twitter.com' }

      it { is_expected.to be_valid }
    end

    context 'when WhatsApp URL format is valid' do
      before { subject.whatsapp_url = 'https://web.whatsapp.com' }

      it { is_expected.to be_valid }
    end

    context 'when TikTok URL format is valid' do
      before { subject.tiktok_url = 'https://example.com/video' }

      it { is_expected.to be_valid }
    end

    it 'validates exclusion of reserved words in slug' do
      retailer = build(:retailer, slug: 'www')
      retailer.valid?
      expect(retailer.errors[:slug]).to include('www is reserved.')
    end
  end

  describe 'callbacks' do
    describe 'after_create' do
      describe 'send_to_slack' do
        context 'when env is production' do
          before { allow(Rails.env).to receive(:production?).and_return(true) }

          it 'notifies via slack' do
            expect_any_instance_of(Slack::Notifier).to receive(:ping).once
            subject.save
          end
        end

        context 'when env is not production' do
          it 'does not notify via slack' do
            expect_any_instance_of(Slack::Notifier).not_to receive(:ping)
            subject.save
          end
        end
      end

      it 'creates retailer onboarding' do
        retailer = build(:retailer)
        expect(retailer).to receive(:create_retailer_onboarding)
        retailer.save
      end
    end

    describe 'before_save callbacks' do
      it 'sets ml_domain before save' do
        retailer = build(:retailer, ml_site: 'MLA')
        expect(retailer).to receive(:set_ml_domain)
        retailer.save
      end

      it 'sets mp_domain before save' do
        retailer = build(:retailer, mp_site: 'AR')
        expect(retailer).to receive(:set_mp_domain)
        retailer.save
      end
    end

    describe 'after_commit' do
      describe 'create_roles' do
        context 'when is a new retailer' do
          let(:retailer) { build(:retailer) }

          it { expect { retailer.save }.to change(Role, :count).by(3) }
        end

        context 'when is a existing retailer' do
          let!(:retailer) { create(:retailer) }

          it { expect { retailer.update(name: 'new retailer name') }.not_to change(Role, :count) }
        end
      end

      describe 'update_shop' do
        let(:retailer) { create(:retailer) }

        context 'when the retailer is updated' do
          it 'calls update_shop after update' do
            retailer.update(name: 'new retailer name')
            allow(retailer).to receive(:update_shop?).and_return(true)
          end
        end
      end

      describe '#sync_timezone' do
        let!(:retailer) { create(:retailer, timezone: 'America/Quito') }

        context 'when timezone changes' do
          context 'without mia chatbot integration' do
            it 'enqueues Mia::Chatbot::SyncRetailerTimezoneJob' do
              expect { retailer.update(timezone: 'America/Bogota') }
                .not_to have_enqueued_job(Mia::Chatbot::SyncRetailerTimezoneJob)
            end
          end

          context 'with mia chatbot integration' do
            let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }

            before { create(:mia_integration, :with_public_key, retailer:, mia_integration_type:) }

            it 'enqueues Mia::Chatbot::SyncRetailerTimezoneJob' do
              expect { retailer.update(timezone: 'America/Bogota') }
                .to have_enqueued_job(Mia::Chatbot::SyncRetailerTimezoneJob)
            end
          end
        end
      end
    end

    describe 'before_update' do
      describe 'remove_mia_platforms_associations_if_chatbot_disabled' do
        let!(:retailer) { create(:retailer) }
        let!(:mia_platform_uno) { create(:mia_platform) }
        let!(:mia_platform_dos) { create(:mia_platform) }

        before do
          retailer.retailer_mia_platforms.create(mia_platform: mia_platform_uno)
          retailer.retailer_mia_platforms.create(mia_platform: mia_platform_dos)
        end

        context 'when chatbot is disabled' do
          it 'removes associated mia platforms' do
            retailer.update(mia_chatbot_active: true)

            expect do
              retailer.update(mia_chatbot_active: false)
            end.to change { retailer.retailer_mia_platforms.count }.from(2).to(0)
          end
        end

        context 'when chatbot is enabled' do
          it 'does not remove associated mia platforms' do
            retailer.update(mia_chatbot_active: false)

            expect do
              retailer.update(mia_chatbot_active: true)
            end.not_to(change { retailer.retailer_mia_platforms.count })
          end
        end

        context 'when chatbot status is unchanged' do
          it 'does not remove associated mia platforms' do
            expect do
              retailer.update(name: 'Updated Retailer Name')
            end.not_to(change { retailer.retailer_mia_platforms.count })
          end
        end
      end
    end
  end

  it 'after save a name generates a slug' do
    expect(retailer.slug).to be_nil
    retailer.save
    expect(retailer.slug).not_to be_nil
  end

  context 'when name is empty' do
    let!(:retailer_user) { build(:retailer_user, retailer: retailer) }

    it 'after create generates a slug based on retailer users name' do
      retailer.name = nil
      retailer.validate_name = 'false'
      expect(retailer.name).to be_nil
      expect(retailer.slug).to be_nil
      retailer_user.save
      expect(retailer.slug).not_to be_nil
    end
  end

  it 'after save a name generates a catalog_slug' do
    expect(retailer.catalog_slug).to be_nil
    retailer.save
    expect(retailer.catalog_slug).not_to be_nil
    same_retailer = build(:retailer, name: retailer.name)
    same_retailer.save!
    expect(same_retailer.catalog_slug).to eq "#{retailer.catalog_slug}1"
    same_retailer2 = build(:retailer, name: retailer.name)
    same_retailer2.save!
    expect(same_retailer2.catalog_slug).to eq "#{retailer.catalog_slug}2"
  end

  it 'sets default ws_conversation_cost int_charges allow_voice_notes when created' do
    retailer = described_class.create(name: 'Retailer Name')

    expect(retailer.ws_conversation_cost).to eq(0.0)
    expect(retailer.int_charges).to be_truthy
    expect(retailer.allow_voice_notes).to be_truthy
  end

  it 'default shop_active_send_order when is created' do
    retailer = described_class.create(name: 'Retailer Name')

    expect(retailer.shop_active_send_order).to be_falsey
  end

  it 'default shop_active_store_pickup when is created' do
    retailer = described_class.create(name: 'Retailer Name')

    expect(retailer.shop_active_store_pickup).to be_falsey
  end

  context 'when there are retailers with the same name' do
    let(:retailer_two) { create(:retailer, name: retailer.name) }

    it 'generates a slug with the retailer id' do
      retailer.save
      expect(retailer_two.slug).not_to eq retailer.slug
      expect(retailer_two.slug).to eq "#{retailer_two.name}-#{retailer_two.id}".parameterize
    end

    context 'when the name has leading or trailing spaces' do
      let(:retailer3) { create(:retailer, name: 'Prueba Test') }
      let(:retailer4) { build(:retailer, name: "   #{retailer3.name}     ") }

      it 'generates a slug with the name and retailer id' do
        retailer4.save
        expect(retailer4.slug).not_to eq retailer3.slug
        expect(retailer4.slug).to eq "#{retailer4.name}-#{retailer4.id}".parameterize
      end
    end

    context 'when occurs an exception generating the slug' do
      subject(:retailer) { create(:retailer, name: 'Prueba Test') }

      it 'set the slug anyways' do
        expect(retailer.slug).to eq('prueba-test')
      end
    end
  end

  describe 'ransackable attributes' do
    it 'includes name in ransackable attributes' do
      expect(described_class.ransackable_attributes).to include('name')
    end
  end

  # TODO: move method to MeliRetailer
  describe '#update_meli_access_token' do
    subject(:retailer) { create(:retailer) }

    let!(:meli_retailer) { create(:meli_retailer, retailer: retailer) }
    let!(:access_token) { meli_retailer.access_token }

    context 'when meli_retailer.meli_token_updated_at is more than current hour - 4' do
      it 'does not update the access_token' do
        retailer.update_meli_access_token
        expect(meli_retailer.access_token).to eq access_token
      end
    end

    context 'when meli_retailer.meli_token_updated_at is minor than current hour - 4' do
      let(:ml_auth) { instance_double(MercadoLibre::Auth) }

      before do
        allow(ml_auth).to receive(:refresh_access_token)
          .and_return(meli_retailer.update(access_token: Faker::Blockchain::Bitcoin.address))
        allow(MercadoLibre::Auth).to receive(:new).with(retailer)
          .and_return(ml_auth)
      end

      it 'updates the access_token' do
        meli_retailer.update meli_token_updated_at: Time.now - 6.hours
        retailer.update_meli_access_token
        expect(meli_retailer.access_token).not_to eq access_token
      end
    end
  end

  describe '#unread_messages' do
    let(:customer) { create(:customer, retailer: retailer) }
    let(:order) { create(:order, customer: customer) }

    context 'when there are unread messages' do
      let!(:unread_messages) { create_list(:message, 5, order: order, customer: customer) }
      let!(:readed_messages) { create_list(:message, 3, :readed, order: order, customer: customer) }

      it 'returns true' do
        expect(retailer.unread_messages).to be(true)
      end
    end

    context 'when there are not unread messages' do
      let!(:readed_messages) { create_list(:message, 3, :readed, order: order, customer: customer) }

      it 'returns false' do
        expect(retailer.unread_messages).to be(false)
      end
    end
  end

  describe '#unread_questions' do
    let(:product) { create(:product, retailer: retailer) }
    let(:customer) { create(:customer, retailer: retailer) }

    context 'when there are unread questions' do
      let!(:unread_questions) { create_list(:question, 5, product: product, customer: customer) }
      let!(:readed_questions) { create_list(:question, 3, :readed, product: product, customer: customer) }

      it 'returns true' do
        expect(retailer.unread_questions).to be_truthy
      end
    end

    context 'when there are not unread questions' do
      let!(:readed_questions) { create_list(:question, 3, :readed, product: product, customer: customer) }

      it 'returns false' do
        expect(retailer.unread_questions).to be_falsey
      end
    end
  end

  describe '#incomplete_meli_profile?' do
    context 'when id_number, address, city or state are filled' do
      it 'returns false' do
        expect(retailer.incomplete_meli_profile?).to be false
      end
    end

    context 'when any of id_number, address, city or state is empty' do
      it 'returns true' do
        retailer.id_number = nil
        expect(retailer.incomplete_meli_profile?).to be true
      end
    end
  end

  describe '#generate_api_key' do
    it 'returns unique api_key string of 32 chars and stores the encripted api_key to the db' do
      api_key = retailer.generate_api_key
      expect(api_key.size).to be 32
      expect(described_class.where.not(encrypted_api_key: nil).count).to be 1
    end
  end

  describe '#to_param' do
    it 'returns the retailer slug' do
      retailer.save
      expect(retailer.to_param).to eq(retailer.slug)
    end
  end

  describe '#gupshup_unread_whatsapp_messages' do
    subject(:retailer) { create(:retailer) }

    let(:customer) { create(:customer, retailer: retailer) }
    let(:admin) { create(:retailer_user, :admin, retailer: retailer) }
    let(:supervisor) { create(:retailer_user, :supervisor, retailer: retailer) }
    let(:agent_all) { create(:retailer_user, :agent, retailer: retailer, only_assigned: false) }
    let(:agent_only_assigned) { create(:retailer_user, :agent, retailer: retailer, only_assigned: true) }

    let(:customer_admin) { create(:customer, retailer: retailer) }
    let!(:agent_customer_admin) { create(:agent_customer, customer: customer_admin, retailer_user: admin) }
    let(:customer_supervisor) { create(:customer, retailer: retailer) }
    let!(:agent_customer_supervisor) do
      create(:agent_customer, customer: customer_supervisor, retailer_user: supervisor)
    end

    let(:customer_agent_all) { create(:customer, retailer: retailer) }
    let!(:agent_customer_agent_all) { create(:agent_customer, customer: customer_agent_all, retailer_user: agent_all) }
    let(:customer_only_assigned) { create(:customer, retailer: retailer) }
    let!(:agent_customer_only_assigned) do
      create(:agent_customer, customer: customer_only_assigned, retailer_user: agent_only_assigned)
    end

    let!(:messages_admin) do
      create_list(:gupshup_whatsapp_message, 2, :inbound, retailer: retailer, customer: customer_admin, status: 4)
    end

    let!(:messages_supervisor) do
      create_list(:gupshup_whatsapp_message, 4, :inbound, retailer: retailer, customer: customer_supervisor, status: 4)
    end

    let!(:messages_agent_all) do
      create_list(:gupshup_whatsapp_message, 3, :inbound, retailer: retailer, customer: customer_agent_all, status: 4)
    end

    let!(:message_agent_only_assigned) do
      create(:gupshup_whatsapp_message, :inbound, retailer: retailer, customer: customer_only_assigned, status: 4)
    end

    let!(:messages_not_assigned) do
      create_list(:gupshup_whatsapp_message, 3, :inbound, retailer: retailer, customer: customer, status: 4)
    end

    before do
      allow_any_instance_of(Exponent::Push::Client).to receive(:send_messages).and_return(true)
    end
  end

  describe '#team_agents' do
    subject(:retailer) { create(:retailer) }

    let!(:retailer_user_admin) do
      create(:retailer_user, :with_retailer, :admin, retailer: retailer, invitation_token: nil)
    end

    let!(:retailer_user_agent) do
      create(:retailer_user, :with_retailer, :agent, retailer: retailer, invitation_accepted_at:
        Date.today, invitation_token: nil)
    end

    let!(:retailer_user_supervisor) do
      create(:retailer_user, :with_retailer, :supervisor, retailer: retailer, invitation_accepted_at:
        Date.today, invitation_token: nil)
    end

    let!(:retailer_user_agent_two) do
      create(:retailer_user, :with_retailer, :agent, retailer: retailer, invitation_accepted_at:
        Date.today, removed_from_team: true, invitation_token: nil)
    end

    let!(:retailer_user_admin_two) do
      create(:retailer_user, :with_retailer, :admin, :with_invitation_token, retailer: retailer)
    end

    let!(:retailer_user_supervisor_two) do
      create(:retailer_user, :with_retailer, :supervisor, :with_invitation_token, retailer: retailer)
    end

    it 'returns the active retailer users belonging to the retailer' do
      expect(retailer.team_agents.count).to eq(3)
    end
  end

  describe 'scopes' do
    describe '.active' do
      let(:active_retailer) { create(:retailer) }
      let(:inactive_retailer) { create(:retailer) }

      before { inactive_retailer.payment_plan.update(status: :inactive) }

      it 'returns active retailers' do
        expect(described_class.active).to include(active_retailer)
        expect(described_class.active).not_to include(inactive_retailer)
      end
    end

    describe '.inactive' do
      let(:active_retailer) { create(:retailer) }
      let(:inactive_retailer) { create(:retailer) }

      before do
        inactive_retailer.payment_plan.update(status: :inactive, plan: :enterprise)
        active_retailer.payment_plan.update(plan: :enterprise)
      end

      it 'returns active retailers' do
        expect(described_class.inactive).not_to include(active_retailer)
        expect(described_class.inactive).to include(inactive_retailer)
      end
    end

    describe '.paying_retailers' do
      it 'returns paying retailers' do
        paying_retailer = create(:retailer, payment_plan: create(:payment_plan, plan: :basic))
        free_retailer = create(:retailer, payment_plan: create(:payment_plan, plan: :free))
        expect(described_class.paying_retailers).to include(paying_retailer)
        expect(described_class.paying_retailers).not_to include(free_retailer)
      end
    end
  end

  describe '#admins' do
    subject(:retailer) { create(:retailer) }

    let!(:retailer_user_admin1) { create(:retailer_user, :with_retailer, :admin, retailer: retailer) }
    let!(:retailer_user_admin2) { create(:retailer_user, :with_retailer, :admin, retailer: retailer) }

    it 'returns all admins for the retailer' do
      expect(retailer.admins.order(id: :desc).to_a).to eq(
        RetailerUser.where(retailer_admin: true).order(id: :desc).to_a
      )
    end
  end

  describe '#positive_balance?' do
    context 'when connect_bsp_mercately is true' do
      it 'returns true' do
        retailer = build(:retailer, connect_bsp_mercately: true)
        expect(retailer.positive_balance?).to be true
      end
    end

    context 'when connect_bsp_mercately is false' do
      before { allow_any_instance_of(described_class).to receive(:connect_bsp_mercately).and_return(false) }

      it 'returns true if ws_balance is greater than -10.0' do
        retailer = build(:retailer, ws_balance: -5.0)
        expect(retailer.positive_balance?).to be true
      end

      it 'returns false if ws_balance is less than or equal to -10.0' do
        retailer = build(:retailer, ws_balance: -10.0)
        expect(retailer.positive_balance?).to be false
      end
    end
  end

  describe '#mia_insufficient_balance?' do
    context 'when retailer is gupshup_integrated' do
      subject(:retailer) { create(:retailer, :gupshup_integrated) }

      it 'returns true if balance is minor than -5' do
        retailer.ws_balance = -5.1
        retailer.save
        expect(retailer.mia_insufficient_balance?).to be_truthy
      end

      it 'returns false if balance is greater than -5' do
        retailer.ws_balance = 1.0
        retailer.save
        expect(retailer.mia_insufficient_balance?).to be_falsey
      end
    end
  end

  describe '#whatsapp_welcome_message' do
    subject(:retailer) { create(:retailer, :gupshup_integrated) }

    context 'when the retailer has a whatsapp active welcome message configured' do
      let!(:welcome_message) { create(:automatic_answer, :welcome, whatsapp: true, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: welcome_message) }

      it 'returns it' do
        expect(retailer.whatsapp_welcome_message).to be_an_instance_of(AutomaticAnswer)
      end
    end

    context 'when the retailer does not have a whatsapp active welcome message configured' do
      it 'returns nil' do
        expect(retailer.whatsapp_welcome_message).to be_nil
      end
    end
  end

  describe '#whatsapp_inactive_message' do
    subject(:retailer) { create(:retailer, :gupshup_integrated) }

    context 'when the retailer has a whatsapp inactive message configured' do
      let!(:inactive_message) { create(:automatic_answer, :inactive, whatsapp: true, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: inactive_message) }

      it 'returns it' do
        expect(retailer.whatsapp_inactive_message).to be_an_instance_of(AutomaticAnswer)
      end
    end

    context 'when the retailer does not have a whatsapp inactive message configured' do
      it 'returns nil' do
        expect(retailer.whatsapp_inactive_message).to be_nil
      end
    end
  end

  describe '#messenger_welcome_message' do
    subject(:retailer) { create(:retailer) }

    let!(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }

    context 'when the retailer has a messenger active welcome message configured' do
      let!(:welcome_message) { create(:automatic_answer, :welcome, :messenger, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: welcome_message) }

      it 'returns it' do
        expect(retailer.messenger_welcome_message).to be_an_instance_of(AutomaticAnswer)
      end
    end

    context 'when the retailer does not have a messenger active welcome message configured' do
      it 'returns nil' do
        expect(retailer.messenger_welcome_message).to be_nil
      end
    end
  end

  describe '#messenger_inactive_message' do
    subject(:retailer) { create(:retailer) }

    let!(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }

    context 'when the retailer has a messenger inactive message configured' do
      let!(:inactive_message) { create(:automatic_answer, :inactive, :messenger, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: inactive_message) }

      it 'returns it' do
        expect(retailer.messenger_inactive_message).to be_an_instance_of(AutomaticAnswer)
      end
    end

    context 'when the retailer does not have a messenger inactive message configured' do
      it 'returns nil' do
        expect(retailer.messenger_inactive_message).to be_nil
      end
    end
  end

  describe '#instagram_welcome_message' do
    context 'when the retailer has an instagram active welcome message configured' do
      let!(:facebook_retailer) do
        create(:facebook_retailer, :with_instagram, retailer: retailer, instagram_uid: 'uid', access_token: 'token')
      end
      let!(:welcome_message) { create(:automatic_answer, :welcome, :instagram, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: welcome_message) }

      it 'returns it' do
        expect(retailer.reload.instagram_integrated?).to be true
        expect(retailer.instagram_welcome_message).to be_an_instance_of(AutomaticAnswer)
      end
    end
  end

  describe '#instagram_inactive_message' do
    context 'when the retailer has an instagram inactive message configured' do
      let!(:facebook_retailer) do
        create(:facebook_retailer, :with_instagram, retailer: retailer, instagram_uid: 'uid', access_token: 'token')
      end
      let!(:inactive_message) { create(:automatic_answer, :inactive, :instagram, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: inactive_message) }

      it 'returns it' do
        expect(retailer.instagram_inactive_message).to be_an_instance_of(AutomaticAnswer)
      end
    end
  end

  describe '#retailer_user_connected_to_fb' do
    let(:retailer) { create(:retailer) }
    let!(:retailer_user) { create(:retailer_user, retailer: retailer) }

    context 'when it is connected to facebook' do
      let!(:retailer_user_fb) { create(:retailer_user, :from_fb, retailer: retailer) }

      it 'returns the retailer user with the credentials' do
        expect(retailer.retailer_user_connected_to_fb).to eq(retailer_user_fb)
      end
    end

    context 'when it is not connected to facebook' do
      it 'returns nil' do
        expect(retailer.retailer_user_connected_to_fb).to be_nil
      end
    end
  end

  describe '#attributes_to_nil' do
    context 'when the gupshup src name is given' do
      let(:retailer) { create(:retailer, gupshup_src_name: 'MercatelyTest') }

      it 'saves the attribute as it comes' do
        expect(retailer.gupshup_src_name).to eq('MercatelyTest')
      end
    end

    context 'when the gupshup src name is blank' do
      let(:retailer) { create(:retailer, gupshup_src_name: '') }

      it 'saves the attribute as nil' do
        expect(retailer.gupshup_src_name).to be_nil
      end
    end

    context 'when the timezone is given' do
      let(:retailer) { create(:retailer, timezone: 'America/Guayaquil') }

      it 'saves the attribute as it comes' do
        expect(retailer.timezone).to eq('America/Guayaquil')
      end
    end

    context 'when the timezone is blank' do
      let(:retailer) { create(:retailer, timezone: '') }

      it 'saves the attribute as nil' do
        expect(retailer.timezone).to be_nil
      end
    end
  end

  describe '#strip_name' do
    context 'when the name is not present' do
      subject(:retailer) { build(:retailer, name: nil) }

      it 'returns the same name' do
        expect(retailer.send(:strip_name)).to be_nil
      end
    end

    context 'when the name is present' do
      subject(:retailer) { build(:retailer, name: '    Prueba     ') }

      it 'returns name without leading and trailing spaces' do
        expect(retailer.send(:strip_name)).to eq('Prueba')
      end
    end
  end

  describe '#whatsapp_integrated?' do
    context 'when the retailer is gupshup integrated' do
      let(:retailer) { create(:retailer, :gupshup_integrated) }

      it 'returns true' do
        expect(retailer.whatsapp_integrated?).to be true
      end
    end
  end

  describe '#stripe_integrated?' do
    context 'when the retailer is stripe integrated' do
      let(:retailer) { create(:retailer) }
      let!(:stripe_retailer) { create(:stripe_retailer, retailer: retailer, confirmed: true) }

      it 'returns true' do
        expect(retailer.stripe_integrated?).to be true
      end
    end
  end

  describe '#main_paymentez_credit_card' do
    it 'returns the main credit card' do
      create_list(:paymentez_credit_card, 2, retailer: retailer)

      main_card = retailer.paymentez_credit_cards.first
      expect(retailer.main_paymentez_credit_card).to eq(main_card)
    end

    it 'returns the nil if no main credit card is set' do
      create_list(:paymentez_credit_card, 2, retailer: retailer)
      retailer.paymentez_credit_cards.first.update(main: false)

      expect(retailer.main_paymentez_credit_card).to be_nil
    end
  end

  describe '#available_customer_tags' do
    context 'when customer_id is an argument' do
      let(:customer) { create(:customer, retailer: retailer) }
      let(:tag) { create(:tag, retailer: retailer, tag: 'Prueba 1') }
      let!(:tag2) { create(:tag, retailer: retailer, tag: 'Prueba 2') }
      let!(:customer_tag) { create(:customer_tag, tag: tag, customer: customer) }

      it 'returns the retailer tags except the ones already assigned to the customer' do
        expect(retailer.available_customer_tags(customer.id).size).to eq(1)
        expect(retailer.available_customer_tags(customer.id).first.tag).to eq('Prueba 2')
      end
    end

    context 'when customer_id is not an argument' do
      let!(:tag) { create(:tag, retailer: retailer) }
      let!(:tag2) { create(:tag, retailer: retailer) }

      it 'returns all the retailer tags' do
        expect(retailer.available_customer_tags.size).to eq(2)
      end
    end
  end

  describe '#add_sales_channel' do
    context 'when a retailer is created/updated' do
      context 'when it is not whatsapp integrated' do
        let(:retailer_not_integrated) { build(:retailer) }

        it 'does not create a whatsapp sales channel' do
          retailer_not_integrated.save

          retailer_not_integrated.reload
          expect(retailer_not_integrated.sales_channels.size).to eq(0)
        end
      end

      context 'when it is whatsapp integrated' do
        context 'when does not exist the sales channel yet' do
          let(:retailer_integrated) { build(:retailer, :gupshup_integrated) }

          it 'creates a whatsapp sales channel' do
            retailer_integrated.save

            retailer_integrated.reload
            expect(retailer_integrated.sales_channels.size).to eq(0)
          end
        end

        context 'when the sales channel already exists' do
          let(:retailer_integrated) { create(:retailer, :gupshup_integrated) }

          it 'does not create a whatsapp sales channel' do
            expect(retailer_integrated.sales_channels.size).to eq(0)

            retailer_integrated.update(name: 'Test')

            retailer_integrated.reload
            expect(retailer_integrated.sales_channels.size).to eq(0)
          end
        end
      end
    end
  end

  describe '#refund_message_cost' do
    let(:retailer) { create(:retailer, ws_balance: 15.0) }

    it 'adds the amount passed to the ws balance' do
      expect(retailer.ws_balance).to eq(15.0)
      retailer.refund_message_cost(0.5)
      expect(retailer.reload.ws_balance).to eq(15.5)
    end
  end

  describe '#self.platform_filter' do
    context 'when the values are TRUE to:' do
      let(:retailer) { create(:retailer) }
      let!(:meli_retailer) { create(:meli_retailer, retailer: retailer) }
      let!(:mp_retailer) { create(:mp_retailer, retailer: retailer) }
      let!(:stripe_retailer) { create(:stripe_retailer, retailer: retailer, confirmed: true) }
      let!(:gupshup_retailer) { create(:retailer, :gupshup_integrated) }
      let!(:qr_retailer) { create(:retailer, :qr_integrated) }
      let(:fb_retailer) { create(:retailer) }
      let!(:facebook_retailer) do
        create(:facebook_retailer, :with_instagram, :with_messenger,
               retailer: fb_retailer, instagram_comments: true, facebook_comments: true)
      end

      before do
        update_retailers = [retailer, gupshup_retailer, qr_retailer, fb_retailer]
        PaymentPlan.where(retailer: update_retailers).update_all(plan: :enterprise)
      end

      it 'whatsapp_integrated' do
        expect(described_class.platform_filter('whatsapp_integrated', true).size).to eq(2)
      end

      it 'facebook_integrated' do
        expect(described_class.platform_filter('facebook_integrated', true).size).to eq(1)
      end

      it 'instagram_integrated' do
        expect(described_class.platform_filter('instagram_integrated', true).size).to eq(1)
      end

      it 'fb_comments_integrated' do
        expect(described_class.platform_filter('fb_comments_integrated', true).size).to eq(1)
      end

      it 'ig_comments_integrated' do
        expect(described_class.platform_filter('ig_comments_integrated', true).size).to eq(1)
      end

      it 'ml_integrated' do
        expect(described_class.platform_filter('ml_integrated', true).size).to eq(1)
      end

      it 'stripe_integrated' do
        expect(described_class.platform_filter('stripe_integrated', true).size).to eq(1)
      end

      it 'mercado_pago_integrated' do
        retailers = described_class.platform_filter('mercado_pago_integrated', true)
        expect(retailers.size).to eq(1)
        expect(retailers.first.id).to eq(retailer.id)
      end
    end

    context 'when the values are FALSE to:' do
      let!(:retailers) { create_list(:retailer, 5) }
      let!(:gs_retailers) { create_list(:retailer, 2, :gupshup_integrated) }
      let!(:qr_retailers) { create_list(:retailer, 3, :qr_integrated) }

      before do
        PaymentPlan.where(retailer: retailers).update_all(plan: :enterprise)
        PaymentPlan.where(retailer: gs_retailers).update_all(plan: :enterprise)
        PaymentPlan.where(retailer: qr_retailers).update_all(plan: :enterprise)
      end

      it 'whatsapp_integrated' do
        expect(described_class.platform_filter('whatsapp_integrated', false).size).to eq(5)
      end

      it 'facebook_integrated' do
        expect(described_class.platform_filter('facebook_integrated', false).size).to eq(10)
      end

      it 'instagram_integrated' do
        expect(described_class.platform_filter('instagram_integrated', false).size).to eq(10)
      end

      it 'fb_comments_integrated' do
        expect(described_class.platform_filter('fb_comments_integrated', false).size).to eq(10)
      end

      it 'ig_comments_integrated' do
        expect(described_class.platform_filter('ig_comments_integrated', false).size).to eq(10)
      end

      it 'ml_integrated' do
        expect(described_class.platform_filter('ml_integrated', false).size).to eq(10)
      end

      it 'stripe_integrated' do
        expect(described_class.platform_filter('stripe_integrated', false).size).to eq(10)
      end

      it 'mercado_pago_integrated' do
        retailers = described_class.platform_filter('mercado_pago_integrated', false)
        expect(retailers.size).to eq(10)
      end
    end
  end

  describe 'paying_retailers scope' do
    let!(:paying_retailers) { create_list(:retailer, 5) }

    before do
      PaymentPlan.where(retailer: paying_retailers).update_all(plan: :enterprise)
    end

    it 'return paying retailers' do
      paying_retailers = described_class.paying_retailers
      expect(paying_retailers.size).to eq(5)
      expect(paying_retailers.first.payment_plan.plan).not_to eq('free')
    end
  end

  describe '#disconnect_qr_code' do
    before do
      ActionMailer::Base.deliveries = []
    end

    context 'when qr_phone_number is null' do
      it 'returns false' do
        expect(retailer.disconnect_qr_code).to be_falsey
      end
    end

    context 'when qr_phone_number is not null' do
      it 'update qr_phone_number column and queue whatsapp disconnect notice' do
        retailer_qr_integrated.save
        retailer_qr_integrated.disconnect_qr_code

        expect(retailer_qr_integrated.reload.qr_phone_number).to be_nil
        expect(ActionMailer::Base.deliveries.size).to eq(2)
        expect(ActionMailer::Base.deliveries.last.subject)
          .to match('Perdiste tu conexión a WhatsApp. Vuelve a conectarte.')
      end
    end
  end

  describe '#send_follow_up' do
    let(:retailer) do
      create(:retailer,
             retailer_number: '+593999999998',
             name: 'Test Retailer',
             payment_plan: create(:payment_plan),
             welcome_message_sent: false)
    end

    let!(:retailer_user) do
      create(:retailer_user,
             :admin,
             retailer: retailer,
             first_name: 'John',
             last_name: 'Doe',
             email: '<EMAIL>')
    end

    let!(:mercately_retailer) do
      create(:retailer,
             id: ENV.fetch('MERCATELY_RETAILER_ID', nil),
             gupshup_phone_number: '+593999999999',
             gupshup_src_name: 'test',
             payment_plan: create(:payment_plan))
    end

    let!(:template) do
      create(:whatsapp_template,
             :with_gs_id,
             id: ENV.fetch('WHATSAPP_TEMPLATE', nil),
             retailer: mercately_retailer,
             text: 'Test template {{1}} {{2}}')
    end

    let!(:tag) do
      create(:tag,
             id: ENV.fetch('REGISTER_TAG_ID', nil),
             retailer: mercately_retailer)
    end

    let(:message_service) { instance_double(Whatsapp::Outbound::Msg) }

    before do
      # Limpiar datos existentes
      Customer.delete_all
      CustomerTag.delete_all
      GupshupWhatsappMessage.delete_all

      # Configurar mocks
      allow(Whatsapp::Outbound::Msg).to receive(:new).and_return(message_service)
      allow(message_service).to receive(:send_message).and_return(
        { status: 'submitted', messageId: 'test-id' }.to_json
      )
      allow_any_instance_of(Whatsapp::Outbound::Users).to receive(:opt_in).and_return({ code: '202' })
      allow(WhatsappTemplate).to receive(:find_by).and_return(template)
    end

    context 'when customer does not exist on mercately prod retailer' do
      it 'creates the customer and sends a follow up template' do
        # Establecer expectativa específica para el mensaje
        expect(message_service).to receive(:send_message).with(hash_including(params: hash_including(
          gupshup_template_id: template.gupshup_template_id, template: 'true', type: 'image',
          file_url: 'https://d1zmac1huz14sq.cloudfront.net/caracteristicas+servicio+3.png',
          url: 'https://d1zmac1huz14sq.cloudfront.net/caracteristicas+servicio+3.png',
          file_name: 'caracteristicas-servicio-3.png'
        )))

        # Asegurarse de que el mensaje se cree al hacer el update
        retailer.update!(name: 'Updated Name')

        # Verificar que el cliente fue creado con los atributos correctos
        customer = Customer.last
        expect(customer.phone).to eq(retailer.retailer_number)
        expect(customer.first_name).to eq(retailer_user.first_name)
      end
    end

    context 'when sending message' do
      before do
        allow(message_service).to receive(:send_message).and_return(
          { status: 'submitted', messageId: 'test-id' }.to_json
        )
      end

      it 'adds the tag' do
        expect do
          retailer.update!(name: 'Updated Name')
        end.to change(CustomerTag, :count).by(1)

        customer = Customer.last
        expect(customer.tags).to include(tag)
      end

      it 'marks welcome message as sent' do
        retailer.update!(name: 'Updated Name')
        expect(retailer.reload.welcome_message_sent).to be true
      end
    end

    context 'when sending follow-up message' do
      it 'sends message with correct parameters' do
        expect(message_service).to receive(:send_message).with(
          hash_including(params: hash_including(
            gupshup_template_id: template.gupshup_template_id, template: 'true', type: 'image',
            file_url: 'https://d1zmac1huz14sq.cloudfront.net/caracteristicas+servicio+3.png',
            url: 'https://d1zmac1huz14sq.cloudfront.net/caracteristicas+servicio+3.png',
            file_name: 'caracteristicas-servicio-3.png'
          ))
        )

        retailer.update!(name: 'Updated Name')
      end
    end
  end

  describe '#prev_catalog_slug' do
    let(:retailer) { build(:retailer, catalog_slug: 'catalog-slug') }

    context 'when prev_catalog_slug is present' do
      it 'returns prev_catalog_slug' do
        retailer.prev_catalog_slug = 'prev-catalog-slug'
        expect(retailer.prev_catalog_slug).to eq('prev-catalog-slug')
      end
    end

    context 'when prev_catalog_slug is nil' do
      it 'returns catalog_slug' do
        retailer.prev_catalog_slug = nil
        expect(retailer.prev_catalog_slug).to eq('catalog-slug')
      end
    end

    context 'when prev_catalog_slug is an empty string' do
      it 'returns catalog_slug' do
        retailer.prev_catalog_slug = ''
        expect(retailer.prev_catalog_slug).to eq('catalog-slug')
      end
    end
  end

  describe '#facebook_unread_messages' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }
    let(:facebook_retailer) { nil }
    let(:unread_messages) { 5 }

    before do
      allow(retailer).to receive(:facebook_retailer).and_return(facebook_retailer)
    end

    context 'when facebook_retailer is nil' do
      it 'returns 0' do
        expect(retailer.facebook_unread_messages(retailer_user)).to be_nil
      end
    end

    context 'when facebook_retailer is not nil' do
      let(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }

      before do
        allow(facebook_retailer).to receive(:facebook_unread_messages).with(retailer_user).and_return(unread_messages)
      end

      it 'returns the number of unread messages for the retailer user' do
        expect(retailer.facebook_unread_messages(retailer_user)).to eq(unread_messages)
      end
    end
  end

  describe '#instagram_unread_messages' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }
    let(:facebook_retailer) { nil }
    let(:unread_messages) { 5 }

    before do
      allow(retailer).to receive(:facebook_retailer).and_return(facebook_retailer)
    end

    context 'when facebook_retailer is nil' do
      it 'returns nil' do
        expect(retailer.instagram_unread_messages(retailer_user)).to be_nil
      end
    end

    context 'when facebook_retailer is not nil' do
      let(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }

      before do
        allow(facebook_retailer).to receive(:instagram_unread_messages).with(retailer_user).and_return(unread_messages)
      end

      it 'returns the number of unread messages for the retailer user' do
        expect(retailer.instagram_unread_messages(retailer_user)).to eq(unread_messages)
      end
    end
  end

  describe '#plan_details' do
    let(:retailer) { create(:retailer) }
    let(:payment_plan) { retailer.payment_plan }
    let(:chargebee) { instance_double(Chargebee::Api) }
    let(:cb_result) { instance_double(ChargeBee::ListResult) }
    let(:subscription) { instance_double(ChargeBee::Subscription) }
    let(:result) { instance_double(ChargeBee::Result, subscription: subscription) }
    let(:subscription_item) do
      double('Item', item_price_id: 'Plan-Business-USD-Monthly', amount: 1000, item_type: 'plan')
    end

    # rubocop:disable RSpec/ReceiveMessages
    context 'when chargebee_subscription_id is present' do
      before do
        allow_any_instance_of(described_class).to receive(:chargebee).and_return(chargebee)
        allow(Chargebee::Api).to receive(:new).and_return(chargebee)
        allow(chargebee).to receive(:subscription).with(anything).and_return(result)
        allow(subscription).to receive(:current_term_start).and_return(Time.now.to_i)
        allow(subscription).to receive(:current_term_end).and_return(Time.now.to_i + 1.month)
        allow(subscription).to receive(:id).and_return('enterprise')
        allow(subscription).to receive(:currency_code).and_return('USD')
        allow(subscription).to receive(:billing_period).and_return(1)
        allow(subscription).to receive(:billing_period_unit).and_return('month')
        allow(subscription).to receive(:next_billing_at).and_return((Time.now + 1.month).to_i)
        allow(subscription).to receive(:status).and_return('active')
        allow(subscription).to receive(:mrr).and_return(100)
        allow(subscription).to receive(:subscription_items).and_return([subscription_item])
        allow([subscription_item]).to receive(:first).and_return(subscription_item)
        allow(subscription_item).to receive(:item_price_id).and_return('Plan-Business-USD-Monthly')
        allow(subscription_item).to receive(:amount).and_return(1000)
      end

      it 'returns the plan details' do
        payment_plan.update(chargebee_subscription_id: 'test')
        expect(retailer.plan_details).to include(:plan_id, :plan_currency_code, :billing_period, :days_to_next_billing,
                                                 :billing_period_unit, :next_billing_at, :current_term_start, :status,
                                                 :current_term_end, :mrr, :addons)
      end

      it 'returns the plan details if next_billing_at is nil' do
        allow(subscription).to receive(:next_billing_at).and_return(nil)
        payment_plan.update(chargebee_subscription_id: 'test')
        expect(retailer.plan_details).to include(:plan_id, :plan_currency_code, :billing_period, :days_to_next_billing,
                                                 :billing_period_unit, :next_billing_at, :current_term_start, :status,
                                                 :current_term_end, :mrr, :addons)
      end
    end
    # rubocop:enable RSpec/ReceiveMessages

    context 'when chargebee_subscription_id is not present' do
      before do
        allow(retailer.payment_plan).to receive(:chargebee_subscription_id).and_return(nil)
      end

      it 'returns an empty hash' do
        expect(retailer.plan_details).to eq({})
      end
    end
  end

  describe '#find_or_create_follow_up_customer' do
    let(:retailer) { create(:retailer, retailer_number: '+5931234567890', welcome_message_sent: true) }
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }
    let(:agent_id) { create(:retailer_user, retailer: retailer).id }

    before do
      ENV['MERCATELY_RETAILER_ID'] = retailer.id.to_s
    end

    context 'when customer exists' do
      let!(:customer) { create(:customer, phone: retailer.retailer_number, retailer_id: retailer.id) }

      it 'updates the customer and returns it' do
        result = retailer.send(:find_or_create_follow_up_customer, retailer_user)
        expect(result).to eq(customer)
        expect(result.from_registration).to be_truthy
        expect(result.company_name).to eq(retailer.name)
      end
    end

    context 'when customer does not exist' do
      it 'creates a new customer' do
        expect { retailer.send(:find_or_create_follow_up_customer, retailer_user) }.to change(Customer, :count).by(1)

        new_customer = Customer.last
        expect(new_customer.retailer_id.to_s).to eq(ENV.fetch('MERCATELY_RETAILER_ID', nil))
        expect(new_customer.first_name).to eq(retailer_user.first_name)
        expect(new_customer.last_name).to eq(retailer_user.last_name)
        expect(new_customer.country_id).to eq(retailer.country_code)
        expect(new_customer.phone).to eq(retailer.retailer_number)
        expect(new_customer.company_name).to eq(retailer.name)
        expect(new_customer.from_registration).to be_truthy
      end
    end

    context 'when agent_id is present' do
      before do
        allow_any_instance_of(Customers::AssignResponsible).to receive(:call).and_return(agent_id)
      end

      it 'assigns retailer_user_id to agent_id' do
        new_customer = retailer.send(:find_or_create_follow_up_customer, retailer_user)
        expect(new_customer.retailer_user_id).to eq(agent_id)
      end

      it 'assigns agent_customer_attributes to agent_id' do
        new_customer = retailer.send(:find_or_create_follow_up_customer, retailer_user)
        expect(new_customer.agent.id).to eq(agent_id)
      end
    end

    context 'when retailer_users are present count active team agents' do
      let(:retailer) { create(:retailer) }
      let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
      let!(:retailer_user2) { create(:retailer_user, retailer: retailer) }
      let!(:retailer_user3) { create(:retailer_user, retailer: retailer, removed_from_team: true) }

      it 'returns the number of active retailer users' do
        expect(retailer.count_active_team_agents).to eq(2)
      end
    end
  end

  describe 'instance methods' do
    describe '#facebook_unread_messages' do
      it 'returns unread Facebook messages' do
        retailer = create(:retailer)
        retailer_user = create(:retailer_user, retailer: retailer)
        facebook_retailer = create(:facebook_retailer, retailer: retailer)
        allow(facebook_retailer).to receive(:facebook_unread_messages).with(retailer_user).and_return(5)
        expect(retailer.facebook_unread_messages(retailer_user)).to eq(5)
      end

      it 'returns nil when there is no facebook_retailer' do
        retailer = create(:retailer)
        retailer_user = create(:retailer_user, retailer: retailer)
        expect(retailer.facebook_unread_messages(retailer_user)).to be_nil
      end
    end

    describe '#instagram_unread_messages' do
      it 'returns unread Instagram messages' do
        retailer = create(:retailer)
        retailer_user = create(:retailer_user, retailer: retailer)
        facebook_retailer = create(:facebook_retailer, retailer: retailer)
        allow(facebook_retailer).to receive(:instagram_unread_messages).with(retailer_user).and_return(3)
        expect(retailer.instagram_unread_messages(retailer_user)).to eq(3)
      end

      it 'returns nil when there is no facebook_retailer' do
        retailer = create(:retailer)
        retailer_user = create(:retailer_user, retailer: retailer)
        expect(retailer.instagram_unread_messages(retailer_user)).to be_nil
      end
    end

    describe '#prev_catalog_slug' do
      it 'returns the previous catalog_slug' do
        retailer = create(:retailer, catalog_slug: 'old-slug')
        retailer.catalog_slug = 'new-slug'
        retailer.save
        expect(retailer.prev_catalog_slug).to eq('oldslug')
      end
    end

    describe '#to_param' do
      it 'returns the slug' do
        retailer = create(:retailer, slug: 'test-slug')
        expect(retailer.to_param).to eq('test-slug')
      end
    end

    describe '#unread_orders' do
      it 'returns orders with unread messages' do
        customer = create(:customer, retailer: retailer)
        order_with_unread = create(:order, customer: customer, count_unread_messages: 1)
        create(:order, customer: customer, count_unread_messages: 0)
        expect(retailer.unread_orders).to contain_exactly(order_with_unread)
      end
    end

    describe '#transactions' do
      it 'returns stripe_transactions if int_charges is true' do
        retailer.update(int_charges: true)
        expect(retailer.transactions).to eq(retailer.stripe_transactions)
      end

      it 'returns paymentez_transactions if int_charges is false' do
        retailer.update(int_charges: false)
        expect(retailer.transactions).to eq(retailer.paymentez_transactions)
      end
    end

    describe '#public_phone_number' do
      it 'returns qr_phone_number if gupshup_phone_number is blank' do
        retailer.update(phone_number: '1234567890', qr_phone_number: '0987654321')
        expect(retailer.public_phone_number).to eq('0987654321')
      end

      it 'returns formated gupshup_phone_number number if both phone numbers are present' do
        retailer.update(qr_phone_number: '0987654321', gupshup_phone_number: '1234567890')
        expect(retailer.public_phone_number).to eq('+1234567890')
      end

      it 'returns qr_phone_number if phone_number is not present' do
        retailer.update(phone_number: nil, qr_phone_number: '0987654321')
        expect(retailer.public_phone_number).to eq('0987654321')
      end
    end

    describe '#supervisors' do
      it 'returns supervisor retailer users' do
        create(:retailer_user, retailer: retailer, retailer_supervisor: true)
        create(:retailer_user, retailer: retailer, retailer_supervisor: false)
        expect(retailer.supervisors.count).to eq(1)
        expect(retailer.supervisors.first.retailer_supervisor).to be true
      end
    end

    describe '#gupshup_temporal_messages' do
      before do
        GupshupTemporalMessageState.delete_all
      end

      it 'returns gupshup temporal messages for the retailer' do
        create_list(:gupshup_temporal_message_state, 3, retailer_id: retailer.id)
        expect(retailer.gupshup_temporal_messages.count).to eq(3)
      end
    end

    describe '#whatsapp_phone_number' do
      context 'when retailer has gupshup_phone_number' do
        let(:retailer) { create(:retailer, gupshup_phone_number: '1234567890') }

        it { expect(retailer.whatsapp_phone_number).to eq('+1234567890') }
      end

      context 'when retailer has qr_phone_number' do
        let(:retailer) { create(:retailer, qr_phone_number: '1234567890') }

        it { expect(retailer.whatsapp_phone_number).to eq('+1234567890') }
      end

      context 'when retailer has phone_number' do
        let(:retailer) { create(:retailer, phone_number: '1234567890') }

        it { expect(retailer.whatsapp_phone_number).to eq('+1234567890') }
      end

      context 'when retailer has gupshup_phone_number is empty, qr_phone_number is empty, and is phone_number empty' do
        let(:retailer) { create(:retailer, phone_number: nil) }

        it { expect(retailer.whatsapp_phone_number).to eq('') }
      end
    end

    describe '#hubspot_integrated?' do
      it 'returns true if hs_access_token is present' do
        retailer.update(hs_access_token: 'token')
        expect(retailer.hubspot_integrated?).to be true
      end

      it 'returns false if hs_access_token is not present' do
        retailer.update(hs_access_token: nil)
        expect(retailer.hubspot_integrated?).to be false
      end
    end

    describe '#facebook_integrated?' do
      it 'returns true if facebook_retailer is connected' do
        create(:facebook_retailer, retailer: retailer, uid: 'uid', access_token: 'token')
        expect(retailer.facebook_integrated?).to be true
      end

      it 'returns false if facebook_retailer is not connected' do
        expect(retailer.facebook_integrated?).to be false
      end
    end

    describe '#instagram_integrated?' do
      it 'returns true if facebook_retailer is instagram integrated' do
        create(:facebook_retailer, retailer: retailer, instagram_uid: 'uid', access_token: 'token',
                                   instagram_integrated: true)
        expect(retailer.reload.instagram_integrated?).to be true
      end

      it 'returns false if facebook_retailer is not instagram integrated' do
        create(:facebook_retailer, retailer: retailer, instagram_uid: nil, access_token: nil)
        expect(retailer.reload.instagram_integrated?).to be false
      end
    end

    describe '#ml_integrated?' do
      it 'returns true if meli_retailer is present' do
        create(:meli_retailer, retailer: retailer)
        expect(retailer.ml_integrated?).to be true
      end

      it 'returns false if meli_retailer is not present' do
        expect(retailer.ml_integrated?).to be false
      end
    end

    describe '#mercado_pago_integrated?' do
      it 'returns true if mp_retailer is present' do
        create(:mp_retailer, retailer: retailer)
        expect(retailer.mercado_pago_integrated?).to be true
      end

      it 'returns false if mp_retailer is not present' do
        expect(retailer.mercado_pago_integrated?).to be false
      end
    end

    describe '#fb_comments_integrated?' do
      it 'returns true if facebook_retailer has facebook_comments enabled' do
        create(:facebook_retailer, retailer: retailer, facebook_comments: true)
        expect(retailer.fb_comments_integrated?).to be true
      end

      it 'returns false if facebook_retailer does not have facebook_comments enabled' do
        create(:facebook_retailer, retailer: retailer, facebook_comments: false)
        expect(retailer.fb_comments_integrated?).to be false
      end
    end

    describe '#ig_comments_integrated?' do
      it 'returns true if facebook_retailer has instagram_comments enabled' do
        create(:facebook_retailer, retailer: retailer, instagram_comments: true)
        expect(retailer.ig_comments_integrated?).to be true
      end

      it 'returns false if facebook_retailer does not have instagram_comments enabled' do
        create(:facebook_retailer, retailer: retailer, instagram_comments: false)
        expect(retailer.ig_comments_integrated?).to be false
      end
    end

    describe '#stripe_integrated?' do
      it 'returns true if stripe_retailer is confirmed' do
        create(:stripe_retailer, retailer: retailer, confirmed: true)
        expect(retailer.stripe_integrated?).to be true
      end

      it 'returns false if stripe_retailer is not confirmed' do
        create(:stripe_retailer, retailer: retailer, confirmed: false)
        expect(retailer.stripe_integrated?).to be false
      end
    end

    describe '#payphone_integrated?' do
      it 'returns true if payphone is connected' do
        retailer.update(payphone_connected: true)
        expect(retailer.payphone_integrated?).to be true
      end

      it 'returns false if payphone is not connected' do
        retailer.update(payphone_connected: false)
        expect(retailer.payphone_integrated?).to be false
      end
    end

    describe '#electronic_payments_connected?' do
      it 'returns true if any electronic payment method is connected' do
        create(:stripe_retailer, retailer: retailer, confirmed: true)
        expect(retailer.electronic_payments_connected?).to be true
      end

      it 'returns false if no electronic payment method is connected' do
        expect(retailer.electronic_payments_connected?).to be false
      end
    end
  end

  describe 'encrypted attributes' do
    it 'encrypts the api_key' do
      retailer = create(:retailer, api_key: 'secret_key')
      expect(retailer.encrypted_api_key).not_to eq('secret_key')
      expect(retailer.api_key).to eq('secret_key')
    end
  end

  describe '#handle_slug_generation_error' do
    let(:retailer) { create(:retailer) }
    let(:new_slug) { 'new-slug' }
    let(:error) { StandardError.new('Test error') }
    let(:custom_error_message) { 'Custom error message' }

    before do
      allow(retailer).to receive(:update_slug_query)
      allow(Rails.logger).to receive(:error)
      allow(SlackError).to receive(:send_error)
    end

    it 'calls update_slug_query with new_slug' do
      expect(retailer).to receive(:update_slug_query).with(new_slug)
      retailer.send(:handle_slug_generation_error, new_slug, error, custom_error_message)
    end

    it 'logs the error message' do
      expect(Rails.logger).to receive(:error).with(error.message)
      retailer.send(:handle_slug_generation_error, new_slug, error, custom_error_message)
    end

    it 'sends error to Slack' do
      expect(SlackError).to receive(:send_error).with(error, custom_error_message, true)
      retailer.send(:handle_slug_generation_error, new_slug, error, custom_error_message)
    end
  end

  describe '#generate_slug_email' do
    let(:retailer) { create(:retailer) }

    context 'when slug has changed' do
      before { allow(retailer).to receive(:saved_change_to_slug?).and_return(true) }

      it 'returns early' do
        expect(retailer).not_to receive(:update)
        retailer.generate_slug_email
      end
    end

    context 'when slug has not changed' do
      let!(:retailer_user) { create(:retailer_user, retailer: retailer, email: '<EMAIL>') }

      before { allow(retailer).to receive(:saved_change_to_slug?).and_return(false) }

      it 'generates a new slug based on email' do
        expect(retailer).to receive(:update).with(slug: 'test')
        retailer.generate_slug_email
      end

      context 'when a similar slug already exists' do
        before do
          create(:retailer, slug: 'test')
        end

        it 'appends the id to the slug' do
          expect(retailer).to receive(:update).with(slug: "test-#{retailer.id}")
          retailer.generate_slug_email
        end
      end

      context 'when an error occurs' do
        before do
          allow(retailer).to receive(:update).and_raise(StandardError.new('Test error'))
          allow(retailer).to receive(:handle_slug_generation_error)
        end

        it 'calls handle_slug_generation_error' do
          expect(retailer)
            .to receive(:handle_slug_generation_error)
            .with(
              anything,
              instance_of(StandardError), "Retailer: #{retailer.id} #{retailer.name} no pudo crear el slug email"
            )
          retailer.generate_slug_email
        end
      end
    end

    describe '#gupshup_unread_whatsapp_messages' do
      let(:retailer) { create(:retailer) }
      let(:retailer_user) { create(:retailer_user, retailer: retailer) }

      it 'always returns an empty array' do
        expect(retailer.gupshup_unread_whatsapp_messages(retailer_user)).to eq([])
      end
    end
  end

  describe 'retailer_user_data' do
    let(:retailer) { create(:retailer) }
    let(:user) { create(:user, first_name: 'John', last_name: 'Doe', email: '<EMAIL>') }
    let(:retailer_user) { create(:retailer_user, retailer: retailer, user: user) }

    before do
      retailer_user
    end

    describe '#owner' do
      let(:user2) { create(:user, first_name: 'Jane', last_name: 'Doe', email: 'jane.doe@example') }
      let!(:retailer_user2) { create(:retailer_user, :admin, retailer: retailer, user: user2) }

      context 'when first retailer_user is the owner' do
        it 'returns the first retailer_user' do
          expect(retailer.owner).to eq(retailer_user)
        end
      end

      context 'when first retailer_user was removed from team' do
        before do
          retailer_user.update(removed_from_team: true)
        end

        it 'returns the first active admin' do
          expect(retailer.owner).to eq(retailer_user2)
        end
      end

      context 'when all admin retailer_users was removed from team' do
        let(:user3) { create(:user, first_name: 'Jorel', last_name: 'Doe', email: 'jorel.doe@example') }
        let!(:retailer_user3) { create(:retailer_user, retailer: retailer, retailer_admin: false, user: user3) }

        before do
          retailer_user.update(removed_from_team: true)
          retailer_user2.update(removed_from_team: true)
        end

        it 'returns the first retailer_user' do
          expect(retailer.owner).to eq(retailer_user3)
        end
      end
    end

    describe '#email' do
      it 'delegates email to the owner user' do
        expect(retailer.email).to eq('<EMAIL>')
      end
    end

    describe '#first_name' do
      it 'delegates first_name to the owner' do
        expect(retailer.first_name).to eq('John')
      end
    end

    describe '#last_name' do
      it 'delegates last_name to the owner' do
        expect(retailer.last_name).to eq('Doe')
      end
    end
  end

  describe '#mia_suggestions_integrated?' do
    let(:retailer) { create(:retailer) }

    context 'when integration exists' do
      let(:mia_itegration_suggestions) { create(:mia_integration_type, :suggestions) }
      let!(:integration) do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_itegration_suggestions, retailer: retailer)
      end

      it { expect(retailer.mia_suggestions_integrated?).to be_truthy }
    end

    context 'when integration does not exist' do
      it { expect(retailer.mia_suggestions_integrated?).to be_falsey }
    end
  end

  describe '#suggestions_mia_integration' do
    let(:retailer) { create(:retailer) }

    context 'when integration exists' do
      let(:mia_itegration_suggestions) { create(:mia_integration_type, :suggestions) }
      let!(:integration) do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_itegration_suggestions, retailer: retailer)
      end

      it { expect(retailer.suggestions_mia_integration).to eq integration }
    end

    context 'when integration does not exist' do
      it { expect(retailer.suggestions_mia_integration).to be_nil }
    end
  end

  describe '#mia_integrated?' do
    let(:retailer) { create(:retailer) }

    context 'when integration exists' do
      let(:mia_integration_type) { create(:mia_integration_type, :guides) }
      let!(:integration) do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
      end

      it { expect(retailer.mia_integrated?(mia_integration_type.name)).to be_truthy }
    end

    context 'when integration does not exist' do
      it { expect(retailer.mia_integrated?('whatever')).to be_falsey }
    end
  end

  describe '#mia_integration' do
    let(:retailer) { create(:retailer) }
    let(:suggestions_type) { create(:mia_integration_type, :suggestions) }
    let(:suggestions) { create(:mia_integration, :with_public_key, mia_integration_type: suggestions_type) }

    context 'when integration exists' do
      let(:mia_integration_type) { create(:mia_integration_type, :guides) }
      let!(:integration) do
        create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type, retailer: retailer)
      end

      it { expect(retailer.mia_integration(mia_integration_type.name)).to eq integration }
    end

    context 'when integration does not exist' do
      it { expect(retailer.mia_integration('whatever')).to be_nil }
    end
  end

  describe '#mia_platforms_amount' do
    subject { described_class.new }

    context 'when no platforms are selected' do
      it 'adds an error to the base' do
        subject.retailer_mia_platforms = []

        subject.mia_platforms_amount

        expect(subject.errors[:base]).to include('Debe seleccionar una plataforma')
      end
    end

    context 'when platforms are selected' do
      let!(:platform) { create(:retailer_mia_platform) }

      it 'does not add an error to the base' do
        subject.retailer_mia_platforms = [platform]

        subject.mia_platforms_amount

        expect(subject.errors[:base]).to be_empty
      end
    end
  end

  describe '#payment_plan_details' do
    let(:retailer) { described_class.new }
    let(:plan) { create(:payment_plan, retailer: retailer) }
    let(:pricing_tier) { double('PricingTier', identifier: 'tier-123', contains_ai?: true) }
    let(:addons) { [{ amount: 10, item_price_id: 'addon-1' }] }
    let(:start_date) { Time.zone.parse('2024-01-01') }
    let(:next_pay_date) { Time.zone.parse('2024-02-01') }

    before do
      allow(retailer).to receive(:payment_plan).and_return(plan)
      allow(plan).to receive(:pricing_tier_id).and_return(pricing_tier_id)
    end

    context 'when pricing_tier_id is blank' do
      let(:pricing_tier_id) { nil }

      it 'returns an empty hash' do
        expect(retailer.payment_plan_details).to eq({})
      end
    end

    context 'when pricing_tier_id is present' do
      let(:pricing_tier_id) { 1 }

      before do
        allow(plan).to receive(:payment_start_date).and_return(start_date)
        allow(plan).to receive(:next_pay_date).and_return(next_pay_date)
        allow(plan).to receive(:pricing_tier).and_return(pricing_tier)
        allow(plan).to receive(:active?).and_return(true)
        allow(plan).to receive(:bought_addons).and_return(addons)
        allow(plan).to receive(:max_agents).and_return(5)
      end

      it 'returns the correct payment plan details hash' do
        details = retailer.payment_plan_details
        expect(details.keys).to eq(
          [:plan_id,
          :plan_currency_code,
          :next_billing_at,
          :current_term_start,
          :current_term_end,
          :status,
          :addons,
          :chargebee_plan_id,
          :bought_agents, :has_ai]
        )
      end

      it 'returns status cancelled if plan is not active' do
        allow(plan).to receive(:active?).and_return(false)
        details = retailer.payment_plan_details
        expect(details[:status]).to eq('cancelled')
      end
    end
  end
end
# rubocop:enable Rails/Date
