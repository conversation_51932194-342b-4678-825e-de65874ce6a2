require 'rails_helper'

RSpec.describe PricingTier, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:priceable) }
    it { is_expected.to have_many(:entitlements) }
    it { is_expected.to have_many(:payment_plans) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:price) }
    it { is_expected.to validate_numericality_of(:price).is_greater_than_or_equal_to(0) }
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:billing_cycle).with_values(monthly: 0, quarterly: 1, yearly: 2, one_time: 3) }
    it { is_expected.to define_enum_for(:pricing_model).with_values(tiered: 0, per_unit: 1, flat_fee: 2) }
    it { is_expected.to define_enum_for(:status).with_values(active: 0, inactive: 1) }
  end

  describe 'nested attributes' do
    it { is_expected.to accept_nested_attributes_for(:entitlements).allow_destroy(true) }
  end

  describe 'scopes' do
    describe '.active' do
      let(:plan) { create(:plan) }
      let!(:active_tier) { create(:pricing_tier, status: :active, priceable: plan, priceable_type: 'Plan') }
      let!(:inactive_tier) { create(:pricing_tier, status: :inactive, priceable: plan, priceable_type: 'Plan') }

      it 'returns only active pricing tiers' do
        expect(PricingTier.active).to include(active_tier)
        expect(PricingTier.active).not_to include(inactive_tier)
      end
    end
  end

  describe '.ransackable_attributes' do
    it 'returns the correct attributes' do
      expect(described_class.ransackable_attributes).to match_array(
        %w[billing_cycle created_at id price updated_at priceable_type]
      )
    end
  end

  describe '.ransackable_associations' do
    it 'returns the correct associations' do
      expect(described_class.ransackable_associations).to eq(%w[priceable])
    end
  end

  describe '#month_interval' do
    it 'returns 1 for monthly' do
      tier = build(:pricing_tier, billing_cycle: :monthly)
      expect(tier.month_interval).to eq(1)
    end

    it 'returns 3 for quarterly' do
      tier = build(:pricing_tier, billing_cycle: :quarterly)
      expect(tier.month_interval).to eq(3)
    end

    it 'returns 12 for yearly' do
      tier = build(:pricing_tier, billing_cycle: :yearly)
      expect(tier.month_interval).to eq(12)
    end

    it 'returns 0 for one_time or unknown' do
      tier = build(:pricing_tier, billing_cycle: :one_time)
      expect(tier.month_interval).to eq(0)
      tier = build(:pricing_tier, billing_cycle: nil)
      expect(tier.month_interval).to eq(0)
    end
  end

  describe '#acquired?' do
    let(:plan) { create(:plan, name: 'Scale', identifier: 'scale') }
    let(:tier) { create(:pricing_tier, :quarterly, priceable: plan) }
    let(:payment_plan) { create(:payment_plan, plan: 'scale', month_interval: 3, pricing_tier: tier) }
    let(:addon) { create(:addon) }
    let(:addon_tier) { create(:pricing_tier, priceable: addon) }
    let(:acquired_addon) { create(:acquired_addon, addon_id: addon.id, payment_plan: payment_plan) }

    it 'returns true if acquired_addons exists with addon_id' do
      acquired_addon
      expect(addon_tier.acquired?(payment_plan)).to be true
    end

    it 'returns false if acquired_addons does not exist with addon_id' do
      expect(addon_tier.acquired?(payment_plan)).to be false
    end
  end

  describe '#contains_ai?' do
    let(:plan) { create(:plan) }
    let(:pricing_tier) { create(:pricing_tier, priceable: plan) }

    context 'when entitlements include mia-contacts-included' do
      before do
        create(:entitlement, pricing_tier: pricing_tier, identifier: 'mia-contacts-included')
      end

      it 'returns true' do
        expect(pricing_tier.contains_ai?).to be_truthy
      end
    end

    context 'when entitlements do not include mia-contacts-included' do
      before do
        create(:entitlement, pricing_tier: pricing_tier, identifier: 'other-identifier')
      end

      it 'returns false' do
        expect(pricing_tier.contains_ai?).to be_falsey
      end
    end

    context 'when there are no entitlements' do
      it 'returns false' do
        expect(pricing_tier.contains_ai?).to be_falsey
      end
    end
  end
end
