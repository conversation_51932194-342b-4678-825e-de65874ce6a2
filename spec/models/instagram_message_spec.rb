require 'rails_helper'

RSpec.describe InstagramMessage do
  let!(:retailer) { create(:retailer) }
  let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
  let!(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }

  describe 'associations' do
    it { is_expected.to belong_to(:facebook_retailer) }
    it { is_expected.to belong_to(:customer) }
    it { is_expected.to belong_to(:retailer_user).required(false) }
    it { is_expected.to belong_to(:instagram_comment).required(false) }
  end

  describe 'scopes' do
    it { expect(described_class).to respond_to(:customer_unread) }
    it { expect(described_class).to respond_to(:retailer_unread) }
    it { expect(described_class).to respond_to(:inbound) }
    it { expect(described_class).to respond_to(:unread) }
    it { expect(described_class).to respond_to(:range_between) }
    it { expect(described_class).to respond_to(:first_load) }
  end

  describe 'attributes' do
    it { is_expected.to respond_to(:block_chat_reactivation) }
  end

  describe 'message events' do
    let(:customer) { create(:customer, retailer: retailer, pstype: :instagram, agent_event: retailer_user) }

    context 'when create a note message' do
      let!(:message) do
        create(:instagram_message, :outbound, facebook_retailer: facebook_retailer, customer: customer, note: true)
      end

      it 'create event for deal created' do
        activities_customer = Activities::Customer.find_by(customer_id: customer.id)
        expect(activities_customer.events.count).to eq(2)
        expect(activities_customer.events.last.action).to eq('Nota creada')
      end
    end

    context 'when create a order note message' do
      let!(:message) do
        create(:instagram_message, :outbound, facebook_retailer: facebook_retailer, customer: customer, note: true,
                                              order_note: true)
      end

      it 'create event for deal created' do
        activities_customer = Activities::Customer.find_by(customer_id: customer.id)
        expect(activities_customer.events.count).to eq(2)
        expect(activities_customer.events.last.action).to eq('Nota de orden creada')
      end
    end
  end

  describe '#mark_flag_unread' do
    let!(:customer) { create(:customer, :from_fb, :instagram, retailer: retailer, count_unread_messages: 0) }
    let(:message) { build(:instagram_message, :inbound, customer: customer, facebook_retailer: facebook_retailer) }
    let(:message_out) do
      create(:instagram_message, :outbound, customer: customer, facebook_retailer: facebook_retailer)
    end

    context 'when message is not inbound' do
      it 'returns nil' do
        expect(message_out.send(:mark_unread_flag)).to be_nil
      end
    end

    context 'when message is inbound' do
      it 'increases by 1 the customer unread counter' do
        message.save

        expect(customer.unread_instagram_messages).to eq(1)
      end

      context 'when the customer is not assigned' do
        it 'increases by 1 the total counter and unassigned counter' do
          message.save

          expect(retailer.retailer_counter.ig_messages).to eq(1)
          expect(retailer.retailer_counter.ig_messages_na).to eq(1)
        end
      end

      context 'when the customer is assigned' do
        let!(:agent_customer) { create(:agent_customer, retailer_user: retailer_user, customer: customer) }

        before do
          customer.update(retailer_user_id: retailer_user.id)
        end

        it 'increases by 1 the agent counter and total counter' do
          message.save

          expect(retailer.retailer_counter.ig_messages).to eq(1)
          expect(retailer.retailer_counter.ig_messages_na).to eq(0)
          expect(retailer_user.reload.unread_instagram_chats_count).to eq(1)
        end
      end
    end
  end

  describe '#inbound_media?' do
    let(:ig_message) do
      build(:instagram_message, :inbound, url: 'https://example.com/image.jpg', file_type: 'image')
    end
    let(:ig_message2) do
      build(:instagram_message, :outbound)
    end

    context 'when sent_by_retailer is false, url is present, and file_type is valid' do
      it 'returns true' do
        expect(ig_message.send(:inbound_media?)).to be true
      end
    end

    context 'when sent_by_retailer is true or url is not present or file_type is invalid' do
      it 'returns true' do
        expect(ig_message2.send(:inbound_media?)).to be false
      end
    end
  end

  describe '#upload_file' do
    let(:media_ig_message) do
      build(:instagram_message, :inbound, file_type: 'image', attached_file: 'spec/fixtures/files/profile.jpg')
    end

    let(:s3_instance) { instance_double(Uploaders::S3Aws) }

    context 'when valid attached file' do
      before do
        allow(URI).to receive(:open).and_return(true)
        allow(Uploaders::S3Aws).to receive(:new).with(anything).and_return(s3_instance)
        allow(s3_instance).to receive(:upload_file).and_return(
          {
            'type' => 'upload',
            'secure_url' => 'https://dc2p3rqc7bvrd.cloudfront.net/test.jpg',
            'original_filename' => 'test.jpg'
          }
        )
      end

      it 'save uploaded s3 url' do
        media_ig_message.save
        expect(media_ig_message.url).to match('https://dc2p3rqc7bvrd.cloudfront.net/test.jpg')
      end
    end

    context 'when returns invalid secure_url' do
      before do
        allow(URI).to receive(:open).and_return(true)
        allow(Uploaders::S3Aws).to receive(:new).with(anything).and_return(s3_instance)
        allow(s3_instance).to receive(:upload_file).and_return(
          {
            'type' => 'upload',
            'secure_url' => nil,
            'original_filename' => nil
          }
        )
      end

      it 'save uploaded s3 url' do
        media_ig_message.save
        expect(media_ig_message.url).to be_nil
      end
    end
  end

  describe '#get_comment' do
    context 'when there is not a comment associated' do
      it 'returns nil' do
        expect(subject.send(:get_comment)).to be_nil
      end
    end

    context 'when there is a comment associated' do
      subject(:instagram_message) do
        create(:instagram_message, facebook_retailer: facebook_retailer, instagram_comment: instagram_comment)
      end

      let(:customer_fb_post) do
        create(:customer_fb_post, customer: customer, platform: :instagram, retailer: retailer)
      end

      let(:customer) { create(:customer, retailer: retailer, pstype: :messenger) }
      let(:instagram_comment) { create(:instagram_comment, customer_fb_post: customer_fb_post) }

      it 'returns the comment' do
        expect(subject.send(:get_comment)).to eq(instagram_comment)
      end
    end
  end

  describe 'Concerns::InstagramChatBotActionConcern' do
    subject(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer) }

    describe '#send_message_step' do
      let(:customer) { create(:customer) }
      let(:option) { create(:chat_bot_option) }
      let(:bot_api) { instance_double(ChatBots::V2::BotApi, replace_message_variables: '', execute_option: true) }
      let(:bot_message) { instance_double(ChatBots::V2::Message, send_message: true) }
      let(:bot_process) do
        instance_double(
          ChatBots::V2::ProcessChatbot,
          manage_chat_bot: true,
          manage_skip_option: true,
          execute_actions: true
        )
      end
      let(:file) do
        double(
          ActiveStorage::Attached::One,
          attached?: true,
          is_aws: true,
          content_type: 'image/jpg',
          filename: 'image.jpg'
        )
      end

      before do
        allow(ChatBots::V2::BotApi).to receive(:new).and_return(bot_api)
        allow(ChatBots::V2::Message).to receive(:new).and_return(bot_message)
        allow(ChatBots::V2::ProcessChatbot).to receive(:new).and_return(bot_process)
      end

      context 'when option is not a message' do
        let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer, customer: customer) }

        it 'sends the message' do
          instagram_message.send_message_step(option)
          expect(bot_api).to have_received(:execute_option)
        end

        context 'when option is form and has skip_option' do
          it 'calls manage_skip_option' do
            option.option_type = :form
            option.skip_option = true
            instagram_message.send_message_step(option)
            expect(bot_api).to have_received(:execute_option)
          end
        end
      end

      context 'when option is a message' do
        let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer, customer: customer) }

        before do
          option.option_type = :message
          allow(instagram_message).to receive(:send_bot_message).and_return(true)
        end

        context 'when option has childs' do
          let!(:child) { create(:chat_bot_option, option_type: :message, parent: option) }

          before do
            option.message_type = :document
          end

          it 'updates customer chat_bot_option_id' do
            allow(customer).to receive(:update_column).and_return(true)
            allow(Customer::ChatBotDeactivator).to receive(:call).and_return(true)
            allow(ChatBots::CustomerFlowUpdater).to receive(:call).and_return(true)
            instagram_message.send_message_step(option)
            expect(ChatBots::CustomerFlowUpdater).to have_received(:call)
          end

          it 'calls itself' do
            allow(instagram_message).to receive(:send_message_step)
            instagram_message.send_message_step(option)
            expect(instagram_message).to have_received(:send_message_step)
          end

          context 'with child option as jump_to' do
            before do
              customer.update(chat_bot_option_id: opt.id)
            end

            let!(:ig_message) { build(:instagram_message, facebook_retailer: facebook_retailer, customer: customer) }
            let!(:opt) { create(:chat_bot_option, :message, message_type: :text) }
            let!(:jump_to) { create(:chat_bot_option, :jump_to, parent: opt) }

            it 'does not update current option on customer' do
              ig_message.send_message_step(opt)

              expect(customer.chat_bot_option_id).to eq(opt.id)
              expect(bot_api).to have_received(:execute_option).twice
            end
          end
        end

        context 'when option has no childs' do
          it 'does not update customer chat_bot_option_id' do
            allow(customer).to receive(:update_column).and_return(true)
            instagram_message.send_message_step(option)
            expect(customer).not_to have_received(:update_column)
          end

          it 'deactivates customer chat_bot' do
            allow(Customer::ChatBotDeactivator).to receive(:call).and_return(true)
            allow(ChatBots::CustomerFlowUpdater).to receive(:call).and_return(true)
            instagram_message.send_message_step(option)
            expect(Customer::ChatBotDeactivator).to have_received(:call)
          end
        end

        context 'when option is a text message' do
          let(:instagram_message) do
            build(:instagram_message, facebook_retailer: facebook_retailer, customer: customer)
          end

          before do
            option.option_type = :message
            option.message_type = :text
          end

          it 'calls replace_message_variables' do
            instagram_message.send_message_step(option)
            expect(bot_api).to have_received(:replace_message_variables)
          end

          it 'calls send_bot_message once' do
            allow(instagram_message).to receive(:send_bot_message).and_return(true)
            instagram_message.send_message_step(option)
            expect(instagram_message).to have_received(:send_bot_message)
          end
        end

        context 'when option is a image message' do
          let(:instagram_message) do
            build(:instagram_message, facebook_retailer: facebook_retailer, customer: customer)
          end

          before do
            option.option_type = :message
            option.message_type = :image
            allow(option).to receive_messages(file: file, file_url: 'https://example.com/image.jpg')
          end

          it 'calls replace_message_variables' do
            instagram_message.send_message_step(option)
            expect(bot_api).to have_received(:replace_message_variables)
          end

          it 'calls send_bot_message twice' do
            allow(instagram_message).to receive(:send_bot_message).and_return(true)
            instagram_message.send_message_step(option)
            expect(instagram_message).to have_received(:send_bot_message).twice
          end
        end

        context 'when option is not text nor image message' do
          let(:instagram_message) do
            build(:instagram_message, facebook_retailer: facebook_retailer, customer: customer)
          end

          before do
            option.option_type = :message
            option.message_type = :document
          end

          it 'calls send_bot_message once' do
            allow(instagram_message).to receive(:send_bot_message).and_return(true)
            instagram_message.send_message_step(option)
            expect(instagram_message).not_to have_received(:send_bot_message)
          end
        end
      end

      context 'when option parent has a file attached' do
        let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer, file_type: 'image') }
        let(:attached_file) { instance_double(ActiveStorage::Attached::One, attached?: true) }
        let(:parent) { instance_double(ChatBotOption, file: attached_file) }

        it 'sends the message and updates the customer\'s chat_bot_option_id' do
          allow(option).to receive(:parent).and_return(parent)
          allow(instagram_message).to receive(:sleep).and_return(true)
          instagram_message.send_message_step(option)
          expect(instagram_message).to have_received(:sleep)
        end
      end
    end

    describe '#chat_bot_execution' do
      context 'when the message is not inbound' do
        let(:instagram_message) { build(:instagram_message, :outbound, facebook_retailer: facebook_retailer) }

        it 'returns nil' do
          expect(instagram_message.send(:chat_bot_execution)).to be_nil
        end

        it 'calls chat_bot_execution' do
          allow(instagram_message).to receive(:chat_bot_execution)
          instagram_message.save
          expect(instagram_message).to have_received(:chat_bot_execution)
        end
      end

      context 'when the customer is not messenger' do
        let(:customer) { create(:customer, :instagram, retailer: retailer) }
        let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer) }

        it 'returns nil' do
          expect(instagram_message.send(:chat_bot_execution)).to be_nil
        end

        it 'calls chat_bot_execution' do
          allow(instagram_message).to receive(:chat_bot_execution)
          instagram_message.save
          expect(instagram_message).to have_received(:chat_bot_execution)
        end
      end

      context 'when the customer is messenger but not chatbot found' do
        let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer) }

        before do
          allow(instagram_message).to receive(:get_current_bot).and_return(nil)
        end

        it 'returns nil' do
          expect(instagram_message.send(:chat_bot_execution)).to be_nil
        end

        it 'assigns nil to @chat_bot' do
          instagram_message.save
          expect(instagram_message.instance_variable_get(:@chat_bot)).to be_nil
        end
      end

      context 'when url is present' do
        let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer) }

        before do
          allow(instagram_message).to receive_messages(get_current_bot: true, url: 'https://media_url.com')
        end

        it 'assigns true to @media' do
          instagram_message.send(:chat_bot_execution)
          expect(instagram_message.instance_variable_get(:@media)).to be_truthy
        end

        context 'when message has text' do
          let(:instagram_message) do
            build(:instagram_message, facebook_retailer: facebook_retailer, text: 'Hola', url: 'https://media_url.com')
          end

          before do
            allow(instagram_message).to receive(:get_current_bot).and_return(true)
          end

          it 'assigns false to @media_selection' do
            instagram_message.send(:chat_bot_execution)
            expect(instagram_message.instance_variable_get(:@media_selection)).to be_falsy
          end
        end

        context 'when message does not have text' do
          let(:instagram_message) do
            build(:instagram_message, facebook_retailer: facebook_retailer, text: '', url: 'https://media_url.com')
          end

          before do
            allow(instagram_message).to receive(:get_current_bot).and_return(true)
          end

          it 'assigns true to @media_selection' do
            instagram_message.send(:chat_bot_execution)
            expect(instagram_message.instance_variable_get(:@media_selection)).to be_truthy
          end
        end
      end

      context 'when url is not present' do
        let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer) }

        before do
          allow(instagram_message).to receive_messages(get_current_bot: true, url: nil)
        end

        it 'assigns false to @media' do
          instagram_message.send(:chat_bot_execution)
          expect(instagram_message.instance_variable_get(:@media)).to be_falsey
        end
      end

      context 'when chat bot version is 1' do
        let(:chat_bot) { build(:chat_bot, :for_instagram, retailer: retailer, version: 1) }
        let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer) }
        let(:chat_bot_process) { instance_double(ChatBots::ChatBotProcess, manage_chat_bot: true) }

        before do
          allow(instagram_message).to receive(:get_current_bot).and_return(chat_bot)
          allow(ChatBots::ChatBotProcess).to receive(:new).and_return(chat_bot_process)
        end

        it 'calls manage_chat_bot v1' do
          instagram_message.send(:chat_bot_execution)
          expect(chat_bot_process).to have_received(:manage_chat_bot)
        end
      end

      context 'when chat bot version is 2' do
        let(:chat_bot) { build(:chat_bot, :for_messenger, retailer: retailer, version: 2) }
        let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer) }
        let(:chat_bot_process) { instance_double(ChatBots::V2::ProcessChatbot, manage_chat_bot: true) }

        before do
          allow(instagram_message).to receive(:get_current_bot).and_return(chat_bot)
          allow(ChatBots::V2::ProcessChatbot).to receive(:new).and_return(chat_bot_process)
        end

        it 'calls manage_chat_bot v2' do
          instagram_message.send(:chat_bot_execution)
          expect(chat_bot_process).to have_received(:manage_chat_bot)
        end
      end
    end

    describe '#get_current_bot' do
      let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer) }
      let(:customer) { create(:customer, :instagram, retailer: retailer) }
      let(:url_instagram_message) do
        create(:instagram_message, facebook_retailer: facebook_retailer,
                                   text: 'I need Help https://example.com',
                                   customer: customer)
      end

      let!(:support_chat_bot) do
        create(:chat_bot, :for_instagram, retailer: retailer, triggers: ['I need help'], enabled: true)
      end

      let!(:chat_bot) { create(:chat_bot, :for_instagram, retailer: retailer, triggers: ['hello'], enabled: true) }
      let!(:chat_bot_v2) do
        create(:chat_bot, :for_instagram, retailer: retailer,
                                          version: 2,
                                          enabled: true,
                                          triggers: ['hello support', 'Help me'])
      end

      let!(:chat_bot_v2_any_interaction) do
        create(:chat_bot, :for_instagram, retailer: retailer, version: 2, enabled: true, triggers: ['sales payments'])
      end

      let!(:chat_bot_v2_with_triggers) do
        create(:chat_bot, :for_instagram, retailer: retailer, version: 2, triggers: ['hello world'], enabled: true)
      end

      context 'when message text contains a trigger' do
        it 'returns the chat_bot with triggers' do
          subject.instance_variable_set(:@text, 'hello')
          expect(instagram_message.send(:get_current_bot)).to eq(chat_bot)
        end
      end

      context 'when message text does not contain a trigger' do
        it 'returns nil' do
          subject.instance_variable_set(:@text, 'hi world')
          expect(instagram_message.send(:get_current_bot)).to be_nil
        end
      end

      context 'when message text contains a url' do
        it 'returns the chat_bot with triggers' do
          subject.instance_variable_set(:@text, 'I need Help https://example.com')
          expect(url_instagram_message.send(:get_current_bot)).to eq(support_chat_bot)
        end
      end
    end

    describe '#send_bot_message' do
      let(:customer) { create(:customer, :instagram, retailer: retailer) }
      let(:instagram_message) { build(:instagram_message, facebook_retailer: facebook_retailer, customer: customer) }

      context 'when message, file_url and file_data are not present' do
        let(:params) { {} }

        it 'does not create a InstagramMessage' do
          expect { instagram_message.send_bot_message(params) }.not_to change(described_class, :count)
        end

        it 'returns nil' do
          expect(instagram_message.send_bot_message(params)).to be_nil
        end
      end

      context 'when message is present' do
        let(:params) { { message: 'Hello, world!' } }
        let(:facebook_service) { instance_double(Facebook::Messages, send_message: {}, send_attachment: {}) }

        before do
          allow_any_instance_of(described_class).to receive(:facebook_service).and_return(facebook_service)
        end

        it 'calls create on InstagramMessage' do
          allow(described_class).to receive(:create).and_return(true)
          instagram_message.send_bot_message(params)
          expect(described_class).to have_received(:create)
        end

        it 'creates a InstagramMessage with the correct params' do
          allow(described_class).to receive(:create).and_return(true)
          instagram_message.send_bot_message(params)
          expect(described_class).to have_received(:create).with({
            customer: instagram_message.customer,
            facebook_retailer: instagram_message.facebook_retailer,
            text: params[:message], payload: params,
            sent_from_mercately: true, sent_by_retailer: true,
            file_content_type: nil, file_data: nil, file_type: nil, file_url: nil, filename: nil, id_client: nil
          })
        end

        it 'creates a InstagramMessage with the correct attributes' do
          expect { instagram_message.send_bot_message(params) }.to change(described_class, :count).by(1)
          message = described_class.last
          expect(message.customer).to eq(customer)
          expect(message.id_client).to eq(customer.psid)
          expect(message.facebook_retailer).to eq(facebook_retailer)
          expect(message.text).to eq(params[:message])
          expect(message.sent_from_mercately).to be true
          expect(message.sent_by_retailer).to be true
          expect(message.payload.values).to eq(params.values)
        end
      end
    end
  end
end
