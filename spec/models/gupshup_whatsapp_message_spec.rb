require 'rails_helper'

RSpec.describe GupshupWhatsappMessage do
  before do
    allow_any_instance_of(Exponent::Push::Client).to receive(:send_messages).and_return(true)
  end

  describe 'associations' do
    it { is_expected.to belong_to(:retailer) }
    it { is_expected.to belong_to(:customer) }
    it { is_expected.to belong_to(:retailer_user).required(false) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:retailer) }
    it { is_expected.to validate_presence_of(:customer) }
    it { is_expected.to validate_presence_of(:status) }
    it { is_expected.to validate_presence_of(:direction) }
    it { is_expected.to validate_presence_of(:source) }
    it { is_expected.to validate_presence_of(:destination) }
    it { is_expected.to validate_presence_of(:channel) }
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:status).with_values(%w[error submitted enqueued sent delivered read]) }

    it {
      expect(subject).to define_enum_for(:conversation_type)
        .with_values(%w[free_point user_initiated business_initiated free_tier marketing authentication utility
                        service referral_conversion authentication_international marketing_lite])
    }

    it { is_expected.to define_enum_for(:integration).with_values(%w[gupshup qr]) }
    it { is_expected.to define_enum_for(:mia_flag).with_values(%w[buy human_help]) }
  end

  describe 'scopes' do
    let(:inbound) { create(:gupshup_whatsapp_message, :inbound, created_at: 2.hours.ago) }
    let(:outbound) { create(:gupshup_whatsapp_message, :outbound, created_at: 3.minutes.ago) }

    let(:notification_message) { create(:gupshup_whatsapp_message, :notification, created_at: 3.minutes.ago) }
    let(:conversation_message) { create(:gupshup_whatsapp_message, :conversation, created_at: 3.minutes.ago) }

    let(:sent_message) { create(:gupshup_whatsapp_message, status: 3) }
    let(:delivered_message) { create(:gupshup_whatsapp_message, status: 4) }
    let(:read_message) { create(:gupshup_whatsapp_message, status: 5) }

    it 'returns inbound messages' do
      inbound
      expect(described_class.inbound_messages.count).to eq(1)
    end

    it 'returns outbound messages' do
      outbound
      expect(described_class.outbound_messages.count).to eq(1)
    end

    it 'returns notification messages' do
      notification_message
      expect(described_class.notification_messages.count).to eq(1)
    end

    it 'returns conversation messages' do
      conversation_message
      expect(described_class.conversation_messages.count).to eq(2)
    end

    it 'returns messages by range' do
      inbound
      outbound
      notification_message
      conversation_message
      expect(described_class.range_between(1.hour.ago, Time.zone.now).count).to eq(3)
    end

    it 'returns messages by status sent, delivered and read' do
      sent_message
      delivered_message
      read_message
      expect(described_class.sent_delivered_read.count).to eq(3)
    end
  end

  describe 'attributes' do
    it { is_expected.to respond_to(:block_chat_reactivation) }
  end

  describe 'message events' do
    let(:retailer) { create(:retailer, :gupshup_integrated) }
    let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
    let(:customer) { create(:customer, retailer: retailer, ws_active: true, agent_event: retailer_user) }

    context 'when create a note message' do
      let!(:message) { create(:gupshup_whatsapp_message, :outbound, customer: customer, note: true) }

      it 'create event for deal created' do
        activities_customer = Activities::Customer.find_by(customer_id: customer.id)
        expect(activities_customer.events.count).to eq(2)
        expect(activities_customer.events.last.action).to eq('Nota creada')
      end
    end

    context 'when create a order note message' do
      let!(:message) { create(:gupshup_whatsapp_message, :outbound, customer: customer, note: true, order_note: true) }

      it 'create event for deal created' do
        activities_customer = Activities::Customer.find_by(customer_id: customer.id)
        expect(activities_customer.events.count).to eq(2)
        expect(activities_customer.events.last.action).to eq('Nota de orden creada')
      end
    end
  end

  describe 'bots execution' do
    let(:chat_bot_option) { create(:chat_bot_option) }

    let(:chat_bot_option_a) do
      create(:chat_bot_option, parent: chat_bot_option, position: 1, text: 'Cuentame más')
    end

    let(:chat_bot_option_b) do
      create(:chat_bot_option, :with_children, parent: chat_bot_option, position: 2, text: 'Quiero Volver atrás')
    end

    let(:chat_bot_option_c) do
      create(:chat_bot_option, :with_children, parent: chat_bot_option, position: 3, text: 'Volver volver atrás')
    end

    let(:chat_bot) do
      create(:chat_bot, :bot_enabled, :with_accented_trigger, :for_whatsapp, all_days: true, chat_bot_options:
        [chat_bot_option, chat_bot_option_a, chat_bot_option_b, chat_bot_option_c])
    end

    let(:retailer) { create(:retailer, :gupshup_integrated, chat_bots: [chat_bot]) }
    let(:customer) { create(:customer, :able_to_start_bots, retailer: retailer) }
    let(:group) { create(:customer, :able_to_start_bots, :is_group, retailer: retailer) }
    let(:message) { build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer) }
    let(:msg_group) { build(:gupshup_whatsapp_message, :inbound, customer: group, retailer: retailer) }
    let(:answer_message) { build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer) }

    context 'when the customer has not activated the chat bot yet' do
      context 'when the message belongs to a group' do
        it 'does not activate the chatbot' do
          msg_group.save!
          expect(group.active_bot).to be false
          expect(group.chat_bot_option_id).to be_nil
        end
      end

      it 'triggers a chat bot ignoring accent marks' do
        message.save!
        expect(customer.policy.active_bot_for?(platform: :whatsapp)).to be(true)
        expect(customer.active_bots.find_by(platform: :whatsapp).chat_bot_option_id).to eq(chat_bot_option.id)
      end

      it 'selects an option by matching whole sentence ignoring accent marks' do
        message.save!
        answer_message.message_payload = { type: 'text', text: 'quiero volver atras' }
        answer_message.save!
        expect(customer.active_bots.find_by(platform: :whatsapp).chat_bot_option_id).to eq(chat_bot_option_b.id)
      end

      it 'rejects selection and stays in last valid option if more than one option has the same ranking' do
        message.save!
        answer_message.message_payload = { type: 'text', text: 'atras' }
        answer_message.save!
        expect(customer.chat_bot_option_id).to be_nil
      end

      it 'selects an option by matches count ranking' do
        message.save!
        answer_message.message_payload = { type: 'text', text: 'atras volver' }
        answer_message.save!
        expect(customer.active_bots.find_by(platform: :whatsapp).chat_bot_option_id).to eq(chat_bot_option_c.id)
      end

      context 'when chat bot options with media' do
        let(:root_option) { create(:chat_bot_option, text: 'Root node') }
        let(:chat_bot_option_image) do
          create(:chat_bot_option, :with_children, :with_image_file, parent: root_option, position: 1, text: '1 Image')
        end

        let(:chat_bot_option_pdf) do
          create(:chat_bot_option, :with_pdf_file, :with_children, parent: root_option, position: 2, text: '2 PDF')
        end

        let(:additional_image) { create(:additional_bot_answer, :with_image_file) }

        let(:chat_bot_option_text) do
          create(:chat_bot_option, parent: root_option, position: 3, text: 'Text',
                                   additional_bot_answers: [additional_image])
        end

        let(:additional_pdf) { create(:additional_bot_answer, :with_pdf_file) }

        let(:chat_bot_option_text2) do
          create(:chat_bot_option, parent: root_option, position: 4, text: 'Text PDF',
                                   additional_bot_answers: [additional_pdf])
        end

        let(:additional_audio) { create(:additional_bot_answer, :with_audio_file) }

        let(:chat_bot_option_text3) do
          create(:chat_bot_option, parent: root_option, position: 5, text: 'Text Audio',
                                   additional_bot_answers: [additional_audio])
        end

        let(:chat_bot) do
          create(:chat_bot, :bot_enabled, :with_accented_trigger, :for_whatsapp,
                 chat_bot_options: [root_option, chat_bot_option_image, chat_bot_option_pdf, chat_bot_option_text,
                                    chat_bot_option_text2, chat_bot_option_text3],
                 triggers: ['Estoy interesado en Mercately'])
        end

        let(:retailer) { create(:retailer, :gupshup_integrated, chat_bots: [chat_bot]) }
        let(:customer) { create(:customer, :able_to_start_bots, retailer: retailer) }
        let(:message) do
          build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer,
                                                     message_payload: {
                                                       type: 'text',
                                                       text: 'Estoy interesado en Mercately'
                                                     })
        end

        let(:answer_message) do
          build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer)
        end

        it 'selects a chat bot option with image' do
          message.save!
          answer_message.message_payload = { type: 'text', text: '1' }
          answer_message.save!
          expect(customer.active_bots.find_by(platform: :whatsapp).chat_bot_option_id).to eq(chat_bot_option_image.id)
        end

        it 'selects a chat bot option with pdf' do
          message.save!
          answer_message.message_payload = { type: 'text', text: '2' }
          answer_message.save!
          expect(customer.active_bots.find_by(platform: :whatsapp).chat_bot_option_id).to eq(chat_bot_option_pdf.id)
        end

        context 'when the option has additional answers' do
          before do
            allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(ok_net_response)
            allow(Faraday).to receive(:get).and_return(faraday_response)
            allow(faraday_response).to receive(:status).and_return(200)
            allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
          end

          let(:faraday_response) { instance_double(Faraday::Response) }
          let(:ok_net_response) { Net::HTTPOK.new(1.0, '200', 'OK') }
          let(:ok_body_response) do
            {
              status: 'submitted',
              messageId: 'ee4a68a0-1203-4c85-8dc3-49d0b3226a35'
            }.to_json
          end

          context 'when the additional is an image' do
            it 'sends three messages' do
              message.save!
              answer_message.message_payload = { type: 'text', text: 'Text' }
              answer_message.save!
              expect(described_class.outbound_messages.count).to eq(3)
            end
          end

          context 'when the additional is a PDF' do
            it 'sends three messages' do
              message.save!
              answer_message.message_payload = { type: 'text', text: 'Text PDF' }
              answer_message.save!
              expect(described_class.outbound_messages.count).to eq(3)
            end
          end

          context 'when the additional is an audio' do
            it 'sends four messages' do
              message.save!
              answer_message.message_payload = { type: 'text', text: 'Text Audio' }
              answer_message.save!
              expect(described_class.outbound_messages.count).to eq(4)
            end
          end
        end
      end

      context 'when the matching bot is version 1' do
        let(:chat_bot_process) { instance_double(ChatBots::ChatBotProcess) }

        before do
          allow(ChatBots::ChatBotProcess).to receive(:new)
            .with(anything, anything, anything, anything, anything, anything).and_return(chat_bot_process)
          allow(chat_bot_process).to receive(:manage_chat_bot).and_return(true)
        end

        it 'executes the ChatBots::ChatBotProcess class' do
          message.save!
          expect(chat_bot_process).to have_received(:manage_chat_bot)
        end
      end

      context 'when the matching bot is version 2' do
        let(:chat_bot_process) { instance_double(ChatBots::V2::ProcessChatbot) }

        before do
          chat_bot.update(version: 2)
          allow(ChatBots::V2::ProcessChatbot).to receive(:new)
            .with(anything, anything, anything, anything, anything).and_return(chat_bot_process)
          allow(chat_bot_process).to receive(:manage_chat_bot).and_return(true)
        end

        it 'executes the ChatBots::V2::ProcessChatbot class' do
          message.save!
          expect(chat_bot_process).to have_received(:manage_chat_bot)
        end
      end
    end

    context 'when the customer has already activated the chat bot' do
      let(:customer) { create(:customer, :whatsapp, retailer: retailer, allow_start_bots: false) }
      let(:message) { build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer) }

      context 'when the chat bot does not have reactivation time and customer is not allowed to activate bots' do
        let!(:chat_bot_customer) { create(:chat_bot_customer, customer: customer, chat_bot: chat_bot) }

        before { customer.active_bots.create(chat_bot_option_id: chat_bot_option.id, platform: 'whatsapp') }

        it 'does not activate the chat bot' do
          message.message_payload = { type: 'text', text: 'hola test' }
          message.save
          expect(customer.policy.active_bot_for?(platform: :whatsapp)).to be_falsey
          expect(customer.active_bots.where(platform: :whatsapp)).to be_empty
        end
      end

      context 'when customer is allowed to activate bots' do
        let!(:chat_bot_customer) { create(:chat_bot_customer, customer: customer, chat_bot: chat_bot) }

        before do
          customer.toggle_allow_start_bots! 'whatsapp'
        end

        it 'activates the chat bot' do
          message.message_payload = { type: 'text', text: 'hola test' }
          message.save
          expect(customer.policy.active_bot_for?(platform: :whatsapp)).to be true
          expect(customer.active_bots.find_by(platform: :whatsapp).chat_bot_option_id).to eq(chat_bot_option.id)
        end
      end

      context 'when the chat bot has reactivation time' do
        context 'when the message is received before the reactivation time' do
          let(:answer_message) { build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer) }

          let!(:prev_message) do
            create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer, created_at:
              5.hours.ago)
          end

          let!(:chat_bot_customer) do
            create(:chat_bot_customer, customer: customer, chat_bot: chat_bot, created_at: 5.hours.ago)
          end

          before do
            chat_bot_option_b
            chat_bot.update(reactivate_after: 12)
            customer.update(active_bot: true)
          end

          it 'continues the flow of the chat bot' do
            answer_message.message_payload = { type: 'text', text: 'quiero volver atras' }
            answer_message.save

            expect(customer.active_bots.find_by(platform: 'whatsapp').chat_bot_option_id).to eq(chat_bot_option_b.id)
          end
        end

        context 'when the message is received after the reactivation time' do
          context 'when the text of the message matches the activation text of a bot' do
            let(:answer_message) { build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer) }

            let!(:prev_message) do
              create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer, created_at:
                13.hours.ago, skip_automatic: true)
            end

            let!(:chat_bot_customer) do
              create(:chat_bot_customer, customer: customer, chat_bot: chat_bot, created_at: 12.hours.ago)
            end

            before do
              chat_bot.update(reactivate_after: 1)
              customer.update_columns(allow_start_bots: false, allow_start_messenger_bots: false,
                                      allow_start_instagram_bots: false)
              ChatBots::CustomerFlowUpdater.call(customer, selected: chat_bot_option_b.id, platform: :whatsapp)
            end

            it 'reactivates the chat bot from the beginning' do
              expect(customer.active_bots.find_by(platform: :whatsapp).chat_bot_option_id).to eq(chat_bot_option_b.id)

              answer_message.message_payload = { type: 'text', text: 'hola test' }
              answer_message.save

              expect(customer.active_bots.find_by(platform: :whatsapp)&.chat_bot_option_id).to eq(chat_bot_option.id)
            end
          end

          context 'when the text of the message does not match the activation text of any bot' do
            context 'when there is not bot activated with any interaction' do
              let(:answer_message) do
                build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer)
              end

              let!(:prev_message) do
                create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer, created_at:
                  13.hours.ago)
              end

              let!(:chat_bot_customer) do
                create(:chat_bot_customer, customer: customer, chat_bot: chat_bot, created_at: 13.hours.ago)
              end

              before do
                chat_bot.update(reactivate_after: 12)
                customer.update(active_bot: true, chat_bot_option: chat_bot_option_b)
                customer.active_bots.create(chat_bot_option_id: chat_bot_option_b.id, platform: 'whatsapp')
              end

              it 'deactivates the bot to the customer' do
                expect(customer.chat_bot_option_id).to eq(chat_bot_option_b.id)

                answer_message.message_payload = { type: 'text', text: 'Probando' }
                answer_message.save

                expect(customer.policy.active_bot_for?(platform: :whatsapp)).to be false
                expect(customer.chat_bot_option_id).to be_nil
              end
            end

            context 'when there is a bot activated with any interaction' do
              let(:answer_message) do
                build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer)
              end

              let!(:chat_bot_option_d) { create(:chat_bot_option, :with_children, chat_bot: chat_bot_any_interaction) }

              let(:chat_bot_any_interaction) do
                create(:chat_bot, :with_any_interaction, :not_only_new_customers, :for_whatsapp, retailer: retailer)
              end

              let!(:prev_message) do
                create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer, created_at:
                  13.hours.ago)
              end

              let!(:chat_bot_customer) do
                create(:chat_bot_customer, customer: customer, chat_bot: chat_bot, created_at: 12.hours.ago)
              end

              before do
                chat_bot.update(reactivate_after: 12)
                ChatBots::CustomerFlowUpdater.call(customer, selected: chat_bot_option_b.id, platform: 'whatsapp')
                chat_bot_any_interaction.update_columns(enabled: true, reactivate_after: 1)
              end

              it 'deactivates the bot to the customer' do
                expect(customer.active_bots.find_by(platform: :whatsapp).chat_bot_option_id).to eq(chat_bot_option_b.id)

                answer_message.message_payload = { type: 'text', text: 'Probando' }
                answer_message.save

                expect(customer.policy.active_bot_for?(platform: :whatsapp)).to be true
                expect(customer.active_bots.find_by(platform: :whatsapp).chat_bot_option_id).to eq(chat_bot_option_d.id)
              end
            end
          end
        end
      end
    end
  end

  context 'when set the message type' do
    context 'when customer has an open chat' do
      let(:customer) { create(:customer) }
      let!(:message) { create(:gupshup_whatsapp_message, :inbound, customer: customer, created_at: 1.hour.ago) }
      let(:message2) { create(:gupshup_whatsapp_message, :outbound, customer: customer) }

      it 'is conversation after create' do
        expect(message2.message_type).to eq('conversation')
      end
    end

    context 'when customer does not have an open chat' do
      let(:message) { create(:gupshup_whatsapp_message, :outbound) }

      it 'is notification after create' do
        expect(message.message_type).to eq('notification')
      end
    end
  end

  describe '#type' do
    it 'returns the message outbound payload type' do
      message = create(:gupshup_whatsapp_message, message_payload: { isHSM: 'false', type: 'text' })

      expect(message.type).to eq('text')
    end

    it 'returns the message inbound payload type' do
      message = create(:gupshup_whatsapp_message, message_payload: { payload: { type: 'document' } })

      expect(message.type).to eq('document')
    end
  end

  describe '#send_welcome_message' do
    let(:set_gupshup_messages_service) { instance_double(Whatsapp::Outbound::Msg) }

    before do
      allow(set_gupshup_messages_service).to receive(:send_message)
        .and_return('Sent')
      allow(Whatsapp::Outbound::Msg).to receive(:new)
        .and_return(set_gupshup_messages_service)
    end

    context 'when the retailer does not have an active welcome message configured' do
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }

      let(:message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer)
      end

      it 'returns nil' do
        expect(message.send(:send_welcome_message)).to be_nil
      end
    end

    context 'when the customer does not text for the first time' do
      let!(:automatic_answer) { create(:automatic_answer, :welcome, :whatsapp, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: automatic_answer) }
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }

      let!(:first_message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer)
      end

      let(:message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer)
      end

      it 'returns nil' do
        expect(message.send(:send_welcome_message)).to be_nil
      end
    end

    context 'when the message is not inbound' do
      let!(:automatic_answer) { create(:automatic_answer, :welcome, :whatsapp, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: automatic_answer) }
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }

      let(:message) do
        create(:gupshup_whatsapp_message, :outbound, customer: customer)
      end

      it 'returns nil' do
        expect(message.send(:send_welcome_message)).to be_nil
      end
    end

    context 'when all conditions are present' do
      let!(:automatic_answer) { create(:automatic_answer, :welcome, whatsapp: true, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: automatic_answer) }
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }

      let(:message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: customer.retailer)
      end

      it 'sends the message' do
        expect(message.send(:send_welcome_message)).to eq('Sent')
      end
    end
  end

  describe '#send_inactive_message' do
    let(:set_gupshup_messages_service) { instance_double(Whatsapp::Outbound::Msg) }

    before do
      allow(set_gupshup_messages_service).to receive(:send_message)
        .and_return('Sent')
      allow(Whatsapp::Outbound::Msg).to receive(:new)
        .and_return(set_gupshup_messages_service)
    end

    context 'when the retailer does not have an inactive message configured' do
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }

      let!(:first_message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer)
      end

      let(:message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer)
      end

      it 'returns nil' do
        expect(message.send(:send_inactive_message)).to be_nil
      end
    end

    context 'when the customer does not have a prior message sent' do
      let!(:automatic_answer) { create(:automatic_answer, :inactive, :whatsapp, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: automatic_answer) }
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }

      let(:message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer)
      end

      it 'returns nil' do
        expect(message.send(:send_inactive_message)).to be_nil
      end
    end

    context 'when the message is not inbound' do
      let!(:automatic_answer) { create(:automatic_answer, :inactive, :whatsapp, retailer: retailer) }
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }

      let!(:first_message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer)
      end

      let(:message) do
        create(:gupshup_whatsapp_message, :outbound, customer: customer)
      end

      it 'returns nil' do
        expect(message.send(:send_inactive_message)).to be_nil
      end
    end

    context 'when the created time of the prior message is not passed yet' do
      let!(:automatic_answer) { create(:automatic_answer, :inactive, :whatsapp, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: automatic_answer) }
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }

      let!(:first_message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer, created_at:
          11.hours.ago)
      end

      let(:message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer)
      end

      it 'returns nil' do
        expect(message.send(:send_inactive_message)).to be_nil
      end
    end

    context 'when all conditions are present' do
      let!(:automatic_answer) { create(:automatic_answer, :inactive, whatsapp: true, retailer: retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: automatic_answer) }
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:customer) { create(:customer, retailer: retailer) }

      let!(:first_message) do
        create(:gupshup_whatsapp_message, :inbound, retailer: retailer, customer: customer, created_at:
          13.hours.ago)
      end

      let(:message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: customer.retailer)
      end

      it 'sends the message' do
        expect(message.send(:send_inactive_message)).to eq('Sent')
      end
    end
  end

  describe '#apply_cost' do
    let(:retailer) { create(:retailer, :gupshup_integrated, ws_balance: 10.0) }

    context 'when the message status is error' do
      context 'when the message cost is not zero' do
        let(:message) { create(:gupshup_whatsapp_message, :outbound, :notification, retailer: retailer, cost: 0.0565) }

        it 'sets the message cost to zero' do
          retailer.update(ws_balance: 10.0 - message.cost)
          message.update(status: 'error')
          expect(message.reload.cost).to eq(0.0)
          expect(retailer.reload.ws_balance).to eq(10.0)
        end
      end

      context 'when the message cost is zero' do
        let(:message) { create(:gupshup_whatsapp_message, :outbound, :conversation, retailer: retailer, cost: 0.0) }

        it 'does not change the message cost' do
          retailer.update(ws_balance: 10.0 - message.cost)
          message.update(status: 'error')
          expect(message.reload.cost).to eq(0.0)
          expect(retailer.reload.ws_balance).to eq(10.0)
        end
      end
    end
  end

  describe '#assign_agent' do
    context 'when the retailer has assignment teams permission' do
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:retailer_user) { create(:retailer_user, retailer: retailer) }
      let(:another_retailer_user) { create(:retailer_user, retailer: retailer) }
      let(:customer) { create(:customer, retailer: retailer) }
      let(:customer2) { create(:customer, retailer: retailer) }

      let(:message) do
        create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer)
      end

      context 'when the message is inbound' do
        context 'with an agent already assigned' do
          let!(:agent_customer) { create(:agent_customer, customer: customer, retailer_user: retailer_user) }

          it 'returns nil' do
            expect(message.send(:assign_agent)).to be_nil
          end
        end

        context 'with the chat already answered by agent' do
          let!(:outbound_message) do
            create(:gupshup_whatsapp_message, :outbound, customer: customer, retailer: retailer, retailer_user:
              retailer_user)
          end

          it 'returns nil' do
            expect(message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the retailer does not have a default team assignment created' do
          it 'returns nil' do
            expect(message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the retailer does not have a default team assignment activated' do
          let!(:default_team) { create(:team_assignment, retailer: retailer, active_assignment: false, whatsapp: true) }

          it 'returns nil' do
            expect(message.send(:assign_agent)).to be_nil
          end
        end

        context 'when there is at least one agent with free spots to assign' do
          let(:default_team) { create(:team_assignment, :assigned_default, retailer: retailer, whatsapp: true) }
          let!(:agent_team1) do
            create(:agent_team, :activated, team_assignment: default_team, retailer_user: retailer_user,
                                            max_assignments: 2, assigned_amount: 2)
          end

          let!(:agent_team2) do
            create(:agent_team, :activated, team_assignment: default_team, retailer_user: another_retailer_user,
                                            max_assignments: 4, assigned_amount: 3)
          end

          it 'assigns an agent to the customer' do
            retailer.payment_plan.update(plan: :advanced, contact_based: false)

            expect do
              create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer)
            end.to change(AgentCustomer, :count).by(1)
          end
        end

        context 'when there is more than one agent with free spots to assign' do
          let(:default_team) { create(:team_assignment, :assigned_default, retailer: retailer, whatsapp: true) }
          let!(:agent_team1) do
            create(:agent_team, :activated, team_assignment: default_team, retailer_user: retailer_user,
                                            max_assignments: 2, assigned_amount: 0)
          end

          let!(:agent_team2) do
            create(:agent_team, :activated, team_assignment: default_team, retailer_user: another_retailer_user,
                                            max_assignments: 4, assigned_amount: 0)
          end

          let(:gupshup_whatsapp_message1) do
            create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer)
          end

          let(:gupshup_whatsapp_message2) do
            create(:gupshup_whatsapp_message, :inbound, customer: customer2, retailer: retailer)
          end

          before do
            retailer.payment_plan.update(plan: :advanced, contact_based: false)
          end

          it 'assigns the agent with less assignments to the customer and so on' do
            expect { gupshup_whatsapp_message1 }.to change(AgentCustomer, :count).by(1)
            expect(agent_team1.reload.assigned_amount).to eq(1)
            expect(AgentCustomer.last.retailer_user_id).to eq(agent_team1.retailer_user_id)
            expect(AgentCustomer.last.team_assignment_id).to eq(default_team.id)
            expect(AgentCustomer.count).to eq(1)
            expect { gupshup_whatsapp_message2 }.to change(AgentCustomer, :count).by(1)
            expect(agent_team2.reload.assigned_amount).to eq(1)
            expect(AgentCustomer.last.retailer_user_id).to eq(agent_team2.retailer_user_id)
            expect(AgentCustomer.last.team_assignment_id).to eq(default_team.id)
            expect(AgentCustomer.count).to eq(2)
          end
        end
      end

      context 'when the message is outbound' do
        let!(:inbound_message) do
          create(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer)
        end

        let(:outbound_message) do
          create(:gupshup_whatsapp_message, :outbound, customer: customer, retailer: retailer, retailer_user:
            retailer_user)
        end

        context 'when the message does not come from an agent' do
          let(:not_from_agent) do
            create(:gupshup_whatsapp_message, :outbound, customer: customer, retailer: retailer)
          end

          it 'returns nil' do
            expect(not_from_agent.send(:assign_agent)).to be_nil
          end
        end

        context 'when the customer does not have an agent assigned' do
          it 'returns nil' do
            expect(outbound_message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the agent assigned to the customer is not from a team' do
          let!(:agent_customer) { create(:agent_customer, customer: customer, retailer_user: retailer_user) }

          it 'returns nil' do
            expect(outbound_message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the message is not the first one sent to the customer' do
          let(:default_team) { create(:team_assignment, :assigned_default, retailer: retailer, whatsapp: true) }
          let!(:agent_team) do
            create(:agent_team, :activated, team_assignment: default_team, retailer_user: retailer_user,
                                            max_assignments: 2, assigned_amount: 0)
          end

          let!(:agent_customer) do
            create(:agent_customer, customer: customer, retailer_user: retailer_user, team_assignment: default_team)
          end

          let!(:other_outbound) do
            create(:gupshup_whatsapp_message, :outbound, customer: customer, retailer: retailer, retailer_user:
              retailer_user)
          end

          it 'returns nil' do
            expect(outbound_message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the customer is resolved' do
          let(:default_team) { create(:team_assignment, :assigned_default, retailer: retailer, whatsapp: true) }
          let!(:agent_team) do
            create(:agent_team, :activated, team_assignment: default_team, retailer_user: retailer_user,
                                            max_assignments: 2, assigned_amount: 1)
          end

          let!(:agent_customer) do
            create(:agent_customer, customer: customer, retailer_user: retailer_user, team_assignment: default_team)
          end

          it 'decreases by one the assigned amount of the agent team' do
            expect(agent_team.assigned_amount).to eq(1)

            customer.whatsapp_resolved!

            expect(agent_team.reload.assigned_amount).to eq(0)
          end
        end
      end
    end

    context 'when the retailer does not have assignment teams permission' do
      let(:retailer) { create(:retailer, :gupshup_integrated) }
      let(:message) { create(:gupshup_whatsapp_message, retailer: retailer) }

      it 'returns nil' do
        expect(message.send(:assign_agent)).to be_nil
      end
    end
  end

  describe '#retry_message' do
    let(:retailer) { create(:retailer, :gupshup_integrated) }
    let(:customer) { create(:customer, retailer: retailer, phone: '+5215599999999', country_id: 'MX') }
    let(:net_response) { Net::HTTPOK.new(1.0, '200', 'OK') }
    let(:customer_to_use) do
      create(:customer, retailer: retailer, phone: '+5215599999999', number_to_use: '+525599999999', country_id: 'MX')
    end

    let(:ok_body_response) do
      {
        status: 'submitted',
        messageId: 'c011a9c0-051d-4e01-a130-7b12903decb8'
      }.to_json
    end

    let(:error_1005_response) do
      {
        app: 'MercatelyTest',
        timestamp: 1_617_133_991_906,
        version: 2,
        type: 'message-event',
        payload: {
          id: 'e0058485-4fa3-415f-b299-d47274e2017f',
          type: 'failed',
          destination: '5215599999999',
          payload: {
            code: 1005,
            reason: 'Message sending failed as user is inactive for session message and template did not match'
          }
        }
      }
    end

    let(:error_1002_response) do
      {
        app: 'MercatelyTest',
        timestamp: 1_617_133_991_906,
        version: 2,
        type: 'message-event',
        payload: {
          id: 'e0058485-4fa3-415f-b299-d47274e2017f',
          type: 'failed',
          destination: '5215599999999',
          payload: {
            code: 1002,
            reason: 'Number Does Not Exists On WhatsApp'
          }
        }
      }
    end

    let(:text_payload) do
      {
        isHSM: 'false',
        type: 'text',
        text: 'Test'
      }
    end

    let(:template_payload) do
      {
        isHSM: 'true',
        type: 'text',
        text: 'Your OTP for 1 is 2. This is valid for 3.',
        id: '997dd550-c8d8-4bf7-ad98-a5ac4844a1ed',
        params: %w[1 2 3]
      }
    end

    let(:document_payload) do
      {
        type: 'file',
        caption: '',
        url: 'https://res.cloudinary.com/dhhrdm74a/raw/upload/v1617135241/' \
             'Informacio%CC%81n%20para%20el%20registro%20de%20la%20empresa%20%281%29.docx',
        filename: 'informacion-para-el-registro-de-la-empresa-1'
      }
    end

    let(:document_template_payload) do
      {
        isHSM: 'true',
        type: 'file',
        url: 'https://res.cloudinary.com/dhhrdm74a/raw/upload/v1606413278/nvpot3kncgybpncukoo0.pdf',
        caption:
          'Estimado Henry cómo estás? 🙋 Te saluda Elvis de AutoSeguro, gracias por utilizar nuestro ' \
          'servicio.\n\nNos habías solicitado una cotización para el Seguro de Tu Auto🚘 que incluye la cobertura ' \
          'de Amparo Patrimonial <U+200D>😮!\n\nEnviamos al correo registrado 📩. Si en el caso que no la pudiste ' \
          'mirar 👀, aquí te la hacemos llegar.\n\nAhora Si Tu Auto Va a Estar 100% PROTEGIDO..! 💯\n\nEs una muy ' \
          'buena oportunidad, si realmente quieres sentirte seguro.\n\nRecuerda que la puedes pagar hasta 12 meses ' \
          'SIN INTERESES.👌\n\nSi tienes alguna duda o pregunta escríbeme, estoy aquí para ayudarte.\n\nSaludos ' \
          'Cordiales. 🙂',
        filename: 'notas-para-tareas-comunes.pdf'
      }
    end

    let(:image_payload) do
      {
        type: 'image',
        originalUrl: 'https://res.cloudinary.com/dhhrdm74a/image/upload/v1617135467/sr68x4ubgkhfdf1dwljj.png',
        previewUrl: 'https://res.cloudinary.com/dhhrdm74a/image/upload/v1617135467/sr68x4ubgkhfdf1dwljj.png',
        caption: ''
      }
    end

    let(:image_with_caption_payload) do
      {
        type: 'image',
        originalUrl: 'https://res.cloudinary.com/dhhrdm74a/image/upload/FeCgH7dy8Fe6nGWbWVsVuX3L',
        previewUrl: 'https://res.cloudinary.com/dhhrdm74a/image/upload/FeCgH7dy8Fe6nGWbWVsVuX3L',
        caption: 'Caption de ejemplo.\\n\\nCon saltos de linea para probar el pre-line nuevo de las divisiones.' \
                 '\\n\\nBye!'
      }
    end

    let(:location_payload) do
      {
        type: 'location',
        longitude: -67.5894155,
        latitude: 10.2546799,
        name: ' ',
        address: ' '
      }
    end

    let(:audio_payload) do
      {
        type: 'audio',
        url: 'https://res.cloudinary.com/dhhrdm74a/video/upload/v1617136125/cu12du9kppdapj1q0hyl.aac'
      }
    end

    context 'when the message status is not error' do
      let!(:gsm) do
        create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer,
                                                     message_payload: text_payload)
      end

      it 'does not retry sending the message to the auxiliar number' do
        expect do
          gsm.send(:retry_message)
        end.not_to change(described_class, :count)
      end
    end

    context 'when the message is not outbound' do
      let!(:gsm) do
        create(:gupshup_whatsapp_message, :inbound, retailer: retailer, customer: customer,
                                                    message_payload: text_payload, status: 'error')
      end

      it 'does not retry sending the message to the auxiliar number' do
        expect do
          gsm.send(:retry_message)
        end.not_to change(described_class, :count)
      end
    end

    context 'when the customer is not from Mexico' do
      let(:ve_customer) { create(:customer, retailer: retailer, phone: '+584125558899', country_id: 'VE') }
      let!(:gsm) do
        create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: ve_customer,
                                                     message_payload: text_payload, status: 'error')
      end

      it 'does not retry sending the message to the auxiliar number' do
        expect do
          gsm.send(:retry_message)
        end.not_to change(described_class, :count)
      end
    end

    context 'when the message error_payload is blank' do
      let!(:gsm) do
        create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer,
                                                     message_payload: text_payload, status: 'error')
      end

      it 'does not retry sending the message to the auxiliar number' do
        expect do
          gsm.send(:retry_message)
        end.not_to change(described_class, :count)
      end
    end

    context 'when the customer does not have number to use' do
      let!(:gsm) do
        create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer,
                                                     message_payload: text_payload, status: 'error',
                                                     error_payload: error_1002_response)
      end

      it 'does not retry sending the message to the auxiliar number' do
        expect do
          gsm.send(:retry_message)
        end.not_to change(described_class, :count)
      end
    end

    context 'when the customer has number to use but it is equal to the phone number' do
      let(:same_customer) do
        create(:customer, retailer: retailer, phone: '+5215599999999', number_to_use: '+5215599999999',
                          country_id: 'MX')
      end

      let!(:gsm) do
        create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: same_customer,
                                                     message_payload: text_payload, status: 'error',
                                                     error_payload: error_1005_response)
      end

      it 'does not retry sending the message to the auxiliar number' do
        expect do
          gsm.send(:retry_message)
        end.not_to change(described_class, :count)
      end
    end

    context 'when the message destination is equal to the number to use' do
      let(:same_customer) do
        create(:customer, retailer: retailer, phone: '+5215599999999', number_to_use: '+525599999999',
                          country_id: 'MX')
      end

      let!(:gsm) do
        create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: same_customer,
                                                     message_payload: text_payload, status: 'error',
                                                     error_payload: error_1005_response)
      end

      it 'does not retry sending the message to the auxiliar number' do
        expect do
          gsm.send(:retry_message)
        end.not_to change(described_class, :count)
      end
    end

    context 'when all the requirements are fullfiled' do
      before do
        allow_any_instance_of(Whatsapp::Outbound::Base).to receive(:post).and_return(net_response)
        allow_any_instance_of(Net::HTTPOK).to receive(:read_body).and_return(ok_body_response)
      end

      context 'when it is a text message' do
        let!(:gsm) do
          create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer_to_use,
                                                       message_payload: text_payload, status: 'error',
                                                       error_payload: error_1005_response,
                                                       destination: customer_to_use.phone_number(false))
        end

        it 'sends the message to the auxiliar number' do
          expect do
            gsm.send(:retry_message)
          end.to change(described_class, :count).by(1)

          last_message = described_class.last

          expect(gsm.message_payload).to eq(last_message.message_payload)
          expect(last_message.destination).to eq(customer_to_use.phone_number_to_use(false))
        end
      end

      context 'when it is a template message' do
        let!(:gsm) do
          create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer_to_use,
                                                       message_payload: template_payload, status: 'error',
                                                       error_payload: error_1005_response,
                                                       destination: customer_to_use.phone_number(false))
        end

        it 'sends the message to the auxiliar number' do
          expect do
            gsm.send(:retry_message)
          end.to change(described_class, :count).by(1)

          last_message = described_class.last

          expect(gsm.message_payload).to eq(last_message.message_payload)
          expect(last_message.destination).to eq(customer_to_use.phone_number_to_use(false))
        end
      end

      context 'when it is a document message' do
        let!(:gsm) do
          create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer_to_use,
                                                       message_payload: document_payload, status: 'error',
                                                       error_payload: error_1005_response,
                                                       destination: customer_to_use.phone_number(false))
        end

        it 'sends the message to the auxiliar number' do
          expect do
            gsm.send(:retry_message)
          end.to change(described_class, :count).by(1)

          last_message = described_class.last

          expect(gsm.message_payload).to eq(last_message.message_payload)
          expect(last_message.destination).to eq(customer_to_use.phone_number_to_use(false))
        end
      end

      context 'when it is a document message as template' do
        let!(:gsm) do
          create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer_to_use,
                                                       message_payload: document_template_payload, status: 'error',
                                                       error_payload: error_1005_response,
                                                       destination: customer_to_use.phone_number(false))
        end

        it 'sends the message to the auxiliar number', skip: 'document template retry not implemented yet' do
          expect do
            gsm.send(:retry_message)
          end.to change(described_class, :count).by(1)

          last_message = described_class.last

          expect(gsm.message_payload).to eq(last_message.message_payload)
          expect(last_message.destination).to eq(customer_to_use.phone_number_to_use(false))
        end
      end

      context 'when it is an image message' do
        let!(:gsm) do
          create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer_to_use,
                                                       message_payload: image_payload, status: 'error',
                                                       error_payload: error_1005_response,
                                                       destination: customer_to_use.phone_number(false))
        end

        it 'sends the message to the auxiliar number' do
          expect do
            gsm.send(:retry_message)
          end.to change(described_class, :count).by(1)

          last_message = described_class.last

          expect(gsm.message_payload).to eq(last_message.message_payload)
          expect(last_message.destination).to eq(customer_to_use.phone_number_to_use(false))
        end
      end

      context 'when it is an image message with caption' do
        let!(:gsm) do
          create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer_to_use,
                                                       message_payload: image_with_caption_payload, status: 'error',
                                                       error_payload: error_1005_response,
                                                       destination: customer_to_use.phone_number(false))
        end

        it 'sends the message to the auxiliar number' do
          expect do
            gsm.send(:retry_message)
          end.to change(described_class, :count).by(1)

          last_message = described_class.last

          expect(gsm.message_payload).to eq(last_message.message_payload)
          expect(last_message.destination).to eq(customer_to_use.phone_number_to_use(false))
        end
      end

      context 'when it is a location message' do
        let!(:gsm) do
          create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer_to_use,
                                                       message_payload: location_payload, status: 'error',
                                                       error_payload: error_1005_response,
                                                       destination: customer_to_use.phone_number(false))
        end

        it 'sends the message to the auxiliar number' do
          expect do
            gsm.send(:retry_message)
          end.to change(described_class, :count).by(1)

          last_message = described_class.last

          expect(gsm.message_payload).to eq(last_message.message_payload)
          expect(last_message.destination).to eq(customer_to_use.phone_number_to_use(false))
        end
      end

      context 'when it is an audio message' do
        let!(:gsm) do
          create(:gupshup_whatsapp_message, :outbound, retailer: retailer, customer: customer_to_use,
                                                       message_payload: audio_payload, status: 'error',
                                                       error_payload: error_1005_response,
                                                       destination: customer_to_use.phone_number(false))
        end

        it 'sends the message to the auxiliar number' do
          expect do
            gsm.send(:retry_message)
          end.to change(described_class, :count).by(1)

          last_message = described_class.last

          expect(gsm.message_payload).to eq(last_message.message_payload)
          expect(last_message.destination).to eq(customer_to_use.phone_number_to_use(false))
        end
      end
    end
  end

  describe '#set_sender_information' do
    let(:set_gupshup_messages_service) { instance_double(Whatsapp::Outbound::Msg) }
    let(:retailer) { create(:retailer, :gupshup_integrated) }
    let(:retailer_user) do
      create(:retailer_user, retailer: retailer, first_name: 'Test', last_name: 'Example', email: '<EMAIL>')
    end
    let(:message) do
      build(:gupshup_whatsapp_message, :outbound, retailer: retailer, retailer_user: retailer_user)
    end

    before do
      allow(set_gupshup_messages_service).to receive(:send_message)
        .and_return('Sent')
      allow(Whatsapp::Outbound::Msg).to receive(:new)
        .and_return(set_gupshup_messages_service)
    end

    it 'saves the sender information in the message' do
      expect(message.sender_first_name).to be_nil
      message.save
      expect(message.sender_first_name).to eq(retailer_user.first_name)
      expect(message.sender_last_name).to eq(retailer_user.last_name)
      expect(message.sender_email).to eq(retailer_user.email)
    end
  end

  describe '#insert_on_agent_queue' do
    let(:retailer) { create(:retailer, :gupshup_integrated) }
    let(:team_assignment) { create(:team_assignment, :assigned_default, retailer: retailer, whatsapp: true) }
    let(:agent1) { create(:retailer_user, retailer: retailer) }
    let(:agent2) { create(:retailer_user, retailer: retailer) }
    let(:agent3) { create(:retailer_user, retailer: retailer) }
    let!(:agent_team1) { create(:agent_team, team_assignment: team_assignment, retailer_user: agent1) }
    let!(:agent_team2) { create(:agent_team, team_assignment: team_assignment, retailer_user: agent2) }
    let!(:agent_team3) { create(:agent_team, team_assignment: team_assignment, retailer_user: agent3) }
    let(:customer1) { create(:customer, retailer: retailer) }
    let(:customer2) { create(:customer, retailer: retailer) }
    let(:customer3) { create(:customer, retailer: retailer) }
    let(:message1) { build(:gupshup_whatsapp_message, :inbound, retailer: retailer, customer: customer1) }
    let(:message2) { build(:gupshup_whatsapp_message, :inbound, retailer: retailer, customer: customer2) }
    let(:message3) { build(:gupshup_whatsapp_message, :inbound, retailer: retailer, customer: customer3) }

    # TODO: En esta versión, simplemente guardamos los mensajes uno tras otro.
    # Esto debería ser suficiente para probar que cada equipo de agentes recibe una asignación.
    # Sin embargo, si es crucial probar el comportamiento concurrente, podrías considerar las siguientes alternativas:
    # - Usar ActiveJob
    # - Usar threads de Ruby
    it 'assigns one chat to every agent team' do
      retailer.payment_plan.update(plan: :advanced, contact_based: false)

      message1.save
      message2.save
      message3.save

      expect(agent_team1.reload.assigned_amount).to eq(1)
      expect(agent_team2.reload.assigned_amount).to eq(1)
      expect(agent_team3.reload.assigned_amount).to eq(1)
      expect(team_assignment.reload.last_assigned).to eq(agent_team3.id)
    end
  end

  describe '#increase_unread_counter' do
    let(:retailer) { create(:retailer, :gupshup_integrated) }
    let(:customer) { create(:customer, retailer: retailer, count_unread_messages: 0) }
    let(:message) { build(:gupshup_whatsapp_message, :inbound, customer: customer, retailer: retailer) }
    let(:message_out) { create(:gupshup_whatsapp_message, :outbound, customer: customer, retailer: retailer) }

    context 'when message is not inbound' do
      it 'returns nil' do
        expect(message_out.send(:increase_unread_counter)).to be_nil
      end
    end

    context 'when message is inbound' do
      it 'increases by 1 the customer unread counter' do
        message.save

        expect(customer.count_unread_messages).to eq(1)
      end

      context 'when the customer is not assigned' do
        it 'increases by 1 the total counter and unassigned counter' do
          message.save

          expect(retailer.retailer_counter.ws_messages).to eq(1)
          expect(retailer.retailer_counter.ws_messages_na).to eq(1)
        end
      end

      context 'when the customer is assigned' do
        let(:retailer_user) { create(:retailer_user, retailer: retailer) }
        let!(:agent_customer) { create(:agent_customer, retailer_user: retailer_user, customer: customer) }

        before do
          customer.update(retailer_user_id: retailer_user.id)
        end

        it 'increases by 1 the agent counter and total counter' do
          message.save

          expect(retailer.retailer_counter.ws_messages).to eq(1)
          expect(retailer_user.reload.unread_whatsapp_chats_count).to eq(1)
        end
      end
    end
  end

  describe '#inbound_media?' do
    let(:retailer) { create(:retailer, :gupshup_integrated) }
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }
    let(:payload) do
      {
        payload: {
          type: 'image'
        }
      }
    end

    let(:payload1) do
      {
        payload: {
          type: 'sticker'
        }
      }
    end

    let(:payload2) do
      {
        payload: {
          type: 'file'
        }
      }
    end

    context 'when direction is "inbound"' do
      let(:message) do
        create(:gupshup_whatsapp_message, :inbound, retailer: retailer,
                                                    retailer_user: retailer_user,
                                                    message_payload: payload)
      end

      let(:message1) do
        create(:gupshup_whatsapp_message, :inbound, retailer: retailer,
                                                    retailer_user: retailer_user,
                                                    message_payload: payload1)
      end

      let(:message2) do
        create(:gupshup_whatsapp_message, :inbound, retailer: retailer,
                                                    retailer_user: retailer_user,
                                                    message_payload: payload2)
      end

      context 'when includes media type image/sticker/file' do
        it 'returns true' do
          expect(message.send(:inbound_media?)).to be(true)
          expect(message1.send(:inbound_media?)).to be(true)
          expect(message2.send(:inbound_media?)).to be(true)
        end
      end
    end

    context 'when direction is not "inbound"' do
      let(:payload) do
        {
          payload: {
            type: 'image'
          }
        }
      end

      let(:message) do
        create(:gupshup_whatsapp_message, :outbound, retailer: retailer,
                                                     retailer_user: retailer_user,
                                                     message_payload: payload)
      end

      it 'returns false' do
        expect(message.send(:inbound_media?)).to be(false)
      end
    end
  end

  describe '#get_media_caption' do
    let(:text_payload) do
      {
        app: 'Fransur',
        timestamp: 171_225_991_404_3,
        version: 2,
        type: 'message',
        payload: {
          id: 'ABEGWEFFIjd2AhDvfeXwrNKN9f-zEenBOu2V',
          source: '584145220000',
          type: 'text',
          payload: {
            text: 'Prueba'
          },
          sender: {
            phone: '584145220000',
            name: 'Elvis Olivar',
            country_code: '58',
            dial_code: '4145220000'
          }
        }
      }
    end

    let(:image_payload) do
      {
        app: 'Fransur',
        timestamp: 171_224_566_003_2,
        version: 2,
        type: 'message',
        payload: {
          id: 'ABEGWEFFIjd2AhD9n0igiRW6ZIg8ZdD-F_V9',
          source: '584145220000',
          type: 'image',
          payload: {
            caption: 'Esto es un caption',
            url: 'https://filemanager.gupshup.io/wa/d08fb0a7-c48c-4640-b50a-ecef0ed1a2a1/wa/media/',
            contentType: 'image/jpeg',
            urlExpiry: 171_224_566_003_2
          },
          sender: {
            phone: '584145220000',
            name: 'Elvis Olivar',
            country_code: '58',
            dial_code: '4145220000'
          }
        }
      }
    end

    context 'when the message is not media' do
      let(:gsm) { create(:gupshup_whatsapp_message, message_payload: text_payload) }

      it 'returns nil' do
        expect(gsm.get_media_caption).to be_nil
      end
    end

    context 'when the message is media' do
      let(:gsm) { create(:gupshup_whatsapp_message, message_payload: image_payload) }

      it 'returns the caption' do
        expect(gsm.get_media_caption).to eq('Esto es un caption')
      end
    end
  end

  describe '#chat_bot_execution' do
    context 'when the message is media' do
      let(:image_payload) do
        {
          app: 'Fransur',
          timestamp: 171_224_566_003_2,
          version: 2,
          type: 'message',
          payload: {
            id: 'ABEGWEFFIjd2AhD9n0igiRW6ZIg8ZdD-F_V9',
            source: '584145220000',
            type: 'image',
            payload: {
              caption: 'Esto es un caption',
              url: 'https://filemanager.gupshup.io/wa/d08fb0a7-c48c-4640-b50a-ecef0ed1a2a1/wa/media/',
              contentType: 'image/jpeg',
              urlExpiry: 171_224_566_003_2
            },
            sender: {
              phone: '584145220000',
              name: 'Elvis Olivar',
              country_code: '58',
              dial_code: '4145220000'
            }
          }
        }
      end

      let(:gsm) { create(:gupshup_whatsapp_message, :inbound, message_payload: image_payload) }

      before do
        allow(gsm).to receive(:get_current_bot).and_return(true)
      end

      it 'assigns true to @media' do
        gsm.send(:chat_bot_execution)
        expect(gsm.instance_variable_get(:@media)).to be_truthy
      end

      context 'when message has text' do
        it 'assigns false to @media_selection' do
          gsm.send(:chat_bot_execution)
          expect(gsm.instance_variable_get(:@media_selection)).to be_falsy
        end
      end

      context 'when message does not have text' do
        let(:image_payload) do
          {
            app: 'Fransur',
            timestamp: 171_224_566_003_2,
            version: 2,
            type: 'message',
            payload: {
              id: 'ABEGWEFFIjd2AhD9n0igiRW6ZIg8ZdD-F_V9',
              source: '584145220000',
              type: 'image',
              payload: {
                caption: '',
                url: 'https://filemanager.gupshup.io/wa/d08fb0a7-c48c-4640-b50a-ecef0ed1a2a1/wa/media/',
                contentType: 'image/jpeg',
                urlExpiry: 171_224_566_003_2
              },
              sender: {
                phone: '584145220000',
                name: 'Elvis Olivar',
                country_code: '58',
                dial_code: '4145220000'
              }
            }
          }
        end

        let(:gsm) { create(:gupshup_whatsapp_message, :inbound, message_payload: image_payload) }

        it 'assigns true to @media_selection' do
          gsm.send(:chat_bot_execution)
          expect(gsm.instance_variable_get(:@media_selection)).to be_truthy
        end
      end
    end

    context 'when the message is not media' do
      let(:text_payload) do
        {
          app: 'Fransur',
          timestamp: 171_225_991_404_3,
          version: 2,
          type: 'message',
          payload: {
            id: 'ABEGWEFFIjd2AhDvfeXwrNKN9f-zEenBOu2V',
            source: '584145220000',
            type: 'text',
            payload: {
              text: 'Prueba'
            },
            sender: {
              phone: '584145220000',
              name: 'Elvis Olivar',
              country_code: '58',
              dial_code: '4145220000'
            }
          }
        }
      end

      let(:gsm) { create(:gupshup_whatsapp_message, :inbound, message_payload: text_payload) }

      it 'assigns false to @media' do
        gsm.send(:chat_bot_execution)
        expect(gsm.instance_variable_get(:@media)).to be_falsey
      end
    end
  end

  describe '#get_current_bot' do
    let(:retailer) { create(:retailer, :gupshup_integrated) }

    context 'with chatbots' do
      let!(:support_chat_bot) do
        create(:chat_bot, :for_whatsapp, retailer: retailer, triggers: ['I need help'], enabled: true)
      end

      let!(:chat_bot_v2) do
        create(:chat_bot, :for_whatsapp, retailer: retailer,
                                         version: 2,
                                         enabled: true,
                                         triggers: ['hello support', 'Help me'])
      end

      let!(:chat_bot_v2_any_interaction) do
        create(:chat_bot, :for_whatsapp, :with_any_interaction, retailer: retailer, version: 2, enabled: true)
      end

      context 'when the message is not media' do
        let(:text_payload) do
          {
            app: 'Fransur',
            timestamp: 171_225_991_404_3,
            version: 2,
            type: 'message',
            payload: {
              id: 'ABEGWEFFIjd2AhDvfeXwrNKN9f-zEenBOu2V',
              source: '584145220000',
              type: 'text',
              payload: {
                text: 'HTTPS://YOUTUBE.COM I need help hTTps://mercately.com/precios'
              },
              sender: {
                phone: '584145220000',
                name: 'Elvis Olivar',
                country_code: '58',
                dial_code: '4145220000'
              }
            }
          }
        end

        let(:gsm) { create(:gupshup_whatsapp_message, :inbound, retailer: retailer, message_payload: text_payload) }

        it 'activates the bot by trigger' do
          subject.instance_variable_set(:@text, 'HTTPS://YOUTUBE.COM I need help hTTps://mercately.com/precios')
          expect(gsm.send(:get_current_bot)).to eq(support_chat_bot)
        end
      end

      context 'when the message is media' do
        context 'with caption' do
          context 'with a bot with the trigger' do
            let(:image_payload) do
              {
                app: 'Fransur',
                timestamp: 171_224_566_003_2,
                version: 2,
                type: 'message',
                payload: {
                  id: 'ABEGWEFFIjd2AhD9n0igiRW6ZIg8ZdD-F_V9',
                  source: '584145220000',
                  type: 'image',
                  payload: {
                    caption: 'HTTPS://YOUTUBE.COM  Help me hTTps://mercately.com/precios',
                    url: 'https://filemanager.gupshup.io/wa/d08fb0a7-c48c-4640-b50a-ecef0ed1a2a1/wa/media/',
                    contentType: 'image/jpeg',
                    urlExpiry: 171_224_566_003_2
                  },
                  sender: {
                    phone: '584145220000',
                    name: 'Elvis Olivar',
                    country_code: '58',
                    dial_code: '4145220000'
                  }
                }
              }
            end

            let(:gsm) { create(:gupshup_whatsapp_message, :inbound, retailer:, message_payload: image_payload) }

            it 'activates the bot by trigger' do
              subject.instance_variable_set(:@text, 'HTTPS://YOUTUBE.COM  Help me hTTps://mercately.com/precios')
              expect(gsm.send(:get_current_bot)).to eq(chat_bot_v2)
            end
          end

          context 'without a bot with the trigger' do
            let(:image_payload) do
              {
                app: 'Fransur',
                timestamp: 171_224_566_003_2,
                version: 2,
                type: 'message',
                payload: {
                  id: 'ABEGWEFFIjd2AhD9n0igiRW6ZIg8ZdD-F_V9',
                  source: '584145220000',
                  type: 'image',
                  payload: {
                    caption: 'prueba',
                    url: 'https://filemanager.gupshup.io/wa/d08fb0a7-c48c-4640-b50a-ecef0ed1a2a1/wa/media/',
                    contentType: 'image/jpeg',
                    urlExpiry: 171_224_566_003_2
                  },
                  sender: {
                    phone: '584145220000',
                    name: 'Elvis Olivar',
                    country_code: '58',
                    dial_code: '4145220000'
                  }
                }
              }
            end

            let(:gsm) { create(:gupshup_whatsapp_message, :inbound, retailer:, message_payload: image_payload) }

            it 'activates the bot by any interaction' do
              subject.instance_variable_set(:@text, 'prueba')
              expect(gsm.send(:get_current_bot)).to eq(chat_bot_v2_any_interaction)
            end
          end
        end

        context 'when the message does not have caption' do
          let(:image_payload) do
            {
              app: 'Fransur',
              timestamp: 171_224_566_003_2,
              version: 2,
              type: 'message',
              payload: {
                id: 'ABEGWEFFIjd2AhD9n0igiRW6ZIg8ZdD-F_V9',
                source: '584145220000',
                type: 'image',
                payload: {
                  caption: '',
                  url: 'https://filemanager.gupshup.io/wa/d08fb0a7-c48c-4640-b50a-ecef0ed1a2a1/wa/media/',
                  contentType: 'image/jpeg',
                  urlExpiry: 171_224_566_003_2
                },
                sender: {
                  phone: '584145220000',
                  name: 'Elvis Olivar',
                  country_code: '58',
                  dial_code: '4145220000'
                }
              }
            }
          end

          let(:gsm) { create(:gupshup_whatsapp_message, :inbound, retailer:, message_payload: image_payload) }

          it 'activates the bot with any interaction' do
            subject.instance_variable_set(:@text, '')
            expect(gsm.send(:get_current_bot)).to eq(chat_bot_v2_any_interaction)
          end
        end
      end
    end

    context 'without chatbots' do
      context 'when the message does not have caption' do
        let(:image_payload) do
          {
            app: 'Fransur',
            timestamp: 171_224_566_003_2,
            version: 2,
            type: 'message',
            payload: {
              id: 'ABEGWEFFIjd2AhD9n0igiRW6ZIg8ZdD-F_V9',
              source: '584145220000',
              type: 'image',
              payload: {
                url: 'https://filemanager.gupshup.io/wa/d08fb0a7-c48c-4640-b50a-ecef0ed1a2a1/wa/media/',
                contentType: 'image/jpeg',
                urlExpiry: 171_224_566_003_2
              },
              sender: {
                phone: '584145220000',
                name: 'Elvis Olivar',
                country_code: '58',
                dial_code: '4145220000'
              }
            }
          }
        end

        let(:gsm) { create(:gupshup_whatsapp_message, :inbound, retailer:, message_payload: image_payload) }

        it { expect(gsm.send(:get_current_bot)).to be_nil }
      end
    end
  end

  describe '#update_customer_last_message' do
    let(:message) { build(:gupshup_whatsapp_message) }
    let(:create_or_update_service) { class_double(CustomerMessages::CreateOrUpdate) }

    before do
      allow(CustomerMessages::CreateOrUpdate).to receive(:call).and_return(true)
    end

    it 'calls the CustomerMessages::CreateOrUpdate service' do
      message.save!
      expect(CustomerMessages::CreateOrUpdate).to have_received(:call).with(message)
    end

    it 'logs an error if the service raises an exception' do
      allow(CustomerMessages::CreateOrUpdate).to receive(:call).and_raise(StandardError, 'Test error')
      expect(Rails.logger).to receive(:error).with('Failed to update CustomerLastMessage: Test error')
      message.save!
    end
  end
end
