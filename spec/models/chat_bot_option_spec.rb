require 'rails_helper'

RSpec.describe ChatBotOption do
  subject(:chat_bot_option) { create(:chat_bot_option, chat_bot: chat_bot) }

  let(:retailer) { create(:retailer) }
  let(:chat_bot) { create(:chat_bot, retailer: retailer) }

  describe 'associations' do
    it { is_expected.to have_many(:customer_bot_options).dependent(:destroy) }
    it { is_expected.to belong_to(:chat_bot) }
    it { is_expected.to have_many(:customers).through(:customer_bot_options) }
    it { is_expected.to have_many(:chat_bot_actions).dependent(:destroy) }
    it { is_expected.to have_many(:option_sub_lists).dependent(:destroy) }
    it { is_expected.to have_many(:additional_bot_answers).dependent(:destroy) }
    it { is_expected.to have_one_attached(:file) }
    it { is_expected.to have_one(:chat_bot_option_api).dependent(:destroy) }
    it { is_expected.to have_one(:chat_bot_option_form).dependent(:destroy) }
  end

  describe 'nested attributes' do
    it { is_expected.to accept_nested_attributes_for(:chat_bot_actions).allow_destroy(true) }
    it { is_expected.to accept_nested_attributes_for(:option_sub_lists).allow_destroy(true) }
    it { is_expected.to accept_nested_attributes_for(:additional_bot_answers).allow_destroy(true) }
    it { is_expected.to accept_nested_attributes_for(:chat_bot_option_api).allow_destroy(true) }
    it { is_expected.to accept_nested_attributes_for(:chat_bot_option_form).allow_destroy(true) }
  end

  describe 'enums' do
    it 'defines enum for option_type' do
      expect(subject)
        .to define_enum_for(:option_type)
        .with_values(%i[decision form message jump_to failure active_mia api resolve_chat])
    end

    it { is_expected.to define_enum_for(:interactive).with_values(text: 0, buttons: 1, list: 2, fake: 3) }

    it do
      expect(described_class.message_types).to eq({ 'text' => 0, 'image' => 1, 'document' => 2,
                                                    'audio' => 3, 'video' => 4 })
    end

    it do
      expect(described_class.answer_types).to eq({ 'text' => 0, 'anything' => 1, 'unique' => 2, 'location' => 3 })
    end
  end

  describe 'scopes' do
    describe '.active' do
      let!(:active_option) { create(:chat_bot_option, chat_bot: chat_bot, option_deleted: false) }
      let!(:deleted_option) { create(:chat_bot_option, chat_bot: chat_bot, option_deleted: true) }

      it 'returns active options' do
        expect(described_class.active).to include(active_option)
      end

      it 'does not return deleted options' do
        expect(described_class.active).not_to include(deleted_option)
      end
    end

    describe '.fake_options' do
      let!(:fake_option) { create(:chat_bot_option, :fake_option, chat_bot: chat_bot) }
      let!(:normal_option) { create(:chat_bot_option, chat_bot: chat_bot, fake_option: false) }

      it 'returns only fake options' do
        expect(described_class.fake_options).to include(fake_option)
        expect(described_class.fake_options).not_to include(normal_option)
      end
    end

    describe '.positioned' do
      let!(:option1) { create(:chat_bot_option, chat_bot: chat_bot, position: 1) }
      let!(:option2) { create(:chat_bot_option, chat_bot: chat_bot, position: 2) }
      let!(:option3) { create(:chat_bot_option, chat_bot: chat_bot, position: 3) }

      it 'returns options ordered by position' do
        positioned_options = described_class.positioned.where(chat_bot: chat_bot).to_a
        expect(positioned_options).to eq([option1, option2, option3])
      end
    end

    describe '.active_index' do
      let!(:active_option) { create(:chat_bot_option, chat_bot: chat_bot, option_deleted: false) }
      let!(:deleted_option) { create(:chat_bot_option, chat_bot: chat_bot, option_deleted: true) }

      it 'returns only non-deleted options' do
        expect(described_class.active_index).to include(active_option)
        expect(described_class.active_index).not_to include(deleted_option)
      end
    end
  end

  describe 'instance methods' do
    describe '#file_url' do
      let(:option) { create(:chat_bot_option, :with_image_file, chat_bot: chat_bot) }

      context 'when file is from AWS' do
        before do
          allow(option.file).to receive_messages(
            is_aws: true,
            public_url: 'https://s3.amazonaws.com/test.jpg'
          )
        end

        it 'returns AWS public URL' do
          expect(option.file_url).to eq('https://s3.amazonaws.com/test.jpg')
        end
      end

      context 'when file is video content' do
        before do
          allow(option.file).to receive_messages(
            is_aws: false,
            content_type: 'video/mp4',
            key: 'test_video_key'
          )
          allow(ENV).to receive(:fetch).with('CLOUDINARY_CLOUD_NAME', nil).and_return('test_cloud')
        end

        it 'returns Cloudinary video URL' do
          expected_url = 'https://res.cloudinary.com/test_cloud/video/upload/test_video_key'
          expect(option.file_url).to eq(expected_url)
        end
      end

      context 'when file is audio content' do
        before do
          allow(option.file).to receive_messages(
            is_aws: false,
            content_type: 'audio/mp3',
            key: 'test_audio_key'
          )
          allow(ENV).to receive(:fetch).with('CLOUDINARY_CLOUD_NAME', nil).and_return('test_cloud')
        end

        it 'returns Cloudinary video URL for audio' do
          expected_url = 'https://res.cloudinary.com/test_cloud/video/upload/test_audio_key'
          expect(option.file_url).to eq(expected_url)
        end
      end

      context 'when file is image content' do
        before do
          allow(option.file).to receive_messages(
            is_aws: false,
            content_type: 'image/jpeg',
            key: 'test_image_key'
          )
          allow(ENV).to receive(:fetch).with('CLOUDINARY_CLOUD_NAME', nil).and_return('test_cloud')
        end

        it 'returns Cloudinary image URL' do
          expected_url = 'https://res.cloudinary.com/test_cloud/image/upload/test_image_key'
          expect(option.file_url).to eq(expected_url)
        end
      end
    end

    describe '#file_type' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

      context 'when no file is attached' do
        it 'returns nil' do
          expect(option.file_type).to be_nil
        end
      end

      context 'when file is an image' do
        let(:option) { create(:chat_bot_option, :with_image_file, chat_bot: chat_bot) }

        before do
          allow(option.file.blob).to receive(:content_type).and_return('image/jpeg')
        end

        it 'returns image' do
          expect(option.file_type).to eq('image')
        end
      end

      context 'when file is a PDF' do
        let(:option) { create(:chat_bot_option, :with_pdf_file, chat_bot: chat_bot) }

        before do
          allow(option.file.blob).to receive(:content_type).and_return('application/pdf')
        end

        it 'returns file' do
          expect(option.file_type).to eq('file')
        end
      end

      context 'when file is audio' do
        let(:option) { create(:chat_bot_option, :with_image_file, chat_bot: chat_bot) }

        before do
          allow(option.file.blob).to receive(:content_type).and_return('audio/mp3')
        end

        it 'returns audio' do
          expect(option.file_type).to eq('audio')
        end
      end

      context 'when file is video' do
        let(:option) { create(:chat_bot_option, :with_image_file, chat_bot: chat_bot) }

        before do
          allow(option.file.blob).to receive(:content_type).and_return('video/mp4')
        end

        it 'returns video' do
          expect(option.file_type).to eq('video')
        end
      end
    end

    describe '#execute_endpoint?' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

      context 'when option has exec_callback action' do
        before do
          create(:chat_bot_action, chat_bot_option: option, action_type: :exec_callback)
        end

        it 'returns true' do
          expect(option.execute_endpoint?).to be true
        end
      end

      context 'when option has no exec_callback action' do
        it 'returns false' do
          expect(option.execute_endpoint?).to be false
        end
      end
    end

    describe '#jump_to_option?' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

      context 'when option has jump_to_option action' do
        before do
          create(:chat_bot_action, chat_bot_option: option, action_type: :jump_to_option)
        end

        it 'returns true' do
          expect(option.jump_to_option?).to be true
        end
      end

      context 'when option has no jump_to_option action' do
        it 'returns false' do
          expect(option.jump_to_option?).to be false
        end
      end
    end

    describe '#has_sub_list?' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

      context 'when option has sub lists' do
        before do
          create(:option_sub_list, chat_bot_option: option)
          option.reload
        end

        it 'returns true' do
          expect(option.has_sub_list?).to be true
        end
      end

      context 'when option has no sub lists' do
        it 'returns false' do
          expect(option.has_sub_list?).to be false
        end
      end
    end

    describe '#is_auto_generated?' do
      let(:parent_option) { create(:chat_bot_option, chat_bot: chat_bot) }
      let(:child_option) { create(:chat_bot_option, chat_bot: chat_bot, parent: parent_option) }

      context 'when option has no parent' do
        it 'returns false' do
          expect(parent_option.is_auto_generated?).to be false
        end
      end

      context 'when parent has auto_generate_option action' do
        before do
          create(:chat_bot_action, chat_bot_option: parent_option, action_type: :auto_generate_option)
        end

        it 'returns true' do
          expect(child_option.is_auto_generated?).to be true
        end
      end

      context 'when parent has no auto_generate_option action' do
        it 'returns false' do
          expect(child_option.is_auto_generated?).to be false
        end
      end
    end

    describe '#has_additional_answers_filled?' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

      context 'when no additional answers exist' do
        it 'returns false' do
          expect(option.has_additional_answers_filled?).to be false
        end
      end

      context 'when additional answer has text' do
        before do
          create(:additional_bot_answer, chat_bot_option: option, text: 'Some text')
        end

        it 'returns true' do
          expect(option.has_additional_answers_filled?).to be true
        end
      end

      context 'when additional answer has file attached' do
        before do
          answer = create(:additional_bot_answer, chat_bot_option: option)
          answer.file.attach(create_file_blob)
        end

        it 'returns true' do
          expect(option.has_additional_answers_filled?).to be true
        end
      end

      context 'when additional answer has neither text nor file' do
        before do
          create(:additional_bot_answer, chat_bot_option: option, text: nil)
        end

        it 'returns false' do
          expect(option.has_additional_answers_filled?).to be false
        end
      end
    end

    describe '#has_return_options?' do
      context 'when go_past_option is true' do
        it 'returns true' do
          option = build(:chat_bot_option, go_past_option: true, chat_bot: chat_bot)
          expect(option.has_return_options?).to be true
        end
      end

      context 'when go_start_option is true' do
        it 'returns true' do
          option = build(:chat_bot_option, go_start_option: true, chat_bot: chat_bot)
          expect(option.has_return_options?).to be true
        end
      end

      context 'when both options are false' do
        it 'returns false' do
          option = build(:chat_bot_option, go_past_option: false, go_start_option: false, chat_bot: chat_bot)
          expect(option.has_return_options?).to be false
        end
      end
    end

    describe '#has_dynamic_list?' do
      let(:parent_option) { create(:chat_bot_option, chat_bot: chat_bot) }
      let(:child_option) { create(:chat_bot_option, chat_bot: chat_bot, parent: parent_option) }
      let(:customer) { create(:customer, retailer: retailer) }

      context 'when option has no parent' do
        it 'returns false' do
          expect(parent_option.has_dynamic_list?(customer)).to be false
        end
      end

      context 'when customer has successful bot response with options' do
        before do
          response_double = double('Response', options: %w[option1 option2])
          bot_response = double('CustomerBotResponse', response: response_double)

          allow(customer.customer_bot_responses).to receive_message_chain(:where, :first).and_return(bot_response)
        end

        it 'returns true' do
          expect(child_option.has_dynamic_list?(customer)).to be true
        end
      end

      context 'when customer has no bot response' do
        before do
          allow(customer.customer_bot_responses).to receive_message_chain(:where, :first).and_return(nil)
        end

        it 'returns false' do
          expect(child_option.has_dynamic_list?(customer)).to be false
        end
      end

      context 'when customer bot response has no options' do
        before do
          response_double = double('Response', options: nil)
          bot_response = double('CustomerBotResponse', response: response_double)

          allow(customer.customer_bot_responses).to receive_message_chain(:where, :first).and_return(bot_response)
        end

        it 'returns false' do
          expect(child_option.has_dynamic_list?(customer)).to be false
        end
      end
    end

    describe '#items_list' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

      context 'when option has no return options' do
        before do
          create_list(:option_sub_list, 2, chat_bot_option: option)
          option.update(go_past_option: false, go_start_option: false)
          option.reload
        end

        it 'returns only option_sub_lists' do
          expect(option.items_list).to eq(option.option_sub_lists)
        end
      end

      context 'when option has go_past_option enabled' do
        let(:option) { create(:chat_bot_option, chat_bot: chat_bot, go_past_option: true) }

        before do
          create_list(:option_sub_list, 2, chat_bot_option: option)
          option.reload
        end

        it 'includes back option' do
          items = option.items_list
          expect(items.size).to be >= 3
          back_item = items.find { |item| item.value_to_save == '_back_' }
          expect(back_item).to be_present
          expect(back_item.value_to_show).to eq('Volver')
        end
      end

      context 'when option has go_start_option enabled' do
        let(:option) { create(:chat_bot_option, chat_bot: chat_bot, go_start_option: true) }

        before do
          create_list(:option_sub_list, 2, chat_bot_option: option)
          option.reload
        end

        it 'includes start option' do
          items = option.items_list
          expect(items.size).to be >= 3
          start_item = items.find { |item| item.value_to_save == '_start_' }
          expect(start_item).to be_present
          expect(start_item.value_to_show).to eq('Ir al menú principal')
        end
      end

      context 'when option has both return options enabled' do
        let(:option) { create(:chat_bot_option, chat_bot: chat_bot, go_past_option: true, go_start_option: true) }

        before do
          create_list(:option_sub_list, 2, chat_bot_option: option)
          option.reload
        end

        it 'includes both back and start options' do
          items = option.items_list
          expect(items.size).to be >= 4

          back_item = items.find { |item| item.value_to_save == '_back_' }
          expect(back_item).to be_present
          expect(back_item.value_to_show).to eq('Volver')

          start_item = items.find { |item| item.value_to_save == '_start_' }
          expect(start_item).to be_present
          expect(start_item.value_to_show).to eq('Ir al menú principal')
        end
      end
    end

    describe '#height and #width' do
      let(:option) { create(:chat_bot_option, :with_image_file, chat_bot: chat_bot) }

      context 'when file has metadata' do
        before do
          allow(option.file).to receive(:metadata).and_return({ 'height' => 100, 'width' => 200 })
        end

        it 'returns height from metadata' do
          expect(option.height).to eq(100)
        end

        it 'returns width from metadata' do
          expect(option.width).to eq(200)
        end
      end
    end

    describe '#failure_option' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }
      let!(:failure_child) { create(:chat_bot_option, :fake_option, :failure, chat_bot: chat_bot, parent: option) }
      let!(:normal_child) { create(:chat_bot_option, :fake_option, chat_bot: chat_bot, parent: option) }

      it 'returns the failure type fake option child' do
        expect(option.failure_option).to eq(failure_child)
      end

      context 'when failure option is deleted' do
        before { failure_child.update(option_deleted: true) }

        it 'returns nil' do
          expect(option.failure_option).to be_nil
        end
      end
    end

    describe '#first_active_option' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

      context 'when no children exist' do
        it 'returns nil' do
          expect(option.first_active_option).to be_nil
        end
      end

      context 'when first child is not fake' do
        let!(:child) { create(:chat_bot_option, chat_bot: chat_bot, parent: option) }

        it 'returns the first child' do
          expect(option.first_active_option).to eq(child)
        end
      end

      context 'when first child is fake and parent is form' do
        let(:form_option) { create(:chat_bot_option, :form, chat_bot: chat_bot) }
        let!(:fake_child) { create(:chat_bot_option, :fake_option, chat_bot: chat_bot, parent: form_option) }
        let!(:grandchild) { create(:chat_bot_option, chat_bot: chat_bot, parent: fake_child) }

        it 'returns the first active option of the fake child' do
          expect(form_option.first_active_option).to eq(grandchild)
        end
      end

      context 'when first child is fake and parent is api' do
        let(:api_option) { create(:chat_bot_option, :api, chat_bot: chat_bot) }
        let!(:fake_child) { create(:chat_bot_option, :fake_option, chat_bot: chat_bot, parent: api_option) }
        let!(:grandchild) { create(:chat_bot_option, chat_bot: chat_bot, parent: fake_child) }

        it 'returns the first active option of the fake child' do
          expect(api_option.first_active_option).to eq(grandchild)
        end
      end

      context 'when first child is fake and parent is decision' do
        let(:decision_option) { create(:chat_bot_option, :decision, chat_bot: chat_bot) }
        let!(:fake_child) { create(:chat_bot_option, :fake_option, chat_bot: chat_bot, parent: decision_option) }

        it 'returns the fake child itself' do
          expect(decision_option.first_active_option).to eq(fake_child)
        end
      end
    end

    describe '#contains_exit_action?' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

      context 'when option has get_out_bot action' do
        before do
          create(:chat_bot_action, chat_bot_option: option, action_type: :get_out_bot)
        end

        it 'returns true' do
          expect(option.contains_exit_action?).to be true
        end
      end

      context 'when option has no get_out_bot action' do
        it 'returns false' do
          expect(option.contains_exit_action?).to be false
        end
      end
    end

    describe '#closest_api_ancestor' do
      let(:root_option) { create(:chat_bot_option, chat_bot: chat_bot) }
      let(:api_option1) { create(:chat_bot_option, :api, chat_bot: chat_bot, parent: root_option) }
      let(:middle_option) { create(:chat_bot_option, chat_bot: chat_bot, parent: api_option1) }
      let(:api_option2) { create(:chat_bot_option, :api, chat_bot: chat_bot, parent: middle_option) }
      let(:child_option) { create(:chat_bot_option, chat_bot: chat_bot, parent: api_option2) }

      it 'returns the closest api ancestor' do
        expect(child_option.closest_api_ancestor).to eq(api_option2)
      end

      context 'when no api ancestors exist' do
        let(:non_api_child) { create(:chat_bot_option, chat_bot: chat_bot, parent: root_option) }

        it 'returns nil' do
          expect(non_api_child.closest_api_ancestor).to be_nil
        end
      end
    end
  end

  describe 'validations' do
    describe '#children_length' do
      let(:option) { create(:chat_bot_option, :decision, chat_bot: chat_bot, interactive: :buttons) }

      context 'when interactive is text' do
        before { option.update(interactive: :text) }

        it 'does not validate children length' do
          create_list(:chat_bot_option, 5, chat_bot: chat_bot, parent: option)
          expect(option).to be_valid
        end
      end

      context 'when decision type with buttons has more than 3 children' do
        before do
          create_list(:chat_bot_option, 4, chat_bot: chat_bot, parent: option)
        end

        it 'adds error' do
          option.valid?
          expect(option.errors[:base]).to include('La opción tiene más de 3 hijos. No puede usar el formato de botones')
        end
      end

      context 'when decision type with list has more than 10 children' do
        before do
          option.update(interactive: :list)
          create_list(:chat_bot_option, 10, chat_bot: chat_bot, parent: option)
          create(:chat_bot_option, chat_bot: chat_bot, parent: option) # Creates the 11th item
        end

        it 'adds error' do
          option.valid?
          expect(option.errors[:base]).to include('La opción tiene más de 10 hijos. No puede usar el formato de lista')
        end
      end

      context 'when form type with buttons has more than 3 items' do
        let(:form_option) { create(:chat_bot_option, :form, chat_bot: chat_bot, interactive: :buttons) }

        before do
          create_list(:option_sub_list, 4, chat_bot_option: form_option)
        end

        it 'adds error' do
          form_option.valid?
          expect(form_option.errors[:base]).to include(
            'La sublista tiene más de 3 items. No puede usar el formato de botones'
          )
        end
      end

      context 'when form type with list has more than 10 items' do
        let(:form_option) { create(:chat_bot_option, :form, chat_bot: chat_bot, interactive: :list) }

        before do
          create_list(:option_sub_list, 10, chat_bot_option: form_option)
          create(:option_sub_list, chat_bot_option: form_option) # Creates the 11th item
        end

        it 'adds error' do
          form_option.valid?
          expect(form_option.errors[:base]).to include(
            'La sublista tiene más de 10 items. No puede usar el formato de lista'
          )
        end
      end
    end

    describe '#children_name_length' do
      let(:option) { create(:chat_bot_option, :decision, chat_bot: chat_bot, interactive: :buttons) }

      context 'when interactive is text' do
        before { option.update(interactive: :text) }

        it 'does not validate name length' do
          expect(option).to be_valid
        end
      end

      context 'when fake_option is true' do
        before { option.update(fake_option: true) }

        it 'does not validate name length' do
          expect(option).to be_valid
        end
      end

      context 'when decision type with buttons has child names longer than 20 characters' do
        before do
          create(:chat_bot_option, chat_bot: chat_bot, parent: option, text: 'a' * 21)
        end

        it 'adds error' do
          option.valid?
          expect(option.errors[:text]).to include(
            'Los nombres de las opciones hijo tienen más de 20 caracteres. ' \
            'No puede usar el formato de botones'
          )
        end
      end

      context 'when decision type with list has child names longer than 24 characters' do
        before do
          option.update(interactive: :list)
          create(:chat_bot_option, chat_bot: chat_bot, parent: option, text: 'a' * 25)
        end

        it 'adds error' do
          option.valid?
          expect(option.errors[:text]).to include(
            'Los nombres de las opciones hijo tienen más de 24 caracteres. ' \
            'No puede usar el formato de lista'
          )
        end
      end

      context 'when form type with buttons has items longer than 20 characters' do
        let(:form_option) { create(:chat_bot_option, :form, chat_bot: chat_bot, interactive: :buttons) }

        before do
          create(:option_sub_list, chat_bot_option: form_option, value_to_show: 'a' * 21)
        end

        it 'adds error' do
          form_option.valid?
          expect(form_option.errors[:text]).to include(
            'La sublista tiene items de más de 20 caracteres. ' \
            'No puede usar el formato de botones'
          )
        end
      end

      context 'when form type with list has items longer than 24 characters' do
        let(:form_option) { create(:chat_bot_option, :form, chat_bot: chat_bot, interactive: :list) }

        before do
          create(:option_sub_list, chat_bot_option: form_option, value_to_show: 'a' * 25)
        end

        it 'adds error' do
          form_option.valid?
          expect(form_option.errors[:text]).to include(
            'La sublista tiene items de más de 24 caracteres. ' \
            'No puede usar el formato de lista'
          )
        end
      end
    end

    describe '#answer_length' do
      let(:option) { create(:chat_bot_option, chat_bot: chat_bot, interactive: :list) }

      context 'when interactive is not list' do
        before { option.update(interactive: :text) }

        it 'does not validate answer length' do
          option.update(answer: 'a' * 1025)
          expect(option).to be_valid
        end
      end

      context 'when answer is longer than 1024 characters and interactive is list' do
        before do
          option.update(answer: 'a' * 1025)
        end

        it 'adds error' do
          option.valid?
          expect(option.errors[:answer]).to include(
            'La respuesta de la opción tiene más de 1024 caracteres. ' \
            'No puede usar el formato de lista'
          )
        end
      end

      context 'when answer is 1024 characters or less' do
        before do
          option.update(answer: 'a' * 1024)
        end

        it 'is valid' do
          expect(option).to be_valid
        end
      end
    end

    describe '#answer_minimum_length' do
      context 'when interactive is text' do
        let(:option) { create(:chat_bot_option, chat_bot: chat_bot, interactive: :text, answer: nil) }

        it 'does not validate minimum answer length' do
          expect(option).to be_valid
        end
      end

      context 'when fake_option is true' do
        let(:option) { create(:chat_bot_option, :fake_option, chat_bot: chat_bot, answer: nil) }

        it 'does not validate minimum answer length' do
          expect(option).to be_valid
        end
      end

      context 'when answer is blank and not auto generated' do
        let(:option) { build(:chat_bot_option, chat_bot: chat_bot, interactive: :buttons, answer: nil) }

        before do
          allow(option).to receive(:is_auto_generated?).and_return(false)
        end

        it 'adds error' do
          option.valid?
          expect(option.errors[:answer]).to include(
            'La respuesta de la opción está vacía. ' \
            'No puede usar un formato distinto de texto'
          )
        end
      end

      context 'when answer is blank but is auto generated' do
        let(:option) { build(:chat_bot_option, chat_bot: chat_bot, interactive: :buttons, answer: nil) }

        before do
          allow(option).to receive(:is_auto_generated?).and_return(true)
        end

        it 'is valid' do
          expect(option).to be_valid
        end
      end

      context 'when answer is present' do
        let(:option) { create(:chat_bot_option, chat_bot: chat_bot, interactive: :buttons, answer: 'Some answer') }

        it 'is valid' do
          expect(option).to be_valid
        end
      end
    end
  end

  describe 'callbacks' do
    let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

    describe 'before_destroy' do
      it 'checks destroy requirements' do
        allow(option).to receive(:check_destroy_requirements)
        option.destroy
        expect(option).to have_received(:check_destroy_requirements)
      end
    end

    describe '#check_destroy_requirements' do
      it 'avoids deleting the option' do
        expect { option.send(:check_destroy_requirements) }.to throw_symbol(:abort)
      end
    end

    describe 'before_create' do
      it 'sets position' do
        new_option = build(:chat_bot_option, chat_bot: chat_bot)
        allow(new_option).to receive(:set_position)
        new_option.save
        expect(new_option).to have_received(:set_position)
      end
    end

    describe 'before_save' do
      it 'checks skip option' do
        allow(option).to receive(:check_skip_option)
        option.save
        expect(option).to have_received(:check_skip_option)
      end

      context 'when fake option' do
        let(:option) { build(:chat_bot_option, chat_bot: chat_bot, fake_option: true, interactive: nil) }

        it 'validates interactive' do
          allow(option).to receive(:validate_interactive)
          option.save
          expect(option).to have_received(:validate_interactive)
        end

        it 'sets interactive to fake' do
          option.save
          expect(option.reload.interactive).to eq('fake')
        end
      end
    end

    describe 'after_save' do
      it 'updates descendants on delete' do
        allow(option).to receive(:update_descendants_on_delete)
        option.save
        expect(option).to have_received(:update_descendants_on_delete)
      end

      it 'clears endpoint actions' do
        allow(option).to receive(:clear_endpoint_actions)
        option.save
        expect(option).to have_received(:clear_endpoint_actions)
      end

      it 'clears sub list' do
        allow(option).to receive(:clear_sub_list)
        option.save
        expect(option).to have_received(:clear_sub_list)
      end
    end

    describe 'after_update' do
      context 'when option_deleted is changed' do
        it 'reassigns positions' do
          allow(option).to receive(:reassign_positions)
          option.update(option_deleted: true)
          option.update(option_deleted: false)
          expect(option).to have_received(:reassign_positions).twice
        end
      end

      context 'when option_deleted is not changed' do
        it 'does not reassign positions' do
          allow(option).to receive(:reassign_positions)
          option.update(text: 'New Name')
          expect(option).not_to have_received(:reassign_positions)
        end
      end
    end

    describe '#update_descendants_on_delete' do
      before do
        create_list(:chat_bot_option, 2, chat_bot: chat_bot, parent: option)
      end

      context 'when the option is not soft deleted' do
        it 'does not soft delete its descendants' do
          option.send(:update_descendants_on_delete)
          expect(option.descendants.active.count).to eq(2)
        end
      end

      context 'when the option is soft deleted' do
        it 'does soft delete its descendants' do
          option.option_deleted = true
          option.send(:update_descendants_on_delete)
          expect(option.descendants.active.count).to eq(0)
        end
      end

      context 'when the option has fail_path active' do
        subject(:chat_bot_option) do
          create(:chat_bot_option, :fail_path_active, chat_bot: chat_bot, option_type: :form)
        end

        let!(:fake_option_success) do
          create(:chat_bot_option, :fake_option, chat_bot: chat_bot, parent: chat_bot_option)
        end

        let!(:fake_option_failure) do
          create(:chat_bot_option, :fake_option, :failure, chat_bot: chat_bot, parent: chat_bot_option)
        end

        let!(:success_option_child) do
          create(:chat_bot_option, chat_bot: chat_bot, parent: fake_option_success)
        end

        let!(:failure_option_child) do
          create(:chat_bot_option, chat_bot: chat_bot, parent: fake_option_failure)
        end

        context 'when success path is soft deleted' do
          it 'moves children to become its parent children' do
            fake_option_success.update(option_deleted: true)
            expect(chat_bot_option.children).to include(success_option_child)
          end
        end

        context 'when failure path is soft deleted' do
          it 'soft deletes all its children' do
            fake_option_failure.update(option_deleted: true)
            expect(fake_option_failure.children.pluck(:option_deleted).all?(true)).to be_truthy
          end
        end
      end
    end

    describe '#set_position' do
      context 'when option is inserted between two options' do
        let(:option2) { create(:chat_bot_option, chat_bot: chat_bot, parent: option) }
        let(:option3) do
          create(:chat_bot_option, chat_bot: chat_bot, parent: option, former_child_id: option2.id)
        end

        it 'returns nil' do
          expect(option3.send(:set_position)).to be_nil
        end
      end

      context 'when option is added at the end' do
        let(:option2) { create(:chat_bot_option, chat_bot: chat_bot, parent: option) }
        let(:option3) { create(:chat_bot_option, chat_bot: chat_bot, parent: option) }

        it 'sets the position' do
          expect(option3.send(:set_position)).to eq(2)
        end
      end
    end

    describe '#insert_option' do
      let(:parent_option) { create(:chat_bot_option, chat_bot: chat_bot) }
      let(:former_option) { create(:chat_bot_option, chat_bot: chat_bot, parent: parent_option, position: 1) }
      let(:new_option) { build(:chat_bot_option, chat_bot: chat_bot, former_child_id: former_option.id) }

      it 'inserts option at former option position and makes former option a child' do
        new_option.save
        new_option.reload
        former_option.reload

        expect(new_option.position).to eq(1)
        expect(former_option.parent_id).to eq(new_option.id)
        expect(former_option.position).to eq(1)
        expect(former_option.source).to eq(new_option.node_id)
      end
    end

    describe '#purge_attachment_file' do
      context 'when the option to update has params file_deleted in true' do
        let!(:opt) do
          create(:chat_bot_option,
                 chat_bot: chat_bot,
                 file: fixture_file_upload("#{Rails.root}/spec/fixtures/files/profile.jpg", 'image/jpeg'))
        end

        it 'purge saved file' do
          opt.option_params = { file_deleted: 'true' }.with_indifferent_access
          opt.save

          expect(opt.reload.file.attached?).to be(false)
        end
      end

      context 'when the option to update has params file_deleted in false' do
        let!(:opt) do
          create(:chat_bot_option,
                 chat_bot: chat_bot,
                 file: fixture_file_upload("#{Rails.root}/spec/fixtures/files/profile.jpg", 'image/jpeg'))
        end

        it 'not purge saved file' do
          opt.option_params = { file_deleted: 'false' }.with_indifferent_access
          opt.save!

          expect(opt.reload.file.filename.to_s).to eq('profile.jpg')
        end
      end
    end
  end

  describe 'private methods' do
    let(:option) { create(:chat_bot_option, chat_bot: chat_bot) }

    describe '#clear_endpoint_actions' do
      context 'when option executes endpoint' do
        before do
          create(:chat_bot_action, chat_bot_option: option, action_type: :exec_callback)
          create(:chat_bot_action, chat_bot_option: option, classification: 'success')
        end

        it 'does not delete success/failed actions' do
          expect { option.send(:clear_endpoint_actions) }.not_to(change { option.chat_bot_actions.count })
        end
      end

      context 'when option does not execute endpoint' do
        before do
          create(:chat_bot_action, chat_bot_option: option, classification: 'success')
          create(:chat_bot_action, chat_bot_option: option, classification: 'failed')
          create(:chat_bot_action, chat_bot_option: option, classification: 'default')
        end

        it 'deletes success and failed actions only' do
          expect { option.send(:clear_endpoint_actions) }.to change { option.chat_bot_actions.count }.by(-2)
          expect(option.chat_bot_actions.pluck(:classification)).to eq(['default'])
        end
      end
    end

    describe '#clear_sub_list' do
      context 'when option_type is form' do
        before do
          option.update(option_type: :form)
          create(:option_sub_list, chat_bot_option: option)
        end

        it 'does not clear sub lists' do
          expect { option.send(:clear_sub_list) }.not_to(change { option.option_sub_lists.count })
        end
      end

      context 'when option_type is not form' do
        before do
          option.update(option_type: :decision, go_past_option: true, go_start_option: true)
          create(:option_sub_list, chat_bot_option: option)
        end

        it 'clears sub lists and return options' do
          expect { option.send(:clear_sub_list) }.to change { option.option_sub_lists.count }.to(0)
          expect(option.go_past_option).to be false
          expect(option.go_start_option).to be false
        end
      end
    end

    describe '#check_skip_option' do
      context 'when option_type is decision' do
        let(:option) { create(:chat_bot_option, :decision, chat_bot: chat_bot, skip_option: true) }

        it 'sets skip_option to false' do
          option.send(:check_skip_option)
          expect(option.skip_option).to be false
        end
      end

      context 'when option_type is not decision' do
        let(:option) { create(:chat_bot_option, :form, chat_bot: chat_bot, skip_option: true) }

        it 'does not change skip_option' do
          option.send(:check_skip_option)
          expect(option.skip_option).to be true
        end
      end
    end

    describe '#validate_interactive' do
      context 'when interactive is already present' do
        let(:option) { create(:chat_bot_option, chat_bot: chat_bot, interactive: :text) }

        it 'does not change interactive' do
          option.send(:validate_interactive)
          expect(option.interactive).to eq('text')
        end
      end

      context 'when interactive is nil and fake_option is true' do
        let(:option) { build(:chat_bot_option, chat_bot: chat_bot, interactive: nil, fake_option: true) }

        it 'sets interactive to fake' do
          option.send(:validate_interactive)
          expect(option.interactive).to eq('fake')
        end
      end

      context 'when interactive is nil and fake_option is false' do
        let(:option) { build(:chat_bot_option, chat_bot: chat_bot, interactive: nil, fake_option: false) }

        it 'does not change interactive' do
          option.send(:validate_interactive)
          expect(option.interactive).to be_nil
        end
      end
    end
  end

  describe 'save_dimensions_now' do
    context 'when the option has file' do
      let(:opt) { create(:chat_bot_option, :with_image_file, chat_bot: chat_bot) }

      it 'saves width and height dimensions' do
        # Trigger the analysis
        opt.save

        # Descarga el archivo como un binario
        file_content = StringIO.new(opt.file.download)
        tempfile = MiniMagick::Image.read(file_content)

        # Analizar el archivo de forma síncrona para el test
        opt.file.analyze

        # Recargar y verificar
        opt.reload
        expect(opt.width).to eq(tempfile.width)
        expect(opt.height).to eq(tempfile.height)
      end
    end
  end
end
