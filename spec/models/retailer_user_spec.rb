require 'rails_helper'

RSpec.describe RetailerUser do
  subject(:retailer_user) { build(:retailer_user, retailer: retailer) }

  let(:retailer) { create(:retailer) }
  let(:permissions) do
    [
      {
        permission: 'email',
        status: 'granted'
      },
      {
        permission: 'catalog_management',
        status: 'granted'
      }.with_indifferent_access,
      {
        permission: 'pages_manage_metadata',
        status: 'granted'
      }.with_indifferent_access,
      {
        permission: 'pages_read_engagement',
        status: 'granted'
      },
      {
        permission: 'pages_show_list',
        status: 'granted'
      },
      {
        permission: 'business_management',
        status: 'granted'
      },
      {
        permission: 'pages_messaging',
        status: 'granted'
      },
      {
        permission: 'public_profile',
        status: 'granted'
      }
    ]
  end
  let(:payment_plan) { retailer.payment_plan }

  describe 'associations' do
    it { is_expected.to belong_to(:retailer) }
    it { is_expected.to belong_to(:user).optional }
    it { is_expected.to belong_to(:role).optional }
    it { is_expected.to have_one(:onboarding_schedule) }
    it { is_expected.to have_many(:onboarding_notifications) }
    it { is_expected.to have_many(:agent_customers) }
    it { is_expected.to have_many(:a_customers) }
    it { is_expected.to have_many(:mobile_tokens) }
    it { is_expected.to have_many(:agent_teams).dependent(:destroy) }
    it { is_expected.to have_many(:team_assignments).through(:agent_teams) }
    it { is_expected.to have_many(:templates) }
    it { is_expected.to have_many(:calendar_events).dependent(:destroy) }
    it { is_expected.to have_many(:agent_notifications).dependent(:destroy) }
    it { is_expected.to have_many(:retailer_user_notifications) }
    it { is_expected.to have_many(:notifications).through(:retailer_user_notifications) }
    it { is_expected.to have_many(:deals) }
    it { is_expected.to have_many(:retailer_average_response_times) }
    it { is_expected.to have_many(:agent_watched_videos) }
    it { is_expected.to have_many(:mercately_videos).through(:agent_watched_videos) }
    it { is_expected.to have_many(:contact_groups) }
    it { is_expected.to have_many(:customer_fb_posts) }
    it { is_expected.to have_many(:customers) }
    it { is_expected.to have_many(:team_retailer_users).dependent(:destroy) }
    it { is_expected.to have_many(:teams).through(:team_retailer_users) }
    it { is_expected.to accept_nested_attributes_for(:retailer) }
    it { is_expected.to accept_nested_attributes_for(:user) }
    it { is_expected.to accept_nested_attributes_for(:agent_teams) }
    it { is_expected.to accept_nested_attributes_for(:team_retailer_users) }
  end

  describe 'validations' do
    describe 'at_least_one_admin' do
      let(:retailer) { create(:retailer) }
      let(:retailer_user) { create(:retailer_user, :admin, retailer: retailer) }

      before { retailer_user.retailer_admin = false }

      context 'when there is not more than 1 admin' do
        it { expect(retailer_user).not_to be_valid }
      end

      context 'when there is more than 1 admin' do
        before { create(:retailer_user, :admin, retailer: retailer) }

        it { expect(retailer_user).to be_valid }
      end
    end
  end

  describe 'scopes' do
    describe '.active_agents' do
      let!(:active_agent) do
        create(:retailer_user, retailer_admin: false, retailer_supervisor: false, removed_from_team: false,
                               retailer: retailer, user: create(:user, invitation_token: nil))
      end

      let!(:inactive_agent1) { create(:retailer_user, retailer_admin: true, retailer: retailer) }
      let!(:inactive_agent2) { create(:retailer_user, retailer_supervisor: true, retailer: retailer) }
      let!(:inactive_agent3) { create(:retailer_user, removed_from_team: true, retailer: retailer) }
      let!(:inactive_agent4) do
        create(:retailer_user, retailer: retailer, user: create(:user, invitation_token: 'some_token'))
      end

      it 'returns only active agents' do
        expect(described_class.active_agents).to contain_exactly(active_agent)
      end
    end
  end

  describe '#onboarding_status_format' do
    it 'validates the onboarding_status format' do
      # A valid hash is set as default in migration
      retailer_user.save
      expect(retailer_user.persisted?).to be_truthy
    end

    context 'with invalid options' do
      it 'returns a validation error' do
        retailer_user.onboarding_status = { step: 5, skipped: 'invalid value', invalid_key: true }
        expect(retailer_user.save).to be_falsey
      end
    end
  end

  describe '#active_for_authentication?' do
    context 'when retailer user is removed from team' do
      it 'returns false' do
        retailer_user.removed_from_team = true
        retailer_user.save
        expect(retailer_user.active_for_authentication?).to be_falsey
      end
    end

    context 'when retailer user is not removed from team' do
      it 'returns true' do
        retailer_user.save
        expect(retailer_user.active_for_authentication?).to be_truthy
      end
    end
  end

  describe '#inactive_message' do
    context 'when retailer user is removed from team' do
      it 'returns inactive message' do
        retailer_user.removed_from_team = true
        retailer_user.save
        expect(retailer_user.inactive_message).to eq('Tu cuenta no se encuentra activa')
      end
    end

    context 'when retailer user is not removed from team' do
      it { expect(retailer_user.inactive_message).to be_empty }
    end
  end

  describe '#active_for_authentication' do
    context 'when retailer user is not removed from team' do
      it 'returns true' do
        retailer_user.save
        expect(retailer_user.active_for_authentication?).to be_truthy
      end
    end
  end

  describe '#admin?' do
    subject(:retailer_user) { create(:retailer_user, :with_retailer, :admin) }

    let(:retailer_user_agent) { create(:retailer_user, :with_retailer, :agent) }

    context 'when the retailer user is an admin' do
      it 'returns true' do
        expect(retailer_user.admin?).to be_truthy
      end
    end

    context 'when the retailer user is not an admin' do
      it 'returns false' do
        expect(retailer_user_agent.admin?).to be_falsey
      end
    end
  end

  describe '#can_edit_chatbots?' do
    subject(:retailer_user) { create(:retailer_user, :with_retailer, :admin) }

    let(:retailer_user_agent) { create(:retailer_user, :with_retailer, :agent) }
    let(:retailer_user_supervisor) { create(:retailer_user, :with_retailer, :supervisor) }

    context 'when the retailer user is an admin or supervisor' do
      it 'admin can edit chatbots' do
        expect(retailer_user.can_edit_chatbots?).to be_truthy
      end

      it 'returns true' do
        expect(retailer_user_supervisor.can_edit_chatbots?).to be_truthy
      end
    end

    context 'when the retailer user is not an admin' do
      it 'returns false if no permission' do
        expect(retailer_user_agent.can_edit_chatbots?).to be_falsey
      end

      it 'returns true if has permission' do
        retailer_user_agent.update(allow_bots_edit: true)
        expect(retailer_user_agent.can_edit_chatbots?).to be_truthy
      end

      it 'returns false if permission is nil' do
        retailer_user_agent.update(allow_bots_edit: nil)
        expect(retailer_user_agent.can_edit_chatbots?).to be_falsey
      end
    end
  end

  describe '#storage_id?' do
    subject(:retailer_user) { create(:retailer_user, :with_retailer, :admin) }

    it 'returns storage id' do
      expect(retailer_user.storage_id).to eq("#{retailer_user.id}_#{retailer_user.retailer.id}_#{retailer_user.email}")
    end
  end

  describe '#supervisor?' do
    subject(:retailer_user) { create(:retailer_user, :with_retailer, :supervisor) }

    let(:retailer_user_agent) { create(:retailer_user, :with_retailer, :agent) }

    context 'when the retailer user is a supervisor' do
      it 'returns true' do
        expect(retailer_user.supervisor?).to be_truthy
      end
    end

    context 'when the retailer user is not a supervisor' do
      it 'returns false' do
        expect(retailer_user_agent.supervisor?).to be_falsey
      end
    end
  end

  describe '#agent?' do
    subject(:retailer_user) { create(:retailer_user, :with_retailer, :admin) }

    let(:retailer_user_agent) { create(:retailer_user, :with_retailer, :agent) }

    context 'when the retailer user is an agent' do
      it 'returns true' do
        expect(retailer_user_agent.agent?).to be_truthy
      end
    end

    context 'when the retailer user is not an agent' do
      it 'returns false' do
        expect(retailer_user.agent?).to be_falsey
      end
    end
  end

  describe '#customers' do
    let(:retailer) { create(:retailer) }
    let(:customer_one) { create(:customer, retailer: retailer) }
    let(:customer_two) { create(:customer, retailer: retailer) }
    let!(:customer_three) { create(:customer, retailer: retailer) }
    let(:retailer_user_one) { create(:retailer_user, :with_retailer, retailer: retailer) }
    let(:retailer_user_two) { create(:retailer_user, :with_retailer, retailer: retailer) }

    let!(:agent_customer_one) do
      create(:agent_customer, retailer_user: retailer_user_one, customer: customer_one)
    end

    let!(:agent_customer_two) do
      create(:agent_customer, retailer_user: retailer_user_two, customer: customer_two)
    end

    it 'returns the customers belonging to the retailer user or those not assigned' do
      expect(retailer_user_one.customers.count).to eq(2)
    end
  end

  describe '#filter_agents' do
    context 'when retailer user is admin or supervisor' do
      let(:retailer) { create(:retailer) }
      let!(:retailer_user) { create(:retailer_user, :in_team, retailer: retailer) }
      let!(:retailer_user2) { create(:retailer_user, :in_team, :supervisor, retailer: retailer) }
      let!(:retailer_user3) { create(:retailer_user, :in_team, :admin, retailer: retailer) }

      it 'returns all retailer users in team' do
        expect(retailer_user2.filter_agents.count).to eq(3)
        expect(retailer_user3.filter_agents.count).to eq(3)
      end
    end

    context 'when some retailer user have managed agents' do
      let(:retailer) { create(:retailer) }
      let!(:retailer_user) { create(:retailer_user, :in_team, retailer: retailer) }
      let!(:retailer_user2) { create(:retailer_user, :in_team, retailer: retailer) }
      let!(:retailer_user3) do
        create(:retailer_user, :in_team, retailer: retailer, managed_agents: [retailer_user.id, retailer_user2.id])
      end

      it 'returns the agents belonging to the retailer user' do
        expect(retailer_user3.filter_agents.count).to eq(3)
      end
    end
  end

  # rubocop:disable RSpec/ReceiveMessages
  describe '.from_omniauth' do
    subject(:retailer_user) { create(:retailer_user, :with_retailer) }

    let(:set_fb_api) { instance_double(Facebook::Api) }

    let(:oauth) do
      OmniAuth::AuthHash.new(
        uid: '12345',
        provider: 'facebook',
        credentials: OmniAuth::AuthHash.new(
          token: '**********'
        )
      )
    end

    before do
      allow(set_fb_api).to receive(:update_retailer_access_token).and_return('Example data')
      allow(set_fb_api).to receive(:subscribe_page_to_webhooks).and_return('Example data')
      allow(set_fb_api).to receive(:long_live_user_access_token).and_return('Example data')
      allow(Facebook::Api).to receive(:new).with(anything, anything).and_return(set_fb_api)
      allow(Facebook::Api)
        .to receive(:new).with(anything, anything, anything, anything, anything)
        .and_return(set_fb_api)
    end

    context 'when the permissions for facebook pages is granted' do
      it 'generates the facebook retailer information' do
        expect { described_class.from_omniauth(oauth, retailer_user, permissions, 'messenger') }
          .to change(FacebookRetailer, :count).by(1)
        expect(described_class.from_omniauth(oauth, retailer_user, permissions, 'messenger').uid).to eq(oauth.uid)
      end
    end

    context 'when the permissions for facebook catalogs is granted' do
      it 'generates the facebook catalog information' do
        expect { described_class.from_omniauth(oauth, retailer_user, permissions, 'catalog') }
          .to change(FacebookCatalog, :count).by(1)
        expect(described_class.from_omniauth(oauth, retailer_user, permissions, 'catalog').uid).to eq(oauth.uid)
      end
    end
  end
  # rubocop:enable RSpec/ReceiveMessages

  describe '#schedule_onboarding' do
    context 'when owner' do
      it 'schedules demo onboarding notifications' do
        retailer_user.save
        expect(retailer_user.onboarding_notifications.count).to eq 8
      end

      context 'when owner hits "not now" button' do
        it 'destroys all demo schedules' do
          retailer_user.save
          expect { retailer_user.onboarding_schedule.update(skip_demo: true) }
            .to change(OnboardingNotification, :count).by(-8)
        end
      end
    end
  end

  describe '#renew_invitation_url' do
    context 'when retailer user doesn\'t have a pending invitation' do
      it 'return nil value' do
        retailer_user.save
        expect(retailer_user.renew_invitation_url).to be_nil
      end
    end

    context 'when retailer user has a pending invitation' do
      it 'return url' do
        retailer_user.invitation_token = Devise.friendly_token
        retailer_user.save
        invitation_url = retailer_user.renew_invitation_url
        expect(invitation_url).not_to be_nil
        expect(invitation_url).to match(%r{\A(http|https)://\S+\z})
      end
    end
  end

  describe '#check_counters' do
    describe 'whatsapp' do
      context 'when the whatsapp counter is >= 0' do
        it 'saves the counter as it is' do
          subject.unread_whatsapp_chats_count = 2
          subject.save

          expect(subject.unread_whatsapp_chats_count).to eq(2)
        end
      end

      context 'when the whatsapp counter is < 0' do
        it 'saves the counter as zero' do
          subject.unread_whatsapp_chats_count = -2
          subject.save

          expect(subject.unread_whatsapp_chats_count).to eq(0)
        end
      end
    end

    describe 'messenger' do
      context 'when the messenger counter is >= 0' do
        it 'saves the counter as it is' do
          subject.unread_messenger_chats_count = 2
          subject.save

          expect(subject.unread_messenger_chats_count).to eq(2)
        end
      end

      context 'when the messenger counter is < 0' do
        it 'saves the counter as zero' do
          subject.unread_messenger_chats_count = -2
          subject.save

          expect(subject.unread_messenger_chats_count).to eq(0)
        end
      end
    end

    describe 'instagram' do
      context 'when the instagram counter is >= 0' do
        it 'saves the counter as it is' do
          subject.unread_instagram_chats_count = 2
          subject.save

          expect(subject.unread_instagram_chats_count).to eq(2)
        end
      end

      context 'when the instagram counter is < 0' do
        it 'saves the counter as zero' do
          subject.unread_instagram_chats_count = -2
          subject.save

          expect(subject.unread_instagram_chats_count).to eq(0)
        end
      end
    end
  end

  describe '#has_unread_chats?' do
    describe 'whatsapp' do
      context 'when the agent is an admin/supervisor' do
        context 'when total counter is greater than 0' do
          let(:agent) { create(:retailer_user, :with_retailer, :admin) }
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, ws_messages: 5) }

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when total counter is equal to 0' do
          let(:agent) { create(:retailer_user, :with_retailer, :admin) }
          let!(:retailer_counter) { RetailerCounter.find_or_create_by(retailer_id: agent.retailer.id, ws_messages: 0) }

          it 'returns false' do
            expect(agent.has_unread_chats?).to be_falsy
          end
        end
      end

      context 'when the agent is not an admin/supervisor with access to see chats not assigned' do
        let(:agent) { create(:retailer_user, :with_retailer, :agent, only_assigned: false) }

        context 'when unassigned counter is greater than 0 and agent\'s unread whatsapp counter is equal to 0' do
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, ws_messages_na: 2) }

          before do
            agent.update(unread_whatsapp_chats_count: 0)
          end

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when unassigned counter is equal to 0 and agent\'s unread whatsapp counter is greater than 0' do
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, ws_messages_na: 0) }

          before do
            agent.update(unread_whatsapp_chats_count: 2)
          end

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when unassigned counter is equal to 0 and agent\'s unread whatsapp counter is equal to 0' do
          let!(:retailer_counter) { RetailerCounter.find_or_create_by(retailer_id: agent.retailer.id, ws_messages: 0) }

          before do
            agent.update(unread_whatsapp_chats_count: 0)
          end

          it 'returns false' do
            expect(agent.has_unread_chats?).to be_falsy
          end
        end
      end

      context 'when the agent is not an admin/supervisor with access to see chats assigned only' do
        let(:agent) { create(:retailer_user, :with_retailer, :agent, only_assigned: true) }
        let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id) }

        context 'when agent\'s unread whatsapp counter is greater than 0' do
          before do
            agent.update(unread_whatsapp_chats_count: 2)
          end

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when agent\'s unread whatsapp counter is equal to 0' do
          before do
            agent.update(unread_whatsapp_chats_count: 0)
          end

          it 'returns false' do
            expect(agent.has_unread_chats?).to be_falsy
          end
        end
      end
    end

    describe 'messenger' do
      context 'when the agent is an admin/supervisor' do
        context 'when total counter is greater than 0' do
          let(:agent) { create(:retailer_user, :with_retailer, :admin) }
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, fb_messages: 5) }

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when total counter is equal to 0' do
          let(:agent) { create(:retailer_user, :with_retailer, :admin) }
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, fb_messages: 0) }

          it 'returns false' do
            expect(agent.has_unread_chats?).to be_falsy
          end
        end
      end

      context 'when the agent is not an admin/supervisor with access to see chats not assigned' do
        let(:agent) { create(:retailer_user, :with_retailer, :agent, only_assigned: false) }

        context 'when unassigned counter is greater than 0 and agent\'s unread messenger counter is equal to 0' do
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, fb_messages_na: 2) }

          before do
            agent.update(unread_messenger_chats_count: 0)
          end

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when unassigned counter is equal to 0 and agent\'s unread messenger counter is greater than 0' do
          let!(:retailer_counter) do
            RetailerCounter.find_or_create_by(retailer_id: agent.retailer.id, fb_messages_na: 0)
          end

          before do
            agent.update(unread_messenger_chats_count: 2)
          end

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when unassigned counter is equal to 0 and agent\'s unread messenger counter is equal to 0' do
          let!(:retailer_counter) do
            RetailerCounter.find_or_create_by(retailer_id: agent.retailer.id, fb_messages_na: 0)
          end

          before do
            agent.update(unread_messenger_chats_count: 0)
          end

          it 'returns false' do
            expect(agent.has_unread_chats?).to be_falsy
          end
        end
      end

      context 'when the agent is not an admin/supervisor with access to see chats assigned only' do
        let(:agent) { create(:retailer_user, :with_retailer, :agent, only_assigned: true) }
        let!(:retailer_counter) { RetailerCounter.find_or_create_by(retailer_id: agent.retailer.id) }

        context 'when agent\'s unread messenger counter is greater than 0' do
          before do
            agent.update(unread_messenger_chats_count: 2)
          end

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when agent\'s unread messenger counter is equal to 0' do
          before do
            agent.update(unread_messenger_chats_count: 0)
          end

          it 'returns false' do
            expect(agent.has_unread_chats?).to be_falsy
          end
        end
      end
    end

    describe 'instagram' do
      context 'when the agent is an admin/supervisor' do
        context 'when total counter is greater than 0' do
          let(:agent) { create(:retailer_user, :with_retailer, :admin) }
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, ig_messages: 5) }

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when total counter is equal to 0' do
          let(:agent) { create(:retailer_user, :with_retailer, :admin) }
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, ig_messages: 0) }

          it 'returns false' do
            expect(agent.has_unread_chats?).to be_falsy
          end
        end
      end

      context 'when the agent is not an admin/supervisor with access to see chats not assigned' do
        let(:agent) { create(:retailer_user, :with_retailer, :agent, only_assigned: false) }

        context 'when unassigned counter is greater than 0 and agent\'s unread instagram counter is equal to 0' do
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, ig_messages_na: 2) }

          before do
            agent.update(unread_instagram_chats_count: 0)
          end

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when unassigned counter is equal to 0 and agent\'s unread instagram counter is greater than 0' do
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, ig_messages_na: 0) }

          before do
            agent.update(unread_instagram_chats_count: 2)
          end

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when unassigned counter is equal to 0 and agent\'s unread instagram counter is equal to 0' do
          let!(:retailer_counter) { create(:retailer_counter, retailer_id: agent.retailer.id, ig_messages_na: 0) }

          before do
            agent.update(unread_instagram_chats_count: 0)
          end

          it 'returns false' do
            expect(agent.has_unread_chats?).to be_falsy
          end
        end
      end

      context 'when the agent is not an admin/supervisor with access to see chats assigned only' do
        let(:agent) { create(:retailer_user, :with_retailer, :agent, only_assigned: true) }
        let!(:retailer_counter) { RetailerCounter.find_or_create_by(retailer_id: agent.retailer.id) }

        context 'when agent\'s unread instagram counter is greater than 0' do
          before do
            agent.update(unread_instagram_chats_count: 2)
          end

          it 'returns true' do
            expect(agent.has_unread_chats?).to be_truthy
          end
        end

        context 'when agent\'s unread instagram counter is equal to 0' do
          before do
            agent.update(unread_instagram_chats_count: 0)
          end

          it 'returns false' do
            expect(agent.has_unread_chats?).to be_falsy
          end
        end
      end
    end
  end

  describe '#team_ids' do
    let(:retailer) { create(:retailer) }
    let(:team1) { create(:team, retailer: retailer) }
    let(:team2) { create(:team, retailer: retailer) }
    let(:user) { create(:retailer_user, retailer: retailer) }
    let(:other_user) { create(:retailer_user, retailer: retailer) }

    it 'returns an array of team IDs' do
      user.teams << [team1, team2]

      expect(user.team_ids).to contain_exactly(team1.id, team2.id)
    end

    it 'returns an empty array when user has no teams' do
      expect(other_user.team_ids).to be_empty
    end
  end

  describe '.connect_catalog?' do
    it 'returns true when catalog_management permission is granted and connection_type is catalog' do
      permissions = [{ 'permission' => 'catalog_management', 'status' => 'granted' }]
      expect(described_class.connect_catalog?(permissions, 'catalog')).to be_truthy
    end

    it 'returns false when catalog_management permission is not granted' do
      permissions = [{ 'permission' => 'catalog_management', 'status' => 'declined' }]
      expect(described_class.connect_catalog?(permissions, 'catalog')).to be_falsey
    end

    it 'returns false when connection_type is not catalog' do
      permissions = [{ 'permission' => 'catalog_management', 'status' => 'granted' }]
      expect(described_class.connect_catalog?(permissions, 'other')).to be_falsey
    end
  end

  describe '#full_name' do
    it 'returns the full name of the user' do
      user = build(:retailer_user, first_name: 'John', last_name: 'Doe')
      expect(user.full_name).to eq('John Doe')
    end
  end

  describe '#mobile_info' do
    it 'returns the mobile type and app version' do
      user = build(:retailer_user, mobile_type: 'android', app_version: '1.0')
      expect(user.mobile_info).to eq('android/1.0')
    end
  end

  describe '#superior?' do
    it 'returns true for admin' do
      user = build(:retailer_user, :admin)
      expect(user.superior?).to be_truthy
    end

    it 'returns true for supervisor' do
      user = build(:retailer_user, :supervisor)
      expect(user.superior?).to be_truthy
    end

    it 'returns false for regular agent' do
      user = build(:retailer_user, :agent)
      expect(user.superior?).to be_falsey
    end
  end

  describe '#owner?' do
    let(:retailer) { create(:retailer) }

    it 'returns true when the user is the owner of the retailer' do
      owner = create(:retailer_user, retailer: retailer, retailer_admin: true)
      allow(retailer).to receive(:owner).and_return(owner)
      expect(owner.owner?).to be_truthy
    end

    it 'returns false when the user is not the owner of the retailer' do
      non_owner = create(:retailer_user, retailer: retailer, retailer_admin: false)
      allow(retailer).to receive(:owner).and_return(nil)
      expect(non_owner.owner?).to be_falsey
    end
  end

  describe '#android?' do
    it 'returns true when mobile_type is android' do
      user = build(:retailer_user, mobile_type: 'android')
      expect(user.android?).to be_truthy
    end

    it 'returns true when mobile_type is nil' do
      user = build(:retailer_user, mobile_type: nil)
      expect(user.android?).to be_truthy
    end

    it 'returns false when mobile_type is ios' do
      user = build(:retailer_user, mobile_type: 'ios')
      expect(user.android?).to be_falsey
    end
  end

  describe '#ios?' do
    it 'returns true when mobile_type is ios' do
      user = build(:retailer_user, mobile_type: 'ios')
      expect(user.ios?).to be_truthy
    end

    it 'returns false when mobile_type is android' do
      user = build(:retailer_user, mobile_type: 'android')
      expect(user.ios?).to be_falsey
    end

    it 'returns false when mobile_type is nil' do
      user = build(:retailer_user, mobile_type: nil)
      expect(user.ios?).to be_falsey
    end
  end

  describe '#remove_agent?' do
    let(:retailer) { create(:retailer) }
    let(:customer) { create(:customer, retailer: retailer) }

    context 'when the user has managed agents' do
      let(:agent) { create(:retailer_user, :agent, retailer: retailer) }
      let(:admin) { create(:retailer_user, :admin, retailer: retailer, managed_agents: [agent.id]) }

      context 'when it does not have an agent assigned' do
        it 'returns false' do
          expect(admin.remove_agent?(nil)).to be_falsy
        end
      end

      context 'when it has an agent assigned' do
        context 'when it is assigned to a non managed agent' do
          let(:agent2) { create(:retailer_user, :agent, retailer: retailer) }
          let(:assigned_agent) { create(:agent_customer, customer: customer, retailer_user: agent2) }

          it 'returns true' do
            expect(admin.remove_agent?(assigned_agent)).to be_truthy
          end
        end

        context 'when it is assigned to a managed agent' do
          let(:assigned_agent) { create(:agent_customer, customer: customer, retailer_user: agent) }

          it 'returns false' do
            expect(admin.remove_agent?(assigned_agent)).to be_falsy
          end
        end
      end
    end

    context 'when the user is an admin without managed agents' do
      let(:agent) { create(:retailer_user, :agent, retailer: retailer) }
      let(:admin) { create(:retailer_user, :admin, retailer: retailer, managed_agents: []) }
      let(:assigned_agent) { create(:agent_customer, customer: customer, retailer_user: agent) }

      it 'returns false' do
        expect(admin.remove_agent?(assigned_agent)).to be_falsy
      end
    end

    context 'when the user is an agent' do
      let(:agent) { create(:retailer_user, :agent, retailer: retailer) }

      context 'when the assigned agent is different to the current' do
        let(:agent2) { create(:retailer_user, :agent, retailer: retailer) }
        let(:assigned_agent) { create(:agent_customer, customer: customer, retailer_user: agent2) }

        it 'returns true' do
          expect(agent.remove_agent?(assigned_agent)).to be_truthy
        end
      end

      context 'when the assigned agent is the same to the current' do
        let(:assigned_agent) { create(:agent_customer, customer: customer, retailer_user: agent) }

        it 'returns false' do
          expect(agent.remove_agent?(assigned_agent)).to be_falsy
        end
      end

      context 'when the agent can see only assigned chats' do
        before do
          agent.update(only_assigned: true)
        end

        context 'when the assigned agent is different to the current' do
          let(:agent2) { create(:retailer_user, :agent, retailer: retailer) }
          let(:assigned_agent) { create(:agent_customer, customer: customer, retailer_user: agent2) }

          it 'returns true' do
            expect(agent.remove_agent?(assigned_agent)).to be_truthy
          end
        end

        context 'when it does not have an agent assigned' do
          it 'returns true' do
            expect(agent.remove_agent?(nil)).to be_truthy
          end
        end
      end
    end
  end

  describe '#unsubscribe' do
    let(:retailer_user) { create(:retailer_user, :with_retailer) }
    let(:doppler_service) { instance_double(Doppler::AgentSync) }

    before do
      allow(Doppler::AgentSync).to receive(:new).with(retailer_user).and_return(doppler_service)
      allow(doppler_service).to receive(:unsubscribe).and_return([200])
      allow(Rails.env).to receive(:test?).and_return(false) # Simular que no estamos en entorno de prueba
    end

    it 'calls the Doppler service to unsubscribe' do
      expect(doppler_service).to receive(:unsubscribe).once
      retailer_user.unsubscribe
    end

    it 'updates the doppler_subscribed attribute to false when unsubscribe is successful' do
      retailer_user.unsubscribe
      expect(retailer_user.reload.doppler_subscribed).to be_falsey
    end

    context 'when in test environment' do
      before do
        allow(Rails.env).to receive(:test?).and_return(true)
      end

      it 'does not call the Doppler service' do
        expect(doppler_service).not_to receive(:unsubscribe)
        retailer_user.unsubscribe
      end

      it 'does not update the doppler_subscribed attribute' do
        expect { retailer_user.unsubscribe }.not_to change(retailer_user.reload, :doppler_subscribed)
      end
    end
  end

  describe '#get_unread_fb_comments_count' do
    let(:retailer) { create(:retailer) }
    let(:retailer_counter) { create(:retailer_counter, retailer_id: retailer.id, fb_comments: 5, fb_comments_na: 3) }
    let(:admin) { create(:retailer_user, :admin, retailer: retailer) }
    let(:agent) { create(:retailer_user, :agent, retailer: retailer, only_assigned: true, unread_fb_comments_count: 2) }

    before do
      retailer_counter
    end

    it 'returns total count for admin' do
      expect(admin.get_unread_fb_comments_count).to eq(5)
    end

    it 'returns only assigned count for agent with only_assigned true' do
      expect(agent.get_unread_fb_comments_count).to eq(2)
    end

    it 'returns total unassigned count plus assigned count for agent with only_assigned false' do
      agent.update(only_assigned: false)
      expect(agent.get_unread_fb_comments_count).to eq(5)
    end
  end

  describe '#get_unread_ig_comments_count' do
    let(:retailer) { create(:retailer) }
    let(:retailer_counter) { create(:retailer_counter, retailer_id: retailer.id, ig_comments: 5, ig_comments_na: 3) }
    let(:admin) { create(:retailer_user, :admin, retailer: retailer) }
    let(:agent) { create(:retailer_user, :agent, retailer: retailer, only_assigned: true, unread_ig_comments_count: 2) }

    before do
      retailer_counter
    end

    it 'returns total count for admin' do
      expect(admin.get_unread_ig_comments_count).to eq(5)
    end

    it 'returns only assigned count for agent with only_assigned true' do
      expect(agent.get_unread_ig_comments_count).to eq(2)
    end

    it 'returns total unassigned count plus assigned count for agent with only_assigned false' do
      agent.update(only_assigned: false)
      expect(agent.get_unread_ig_comments_count).to eq(5)
    end
  end

  describe '#can_list_all_chats?' do
    let(:admin) { create(:retailer_user, :with_retailer, :admin) }
    let(:supervisor) { create(:retailer_user, :with_retailer, :supervisor) }
    let(:agent) { create(:retailer_user, :with_retailer, :agent) }
    let(:agent_with_managed) { create(:retailer_user, :with_retailer, :agent, managed_agents: [1, 2, 3]) }

    it 'returns true for admin' do
      expect(admin.can_list_all_chats?).to be_truthy
    end

    it 'returns true for supervisor' do
      expect(supervisor.can_list_all_chats?).to be_truthy
    end

    it 'returns false for regular agent' do
      expect(agent.can_list_all_chats?).to be_falsey
    end

    it 'returns false for agent with managed agents' do
      expect(agent_with_managed.can_list_all_chats?).to be_falsey
    end
  end

  describe '#current!' do
    let(:retailer_user) { create(:retailer_user, :with_retailer, current: false) }

    it 'sets the current attribute to true' do
      retailer_user.current!
      expect(retailer_user.reload.current).to be_truthy
    end
  end

  describe '#send_registered_email' do
    let(:mailer) { double('RetailerUserMailer') }

    before do
      allow(RetailerUserMailer).to receive(:registered).and_return(mailer)
    end

    context 'when errors do not occur' do
      before do
        allow(mailer).to receive(:deliver_now).and_return(true)
      end

      it 'sends the email' do
        subject.send_registered_email

        expect(Rails.logger).not_to receive(:error)
      end
    end

    context 'when errors occur' do
      before do
        allow(mailer).to receive(:deliver_now).and_raise(StandardError.new('Error'))
      end

      it 'does not send the email' do
        expect(Rails.logger).to receive(:error).once

        subject.send_registered_email
      end
    end

    context 'when the email was already sent' do
      before do
        allow(mailer).to receive(:deliver_now).and_return(true)
        subject.save
      end

      it 'does not send the email' do
        subject.send_registered_email

        expect(mailer).not_to receive(:deliver_now)
      end
    end
  end

  describe '.connect_facebook_comments?' do
    let(:connection_type) { 'facebook_comments' }
    let(:permissions) do
      [
        {
          'permission' => 'pages_manage_engagement',
          'status' => 'granted'
        },
        {
          'permission' => 'pages_read_user_content',
          'status' => 'granted'
        }
      ]
    end

    it 'returns true when required permissions are granted and connection type is facebook_comments' do
      expect(described_class.connect_facebook_comments?(permissions, connection_type)).to be true
    end

    it 'returns false when pages_manage_engagement permission is not granted' do
      permissions[0]['status'] = 'denied'
      expect(described_class.connect_facebook_comments?(permissions, connection_type)).to be false
    end

    it 'returns false when pages_read_user_content permission is not granted' do
      permissions[1]['status'] = 'denied'
      expect(described_class.connect_facebook_comments?(permissions, connection_type)).to be false
    end

    it 'returns false when connection_type is not facebook_comments' do
      expect(described_class.connect_facebook_comments?(permissions, 'other')).to be false
    end
  end

  describe '.connect_instagram_comments?' do
    let(:connection_type) { 'instagram_comments' }
    let(:permissions) do
      [
        {
          'permission' => 'instagram_manage_comments',
          'status' => 'granted'
        }
      ]
    end

    it 'returns true when instagram_manage_comments permission is granted and connection type is instagram_comments' do
      expect(described_class.connect_instagram_comments?(permissions, connection_type)).to be true
    end

    it 'returns false when instagram_manage_comments permission is not granted' do
      permissions[0]['status'] = 'denied'
      expect(described_class.connect_instagram_comments?(permissions, connection_type)).to be false
    end

    it 'returns false when connection_type is not instagram_comments' do
      expect(described_class.connect_instagram_comments?(permissions, 'other')).to be false
    end
  end

  describe '.connect_messenger?' do
    let(:connection_type) { 'messenger' }
    let(:permissions) do
      [
        {
          'permission' => 'pages_manage_metadata',
          'status' => 'granted'
        }
      ]
    end

    it 'returns true when pages_manage_metadata permission is granted and connection type is messenger' do
      expect(described_class.connect_messenger?(permissions, connection_type)).to be true
    end

    it 'returns false when pages_manage_metadata permission is not granted' do
      permissions[0]['status'] = 'denied'
      expect(described_class.connect_messenger?(permissions, connection_type)).to be false
    end

    it 'returns false when connection_type is not messenger' do
      expect(described_class.connect_messenger?(permissions, 'other')).to be false
    end
  end

  describe '.connect_instagram?' do
    let(:connection_type) { 'instagram' }
    let(:permissions) do
      [
        {
          'permission' => 'instagram_manage_messages',
          'status' => 'granted'
        }
      ]
    end

    it 'returns true when instagram_manage_messages permission is granted and connection type is instagram' do
      expect(described_class.connect_instagram?(permissions, connection_type)).to be true
    end

    it 'returns false when instagram_manage_messages permission is not granted' do
      permissions[0]['status'] = 'denied'
      expect(described_class.connect_instagram?(permissions, connection_type)).to be false
    end

    it 'returns false when connection_type is not instagram' do
      expect(described_class.connect_instagram?(permissions, 'other')).to be false
    end
  end

  describe '.ransackable_attributes' do
    it 'returns an array of ransackable attribute names' do
      expected_attributes = %w[active agree_terms allow_bots_edit allow_edit_orders allow_export allow_import
                               api_session_expiration app_version created_at current doppler_subscribed email facebook_access_token_expiration
                               favorite_funnel_id first_name id instagram_unread invitation_accepted_at invitation_created_at invitation_limit
                               invitation_sent_at invitations_count invited_by_id invited_by_type last_name
                               last_sync_with_churnzero_at locale managed_agents messenger_unread ml_unread mobile_type notifications_payload
                               onboarding_status only_assigned phone provider remember_created_at removed_from_team reset_password_sent_at
                               retailer_admin retailer_id retailer_supervisor role_id see_phone_numbers super_admin
                               total_unread_ml_count uid unread_fb_comments_count unread_ig_comments_count unread_instagram_chats_count
                               unread_messenger_chats_count unread_ml_chats_count unread_ml_questions_count unread_whatsapp_chats_count
                               updated_at user_id utm_campaign utm_content utm_medium utm_source utm_term whatsapp_unread]
      expect(described_class.ransackable_attributes).to eq(expected_attributes)
    end
  end

  describe '.ransackable_associations' do
    it 'returns an array of ransackable association names' do
      expected_associations = %w[a_customers agent_customers agent_notifications agent_team_changelogs agent_teams
                                 agent_watched_videos calendar_events contact_groups customer_fb_posts customers
                                 deals mercately_videos mobile_tokens notifications onboarding_notifications onboarding_schedule retailer
                                 retailer_average_response_times retailer_user_notifications role team_assignments team_retailer_users teams
                                 templates user]
      expect(described_class.ransackable_associations).to eq(expected_associations)
    end
  end

  describe '#handle_page_connection' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer, uid: '123456', facebook_access_token: 'token') }
    let(:facebook_retailer) { double('FacebookRetailer') }
    let(:facebook_service) { double('Facebook::Api') }

    before do
      allow(FacebookRetailer).to receive(:find_or_create_by).and_return(facebook_retailer)
      allow(Facebook::Api).to receive(:new).and_return(facebook_service)
      allow(facebook_service).to receive(:update_retailer_access_token)
    end

    it 'creates or finds a FacebookRetailer' do
      expect(FacebookRetailer).to receive(:find_or_create_by).with(retailer_id: retailer.id)
      retailer_user.handle_page_connection
    end

    it 'creates a Facebook API service' do
      expect(Facebook::Api).to receive(:new).with(facebook_retailer, retailer_user, 'messenger', '123456', 'token')
      retailer_user.handle_page_connection
    end

    it 'updates retailer access token' do
      expect(facebook_service).to receive(:update_retailer_access_token)
      retailer_user.handle_page_connection
    end

    it 'accepts custom connection_type' do
      expect(Facebook::Api).to receive(:new).with(facebook_retailer, retailer_user, 'instagram', '123456', 'token')
      retailer_user.handle_page_connection(connection_type: 'instagram')
    end
  end

  describe '#handle_catalog_connection' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }

    it 'creates or finds a FacebookCatalog for the retailer' do
      expect(FacebookCatalog).to receive(:find_or_create_by).with(retailer_id: retailer.id)
      retailer_user.handle_catalog_connection
    end
  end

  describe '#long_live_user_access_token' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }
    let(:facebook_service) { double('Facebook::Api') }
    let(:response) { { 'access_token' => 'new_token', 'expires_in' => 5_184_000 } }

    before do
      allow(Facebook::Api).to receive(:new).with(nil, retailer_user).and_return(facebook_service)
      allow(facebook_service).to receive(:long_live_user_access_token).and_return(response)
      allow(retailer_user).to receive(:save!)
    end

    it 'updates facebook_access_token with response token' do
      retailer_user.long_live_user_access_token
      expect(retailer_user.facebook_access_token).to eq('new_token')
    end

    it 'updates facebook_access_token_expiration when expires_in is present' do
      retailer_user.long_live_user_access_token
      expect(retailer_user.facebook_access_token_expiration).not_to be_nil
    end

    it 'does not update expiration when expires_in is not present' do
      response.delete('expires_in')
      retailer_user.long_live_user_access_token
      expect(retailer_user.facebook_access_token_expiration).to be_nil
    end

    it 'saves the record' do
      expect(retailer_user).to receive(:save!)
      retailer_user.long_live_user_access_token
    end
  end

  describe '#customer_fb_posts_by_customers' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }
    let!(:customer_fb_post) { create(:customer_fb_post, retailer_user: retailer_user, retailer: retailer) }

    context 'when user is only_assigned' do
      before { retailer_user.update(only_assigned: true) }

      it 'returns only posts assigned to the user' do
        expect(retailer_user.customer_fb_posts_by_customers).to include(customer_fb_post)
      end
    end

    context 'when user is not only_assigned' do
      before { retailer_user.update(only_assigned: false, managed_agents: []) }

      it 'returns posts for user, unassigned posts, and managed agents posts' do
        allow(CustomerFbPost).to receive(:where).and_return([customer_fb_post])
        expect(CustomerFbPost).to receive(:where).with(
          retailer_user_id: [retailer_user.id, nil],
          retailer_id: retailer.id
        )
        retailer_user.customer_fb_posts_by_customers
      end
    end
  end

  describe '#subscribe' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }
    let(:doppler_service) { double('Doppler::AgentSync') }

    before do
      allow(retailer_user).to receive(:doppler).and_return(doppler_service)
      allow(retailer_user).to receive(:update_column)
    end

    context 'when not in test environment' do
      before { allow(Rails.env).to receive(:test?).and_return(false) }

      it 'calls doppler subscribe and updates doppler_subscribed to true when successful' do
        allow(doppler_service).to receive(:subscribe).and_return([200, 'success'])
        expect(retailer_user).to receive(:update_column).with(:doppler_subscribed, true)
        retailer_user.subscribe
      end

      it 'does not update doppler_subscribed when response is not 200' do
        allow(doppler_service).to receive(:subscribe).and_return([400, 'error'])
        expect(retailer_user).not_to receive(:update_column)
        retailer_user.subscribe
      end
    end

    context 'when in test environment' do
      before { allow(Rails.env).to receive(:test?).and_return(true) }

      it 'does not call doppler service' do
        expect(doppler_service).not_to receive(:subscribe)
        retailer_user.subscribe
      end
    end
  end

  describe '#delete_agent' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer, user: create(:user)) }
    let(:new_agent) { create(:retailer_user, retailer: retailer).id }

    before do
      allow(retailer_user).to receive(:reassign_chats)
      allow(ChatBotAction).to receive(:remove_agent)
      allow(AgentTeam).to receive(:remove_agent)
      allow(TeamRetailerUser).to receive(:remove_agent)
      allow(retailer_user).to receive(:remove_agent_from_deals_engine)
      allow(retailer_user).to receive(:update_current)
      allow(retailer_user).to receive(:update_current_mobile)
    end

    context 'when user belongs to multiple retailers' do
      before do
        allow(retailer_user.user).to receive_message_chain(:retailers, :count).and_return(2)
      end

      it 'sets removed_from_team to true and user_id to nil' do
        old_user_id = retailer_user.user_id
        retailer_user.delete_agent(new_agent)
        expect(retailer_user.removed_from_team).to be true
        expect(retailer_user.user_id).to be_nil
        expect(retailer_user.current).to be false
        expect(retailer_user.current_mobile).to be false
      end

      it 'calls update_current and update_current_mobile with old user_id' do
        old_user_id = retailer_user.user_id
        expect(retailer_user).to receive(:update_current).with(old_user_id)
        expect(retailer_user).to receive(:update_current_mobile).with(old_user_id)
        retailer_user.delete_agent(new_agent)
      end
    end

    context 'when user belongs to only one retailer' do
      before do
        allow(retailer_user.user).to receive_message_chain(:retailers, :count).and_return(1)
      end

      it 'updates user email and sets removed_from_team to true' do
        retailer_user.delete_agent(new_agent)
        expect(retailer_user.removed_from_team).to be true
        expect(retailer_user.user.email).to eq("#{retailer_user.id}-<EMAIL>")
      end
    end

    it 'calls all removal methods' do
      expect(retailer_user).to receive(:reassign_chats).with(new_agent)
      expect(ChatBotAction).to receive(:remove_agent).with(retailer_user.id)
      expect(AgentTeam).to receive(:remove_agent).with(retailer_user.id)
      expect(TeamRetailerUser).to receive(:remove_agent).with(retailer_user.id)
      expect(retailer_user).to receive(:remove_agent_from_deals_engine).with(retailer_user.id)
      retailer_user.delete_agent(new_agent)
    end
  end

  describe '#managed_agents_objects' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }
    let(:managed_agent1) { create(:retailer_user, retailer: retailer) }
    let(:managed_agent2) { create(:retailer_user, retailer: retailer) }

    context 'when managed_agents is empty' do
      before { retailer_user.update(managed_agents: []) }

      it 'returns nil' do
        expect(retailer_user.managed_agents_objects).to be_nil
      end
    end

    context 'when managed_agents has values' do
      before { retailer_user.update(managed_agents: [managed_agent1.id, managed_agent2.id]) }

      it 'returns RetailerUser objects including self and managed agents' do
        result = retailer_user.managed_agents_objects
        expect(result).to include(retailer_user, managed_agent1, managed_agent2)
      end
    end
  end

  describe '#current_mobile!' do
    let(:retailer_user) { create(:retailer_user, retailer: retailer, current_mobile: false) }

    it 'updates current_mobile to true' do
      retailer_user.current_mobile!
      expect(retailer_user.current_mobile).to be true
    end
  end

  describe 'additional scopes' do
    describe '.all_customers' do
      let!(:user_all_customers) do
        create(:retailer_user, retailer: retailer, only_assigned: false, retailer_admin: false,
                               retailer_supervisor: false)
      end
      let!(:user_only_assigned) do
        create(:retailer_user, retailer: retailer, only_assigned: true, retailer_admin: false,
                               retailer_supervisor: false)
      end

      it 'returns users where only_assigned is false' do
        result = described_class.all_customers
        expect(result).to include(user_all_customers)
        expect(result).not_to include(user_only_assigned)
      end
    end

    describe '.only_assigned_customers' do
      let!(:user_all_customers) do
        create(:retailer_user, retailer: retailer, only_assigned: false, retailer_admin: false,
                               retailer_supervisor: false)
      end
      let!(:user_only_assigned) do
        create(:retailer_user, retailer: retailer, only_assigned: true, retailer_admin: false,
                               retailer_supervisor: false)
      end

      it 'returns users where only_assigned is true' do
        result = described_class.only_assigned_customers
        expect(result).to include(user_only_assigned)
        expect(result).not_to include(user_all_customers)
      end
    end

    describe '.active_and_pending_agents' do
      let!(:active_user) { create(:retailer_user, retailer: retailer, removed_from_team: false) }
      let!(:removed_user) { create(:retailer_user, retailer: retailer, removed_from_team: true) }

      it 'returns users not removed from team' do
        result = described_class.active_and_pending_agents(retailer.id)
        expect(result).to include(active_user)
        expect(result).not_to include(removed_user)
      end
    end

    describe '.active_admins' do
      let!(:active_admin) do
        create(:retailer_user, :admin, retailer: retailer, removed_from_team: false,
                                       user: create(:user, invitation_token: nil))
      end
      let!(:removed_admin) do
        create(:retailer_user, :admin, retailer: retailer, removed_from_team: true,
                                       user: create(:user, invitation_token: nil))
      end
      let!(:pending_admin) do
        create(:retailer_user, :admin, retailer: retailer, removed_from_team: false,
                                       user: create(:user, invitation_token: 'token123'))
      end

      it 'returns only active admins without invitation tokens' do
        result = described_class.active_admins(retailer.id)
        expect(result).to include(active_admin)
        expect(result).not_to include(removed_admin, pending_admin)
      end
    end
  end

  describe 'additional validations' do
    describe 'user_id uniqueness' do
      let(:user) { create(:user) }
      let!(:existing_retailer_user) { create(:retailer_user, retailer: retailer, user: user) }

      it 'validates uniqueness of user_id scoped to retailer_id' do
        duplicate_retailer_user = build(:retailer_user, retailer: retailer, user: user)
        expect(duplicate_retailer_user).not_to be_valid
        expect(duplicate_retailer_user.errors[:user_id]).to include('Correo ya está asignado a alguien más')
      end

      it 'allows same user in different retailers' do
        other_retailer = create(:retailer)
        other_retailer_user = build(:retailer_user, retailer: other_retailer, user: user)
        expect(other_retailer_user).to be_valid
      end

      it 'allows nil user_id' do
        retailer_user_without_user = build(:retailer_user, retailer: retailer, user: nil)
        expect(retailer_user_without_user).to be_valid
      end
    end
  end

  describe 'enums' do
    describe 'locale' do
      it 'defines locale enum correctly' do
        expect(described_class.locales).to eq({ 'es' => 0, 'en' => 1, 'pt' => 2 })
      end
    end

    describe 'mobile_type' do
      it 'defines mobile_type enum correctly' do
        expect(described_class.mobile_types).to eq({ 'android' => 0, 'ios' => 1 })
      end
    end
  end
end
