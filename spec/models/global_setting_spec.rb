require 'rails_helper'

RSpec.describe GlobalSetting do
  before(:each) { GlobalSetting.delete_all }
  
  describe 'validations' do
    it { is_expected.to validate_uniqueness_of(:setting_key) }
    it { is_expected.to validate_presence_of(:setting_key) }
    it { is_expected.to validate_presence_of(:value) }
  end

  describe 'class methods' do
    describe '#live_event' do
      context 'when live_event setting exists' do
        let!(:live_event_setting) { create(:global_setting, setting_key: 'live_event', value: 'Live Event') }

        it { expect(described_class.live_event).not_to be_nil }
        it { expect(described_class.live_event).to eq(live_event_setting) }
      end

      context 'when live_event setting does not exist' do
        it { expect(described_class.live_event).to be_nil }
      end
    end
  end
end
