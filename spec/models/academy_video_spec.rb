require 'rails_helper'

RSpec.describe AcademyVideo do
  subject { build(:academy_video) }

  describe 'associations' do
    it { is_expected.to belong_to(:academy_category) }
    it { is_expected.to have_many(:user_academy_progresses).dependent(:destroy) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:title) }
    it { is_expected.to validate_presence_of(:position) }
    it { is_expected.to validate_presence_of(:external_url) }

    it {
      expect(subject).to allow_value('https://www.youtube.com/watch?v=abc12345678').for(:external_url)
    }

    it {
      expect(subject).not_to allow_value('not_a_url').for(:external_url)
    }
  end

  describe '.ransackable_attributes' do
    it 'returns the correct ransackable attributes' do
      expect(described_class.ransackable_attributes).to match_array(%w[academy_category_id description id position
                                                                       title external_url])
    end
  end

  describe '.ransackable_associations' do
    it 'returns the correct ransackable associations' do
      expect(described_class.ransackable_associations).to match_array(%w[academy_category])
    end
  end

  describe '#video_url' do
    let!(:academy_video) { create(:academy_video, external_url: 'https://www.youtube.com/watch?v=abc12345678') }

    it 'returns the external_url' do
      expect(academy_video.video_url).to eq('https://www.youtube.com/watch?v=abc12345678')
    end
  end
end
