require 'rails_helper'

RSpec.describe ContactGroup do
  subject(:contact_group) { build(:contact_group) }

  let(:customers) { create_list(:customer, 2, retailer: contact_group.retailer) }
  let(:service_response) { { code: '202' } }

  before do
    allow_any_instance_of(Whatsapp::Outbound::Users).to receive(:opt_in).and_return(service_response)
  end

  describe 'associations' do
    it { is_expected.to belong_to(:retailer) }
    it { is_expected.to have_many(:customers).through(:contact_group_customers).validate(false) }
    it { is_expected.to have_many(:campaigns) }
  end

  describe '#check_customers' do
    it 'checks the record has customers' do
      expect(contact_group.save).to be false
      contact_group.customers << customers
      expect(contact_group.save).to be true
    end

    context 'when imported' do
      it 'skips validation' do
        contact_group.imported = true
        expect(contact_group.customers.count).to eq 0
        expect(contact_group.save).to be true
      end
    end
  end

  describe 'scopes' do
    describe '.with_customers' do
      let(:retailer) { create(:retailer) }
      let!(:contact_group_with_customers) do
        create(:contact_group, retailer: retailer, customers: create_list(:customer, 1, retailer: retailer))
      end
      let!(:contact_group_without_customers) do
        create(:contact_group, retailer: retailer, customers: create_list(:customer, 1, retailer: retailer))
      end

      before do
        contact_group_without_customers.customers.destroy_all
      end

      it 'returns only contact groups with at least one customer' do
        expect(described_class.with_customers).to include(contact_group_with_customers)
        expect(described_class.with_customers).not_to include(contact_group_without_customers)
      end
    end

    describe '.by_name' do
      let(:retailer) { create(:retailer) }
      let(:contact_group) { create(:contact_group, retailer: retailer, customers: create_list(:customer, 1, retailer: retailer)) }

      it 'returns contact groups with the given name' do
        expect(described_class.by_name(contact_group.name)).to include(contact_group)
      end

      it 'returns contact groups with the given name in a case-insensitive manner' do
        expect(described_class.by_name(contact_group.name.upcase)).to include(contact_group)
      end

      it 'returns contact groups with the given name in a case-insensitive manner' do
        expect(described_class.by_name(contact_group.name.downcase)).to include(contact_group)
      end

      it 'returns empty array if no contact groups are found' do
        expect(described_class.by_name('non_existent_name')).to be_empty
      end
    end
  end
end
