require 'rails_helper'

RSpec.describe FacebookMessage do
  subject(:facebook_message) { create(:facebook_message, facebook_retailer: facebook_retailer) }

  let!(:retailer) { create(:retailer) }
  let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
  let!(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }

  describe 'associations' do
    it { is_expected.to belong_to(:facebook_retailer) }
    it { is_expected.to belong_to(:customer) }
    it { is_expected.to belong_to(:retailer_user).required(false) }
    it { is_expected.to belong_to(:facebook_comment).required(false) }
  end

  describe 'validations' do
    it { is_expected.to validate_uniqueness_of(:mid).scoped_to(:facebook_retailer_id).allow_blank }
  end

  describe 'scopes' do
    it { expect(described_class).to respond_to(:customer_unread) }
    it { expect(described_class).to respond_to(:retailer_unread) }
    it { expect(described_class).to respond_to(:inbound) }
    it { expect(described_class).to respond_to(:unread) }
    it { expect(described_class).to respond_to(:range_between) }
    it { expect(described_class).to respond_to(:first_load) }
  end

  describe 'attributes' do
    it { is_expected.to respond_to(:block_chat_reactivation) }
  end

  describe 'message events' do
    let(:customer) { create(:customer, retailer: retailer, pstype: :messenger, agent_event: retailer_user) }

    context 'when create a note message' do
      let!(:message) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, note: true)
      end

      it 'create event for deal created' do
        activities_customer = Activities::Customer.find_by(customer_id: customer.id)
        expect(activities_customer.events.count).to eq(2)
        expect(activities_customer.events.last.action).to eq('Nota creada')
      end
    end

    context 'when create a order note message' do
      let!(:message) do
        create(:facebook_message, facebook_retailer: facebook_retailer,
                                  customer: customer, note: true, order_note: true)
      end

      it 'create event for deal created' do
        activities_customer = Activities::Customer.find_by(customer_id: customer.id)
        expect(activities_customer.events.count).to eq(2)
        expect(activities_customer.events.last.action).to eq('Nota de orden creada')
      end
    end
  end

  describe '#sent_by_retailer?' do
    context 'when sender and facebook retailer are not the same' do
      it 'does not set to true the sent_by_retailer attribute' do
        expect(facebook_message.send(:sent_by_retailer?)).to be_nil
      end
    end

    context 'when sender and facebook retailer are the same' do
      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, sender_uid: facebook_retailer.uid)
      end

      it 'sets to true the sent_by_retailer attribute' do
        expect(facebook_msg_sent.send(:sent_by_retailer?)).to be true
      end
    end
  end

  describe '#send_facebook_message' do
    let(:new_facebook_message) { instance_double(Facebook::Messages) }
    let(:msn_response) { { message_id: '1234567890' }.with_indifferent_access }
    let(:customer) { create(:customer, retailer: retailer, psid: '1234567890', pstype: :messenger) }

    let(:facebook_msg_sent) do
      create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_from_mercately:
        true, text: 'Testing')
    end

    let(:facebook_file_sent) do
      create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_from_mercately:
        true, file_data: '/tmp/file.pdf')
    end

    let(:facebook_url_sent) do
      create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_from_mercately:
        true, file_url: 'https://www.images.com/image.jpg', file_type: 'image')
    end

    before do
      allow(new_facebook_message).to receive_messages(send_message: msn_response, send_attachment: msn_response,
                                                      import_delivered: new_facebook_message)
      allow(Facebook::Messages).to receive(:new).with(facebook_retailer, 'messenger')
        .and_return(new_facebook_message)
    end

    context 'when the message is sent from mercately' do
      context 'when the message is a text' do
        it 'calls the service to Facebok Message to send a text message' do
          facebook_msg_sent.send(:send_facebook_message)
          expect(facebook_msg_sent.reload.mid).not_to be_nil
        end
      end

      context 'when the message contains an attachment' do
        it 'calls the service to Facebok Message to send an attachment message' do
          facebook_msg_sent.send(:send_facebook_message)
          expect(facebook_file_sent.reload.mid).not_to be_nil
        end
      end

      context 'when the message contains an url' do
        it 'calls the service to Facebok Message to send an attachment message' do
          facebook_url_sent.send(:send_facebook_message)
          expect(facebook_url_sent.reload.mid).not_to be_nil
        end
      end
    end

    context 'when the message is not sent from mercately' do
      it 'does not call the Facebook Message service' do
        expect(facebook_message.send(:send_facebook_message)).to be_nil
      end
    end

    context 'when it is not a template' do
      context 'when exist a business rule' do
        let!(:business_rule) do
          create(:business_rule, name: 'Incluir el nombre del agente al enviar mensaje',
                                 description: 'Incluye el nombre del agente al enviar',
                                 identifier: 'attach_agent_name_in_messages')
        end

        context 'when retailer is rule matching' do
          let!(:retailer_business_rule) do
            create(:retailer_business_rule, retailer: retailer, business_rule: business_rule)
          end

          let!(:facebook_message_br) do
            create(:facebook_message, :outbound, facebook_retailer: facebook_retailer,
                                                 customer: customer,
                                                 retailer_user: retailer_user,
                                                 text: 'Text message to attach agent name')
          end

          it 'attach agent name to text body message' do
            expect(facebook_message_br.text).to match(/#{Regexp.escape(retailer_user.full_name)}/)
          end
        end

        context 'when retailer is not matching rule' do
          let!(:facebook_message_br1) do
            create(:facebook_message, :outbound, facebook_retailer: facebook_retailer,
                                                 customer: customer,
                                                 retailer_user: retailer_user,
                                                 text: 'Text message to attach agent name')
          end

          it 'attach agent name to text body message' do
            expect(facebook_message_br1.text).to match(/Text message to attach agent name/)
          end
        end
      end
    end
  end

  describe '#send_welcome_message' do
    let(:new_facebook_message) { instance_double(Facebook::Messages) }

    before do
      allow(new_facebook_message).to receive(:send_message)
        .and_return('Sent')
      allow(Facebook::Messages).to receive(:new).with(facebook_retailer, 'messenger')
        .and_return(new_facebook_message)
    end

    context 'when the retailer does not have an active welcome message configured' do
      let(:customer) { create(:customer, retailer: facebook_retailer.retailer, pstype: :messenger) }

      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Testing')
      end

      it 'returns nil' do
        expect(facebook_msg_sent.send(:send_welcome_message)).to be_nil
      end
    end

    context 'when the customer does not text for the first time' do
      let!(:automatic_answer) { create(:automatic_answer, :welcome, :messenger, retailer: facebook_retailer.retailer) }
      let(:customer) { create(:customer, retailer: facebook_retailer.retailer, pstype: :messenger) }

      let!(:first_facebook_message) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, text: 'Test')
      end

      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Testing')
      end

      it 'returns nil' do
        expect(facebook_msg_sent.send(:send_welcome_message)).to be_nil
      end
    end

    context 'when the message is sent from the retailer' do
      let!(:automatic_answer) { create(:automatic_answer, :welcome, :messenger, retailer: facebook_retailer.retailer) }
      let(:customer) { create(:customer, retailer: facebook_retailer.retailer, pstype: :messenger) }

      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          true, text: 'Testing')
      end

      it 'returns nil' do
        expect(facebook_msg_sent.send(:send_welcome_message)).to be_nil
      end
    end

    context 'when all conditions are present' do
      let!(:automatic_answer) { create(:automatic_answer, :welcome, :messenger, retailer: facebook_retailer.retailer) }
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: automatic_answer) }
      let(:customer) { create(:customer, retailer: facebook_retailer.retailer, pstype: :messenger) }

      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Testing')
      end

      it 'sends the message' do
        expect { facebook_msg_sent.send(:send_welcome_message) }.to change(described_class, :count).by(2)
      end
    end
  end

  describe '#send_inactive_message' do
    let(:new_facebook_message) { instance_double(Facebook::Messages) }

    before do
      allow(new_facebook_message).to receive(:send_message)
        .and_return('Sent')
      allow(Facebook::Messages).to receive(:new).with(facebook_retailer, 'messenger')
        .and_return(new_facebook_message)
    end

    context 'when the retailer does not have an inactive message configured' do
      let(:customer) { create(:customer, retailer: facebook_retailer.retailer, pstype: :messenger) }

      let!(:first_facebook_message) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Test')
      end

      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Testing')
      end

      it 'returns nil' do
        expect(facebook_msg_sent.send(:send_inactive_message)).to be_nil
      end
    end

    context 'when the customer does not have a prior message sent' do
      let(:customer) { create(:customer, retailer: facebook_retailer.retailer, pstype: :messenger) }

      let!(:automatic_answer) do
        create(:automatic_answer, :inactive, :messenger, retailer: facebook_retailer.retailer)
      end

      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Testing')
      end

      it 'returns nil' do
        expect(facebook_msg_sent.send(:send_inactive_message)).to be_nil
      end
    end

    context 'when the message is sent from the retailer' do
      let(:customer) { create(:customer, retailer: facebook_retailer.retailer, pstype: :messenger) }

      let!(:automatic_answer) do
        create(:automatic_answer, :inactive, :messenger, retailer: facebook_retailer.retailer)
      end

      let!(:first_facebook_message) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Test')
      end

      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          true, text: 'Testing')
      end

      it 'returns nil' do
        expect(facebook_msg_sent.send(:send_inactive_message)).to be_nil
      end
    end

    context 'when the created time of the prior message is not passed yet' do
      let(:customer) { create(:customer, retailer: facebook_retailer.retailer, pstype: :messenger) }

      let!(:automatic_answer) do
        create(:automatic_answer, :inactive, :messenger, retailer: facebook_retailer.retailer)
      end

      let!(:first_facebook_message) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Test', created_at: Time.now - 11.hours)
      end

      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Testing')
      end

      it 'returns nil' do
        expect(facebook_msg_sent.send(:send_inactive_message)).to be_nil
      end
    end

    context 'when all conditions are present' do
      let(:customer) { create(:customer, retailer: facebook_retailer.retailer, pstype: :messenger) }

      let!(:automatic_answer) do
        create(:automatic_answer, :inactive, :messenger, retailer: facebook_retailer.retailer)
      end
      let!(:automatic_answer_day) { create(:automatic_answer_day, automatic_answer: automatic_answer) }

      let!(:first_facebook_message) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Test', created_at: Time.now - 13.hours)
      end

      let(:facebook_msg_sent) do
        create(:facebook_message, facebook_retailer: facebook_retailer, customer: customer, sent_by_retailer:
          false, text: 'Testing')
      end

      it 'sends the message' do
        expect { facebook_msg_sent.send(:send_inactive_message) }.to change(described_class, :count).by(3)
      end
    end
  end

  describe '#assign_agent' do
    context 'when the retailer has assignment teams permission' do
      let(:retailer) { create(:retailer) }
      let(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }
      let(:retailer_user) { create(:retailer_user, retailer: retailer) }
      let(:another_retailer_user) { create(:retailer_user, retailer: retailer) }
      let(:customer) { create(:customer, retailer: retailer, pstype: :messenger) }
      let(:customer2) { create(:customer, retailer: retailer, pstype: :messenger) }

      let!(:message) do
        create(:facebook_message, :inbound, customer: customer, facebook_retailer: facebook_retailer)
      end

      context 'when the message is inbound' do
        context 'with an agent already assigned' do
          let!(:agent_customer) { create(:agent_customer, customer: customer, retailer_user: retailer_user) }

          it 'returns nil' do
            expect(message.send(:assign_agent)).to be_nil
          end
        end

        context 'with the chat already answered by agent' do
          let!(:outbound_message) do
            create(
              :facebook_message,
              :outbound,
              customer: customer,
              facebook_retailer: facebook_retailer,
              retailer_user: retailer_user
            )
          end

          it 'returns nil' do
            expect(message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the retailer does not have a default team assignment created' do
          it 'returns nil' do
            expect(message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the retailer does not have a default team assignment activated' do
          let!(:default_team) do
            create(:team_assignment, retailer: retailer, active_assignment: false, messenger: true, instagram: true)
          end

          it 'returns nil' do
            expect(message.send(:assign_agent)).to be_nil
          end
        end

        context 'when there is at least one agent with free spots to assign' do
          let(:default_team) do
            create(:team_assignment, :assigned_default, retailer: retailer, messenger: true, instagram: true)
          end

          let!(:agent_team1) do
            create(
              :agent_team,
              :activated,
              team_assignment: default_team,
              retailer_user: retailer_user,
              max_assignments: 2,
              assigned_amount: 2
            )
          end

          let!(:agent_team2) do
            create(
              :agent_team,
              :activated,
              team_assignment: default_team,
              retailer_user: another_retailer_user,
              max_assignments: 4,
              assigned_amount: 3
            )
          end

          it 'assigns an agent to the customer' do
            retailer.payment_plan.update(plan: :advanced, contact_based: false)

            expect do
              create(:facebook_message, :inbound, customer: customer, facebook_retailer: facebook_retailer)
            end.to change(AgentCustomer, :count).by(1)
          end
        end

        context 'when there is more than one agent with free spots to assign' do
          let(:default_team) do
            create(:team_assignment, :assigned_default, retailer: retailer, messenger: true, instagram: true)
          end

          let!(:agent_team1) do
            create(
              :agent_team,
              :activated,
              team_assignment: default_team,
              retailer_user: retailer_user,
              max_assignments: 2,
              assigned_amount: 0
            )
          end

          let!(:agent_team2) do
            create(
              :agent_team,
              :activated,
              team_assignment: default_team,
              retailer_user: another_retailer_user,
              max_assignments: 4,
              assigned_amount: 0
            )
          end

          let(:facebook_message1) do
            create(:facebook_message, :inbound, customer: customer, facebook_retailer: facebook_retailer)
          end

          let(:facebook_message2) do
            create(:facebook_message, :inbound, customer: customer2, facebook_retailer: facebook_retailer)
          end

          before { retailer.payment_plan.update(plan: :advanced, contact_based: false) }

          it 'assigns the agent with less assignments to the customer and so on' do
            expect { facebook_message1 }.to change(AgentCustomer, :count).by(1)

            last = AgentCustomer.last
            expect(agent_team1.retailer_user_id).to eq(last.retailer_user_id)
            expect(last.team_assignment_id).to eq(default_team.id)
            expect(agent_team1.reload.assigned_amount).to eq(1)

            expect { facebook_message2 }.to change(AgentCustomer, :count).by(1)

            last = AgentCustomer.last
            expect(agent_team2.retailer_user_id).to eq(last.retailer_user_id)
            expect(last.team_assignment_id).to eq(default_team.id)
            expect(agent_team2.reload.assigned_amount).to eq(1)
          end
        end
      end

      context 'when the message is outbound' do
        let!(:inbound_message) do
          create(:facebook_message, :inbound, customer: customer, facebook_retailer: facebook_retailer)
        end

        let(:outbound_message) do
          create(
            :facebook_message,
            :outbound,
            customer: customer,
            facebook_retailer: facebook_retailer,
            retailer_user: retailer_user
          )
        end

        context 'when the message does not come from an agent' do
          let(:not_from_agent) do
            create(:facebook_message, :outbound, customer: customer, facebook_retailer: facebook_retailer)
          end

          it 'returns nil' do
            expect(not_from_agent.send(:assign_agent)).to be_nil
          end
        end

        context 'when the customer does not have an agent assigned' do
          it 'returns nil' do
            expect(outbound_message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the agent assigned to the customer is not from a team' do
          let!(:agent_customer) { create(:agent_customer, customer: customer, retailer_user: retailer_user) }

          it 'returns nil' do
            expect(outbound_message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the message is not the first one sent to the customer' do
          let(:default_team) do
            create(:team_assignment, :assigned_default, retailer: retailer, messenger: true, instagram: true)
          end

          let!(:agent_team) do
            create(
              :agent_team,
              :activated,
              team_assignment: default_team,
              retailer_user: retailer_user,
              max_assignments: 2,
              assigned_amount: 0
            )
          end

          let!(:agent_customer) do
            create(:agent_customer, customer: customer, retailer_user: retailer_user, team_assignment: default_team)
          end

          let!(:other_outbound) do
            create(
              :facebook_message,
              :outbound,
              customer: customer,
              facebook_retailer: facebook_retailer,
              retailer_user: retailer_user
            )
          end

          it 'returns nil' do
            expect(outbound_message.send(:assign_agent)).to be_nil
          end
        end

        context 'when the customer is resolved' do
          let(:default_team) do
            create(:team_assignment, :assigned_default, retailer: retailer, messenger: true, instagram: true)
          end

          let!(:agent_team) do
            create(
              :agent_team,
              :activated,
              team_assignment: default_team,
              retailer_user: retailer_user,
              max_assignments: 2,
              assigned_amount: 1
            )
          end

          let!(:agent_customer) do
            create(:agent_customer, customer: customer, retailer_user: retailer_user, team_assignment: default_team)
          end

          it 'decreases by one the assigned amount of the agent team' do
            expect(agent_team.assigned_amount).to eq(1)

            customer.reload.whatsapp_resolved!

            expect(agent_team.reload.assigned_amount).to eq(0)
          end
        end
      end
    end

    context 'when the retailer does not have assignment teams permission' do
      let(:retailer) { create(:retailer) }
      let(:message) { create(:facebook_message, facebook_retailer: facebook_retailer) }

      it 'returns nil' do
        expect(message.send(:assign_agent)).to be_nil
      end
    end

    context 'when a chat is answered' do
      let(:customer) { create(:customer, retailer: retailer, pstype: :messenger) }

      context 'when the message is not sent from an agent' do
        let(:outbound_message) do
          create(:facebook_message, :outbound, customer: customer, facebook_retailer: facebook_retailer)
        end

        it 'does not assign the customer to anyone' do
          outbound_message
          expect(customer.agent).to be_nil
        end
      end

      context 'when the message is sent from an agent' do
        let(:outbound_message) do
          create(
            :facebook_message,
            :outbound,
            customer: customer,
            facebook_retailer: facebook_retailer,
            retailer_user: retailer_user
          )
        end

        context 'when the customer does not have an agent yet' do
          it 'assigns the customer to the agent' do
            outbound_message
            expect(customer.agent).to eq(retailer_user)
          end
        end

        context 'when the customer already has an agent' do
          let(:retailer_user2) { create(:retailer_user, retailer: retailer) }
          let!(:agent_customer) { create(:agent_customer, customer: customer, retailer_user: retailer_user2) }

          it 'does not assign the customer to the agent' do
            outbound_message
            expect(customer.agent).to eq(retailer_user2)
          end
        end
      end
    end
  end

  describe '#set_sender_information' do
    let(:new_facebook_message) { instance_double(Facebook::Messages) }

    let(:retailer_user) do
      create(:retailer_user, retailer: retailer, first_name: 'Test', last_name: 'Example', email: '<EMAIL>')
    end

    let(:message) do
      build(:facebook_message, facebook_retailer: facebook_retailer, sent_by_retailer: true,
                               retailer_user: retailer_user)
    end

    before do
      allow(new_facebook_message).to receive(:send_message).and_return('Sent')
      allow(Facebook::Messages).to receive(:new).with(facebook_retailer, 'messenger').and_return(new_facebook_message)
    end

    it 'saves the sender information in the message' do
      expect(message.sender_first_name).to be_nil
      message.save
      expect(message.sender_first_name).to eq(retailer_user.first_name)
      expect(message.sender_last_name).to eq(retailer_user.last_name)
      expect(message.sender_email).to eq(retailer_user.email)
    end
  end

  describe '#insert_on_agent_queue' do
    let(:retailer) { create(:retailer) }
    let(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }
    let(:team_assignment) { create(:team_assignment, :assigned_default, retailer: retailer, messenger: true) }
    let(:agent1) { create(:retailer_user, retailer: retailer) }
    let(:agent2) { create(:retailer_user, retailer: retailer) }
    let(:agent3) { create(:retailer_user, retailer: retailer) }
    let!(:agent_team1) { create(:agent_team, team_assignment: team_assignment, retailer_user: agent1) }
    let!(:agent_team2) { create(:agent_team, team_assignment: team_assignment, retailer_user: agent2) }
    let!(:agent_team3) { create(:agent_team, team_assignment: team_assignment, retailer_user: agent3) }
    let!(:customer1) { create(:customer, :messenger, retailer: retailer) }
    let(:customer2) { create(:customer, :messenger, retailer: retailer) }
    let(:customer3) { create(:customer, :messenger, retailer: retailer) }
    let(:message1) { build(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer1) }
    let(:message2) { build(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer2) }
    let(:message3) { build(:facebook_message, :inbound, facebook_retailer: facebook_retailer, customer: customer3) }

    it 'assigns one chat to every agent team', transactional_fixtures: false do
      retailer.payment_plan.update(plan: :advanced, contact_based: false)
      message1.save
      message2.save
      message3.save
      expect(agent_team1.reload.assigned_amount).to eq(1)
      expect(agent_team2.reload.assigned_amount).to eq(1)
      expect(agent_team3.reload.assigned_amount).to eq(1)
      expect(team_assignment.reload.last_assigned).to eq(agent_team3.id)
    end
  end

  describe '#mark_flag_unread' do
    let!(:customer) { create(:customer, :from_fb, :messenger, retailer: retailer, count_unread_messages: 0) }
    let(:message) { build(:facebook_message, :inbound, customer: customer, facebook_retailer: facebook_retailer) }
    let(:message_out) do
      create(:facebook_message, :outbound, customer: customer, facebook_retailer: facebook_retailer)
    end

    context 'when message is not inbound' do
      it 'returns nil' do
        expect(message_out.send(:mark_unread_flag)).to be_nil
      end
    end

    context 'when message is inbound' do
      it 'increases by 1 the customer unread counter' do
        message.save

        expect(customer.unread_messenger_messages).to eq(1)
      end

      context 'when the customer is not assigned' do
        it 'increases by 1 the total counter and unassigned counter' do
          message.save

          expect(retailer.retailer_counter.fb_messages).to eq(1)
          expect(retailer.retailer_counter.fb_messages_na).to eq(1)
        end
      end

      context 'when the customer is assigned' do
        let!(:agent_customer) { create(:agent_customer, retailer_user: retailer_user, customer: customer) }

        before do
          customer.update(retailer_user_id: retailer_user.id)
        end

        it 'increases by 1 the agent counter and total counter' do
          message.save

          expect(retailer.retailer_counter.fb_messages).to eq(1)
          expect(retailer.retailer_counter.fb_messages_na).to eq(0)
          expect(retailer_user.reload.unread_messenger_chats_count).to eq(1)
        end
      end
    end
  end

  describe '#get_comment' do
    context 'when there is not a comment associated' do
      it 'returns nil' do
        expect(subject.send(:get_comment)).to be_nil
      end
    end

    context 'when there is a comment associated' do
      subject(:facebook_message) do
        create(:facebook_message, facebook_retailer: facebook_retailer, facebook_comment: facebook_comment)
      end

      let(:customer_fb_post) do
        create(:customer_fb_post, customer: customer, platform: :facebook, retailer: retailer)
      end

      let(:customer) { create(:customer, retailer: retailer, pstype: :messenger) }
      let(:facebook_comment) { create(:facebook_comment, customer_fb_post: customer_fb_post) }

      it 'returns the comment' do
        expect(subject.send(:get_comment)).to eq(facebook_comment)
      end
    end
  end

  describe 'Concerns::MessengerChatBotActionConcern' do
    describe '#build_params' do
      let(:customer) { create(:customer) }
      let(:option) { create(:chat_bot_option) }
      let(:bot_api) { ChatBots::V2::BotApi.new(customer, option) }

      before do
        allow(ChatBots::V2::BotApi).to receive(:new).and_return(bot_api)
      end

      context 'when message type is text' do
        before do
          option.message_type = 'text'
        end

        it 'builds params with message' do
          allow(bot_api).to receive(:replace_message_variables).and_return('Hello, World!')
          params = facebook_message.build_params(option)
          expect(bot_api).to have_received(:replace_message_variables)
          expect(params[:message]).to eq('Hello, World!')
        end
      end

      context 'when message type is image or video' do
        let(:file) do
          double(
            'ActiveStorage::Attached::One',
            attached?: true,
            content_type: 'image/jpg',
            filename: 'image.jpg'
          )
        end

        before do
          option.message_type = 'image'
          allow(bot_api).to receive(:replace_message_variables).and_return('Hello, World!')
          allow_any_instance_of(described_class).to receive(:send_bot_message).and_return(true)
          allow(option).to receive_messages(file_url: 'https://www.images.com/image.jpg', file: file)
        end

        # rubocop:disable RSpec/SubjectStub
        it 'builds params with message and file_url' do
          params = facebook_message.build_params(option)
          expect(bot_api).to have_received(:replace_message_variables)
          expect(facebook_message).to have_received(:send_bot_message)
          expect(params[:message]).to eq('')
          expect(params[:file_url]).to eq(option.file_url)
          expect(params[:file_type]).to eq(option.file_type)
          expect(params[:filename]).to eq(option.file.filename.to_s)
        end
        # rubocop:enable RSpec/SubjectStub
      end

      describe '#build_params' do
        context 'when message type is document' do
          include ActiveStorageHelpers

          # Utilizando el helper de ActiveStorage para crear un archivo adjunto
          # Cambiamos a 'document.pdf'
          let(:file_blob) do
            create_file_blob(filename: 'document.pdf', content_type: 'application/pdf')
          end
          let(:option) { create(:chat_bot_option, file: file_blob) }

          before do
            # Simulamos que el archivo está adjunto en el objeto `option`
            option.message_type = 'document'
            allow(option.file).to receive(:attached?).and_return(true)
            allow(bot_api).to receive(:replace_message_variables).and_return('Hello, World!')
            allow_any_instance_of(described_class).to receive(:send_bot_message).and_return(true)
            allow(option).to receive_messages(file_url: 'spec/fixtures/files/document.pdf',
                                              file_type: 'application/pdf')
          end

          it 'builds params with file_data and file_content_type' do
            VCR.use_cassette('facebook_message_document') do
              params = facebook_message.build_params(option)
              expect(params[:file_data]).to eq('spec/fixtures/files/document.pdf')
              expect(params[:file_type]).to eq('application/pdf')
              expect(params[:filename]).to eq('document.pdf') # Cambiamos a 'document.pdf'
            end
          end
        end
      end

      context 'when message type is not text, image, video nor document' do
        let(:file) do
          double(
            'ActiveStorage::Attached::One',
            attached?: true,
            content_type: 'image/jpg',
            filename: 'image.jpg'
          )
        end

        before do
          option.message_type = 'audio'
          allow(bot_api).to receive(:replace_message_variables).and_return('Hello, World!')
          allow_any_instance_of(described_class).to receive(:send_bot_message).and_return(true)
          allow(option).to receive_messages(file_url: 'spec/fixtures/files/test.pdf', file: file)
        end

        it 'builds params with message and file_url, file_type and filename' do
          params = facebook_message.build_params(option)
          expect(params[:file_url]).to eq(option.file_url)
          expect(params[:file_type]).to eq(option.file_type)
          expect(params[:filename]).to eq(option.file.filename.to_s)
        end
      end
    end

    describe '#chat_bot_execution' do
      subject(:facebook_message) { build(:facebook_message, facebook_retailer: facebook_retailer) }

      context 'when the message is not inbound' do
        let(:facebook_message) { build(:facebook_message, :outbound, facebook_retailer: facebook_retailer) }

        it 'returns nil' do
          expect(facebook_message.send(:chat_bot_execution)).to be_nil
        end

        it 'calls chat_bot_execution' do
          allow(facebook_message).to receive(:chat_bot_execution)
          facebook_message.save
          expect(facebook_message).to have_received(:chat_bot_execution)
        end
      end

      context 'when the customer is not messenger' do
        let(:customer) { create(:customer, :instagram, retailer: retailer) }
        let(:facebook_message) { build(:facebook_message, facebook_retailer: facebook_retailer) }

        it 'returns nil' do
          expect(facebook_message.send(:chat_bot_execution)).to be_nil
        end

        it 'calls chat_bot_execution' do
          allow(facebook_message).to receive(:chat_bot_execution)
          facebook_message.save
          expect(facebook_message).to have_received(:chat_bot_execution)
        end
      end

      context 'when the customer is messenger but not chatbot found' do
        let(:facebook_message) { build(:facebook_message, facebook_retailer: facebook_retailer) }

        before do
          allow(facebook_message).to receive(:get_current_bot).and_return(nil)
        end

        it 'returns nil' do
          expect(facebook_message.send(:chat_bot_execution)).to be_nil
        end

        it 'assigns nil to @chat_bot' do
          facebook_message.save
          expect(facebook_message.instance_variable_get(:@chat_bot)).to be_nil
        end
      end

      context 'when url is present' do
        let(:facebook_message) { build(:facebook_message, facebook_retailer: facebook_retailer) }

        before do
          allow(facebook_message).to receive_messages(get_current_bot: true, url: 'https://media_url.com')
        end

        it 'assigns true to @media' do
          facebook_message.send(:chat_bot_execution)
          expect(facebook_message.instance_variable_get(:@media)).to be_truthy
        end

        context 'when message has text' do
          let(:instagram_message) do
            build(:facebook_message, facebook_retailer: facebook_retailer, text: 'Hola', url: 'https://media_url.com')
          end

          before do
            allow(facebook_message).to receive(:get_current_bot).and_return(true)
          end

          it 'assigns false to @media_selection' do
            facebook_message.send(:chat_bot_execution)
            expect(facebook_message.instance_variable_get(:@media_selection)).to be_falsy
          end
        end

        context 'when message does not have text' do
          let(:facebook_message) do
            build(:facebook_message, facebook_retailer: facebook_retailer, text: '', url: 'https://media_url.com')
          end

          before do
            allow(facebook_message).to receive(:get_current_bot).and_return(true)
          end

          it 'assigns true to @media_selection' do
            facebook_message.send(:chat_bot_execution)
            expect(facebook_message.instance_variable_get(:@media_selection)).to be_truthy
          end
        end
      end

      context 'when url is not present' do
        let(:facebook_message) { build(:facebook_message, facebook_retailer: facebook_retailer) }

        before do
          allow(facebook_message).to receive_messages(get_current_bot: true, url: nil)
        end

        it 'assigns false to @media' do
          facebook_message.send(:chat_bot_execution)
          expect(facebook_message.instance_variable_get(:@media)).to be_falsey
        end
      end

      context 'when chat bot version is 1' do
        let(:chat_bot) { build(:chat_bot, :for_messenger, retailer: retailer, version: 1) }
        let(:facebook_message) { build(:facebook_message, facebook_retailer: facebook_retailer) }
        let(:chat_bot_process) { instance_double(ChatBots::ChatBotProcess, manage_chat_bot: true) }

        before do
          allow(facebook_message).to receive(:get_current_bot).and_return(chat_bot)
          allow(ChatBots::ChatBotProcess).to receive(:new).and_return(chat_bot_process)
        end

        it 'calls manage_chat_bot v1' do
          facebook_message.send(:chat_bot_execution)
          expect(chat_bot_process).to have_received(:manage_chat_bot)
        end
      end

      context 'when chat bot version is 2' do
        let(:chat_bot) { build(:chat_bot, :for_messenger, retailer: retailer, version: 2) }
        let(:facebook_message) { build(:facebook_message, facebook_retailer: facebook_retailer) }
        let(:chat_bot_process) { instance_double(ChatBots::V2::ProcessChatbot, manage_chat_bot: true) }

        before do
          allow(facebook_message).to receive(:get_current_bot).and_return(chat_bot)
          allow(ChatBots::V2::ProcessChatbot).to receive(:new).and_return(chat_bot_process)
        end

        it 'calls manage_chat_bot v2' do
          facebook_message.send(:chat_bot_execution)
          expect(chat_bot_process).to have_received(:manage_chat_bot)
        end
      end
    end

    describe '#send_bot_message' do
      let(:customer) { create(:customer, :messenger, retailer: retailer) }
      let(:facebook_message) { build(:facebook_message, facebook_retailer: facebook_retailer, customer: customer) }

      context 'when message, file_url and file_data are not present' do
        let(:params) { {} }

        it 'does not create a FacebookMessage' do
          expect { facebook_message.send_bot_message(params) }.not_to change(described_class, :count)
        end

        it 'returns nil' do
          expect(facebook_message.send_bot_message(params)).to be_nil
        end
      end

      context 'when message is present' do
        let(:params) { { message: 'Hello, world!' } }
        let(:facebook_service) { instance_double(Facebook::Messages, send_message: {}, send_attachment: {}) }

        before do
          allow_any_instance_of(described_class).to receive(:facebook_service).and_return(facebook_service)
        end

        it 'calls create on FacebookMessage' do
          allow(described_class).to receive(:create).and_return(true)
          facebook_message.send_bot_message(params)
          expect(described_class).to have_received(:create)
        end

        it 'creates a FacebookMessage with the correct params' do
          allow(described_class).to receive(:create).and_return(true)
          facebook_message.send_bot_message(params)
          expect(described_class).to have_received(:create).with({
            customer: facebook_message.customer,
            facebook_retailer: facebook_message.facebook_retailer,
            text: params[:message], payload: params,
            sent_from_mercately: true, sent_by_retailer: true,
            file_content_type: nil, file_data: nil, file_type: nil, file_url: nil, filename: nil, id_client: nil
          })
        end

        it 'creates a FacebookMessage with the correct attributes' do
          expect { facebook_message.send_bot_message(params) }.to change(described_class, :count).by(1)
          message = described_class.last
          expect(message.customer).to eq(customer)
          expect(message.id_client).to eq(customer.psid)
          expect(message.facebook_retailer).to eq(facebook_retailer)
          expect(message.text).to eq(params[:message])
          expect(message.sent_from_mercately).to be true
          expect(message.sent_by_retailer).to be true
          expect(message.payload.values).to eq(params.values)
        end
      end
    end
  end
end
