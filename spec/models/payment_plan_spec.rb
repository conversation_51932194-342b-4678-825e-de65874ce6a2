require 'rails_helper'

RSpec.describe PaymentPlan do
  subject { payment_plan }

  let(:retailer) { create(:retailer, :qr_integrated) }
  let(:logout_response) { { 'success' => true } }
  let(:payment_plan) { retailer.payment_plan }

  before do
    allow(Connection).to receive(:prepare_connection).and_return(true)
  end

  describe 'associations' do
    before do
      allow_any_instance_of(described_class).to receive(:min_quantity_of_agents).and_return(0)
    end

    it { is_expected.to belong_to(:retailer) }
    it { is_expected.to belong_to(:sales_development).class_name('Responsible').optional }
    it { is_expected.to belong_to(:sales_agent).class_name('Responsible').optional }
    it { is_expected.to belong_to(:customer_success).class_name('Responsible').optional }
    it { is_expected.to belong_to(:support_agent).class_name('Responsible').optional }
    it { is_expected.to belong_to(:onboarding_specialist).class_name('Responsible').optional }
    it { is_expected.to belong_to(:pricing_tier).optional }
    it { is_expected.to have_many(:acquired_addons) }
    it { is_expected.to have_many(:addons).through(:acquired_addons) }
  end

  describe 'enums' do
    let(:plans_enum) do
      {
        'free' => 0,
        'professional' => 1,
        'advanced' => 2,
        'enterprise' => 3,
        'startup' => 4,
        'business' => 5,
        'basic' => 6,
        'pro' => 7,
        'premium' => 8,
        'launch' => 9,
        'scale' => 10,
        'rocket' => 11
      }
    end

    it { is_expected.to define_enum_for(:status) }
    it { is_expected.to define_enum_for(:plan) }
    it { is_expected.to define_enum_for(:whatsapp_integration_type) }
    it { expect(described_class.statuses).to eq({ 'active' => 0, 'inactive' => 1 }) }
    it { expect(described_class.plans).to eq(plans_enum) }
    it { expect(described_class.whatsapp_integration_types).to eq({ 'qr' => 0, 'api' => 1 }) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:price) }
    it { is_expected.to validate_numericality_of(:month_interval).is_greater_than(0) }
    it { is_expected.to validate_numericality_of(:mia_contact_cost).is_greater_than_or_equal_to(0) }

    context 'when quantity of agents is valid' do
      let(:retailer) { create(:retailer) }
      let(:payment_plan) { retailer.payment_plan }

      before do
        allow(retailer).to receive(:count_active_team_agents).and_return(5)
      end

      it 'does not add an error on max_agents' do
        payment_plan.max_agents = 10
        payment_plan.valid?
        expect(retailer.errors[:max_agents]).to be_empty
      end
    end

    context 'when quantity of agents is not valid' do
      let(:retailer) { create(:retailer) }
      let(:payment_plan) { retailer.payment_plan }

      before do
        allow(retailer).to receive(:count_active_team_agents).and_return(11)
      end

      it 'adds an error on max_agents' do
        payment_plan.max_agents = 10
        payment_plan.valid?
        expect(payment_plan.errors[:max_agents]).to include("can't be less than the quantity of active agents")
      end
    end
  end

  describe 'scopes' do
    before do
      described_class.delete_all
    end

    let(:paying_payment_plan) { create(:payment_plan, :with_defaults, :enterprise, status: :active) }
    let(:free_payment_plan) { create(:payment_plan, :with_defaults, :free, status: :active) }
    let(:inactive_payment_plan) { create(:payment_plan, :with_defaults, :enterprise, status: :inactive) }
    let(:soon_to_due_payment_plan) do
      create(:payment_plan, :with_defaults, :enterprise, status: :active, next_pay_date: DateTime.now + 2.days)
    end

    describe '.paying' do
      before do
        paying_payment_plan
        free_payment_plan
        inactive_payment_plan
      end

      it 'returns only the paying payment_plans' do
        paying_plans = described_class.paying
        expect(paying_plans.count).to eq(1)
        expect(paying_plans.first.id).to eq(paying_payment_plan.id)
        expect(paying_plans.first.status).to eq('active')
        expect(paying_plans.first.plan).not_to eq('free')
      end
    end

    describe '.soon_to_due_payment' do
      before do
        soon_to_due_payment_plan
        create(:payment_plan, :with_defaults, :enterprise, status: :active, next_pay_date: DateTime.now + 10.days)
      end

      it 'returns the records when next_pay_date is within the given limit' do
        expect(described_class.soon_to_due_payment(DateTime.now + 7.days)).to contain_exactly(soon_to_due_payment_plan)
      end
    end
  end

  describe 'defaults' do
    let(:payment_plan) { create(:payment_plan, :with_defaults, :non_contact_based) }

    it { expect(payment_plan.price).to eq(0.0) }
    it { expect(payment_plan.status).to eq('active') }
    it { expect(payment_plan.status_active?).to be(true) }
    it { expect(payment_plan.status_inactive?).to be(false) }
    it { expect(payment_plan.plan).to eq('free') }
    it { expect(payment_plan.free?).to be(true) }
    it { expect(payment_plan.basic?).to be(false) }
    it { expect(payment_plan.professional?).to be(false) }
    it { expect(payment_plan.advanced?).to be(false) }
    it { expect(payment_plan.enterprise?).to be(false) }
    it { expect(payment_plan.pro?).to be(false) }
    it { expect(payment_plan.premium?).to be(false) }
    it { expect(payment_plan.karix_available_messages).to eq(0) }
    it { expect(payment_plan.karix_available_notifications).to eq(0) }
    it { expect(payment_plan.month_interval).to eq(1) }
    it { expect(payment_plan.charge_attempt).to eq(0) }
  end

  describe 'callbacks' do
    let(:plan) { create(:payment_plan, :non_contact_based, retailer: retailer) }

    describe 'after_update' do
      context 'when the plan attribute changes' do
        it 'triggers the update_subscriptions and remove_onboarding_notifications methods' do
          expect(plan).to receive(:update_subscriptions)
          expect(plan).to receive(:remove_onboarding_notifications)
          expect(plan).to receive(:chargebee_notify)
          expect(plan).to receive(:disable_delete_assets)

          plan.update(plan: :professional)
        end
      end

      context 'when the plan attribute does not change' do
        it 'does not trigger the update_subscriptions and remove_onboarding_notifications methods' do
          expect(plan).not_to receive(:update_subscriptions)
          expect(plan).not_to receive(:remove_onboarding_notifications)
          expect(plan).not_to receive(:disable_delete_assets)

          plan.update(status: :inactive)
        end
      end

      context 'when upgrading from free plan' do
        let(:current_time) { Time.current }

        it 'sets payment_start_date when upgrading to premium plan' do
          plan.update(plan: 'premium')
          expect(plan.payment_start_date).to be_within(1.second).of(current_time)
        end

        it 'sets payment_start_date when upgrading to basic plan' do
          plan.update(plan: 'basic')
          expect(plan.payment_start_date).to be_within(1.second).of(current_time)
        end

        context 'when payment_start_date is already set' do
          let(:existing_date) { 1.day.ago }
          let(:payment_plan) do
            create(:payment_plan, plan: 'free', retailer: retailer, payment_start_date: existing_date)
          end

          it 'does not change payment_start_date when upgrading' do
            payment_plan.update(plan: 'premium')
            expect(payment_plan.payment_start_date).to eq(existing_date)
          end
        end

        context 'when not upgrading from free plan' do
          let(:payment_plan) do
            create(:payment_plan, plan: 'premium', retailer: retailer, payment_start_date: nil)
          end

          it 'does not change payment_start_date when switching between paid plans' do
            original_date = payment_plan.payment_start_date
            payment_plan.update(plan: 'basic')
            expect(payment_plan.payment_start_date).to eq(original_date)
          end
        end
      end
    end

    describe 'after_save' do
      context 'when the status attribute changes to inactive' do
        it 'triggers the disconnect_qr method' do
          plan = create(:payment_plan, :non_contact_based, retailer: retailer, status: :active)
          expect(plan).to receive(:disconnect_qr)

          plan.update(status: :inactive)
        end
      end

      context 'when the status attribute changes to active' do
        it 'does not trigger the disconnect_qr method' do
          plan = create(:payment_plan, :non_contact_based, retailer: retailer, status: :inactive)
          expect(plan).not_to receive(:disconnect_qr)

          plan.update(status: :active)
        end
      end

      context 'when the status attribute remains inactive' do
        it 'does not trigger the disconnect_qr method' do
          plan = create(:payment_plan, :non_contact_based, retailer: retailer, status: :inactive)
          expect(plan).not_to receive(:disconnect_qr)

          plan.update(plan: :professional)
        end
      end
    end

    describe 'after_commit' do
      describe 'on update' do
        describe '#create_mia_integrations' do
          let!(:guides) { create(:mia_integration_type, :guides) }
          let!(:products) { create(:mia_integration_type, :products) }
          let!(:chatbot) { create(:mia_integration_type, :chatbots) }

          context 'when retailer does not have mia_integrations' do
            it { expect { plan.update(chatbot_ai: true) }.to change(MiaIntegration, :count).by(3) }
          end

          context 'when retailer has mia_integrations' do
            before do
              retailer.mia_integrations.create(mia_integration_type: guides, openai_key: 'openai_key')
              retailer.mia_integrations.create(mia_integration_type: products, openai_key: 'openai_key')
              retailer.mia_integrations.create(mia_integration_type: chatbot, openai_key: 'openai_key')
            end

            it { expect { plan.update(chatbot_ai: true) }.not_to change(MiaIntegration, :count) }
          end
        end

        describe '#delete_mia_integrations' do
          let(:plan) { create(:payment_plan, :non_contact_based, retailer:, chatbot_ai: true) }
          let!(:guides) { create(:mia_integration_type, :guides) }
          let!(:products) { create(:mia_integration_type, :products) }
          let!(:chatbot) { create(:mia_integration_type, :chatbots) }

          before do
            retailer.mia_integrations.create(mia_integration_type: guides, openai_key: 'openai_key')
            retailer.mia_integrations.create(mia_integration_type: products, openai_key: 'openai_key')
            retailer.mia_integrations.create(mia_integration_type: chatbot, openai_key: 'openai_key')
          end

          it { expect { plan.update(chatbot_ai: false) }.to change(MiaIntegration, :count).by(-3) }
          it { expect { plan.update(status: :inactive) }.to change(MiaIntegration, :count).by(-3) }
          it { expect { plan.update(mia_free_contacts: 1) }.not_to change(MiaIntegration, :count) }

          context 'with MIA products and chatbot active' do
            let!(:mia_platform_uno) { create(:mia_platform) }

            before do
              retailer.retailer_mia_platforms.create(mia_platform: mia_platform_uno)
              retailer.update(mia_chatbot_active: true, mia_products_sync: true, mia_products_synced: 'complete')
            end

            it { expect { plan.update(chatbot_ai: false) }.to change(MiaIntegration, :count).by(-3) }
            it { expect { plan.update(chatbot_ai: false) }.to change(retailer, :mia_chatbot_active).to(false) }
            it { expect { plan.update(chatbot_ai: false) }.to change(retailer, :mia_products_sync).to(false) }
            it { expect { plan.update(chatbot_ai: false) }.to change(retailer, :mia_products_synced).to('not_synced') }
            it { expect { plan.update(chatbot_ai: false) }.to change(retailer.retailer_mia_platforms, :count).by(-1) }
          end
        end
      end
    end

    describe 'after_save callback' do
      let!(:partner) { create(:partner) }
      let!(:retailer) { create(:retailer, partner: partner) }

      context 'when a PaymentPlan is saved' do
        it 'actualiza el MRR del Partner' do
          expect(partner.mrr).to eq(0) # Inicialmente el MRR es 0

          create(:payment_plan, retailer: retailer, price: 300, month_interval: 3, status: :active, plan: :pro)

          partner.reload
          expect(partner.mrr).to eq(100) # 300/3 = 100
        end
      end

      context 'when a PaymentPlan is updated' do
        let!(:payment_plan) do
          create(:payment_plan, retailer: retailer, price: 300, month_interval: 3, status: :active, plan: :pro)
        end

        it 'actualiza el MRR del Partner' do
          partner.reload
          expect(partner.mrr).to eq(100) # 300/3 = 100

          payment_plan.update!(price: 600) # Ahora debería ser 600/3 = 200

          partner.reload
          expect(partner.mrr).to eq(200)
        end
      end
    end

    describe 'after_destroy callback' do
      let!(:partner) { create(:partner) }
      let!(:retailer) { create(:retailer, partner: partner) }
      let!(:payment_plan) do
        create(:payment_plan, retailer: retailer, price: 300, month_interval: 3, status: :active, plan: :pro)
      end

      it 'actualiza el MRR del Partner cuando se elimina un PaymentPlan' do
        partner.reload
        expect(partner.mrr).to eq(100) # 300/3 = 100

        expect { payment_plan.destroy }.to change(described_class, :count).by(-1) # Aseguramos que se eliminó

        partner.reload
        expect(partner.mrr).to eq(0) # Sin plan, MRR debe ser 0
      end
    end
  end

  describe '#remove_onboarding_notifications' do
    it 'destroys demo onboarding notifications for retailer users' do
      retailer = create(:retailer)
      payment_plan = create(:payment_plan, retailer: retailer)
      initial_count = OnboardingNotification.count
      create_list(:onboarding_notification, 9, retailer_user: create(:retailer_user, retailer: retailer))

      expect do
        payment_plan.remove_onboarding_notifications
      end.to change(OnboardingNotification, :count).by(-OnboardingNotification.count + initial_count)
    end
  end

  describe '#allowed_charges' do
    let(:allowed_charges) do
      [
        { internal_column: 'make_my_chatbot', id: 'Hazme-mi-chatbot-USD' },
        { internal_column: 'mercately_setup', id: 'Mercately-Setup-3-hours-USD' }
      ]
    end

    it 'returns the correct array of allowed charges' do
      plan = create(:payment_plan)
      expect(plan.allowed_charges).to eq(allowed_charges)
    end
  end

  describe '#charge!' do
    let(:retailer) { create(:retailer) }
    let(:payment_plan) { create(:payment_plan, retailer: retailer, price: 100) }

    context 'when retailer has internal charges' do
      let(:success_transaction) { double('transaction', save: true) }

      before do
        allow(retailer).to receive(:int_charges).and_return(true)
        allow_any_instance_of(Stripe::Api).to receive(:charge_attempt).and_return({ success: true })
        allow(retailer).to receive_message_chain(:payment_methods, :any?).and_return(true)
      end

      it 'creates a stripe transaction' do
        pm = double('payment_method')
        allow(retailer).to receive_message_chain(:payment_methods, :main).and_return(pm)
        allow(retailer).to receive_message_chain(:stripe_transactions, :create).and_return(success_transaction)

        expect(payment_plan.charge!).to be true
      end
    end

    context 'when creates a stripe transaction but fails the payment' do
      let(:error_message) { 'Error de Stripe' }
      let(:payment_method) { create(:payment_method, retailer: retailer, main: true) }

      before do
        allow(retailer).to receive(:int_charges).and_return(true)
        allow(Stripe::PaymentIntent).to receive(:create).and_raise(StandardError.new(error_message))
        allow(retailer).to receive_message_chain(:payment_methods, :any?).and_return(true)
        allow(retailer.payment_methods).to receive(:update_all).with(main: false).and_return(true)
      end

      it 'creates a stripe transaction and sets payment_failed' do
        allow(retailer).to receive_message_chain(:payment_methods, :main).and_return(payment_method)

        expect(payment_plan.charge!).to be false
      end
    end

    context 'when retailer does not have internal charges' do
      before do
        allow(retailer).to receive(:int_charges).and_return(false)
        allow(retailer).to receive_message_chain(:paymentez_credit_cards, :main, :create_transaction).and_return(true)
      end

      it 'creates a paymentez transaction' do
        payment_plan.update(next_pay_date: Time.zone.today)
        expect(payment_plan.charge!).to be true
      end

      it 'fails to create a paymentez transaction and calls payment_failed' do
        payment_plan.update(next_pay_date: Time.zone.today)
        allow(retailer).to receive_message_chain(:paymentez_credit_cards, :main, :create_transaction).and_return(false)
        allow(payment_plan).to receive(:payment_failed)

        expect(payment_plan.charge!).to be false
      end
    end
  end

  describe '#notify_slack' do
    let(:message_array) do
      [
        '🎉 Tiempo de cobrar 🎉',
        "Retailer: (#{retailer.id})#{retailer.name}",
        "Email para la factura: #{retailer.admins.pluck(:email).join(', ')}",
        "Teléfono de contacto: #{retailer.phone_number}",
        'Monto: 100',
        'Plan: free',
        'Meses del plan: 1',
        'Status del mes pasado: active',
        "Fecha de próximo cobro: #{(Time.zone.today + 1.month).strftime('%m/%d/%Y')}",
        "Cantidad de asesores: Admins (#{retailer.admins.count}), Supervisores (#{retailer.supervisors.count}), " \
        "Asesores (#{retailer.retailer_users.active_agents.count})",
        'Notas: '
      ]
    end

    context 'without facebook and instagram integrated' do
      before do
        allow(ENV).to receive(:[]).and_call_original
        allow(ENV).to receive(:[]).with('SLACK_WEBHOOK').and_return('webhook_url')
        allow(ENV).to receive(:[]).with('ENVIRONMENT').and_return('production')
        allow(ENV).to receive(:[]).with('SLACK_SDRLEADS').and_return('sdrleads_webhook_url')
        allow(ENV).to receive(:[]).with('SLACK_WEBHOOK').and_return('webhook_url')
        allow(ENV).to receive(:[]).with('ENVIRONMENT').and_return('production')
        allow(Slack::Notifier).to receive(:new).and_return(slack_notifier)
      end

      let(:payment_plan) { create(:payment_plan, retailer: retailer, price: 100) }
      let(:slack_notifier) { instance_double(Slack::Notifier) }

      it 'sends a notification message to Slack', :vcr do
        VCR.use_cassette('payment_plan_notify_slack') do
          message_array.join("\n")

          expect(slack_notifier).to receive(:ping).with(
            include("Fecha de próximo cobro: #{(Time.zone.today + 1.month).strftime('%m/%d/%Y')}")
          )
          payment_plan.notify_slack
        end
      end
    end

    context 'with facebook integrated' do
      let(:retailer) { create(:retailer) }
      let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
      let(:customer) { create(:customer, retailer: retailer) }
      let(:facebook_retailer) { create(:facebook_retailer, :with_messenger, retailer: retailer) }
      let(:payment_plan) { create(:payment_plan, retailer: retailer, price: 100, next_pay_date: 1.day.from_now) }
      let!(:facebook_message) do
        create(:facebook_message, customer: customer, facebook_retailer: facebook_retailer, sent_by_retailer: true)
      end

      it 'sends a notification message to Slack' do
        expect_any_instance_of(Slack::Notifier).to receive(:ping).and_return(true)
        payment_plan.notify_slack
      end
    end

    context 'with instagram integrated' do
      let(:retailer) { create(:retailer) }
      let!(:retailer_user) { create(:retailer_user, retailer: retailer) }
      let(:customer) { create(:customer, retailer: retailer) }
      let(:instagram_retailer) { create(:facebook_retailer, :with_instagram, retailer: retailer) }
      let(:payment_plan) { create(:payment_plan, retailer: retailer, price: 100, next_pay_date: 1.day.from_now) }
      let!(:facebook_message) do
        create(:facebook_message, customer: customer, facebook_retailer: instagram_retailer, sent_by_retailer: true)
      end

      it 'sends a notification message to Slack' do
        expect_any_instance_of(Slack::Notifier).to receive(:ping).and_return(true)
        payment_plan.notify_slack
      end
    end
  end

  describe '#active?' do
    let!(:payment_plan) { create(:payment_plan, :with_defaults) }

    it 'status is active' do
      payment_plan.update(status: 0)
      expect(payment_plan.active?).to be(true)
    end

    it 'status is inactive' do
      payment_plan.update(status: 1)
      expect(payment_plan.active?).to be(false)
    end
  end

  describe '#process_cancellation' do
    let(:payment_plan) { create(:payment_plan) }

    context 'when cancel_now? is true' do
      before { allow(payment_plan).to receive(:cancel_now?).and_return(true) }

      it 'sets status to inactive and returns true' do
        expect(payment_plan.process_cancellation).to be true
        expect(payment_plan).to be_status_inactive
      end
    end

    context 'when cancel_now? is false' do
      before { allow(payment_plan).to receive(:cancel_now?).and_return(false) }

      it 'returns true without changing status' do
        expect(payment_plan.process_cancellation).to be true
        expect(payment_plan).not_to be_status_inactive
      end
    end
  end

  describe '#cancel_now?' do
    let(:payment_plan) { create(:payment_plan) }

    before { Timecop.freeze(1.day.from_now.end_of_day) }
    after { Timecop.return }

    it 'returns true when next_pay_date is blank' do
      payment_plan.next_pay_date = nil
      expect(payment_plan.cancel_now?).to be true
    end

    it 'returns true when next_pay_date is today or in the past' do
      payment_plan.next_pay_date = Time.zone.today
      expect(payment_plan.cancel_now?).to be true
    end

    it 'returns false when next_pay_date is in the future' do
      payment_plan.next_pay_date = Date.tomorrow
      expect(payment_plan.cancel_now?).to be false
    end
  end

  describe '#await_cancellation?' do
    let(:retailer) { create(:retailer) }
    let(:payment_plan) { create(:payment_plan, retailer: retailer) }

    it 'returns true when there is a pending cancellation' do
      create(:plan_cancellation, retailer: retailer, cancellation_date: Date.tomorrow, desist_cancellation: false)
      expect(payment_plan.await_cancellation?).to be true
    end

    it 'returns false when there is no pending cancellation' do
      expect(payment_plan.await_cancellation?).to be false
    end
  end

  describe '#update_subscriptions' do
    let(:retailer) { create(:retailer) }
    let(:payment_plan) { create(:payment_plan, retailer: retailer) }
    let(:retailer_user) { create(:retailer_user, retailer: retailer) }

    it 'calls subscribe on each retailer user' do
      retailer = create(:retailer)
      payment_plan = create(:payment_plan, retailer: retailer)
      retailer_user = create(:retailer_user, retailer: retailer)

      expect(retailer_user).to receive(:subscribe)
      allow(retailer).to receive_message_chain(:retailer_users, :each).and_yield(retailer_user)

      payment_plan.update_subscriptions
    end
  end
  # rubocop:enable RSpec/MessageChain

  describe '#monthly_recurring_revenue' do
    let!(:payment_plan) { create(:payment_plan, :with_defaults, retailer: retailer) }
    let!(:free_payment_plan) do
      create(:payment_plan, :with_defaults, retailer: retailer, price: 300, month_interval: 3)
    end

    context 'when price or month_interval are nil' do
      it 'return zero with price nil' do
        payment_plan.update(price: nil, month_interval: 2)
        expect(payment_plan.reload.monthly_recurring_revenue).to eq(0)
      end

      it 'return zero with month_interval nil' do
        payment_plan.update(price: 150, month_interval: nil)
        expect(payment_plan.reload.monthly_recurring_revenue).to eq(0)
      end
    end

    context 'when price and month_interval are not nil' do
      it 'returns monthly recurring revenue' do
        payment_plan.update(price: 300, month_interval: 3)
        expect(payment_plan.monthly_recurring_revenue).to eq(100)
      end
    end
  end

  describe '#month_interval_label' do
    let!(:payment_plan) { create(:payment_plan, :with_defaults, retailer: retailer) }

    it 'returns the correct label for monthly' do
      payment_plan.update(month_interval: 1)
      expect(payment_plan.month_interval_label)
        .to eq(I18n.t('retailer.profile.payment_plans.month_interval_label.monthly'))
    end

    it 'returns the correct label for bimonthly' do
      payment_plan.update(month_interval: 2)
      expect(payment_plan.month_interval_label)
        .to eq(I18n.t('retailer.profile.payment_plans.month_interval_label.bimonthly'))
    end

    it 'returns the correct label for trimonthly' do
      payment_plan.update(month_interval: 3)
      expect(payment_plan.month_interval_label)
        .to eq(I18n.t('retailer.profile.payment_plans.month_interval_label.trimonthly'))
    end

    it 'returns the correct label for six-monthly' do
      payment_plan.update(month_interval: 6)
      expect(payment_plan.month_interval_label)
        .to eq(I18n.t('retailer.profile.payment_plans.month_interval_label.six_monthly'))
    end

    it 'returns the correct label for yearly' do
      payment_plan.update(month_interval: 12)
      expect(payment_plan.month_interval_label)
        .to eq(I18n.t('retailer.profile.payment_plans.month_interval_label.yearly'))
    end

    it 'returns the original value for unknown intervals' do
      payment_plan.update(month_interval: 4)
      expect(payment_plan.month_interval_label).to eq(4)
    end
  end

  describe '#changed_to_paying_plan' do
    let(:payment_plan) { create(:payment_plan, plan: 'free') }

    it 'returns true when plan changes to a paying plan' do
      payment_plan.update(plan: 'free')
      expect do
        payment_plan.update(plan: 'pro')
      end.to change(payment_plan, :changed_to_paying_plan).from(false).to(true)
    end

    it 'returns false when plan remains free' do
      payment_plan.price = 10
      expect(payment_plan.changed_to_paying_plan).to be false
    end
  end

  describe '#chargebee_subscription' do
    let(:payment_plan) { create(:payment_plan, chargebee_subscription_id: 'sub_123') }
    let(:chargebee_api) { instance_double(Chargebee::Api) }
    let(:subscription) { double('Subscription') }

    before do
      allow(payment_plan).to receive(:chargebee).and_return(chargebee_api)
      allow(chargebee_api).to receive(:subscription).with('sub_123').and_return(double(subscription: subscription))
    end

    it 'returns the chargebee subscription' do
      expect(payment_plan.chargebee_subscription).to eq(subscription)
    end
  end

  describe '#chargebee_plan_id' do
    let(:payment_plan) { build(:payment_plan) }

    context 'when both chargebee_subscription_id and pricing_tier_id are nil' do
      it 'returns nil' do
        payment_plan.chargebee_subscription_id = nil
        payment_plan.pricing_tier_id = nil
        expect(payment_plan.chargebee_plan_id).to be_nil
      end
    end

    context 'when chargebee_subscription_id is present' do
      let(:chargebee_api) { double('Chargebee::Api') }
      let(:cb_subscription) { double('CBSubscription', subscription_items: [double('Item', item_price_id: 'plan_123')]) }
      let(:cb_result) { double('CBResult', subscription: cb_subscription) }

      before do
        payment_plan.chargebee_subscription_id = 'sub_123'
        allow(payment_plan).to receive(:chargebee).and_return(chargebee_api)
        allow(chargebee_api).to receive(:subscription).with('sub_123').and_return(cb_result)
      end

      it 'returns the item_price_id from the chargebee subscription' do
        expect(payment_plan.chargebee_plan_id).to eq('plan_123')
      end
    end

    context 'when chargebee_subscription_id is nil but pricing_tier_id is present' do
      let(:plan) { create(:plan) }
      let(:pricing_tier) { create(:pricing_tier, identifier: 'tier_abc', priceable: plan) }

      before do
        payment_plan.chargebee_subscription_id = nil
        payment_plan.pricing_tier_id = pricing_tier.id
        allow(payment_plan).to receive(:pricing_tier).and_return(pricing_tier)
      end

      it 'returns the identifier from the pricing_tier' do
        expect(payment_plan.chargebee_plan_id).to eq('tier_abc')
      end

      it 'returns nil if pricing_tier is nil' do
        allow(payment_plan).to receive(:pricing_tier).and_return(nil)
        expect(payment_plan.chargebee_plan_id).to be_nil
      end
    end
  end

  describe '#chargebee_invoices' do
    let(:payment_plan) { create(:payment_plan, chargebee_subscription_id: 'sub_123') }
    let(:chargebee_api) { instance_double(Chargebee::Api) }
    let(:invoice) { double('Invoice', id: 'inv_123', date: Time.now.to_i, total: 10_000, status: 'paid') }

    before do
      allow(payment_plan).to receive(:chargebee).and_return(chargebee_api)
      allow(chargebee_api).to receive(:invoices).with('sub_123').and_return([invoice])
      allow(chargebee_api).to receive(:invoice_pdf).with('inv_123').and_return(double(download_url: 'http://example.com/invoice.pdf'))
    end

    it 'returns formatted invoice information' do
      invoices = payment_plan.chargebee_invoices
      expect(invoices.size).to eq(1)
      expect(invoices.first[:id]).to eq('inv_123')
      expect(invoices.first[:amount]).to eq(100.0)
      expect(invoices.first[:status]).to eq('paid')
      expect(invoices.first[:pdf]).to eq('http://example.com/invoice.pdf')
    end
  end

  describe 'private methods' do
    describe '#match_chargebee_subscription' do
      let(:retailer) { create(:retailer) }
      let(:chargebee) { instance_double(Chargebee::Api) }
      let(:subscription) { instance_double(ChargeBee::Subscription) }
      let(:subscription_item) { double('Item', item_price_id: 'Plan-Business-USD-Monthly', amount: 1000) }
      let(:result) { double('Result', subscription: subscription) }
      let(:payment_plan) { create(:payment_plan, chargebee_subscription_id: 'sub_123') }

      context 'when chargebee subscription is not present' do
        before do
          allow(payment_plan).to receive(:chargebee_subscription).and_return(nil)
        end

        it 'returns nil' do
          expect(payment_plan.match_chargebee_subscription).to be_nil
        end
      end

      context 'when chargebee subscription ID exists' do
        before do
          allow(chargebee).to receive(:subscription).and_return(result)
          allow(result).to receive(:subscription).and_return(subscription)
          allow(subscription).to receive_messages(subscription_items: [subscription_item], billing_period: 1,
                                                  billing_period_unit: 'month', next_billing_at: DateTime.now.to_i)

          allow(payment_plan).to receive_messages(chargebee: chargebee, find_plan: 'pro', calc_month_interval: 1,
                                                  max_agents_included: 5)
        end

        it 'matches chargebee subscription and updates attributes' do
          expect(payment_plan).to receive(:update).with(
            plan: 'pro', month_interval: 1, price: 10.0, next_pay_date: anything, max_agents: 5,
            accept_terms_and_conditions: true, status: 'active', end_date: nil, charge_attempt: 0
          )

          payment_plan.match_chargebee_subscription
        end

        it 'update max_agents from retailer' do
          payment_plan.update(chargebee_subscription_id: '12345')
          expect(payment_plan.retailer.max_agents).to eq(payment_plan.max_agents)
        end
      end

      context 'when chargebee subscription ID does not exist' do
        before do
          allow(payment_plan).to receive(:chargebee_subscription_id).and_return(nil)
        end

        it 'does not attempt to match chargebee subscription' do
          expect(payment_plan).not_to receive(:chargebee_subscription)
          expect(payment_plan).not_to receive(:update)
          payment_plan.send(:match_chargebee_subscription)
        end
      end
    end

    describe '#chargebee_notify' do
      let(:instance) { described_class.new }

      context 'when notification has already been sent' do
        before do
          allow(instance).to receive(:notification_sent?).and_return(true)
        end

        it 'does not notify chargebee to Slack' do
          expect(instance).not_to receive(:notify_chargebee_to_slack)
          instance.send(:chargebee_notify)
        end
      end

      context 'when notification has not been sent yet' do
        before do
          allow(instance).to receive_messages(notification_sent?: false, chargebee_subscription_id: true)
        end

        context 'when plan is basic, pro or premium' do
          before do
            allow(instance).to receive_messages(plan: 'pro', chargebee_subscription_id: '1234')
          end

          it 'notifies chargebee to Slack' do
            expect(instance).to receive(:notify_chargebee_to_slack)
            instance.send(:chargebee_notify)
          end
        end

        context 'when plan is not basic, pro, or premium' do
          before do
            allow(instance).to receive(:plan).and_return('enterprise')
          end

          it 'does not notify chargebee to Slack' do
            expect(instance).not_to receive(:notify_chargebee_to_slack)
            instance.send(:chargebee_notify)
          end
        end
      end
    end

    describe '#notify_chargebee_to_slack' do
      let(:plan_mock) { described_class.new }
      let(:channel_mock) { instance_double(Slack::Notifier) }

      before do
        allow(ENV).to receive(:[]).with('ENVIRONMENT').and_return('production')
        allow(ENV).to receive(:[]).with('SLACK_PAYMENTS_CHARGEBEE').and_return('')
        allow(Slack::Notifier).to receive(:new).and_return(channel_mock)
        allow(plan_mock).to receive_messages(retailer: double(id: 1, name: 'Retailer Name'), plan: 'premium',
                                             month_interval: 1, mercately_setup?: false, make_my_chatbot?: false,
                                             additional_agents: 0, next_pay_date: Time.zone.today, price: 100)
        allow(plan_mock).to receive(:update_column)
      end

      it 'sends notification message to Slack channel' do
        expect(channel_mock).to receive(:ping).with(
          "🎉 Nuevo plan contratado 🎉\n" \
          "Retailer: (1) Retailer Name\n" \
          "Plan: premium\n" \
          "Intervalo: 1 mes\n" \
          "Próximo pago: #{Time.zone.today.strftime('%m/%d/%Y')}\n" \
          'Total: 100'
        )
        plan_mock.send(:notify_chargebee_to_slack)
      end

      context 'when additional features are enabled' do
        before do
          allow(plan_mock).to receive_messages(mercately_setup?: true, additional_agents: 3)
        end

        it 'includes additional features in notification message' do
          expect(channel_mock).to receive(:ping).with(
            "🎉 Nuevo plan contratado 🎉\n" \
            "Retailer: (1) Retailer Name\n" \
            "Plan: premium\nIntervalo: 1 mes\n" \
            "Adicionales:\nMercately Setup\n" \
            "Agentes: 3\nPróximo pago: #{Time.zone.today.strftime('%m/%d/%Y')}\n" \
            'Total: 100'
          )
          plan_mock.send(:notify_chargebee_to_slack)
        end
      end

      context 'when make_my_chatbot is enabled' do
        before do
          allow(plan_mock).to receive_messages(make_my_chatbot?: true, additional_agents: 3)
        end

        it 'includes additional features in notification message' do
          expect(channel_mock).to receive(:ping).with(
            "🎉 Nuevo plan contratado 🎉\n" \
            "Retailer: (1) Retailer Name\n" \
            "Plan: premium\nIntervalo: 1 mes\n" \
            "Adicionales:\nHazme mi chatbot\n" \
            "Agentes: 3\nPróximo pago: #{Time.zone.today.strftime('%m/%d/%Y')}\n" \
            'Total: 100'
          )
          plan_mock.send(:notify_chargebee_to_slack)
        end
      end

      context 'when month interval is greater than 1' do
        before do
          allow(plan_mock).to receive_messages(month_interval: 2, additional_agents: 3)
        end

        it 'includes additional features in notification message' do
          expect(channel_mock).to receive(:ping).with(
            "🎉 Nuevo plan contratado 🎉\n" \
            "Retailer: (1) Retailer Name\n" \
            "Plan: premium\n" \
            "Intervalo: 2 meses\n" \
            "Agentes: 3\n" \
            "Próximo pago: #{Time.zone.today.strftime('%m/%d/%Y')}\n" \
            'Total: 100'
          )
          plan_mock.send(:notify_chargebee_to_slack)
        end
      end
    end

    describe '#notify_partner_of_failed_payment' do
      let(:partner) { create(:partner) }
      let(:retailer) { create(:retailer, partner: partner) }
      let(:payment_plan) { retailer.payment_plan }

      context 'when charge_attempt is increased' do
        it 'sends an email to the partner' do
          # expect(PartnerMailer).to receive(:retailer_payment_failed).with(retailer, partner).and_call_original
          expect do
            payment_plan.update(charge_attempt: payment_plan.charge_attempt + 1)
          end.to have_enqueued_mail(PartnerMailer, :retailer_payment_failed)
        end
      end

      context 'when charge_attempt is not increased' do
        it 'does not send an email to the partner' do
          expect(PartnerMailer).not_to receive(:retailer_payment_failed)
          expect do
            payment_plan.update(charge_attempt: payment_plan.charge_attempt)
          end.not_to have_enqueued_mail(PartnerMailer, :retailer_payment_failed)
        end

        it 'does not send an email when charge_attempt is decreased' do
          expect(PartnerMailer).not_to receive(:retailer_payment_failed)
          expect do
            payment_plan.update(charge_attempt: payment_plan.charge_attempt - 1)
          end.not_to have_enqueued_mail(PartnerMailer, :retailer_payment_failed)
        end
      end

      context 'when there is no partner' do
        it 'does not send an email' do
          allow(retailer).to receive(:partner).and_return(nil)
          expect(PartnerMailer).not_to receive(:retailer_payment_failed)
          expect do
            payment_plan.update(charge_attempt: payment_plan.charge_attempt + 1)
          end.not_to have_enqueued_mail(PartnerMailer, :retailer_payment_failed)
        end
      end
    end

    describe '#update_delete_assets' do
      let(:payment_plan) { create(:payment_plan, plan: 'basic') }
      let(:retailer) { payment_plan.retailer }

      context 'when plan changes to pro' do
        it 'updates delete_assets to false if it was true' do
          retailer.update(delete_assets: true)
          expect { payment_plan.update(plan: 'pro') }.to change { retailer.reload.delete_assets }.from(true).to(false)
        end

        it 'does not change delete_assets if it was already false' do
          retailer.update(delete_assets: false)
          expect { payment_plan.update(plan: 'pro') }.not_to(change { retailer.reload.delete_assets })
        end
      end

      context 'when plan changes to premium' do
        it 'updates delete_assets to false if it was true' do
          retailer.update(delete_assets: true)
          expect { payment_plan.update(plan: 'premium') }.to change {
                                                               retailer.reload.delete_assets
                                                             }.from(true).to(false)
        end

        it 'does not change delete_assets if it was already false' do
          retailer.update(delete_assets: false)
          expect { payment_plan.update(plan: 'premium') }.not_to(change { retailer.reload.delete_assets })
        end
      end

      context 'when plan changes to a non-pro/premium plan' do
        it 'does not change delete_assets' do
          retailer.update(delete_assets: true)
          expect { payment_plan.update(plan: 'basic') }.not_to(change { retailer.reload.delete_assets })
        end
      end
    end
  end

  describe 'custom validation :check_pricing_tier' do
    let(:retailer) { create(:retailer) }
    let(:plan) { create(:plan, identifier: 'Launch') }
    let(:pricing_tier) { create(:pricing_tier, :monthly, priceable: plan) }

    context 'when pricing_tier_id is not present' do
      it 'does not run the validation' do
        payment_plan = build(:payment_plan, retailer: retailer, pricing_tier_id: nil)
        expect(payment_plan).to be_valid
      end
    end

    context 'when pricing_tier_id is present' do
      it 'is valid if plan and month_interval match pricing_tier' do
        plan_name = pricing_tier.priceable.identifier.downcase
        payment_plan = build(:payment_plan,
          retailer: retailer,
          pricing_tier: pricing_tier,
          plan: plan_name,
          month_interval: pricing_tier.month_interval
        )
        expect(payment_plan).to be_valid
      end

      it 'is invalid if plan does not match pricing_tier' do
        payment_plan = build(:payment_plan,
          retailer: retailer,
          pricing_tier: pricing_tier,
          plan: 'scale',
          month_interval: pricing_tier.month_interval
        )
        expect(payment_plan).not_to be_valid
        expect(payment_plan.errors[:plan]).to include('Plan no coincide con el plan de la tabla de precios')
      end

      it 'is invalid if month_interval does not match pricing_tier' do
        plan_name = pricing_tier.priceable.identifier.downcase
        payment_plan = build(:payment_plan,
          retailer: retailer,
          pricing_tier: pricing_tier,
          plan: plan_name,
          month_interval: pricing_tier.month_interval + 1
        )
        expect(payment_plan).not_to be_valid
        expect(payment_plan.errors[:plan]).to include('Plan no coincide con el plan de la tabla de precios')
      end
    end
  end

  describe '#assign_and_charge!' do
    let(:payment_plan) { build(:payment_plan) }
    let(:subscription) { { id: 'tier-abc' } }
    let(:tier) do
      double(
        'PricingTier',
        identifier: 'tier-abc',
        month_interval: 3,
        priceable: double('Plan', name: 'Pro', identifier: 'PRO')
      )
    end
    let(:plans_utils) { double('Plans::Utils') }

    before do
      allow(PricingTier).to receive(:find_by).with(identifier: 'tier-abc').and_return(tier)
      allow(payment_plan).to receive(:plans_utils).and_return(plans_utils)
      allow(plans_utils).to receive(:calculate_total_price).with(subscription, tier).and_return(12000)
      allow(plans_utils).to receive(:calculate_total_price).with(subscription, tier, false).and_return(12000)
      allow(plans_utils).to receive(:transaction_lines).with(payment_plan, subscription, tier).and_return([:line])
      allow(plans_utils).to receive(:process_entitlements)
      allow(tier).to receive(:priceable).and_return(double('Plan', name: 'Pro', identifier: 'PRO'))
      allow(tier).to receive(:month_interval).and_return(3)
      allow(tier).to receive(:identifier).and_return('tier-abc')
      allow(tier).to receive(:priceable).and_return(double('Plan', name: 'Pro', identifier: 'PRO'))
      allow(payment_plan).to receive(:update).and_return(true)
      allow(payment_plan).to receive(:execute_charge)
      allow(Time.zone).to receive(:today).and_return(Date.new(2024, 1, 1))
    end

    context 'when @result[:success] is true' do
      before { payment_plan.instance_variable_set(:@result, { success: true }) }

      it 'returns success and updates the payment plan' do
        expect(payment_plan).to receive(:update).with(hash_including(:plan, :price, :month_interval, :next_pay_date, :accept_terms_and_conditions, :status, :end_date, :charge_attempt, :pricing_tier)).and_return(true)
        result = payment_plan.assign_and_charge!(subscription)
        expect(result).to eq({ success: true })
      end
    end

    context 'when @result[:status] is not payment_failed' do
      before { payment_plan.instance_variable_set(:@result, { success: false, status: 'pending', message: 'msg' }) }

      it 'returns a redirect hash' do
        result = payment_plan.assign_and_charge!(subscription)
        expect(result).to eq({
          success: false,
          message: 'Su pago fue procesado, pero necesita pasos adicionales para completar su compra.',
          redirect: true
        })
      end
    end

    context 'when @result[:status] is payment_failed' do
      before { payment_plan.instance_variable_set(:@result, { success: false, status: 'payment_failed', message: 'fail msg' }) }

      it 'returns a failure hash with the message' do
        result = payment_plan.assign_and_charge!(subscription)
        expect(result).to eq({ success: false, message: 'fail msg' })
      end
    end
  end

  describe '#bought_addons' do
    let(:plan) { create(:plan, name: 'launch', identifier: 'launch') }
    let(:pricing_tier) { create(:pricing_tier, :monthly, priceable: plan, priceable_type: 'Plan') }
    let!(:payment_plan) { create(:payment_plan, pricing_tier_id: pricing_tier.id, month_interval: 1, plan: 'launch') }
    let(:addon1) { create(:addon) }
    let(:addon2) { create(:addon) }
    let!(:acquired_addon1) { create(:acquired_addon, addon_id: addon1.id, quantity: 2, payment_plan: payment_plan) }
    let!(:acquired_addon2) { create(:acquired_addon, addon_id: addon2.id, quantity: 3, payment_plan: payment_plan) }
    let!(:tier1) { create(:pricing_tier, :monthly, price: 10.0, identifier: 'addon-tier-1', priceable: addon1, priceable_type: 'Addon') }
    let!(:tier2) { create(:pricing_tier, :monthly, price: 5.0, identifier: 'addon-tier-2', priceable: addon2, priceable_type: 'Addon') }

    it 'returns [] if pricing_tier_id is blank' do
      payment_plan.pricing_tier_id = nil
      expect(payment_plan.bought_addons).to eq([])
    end

    it 'returns addon hashes for each acquired addon with a matching tier' do
      result = payment_plan.bought_addons
      expect(result).to eq([
        {
          amount: 2 * 10.0,
          item_price_id: 'addon-tier-1',
          quantity: 2,
          unit_price: 10.0,
          item_type: 'addon'
        },
        {
          amount: 3 * 5.0,
          item_price_id: 'addon-tier-2',
          quantity: 3,
          unit_price: 5.0,
          item_type: 'addon'
        }
      ])
    end

    it 'skips addons with no matching tier' do
      expect(PricingTier).to receive(:where)
        .with(priceable_type: 'Addon', billing_cycle: 'monthly', priceable_id: addon1.id)
        .and_return([])
      expect(PricingTier).to receive(:where)
        .with(priceable_type: 'Addon', billing_cycle: 'monthly', priceable_id: addon2.id)
        .and_return([tier2])

      result = payment_plan.bought_addons
      expect(result).to eq([
        {
          amount: 3 * 5.0,
          item_price_id: 'addon-tier-2',
          quantity: 3,
          unit_price: 5.0,
          item_type: 'addon'
        }
      ])
    end
  end

  describe '#update_and_charge!' do
    let(:plan) { create(:plan, identifier: 'pro', name: 'Pro') }
    let(:plan2) { create(:plan, identifier: 'premium', name: 'Premium') }
    let(:subscription) { { id: 'tier-abc' } }
    let(:tier) { create(:pricing_tier, :quarterly, identifier: 'tier-abc', priceable: plan2) }
    let(:current_tier) { create(:pricing_tier, :quarterly, priceable: plan) }
    let(:payment_plan) do
      create(
        :payment_plan,
        pricing_tier_id: current_tier.id,
        status: 'active',
        charge_attempt: 0,
        month_interval: 3,
        plan: 'pro'
      )
    end
    let(:plans_utils) { double('Plans::Utils') }

    before do
      allow(payment_plan).to receive(:plans_utils).and_return(plans_utils)
      allow(plans_utils).to receive(:prorated_price_by_day).with(payment_plan, tier).and_return(1000)
      allow(plans_utils).to receive(:prorated_price_by_day).with(payment_plan, current_tier).and_return(200)
      allow(plans_utils).to receive(:prorated_price_by_day).with(payment_plan).and_return(200)
      allow(plans_utils).to receive(:calculate_addons_charges).and_return(300)
      allow(plans_utils).to receive(:calculate_total_price).with(subscription, tier).and_return(5000)
      allow(plans_utils).to receive(:calculate_total_price).with(subscription, tier, false).and_return(5000)
      allow(plans_utils).to receive(:transaction_lines).and_return([:line])
      allow(plans_utils).to receive(:process_entitlements)
      allow(payment_plan).to receive(:update).and_return(true)
      allow(payment_plan).to receive(:execute_charge)
      allow(Time.zone).to receive(:today).and_return(Date.new(2024, 1, 1))
    end

    context 'when changing to a different tier with same month_interval and charge succeeds' do
      before do
        payment_plan.instance_variable_set(:@result, { success: true })
      end

      it 'updates the payment plan and returns success' do
        expect(payment_plan).to receive(:update).with(hash_including(:plan, :price, :pricing_tier, :status, :charge_attempt))
        result = payment_plan.update_and_charge!(subscription)
        expect(result).to eq({ success: true })
      end
    end

    context 'when changing to a different tier with different month_interval and charge succeeds' do
      before do
        tier.update(billing_cycle: 'monthly')
        current_tier.update(billing_cycle: 'quarterly')
        payment_plan.instance_variable_set(:@result, { success: true })
      end

      it 'updates the payment plan with new month_interval and next_pay_date' do
        expect(payment_plan).to receive(:update).with(hash_including(:month_interval, :next_pay_date))
        result = payment_plan.update_and_charge!(subscription)
        expect(result).to eq({ success: true })
      end
    end

    context 'when not changing tier (same tier) and charge succeeds' do
      before do
        payment_plan.pricing_tier_id = tier.id
        payment_plan.instance_variable_set(:@result, { success: true })
        allow(plans_utils).to receive(:calculate_addons_charges).and_return(100)
      end

      it 'updates the payment plan and returns success' do
        expect(payment_plan).to receive(:update).with(hash_including(:plan, :price, :pricing_tier, :status, :charge_attempt))
        result = payment_plan.update_and_charge!(subscription)
        expect(result).to eq({ success: true })
      end
    end

    context 'when @result[:success] is true but status is inactive and total is not positive' do
      before do
        payment_plan.instance_variable_set(:@result, { success: true })
        allow(payment_plan).to receive(:status).and_return('inactive')
        allow(plans_utils).to receive(:calculate_addons_charges).and_return(0)
        allow(plans_utils).to receive(:prorated_price_by_day).and_return(0)
        allow(plans_utils).to receive(:transaction_lines).and_return([:line])
      end

      it 'returns an error about pending payments' do
        result = payment_plan.update_and_charge!(subscription)
        expect(result).to eq({
          success: false,
          message: 'Debe cancelar los pagos pendientes antes de hacer cambios a su plan actual.'
        })
      end
    end

    context 'when @result[:status] is not payment_failed' do
      before do
        payment_plan.instance_variable_set(:@result, { success: false, status: 'pending', message: 'msg' })
      end

      it 'returns a redirect hash' do
        result = payment_plan.update_and_charge!(subscription)
        expect(result).to eq({
          success: false,
          message: 'Su pago fue procesado, pero necesita pasos adicionales para completar su compra.',
          redirect: true
        })
      end
    end

    context 'when @result[:status] is payment_failed' do
      before do
        payment_plan.instance_variable_set(:@result, { success: false, status: 'payment_failed', message: 'fail msg' })
        allow(payment_plan).to receive(:status).and_return('inactive')
        allow(payment_plan).to receive(:payment_failed)
      end

      it 'calls payment_failed and returns a failure hash' do
        expect(payment_plan).to receive(:payment_failed).with(force_retry: true)
        result = payment_plan.update_and_charge!(subscription)
        expect(result).to eq({ success: false, message: 'fail msg' })
      end
    end
  end

  describe '#reactivate_and_charge!' do
    let(:plan) { create(:plan, identifier: 'pro', name: 'Pro') }
    let(:plan2) { create(:plan, identifier: 'premium', name: 'Premium') }
    let(:subscription) { { id: 'tier-abc' } }
    let(:tier) { create(:pricing_tier, :quarterly, identifier: 'tier-abc', priceable: plan2) }
    let(:current_tier) { create(:pricing_tier, :quarterly, priceable: plan) }
    let(:payment_plan) do
      create(
        :payment_plan,
        pricing_tier_id: current_tier.id,
        status: 'active',
        charge_attempt: 0,
        month_interval: 3,
        plan: 'pro'
      )
    end
    let(:plans_utils) { double('Plans::Utils') }

    before do
      allow(payment_plan).to receive(:plans_utils).and_return(plans_utils)
      allow(plans_utils).to receive(:prorated_price_by_day).with(payment_plan).and_return(200)
      allow(plans_utils).to receive(:calculate_total_price).with(subscription, tier).and_return(12000)
      allow(plans_utils).to receive(:calculate_total_price).with(subscription, tier, false).and_return(12000)
      allow(plans_utils).to receive(:transaction_lines).with(payment_plan, subscription, tier).and_return([:line])
      allow(plans_utils).to receive(:process_entitlements)
      allow(payment_plan).to receive(:update).and_return(true)
      allow(payment_plan).to receive(:execute_charge)
      allow(Time.zone).to receive(:today).and_return(Date.new(2024, 1, 1))
    end

    context 'when @result[:success] is true' do
      before { payment_plan.instance_variable_set(:@result, { success: true }) }

      it 'updates the payment plan and returns success' do
        expect(payment_plan).to receive(:update).with(hash_including(
          :plan, :price, :pricing_tier, :month_interval, :next_pay_date, :charge_attempt, :status, :end_date
        ))
        result = payment_plan.reactivate_and_charge!(subscription)
        expect(result).to eq({ success: true })
      end
    end

    context 'when @result[:status] is not payment_failed' do
      before { payment_plan.instance_variable_set(:@result, { success: false, status: 'pending', message: 'msg' }) }

      it 'returns a redirect hash' do
        result = payment_plan.reactivate_and_charge!(subscription)
        expect(result).to eq({
          success: false,
          message: 'Su pago fue procesado, pero necesita pasos adicionales para completar su compra.',
          redirect: true
        })
      end
    end

    context 'when @result[:status] is payment_failed' do
      before do
        payment_plan.instance_variable_set(:@result, { success: false, status: 'payment_failed', message: 'fail msg' })
        allow(payment_plan).to receive(:payment_failed)
      end

      it 'calls payment_failed and returns a failure hash' do
        expect(payment_plan).to receive(:payment_failed).with(force_retry: true)
        result = payment_plan.reactivate_and_charge!(subscription)
        expect(result).to eq({ success: false, message: 'fail msg' })
      end
    end
  end

  describe '#get_tier_name' do
    let(:payment_plan) { described_class.new(pricing_tier_id: pricing_tier_id) }

    context 'when pricing_tier_id is nil' do
      let(:pricing_tier_id) { nil }

      it 'returns nil' do
        expect(payment_plan.get_tier_name).to be_nil
      end
    end

    context 'when pricing_tier_id is present' do
      let(:pricing_tier_id) { pricing_tier.id }
      let(:plan) { create(:plan, name: 'Premium', identifier: 'premium') }
      let(:pricing_tier) { create(:pricing_tier, priceable: plan) }

      it 'returns the name of the priceable' do
        expect(payment_plan.get_tier_name).to eq(pricing_tier.priceable.name)
      end
    end
  end

  describe '#set_retailer_max_agents' do
    let(:retailer) { create(:retailer) }
    let(:payment_plan) { described_class.new(max_agents: 5, retailer: retailer) }

    it 'updates the retailer max_agents column with the payment plan max_agents' do
      expect(retailer).to receive(:update_column).with(:max_agents, 5)
      payment_plan.send(:set_retailer_max_agents)
    end
  end

  describe '#plans_utils' do
    let(:payment_plan) { described_class.new }

    it 'returns a Plans::Utils instance' do
      expect(payment_plan.send(:plans_utils)).to be_a(Plans::Utils)
    end

    it 'memoizes the Plans::Utils instance' do
      first_call = payment_plan.send(:plans_utils)
      second_call = payment_plan.send(:plans_utils)
      expect(first_call).to equal(second_call)
    end
  end

  describe '#execute_charge' do
    let(:payment_plan) { described_class.new }
    let(:retailer) { double('Retailer') }
    let(:payment_method) { double('PaymentMethod') }
    let(:stripe_transaction) { double('StripeTransaction') }
    let(:stripe_service) { double('Stripe::Api') }

    before do
      payment_plan.instance_variable_set(:@lines, [])
      payment_plan.instance_variable_set(:@credits, 0)
      allow(payment_plan).to receive(:retailer).and_return(retailer)
      allow(payment_plan).to receive(:stripe_service).and_return(stripe_service)
    end

    it 'returns nil and sets @result if amount is not positive' do
      expect(payment_plan.send(:execute_charge, amount: 0)).to be_nil
      expect(payment_plan.instance_variable_get(:@result)).to eq({ success: true })
    end

    it 'returns nil if payment_method is blank' do
      allow(retailer).to receive_message_chain(:payment_methods, :main).and_return(nil)
      expect(payment_plan.send(:execute_charge, amount: 10)).to be_nil
    end

    it 'creates a stripe transaction and charges if everything is present' do
      allow(retailer).to receive_message_chain(:payment_methods, :main).and_return(payment_method)
      allow(retailer).to receive_message_chain(:stripe_transactions, :create).and_return(stripe_transaction)
      expect(stripe_service).to receive(:charge_attempt).with(stripe_transaction, 'Mercately Monthly Subscription')
        .and_return({ success: true })
      expect(payment_plan.send(:execute_charge, amount: 10, description: 'Mercately Monthly Subscription')).to eq(true)
      expect(payment_plan.instance_variable_get(:@result)).to eq({ success: true })
    end

    it 'sets @result and returns false if charge_attempt fails' do
      allow(retailer).to receive_message_chain(:payment_methods, :main).and_return(payment_method)
      allow(retailer).to receive_message_chain(:stripe_transactions, :create).and_return(stripe_transaction)
      expect(stripe_service).to receive(:charge_attempt).with(stripe_transaction, 'Mercately Monthly Subscription')
        .and_return({ success: false })
      expect(payment_plan.send(:execute_charge, amount: 10)).to eq(false)
      expect(payment_plan.instance_variable_get(:@result)).to eq({ success: false })
    end
  end

  describe '#check_pricing_tier' do
    let(:retailer) { create(:retailer) }
    let(:plan1) { create(:plan, identifier: 'Launch') }
    let(:pricing_tier) { create(:pricing_tier, :quarterly, priceable: plan1, priceable_type: 'Plan') }

    let(:payment_plan) do
      described_class.new(
        plan: plan1.identifier.downcase,
        month_interval: pricing_tier.month_interval,
        pricing_tier: pricing_tier,
        retailer: retailer
      )
    end

    context 'when plan and month_interval match pricing_tier' do
      let(:plan) { 'launch' }
      let(:month_interval) { 3 }

      it 'does not add errors' do
        payment_plan.valid?
        expect(payment_plan.errors[:plan]).to be_empty
      end
    end

    context 'when plan or month_interval do not match pricing_tier' do
      it 'adds an error to plan' do
        payment_plan.assign_attributes(plan: 'pro', month_interval: 1)
        payment_plan.valid?
        expect(payment_plan.errors[:plan]).to include('Plan no coincide con el plan de la tabla de precios')
      end
    end
  end
end
