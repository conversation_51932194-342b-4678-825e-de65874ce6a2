require 'rails_helper'

RSpec.describe ReactivateChatConcern, type: :concern do
  # Create a test class that includes the concern but skips the after_commit callback
  let(:test_class) do
    test_klass = Class.new do
      include ActiveSupport::Callbacks

      attr_accessor :customer, :direction, :note, :block_chat_reactivation

      def initialize(customer = nil, direction = 'inbound', note = nil)
        @customer = customer
        @direction = direction
        @note = note
        @block_chat_reactivation = false
      end

      def self.to_s
        'GupshupWhatsappMessage'
      end

      # We'll add the methods from the concern manually for testing
      def reactivate
        note_present = begin
          note
        rescue StandardError
          false
        end
        return if note_present
        return if direction != 'inbound'

        customer.platform_to_use = Message::Platform.call(message_klass: self.class.to_s)
        customer.reactivate_chat! unless block_chat_reactivation
      rescue StandardError => e
        Rails.logger.error(e)
        SlackError.send_error(e)
      end
    end

    test_klass
  end

  let(:retailer) { create(:retailer) }
  let(:customer) { create(:customer, retailer: retailer) }
  let(:test_instance) { test_class.new(customer) }

  before do
    # Mock external dependencies
    allow(Message::Platform).to receive(:call).and_return(:whatsapp)
    allow(customer).to receive(:reactivate_chat!)
    allow(customer).to receive(:platform_to_use=)
    allow(Rails.logger).to receive(:error)
    allow(SlackError).to receive(:send_error)
  end

  describe 'concern inclusion' do
    it 'can include the ReactivateChatConcern module' do
      expect(described_class).to be_a(Module)
    end

    it 'extends ActiveSupport::Concern' do
      expect(described_class.singleton_class.ancestors).to include(ActiveSupport::Concern)
    end
  end

  describe '#reactivate' do
    context 'when note is present' do
      let(:test_instance) { test_class.new(customer, 'inbound', 'some note') }

      it 'returns early without processing' do
        test_instance.reactivate

        expect(customer).not_to have_received(:reactivate_chat!)
        expect(customer).not_to have_received(:platform_to_use=)
      end
    end

    context 'when note getter raises an exception' do
      before do
        allow(test_instance).to receive(:note).and_raise(StandardError, 'Note error')
      end

      it 'continues processing after the exception' do
        test_instance.reactivate

        expect(customer).to have_received(:reactivate_chat!)
        expect(customer).to have_received(:platform_to_use=).with(:whatsapp)
      end
    end

    context 'when direction is not inbound' do
      let(:test_instance) { test_class.new(customer, 'outbound') }

      it 'returns early without processing' do
        test_instance.reactivate

        expect(customer).not_to have_received(:reactivate_chat!)
        expect(customer).not_to have_received(:platform_to_use=)
      end
    end

    context 'when direction is inbound' do
      let(:test_instance) { test_class.new(customer, 'inbound') }

      it 'sets customer platform_to_use' do
        test_instance.reactivate

        expect(Message::Platform).to have_received(:call).with(message_klass: 'GupshupWhatsappMessage')
        expect(customer).to have_received(:platform_to_use=).with(:whatsapp)
      end

      it 'calls reactivate_chat! on customer' do
        test_instance.reactivate

        expect(customer).to have_received(:reactivate_chat!)
      end
    end

    context 'when block_chat_reactivation is true' do
      let(:test_instance) do
        instance = test_class.new(customer, 'inbound')
        instance.block_chat_reactivation = true
        instance
      end

      it 'does not call reactivate_chat! on customer' do
        test_instance.reactivate

        expect(customer).not_to have_received(:reactivate_chat!)
      end

      it 'still sets platform_to_use' do
        test_instance.reactivate

        expect(Message::Platform).to have_received(:call).with(message_klass: 'GupshupWhatsappMessage')
        expect(customer).to have_received(:platform_to_use=).with(:whatsapp)
      end
    end

    context 'when block_chat_reactivation is false' do
      let(:test_instance) do
        instance = test_class.new(customer, 'inbound')
        instance.block_chat_reactivation = false
        instance
      end

      it 'calls reactivate_chat! on customer' do
        test_instance.reactivate

        expect(customer).to have_received(:reactivate_chat!)
      end
    end

    context 'when an exception occurs during processing' do
      let(:error) { StandardError.new('Test error') }

      before do
        allow(Message::Platform).to receive(:call).and_raise(error)
      end

      it 'logs the error' do
        test_instance.reactivate

        expect(Rails.logger).to have_received(:error).with(error)
      end

      it 'sends error to Slack' do
        test_instance.reactivate

        expect(SlackError).to have_received(:send_error).with(error)
      end

      it 'does not re-raise the exception' do
        expect { test_instance.reactivate }.not_to raise_error
      end
    end

    context 'when customer.reactivate_chat! raises an exception' do
      let(:error) { StandardError.new('Reactivation error') }

      before do
        allow(customer).to receive(:reactivate_chat!).and_raise(error)
      end

      it 'logs the error' do
        test_instance.reactivate

        expect(Rails.logger).to have_received(:error).with(error)
      end

      it 'sends error to Slack' do
        test_instance.reactivate

        expect(SlackError).to have_received(:send_error).with(error)
      end
    end

    context 'when customer is nil' do
      let(:test_instance) { test_class.new(nil, 'inbound') }

      it 'raises an error when trying to set platform_to_use' do
        expect { test_instance.reactivate }.not_to raise_error

        # The method handles the error gracefully
        expect(Rails.logger).to have_received(:error)
        expect(SlackError).to have_received(:send_error)
      end
    end

    context 'when testing with different message types' do
      let(:whatsapp_class) do
        Class.new do
          include ActiveSupport::Callbacks

          attr_accessor :customer, :direction, :note, :block_chat_reactivation

          def initialize(customer = nil, direction = 'inbound', note = nil)
            @customer = customer
            @direction = direction
            @note = note
            @block_chat_reactivation = false
          end

          def self.to_s
            'GupshupWhatsappMessage'
          end

          def reactivate
            note_present = begin
              note
            rescue StandardError
              false
            end
            return if note_present
            return if direction != 'inbound'

            customer.platform_to_use = Message::Platform.call(message_klass: self.class.to_s)
            customer.reactivate_chat! unless block_chat_reactivation
          rescue StandardError => e
            Rails.logger.error(e)
            SlackError.send_error(e)
          end
        end
      end

      let(:facebook_class) do
        Class.new do
          include ActiveSupport::Callbacks

          attr_accessor :customer, :direction, :note, :block_chat_reactivation

          def initialize(customer = nil, direction = 'inbound', note = nil)
            @customer = customer
            @direction = direction
            @note = note
            @block_chat_reactivation = false
          end

          def self.to_s
            'FacebookMessage'
          end

          def reactivate
            note_present = begin
              note
            rescue StandardError
              false
            end
            return if note_present
            return if direction != 'inbound'

            customer.platform_to_use = Message::Platform.call(message_klass: self.class.to_s)
            customer.reactivate_chat! unless block_chat_reactivation
          rescue StandardError => e
            Rails.logger.error(e)
            SlackError.send_error(e)
          end
        end
      end

      it 'calls Message::Platform with correct message class for WhatsApp' do
        whatsapp_instance = whatsapp_class.new(customer, 'inbound')
        whatsapp_instance.reactivate

        expect(Message::Platform).to have_received(:call).with(message_klass: 'GupshupWhatsappMessage')
      end

      it 'calls Message::Platform with correct message class for Facebook' do
        facebook_instance = facebook_class.new(customer, 'inbound')
        facebook_instance.reactivate

        expect(Message::Platform).to have_received(:call).with(message_klass: 'FacebookMessage')
      end
    end

    context 'when testing edge cases' do
      it 'handles when customer.platform_to_use= raises an exception' do
        allow(customer).to receive(:platform_to_use=).and_raise(StandardError, 'Platform error')

        test_instance.reactivate

        expect(Rails.logger).to have_received(:error)
        expect(SlackError).to have_received(:send_error)
      end

      it 'processes correctly when note is empty string' do
        test_instance.note = ''

        test_instance.reactivate

        # Empty string is truthy in Ruby, so it should return early
        expect(customer).not_to have_received(:reactivate_chat!)
        expect(customer).not_to have_received(:platform_to_use=)
      end

      it 'processes correctly when note is nil' do
        test_instance.note = nil

        test_instance.reactivate

        expect(customer).to have_received(:reactivate_chat!)
        expect(customer).to have_received(:platform_to_use=)
      end

      it 'handles different direction values' do
        test_instance.direction = 'INBOUND'

        test_instance.reactivate

        # Should not process because direction is not exactly 'inbound'
        expect(customer).not_to have_received(:reactivate_chat!)
        expect(customer).not_to have_received(:platform_to_use=)
      end
    end
  end

  # Test to verify the actual concern can be included
  describe 'actual concern inclusion' do
    it 'can be included in a class with ActiveRecord callbacks' do
      # Mock the after_commit method to avoid the callback issue
      test_model = Class.new do
        include ActiveSupport::Callbacks

        def self.after_commit(*args)
          # Mock implementation
        end
      end

      expect { test_model.include(described_class) }.not_to raise_error
    end
  end
end
