require 'rails_helper'

RSpec.describe ChatBotSelectionConcern do
  # Create a dummy class to test the concern
  let(:dummy_class) do
    Class.new do
      include ChatBotSelectionConcern

      attr_accessor :customer, :created_at

      def initialize(customer:, created_at: Time.current)
        @customer = customer
        @created_at = created_at
      end

      # Mock method that would be implemented in the including class
      attr_reader :before_last_message

      def set_before_last_message(message)
        @before_last_message = message
      end
    end
  end

  let(:retailer) { create(:retailer) }
  let(:customer) { create(:customer, retailer: retailer) }
  let(:subject_instance) { dummy_class.new(customer: customer, created_at: Time.current) }

  describe '#chat_bot_out_of_time?' do
    let(:chat_bot) { create(:chat_bot, retailer: retailer, reactivate_after: reactivate_after) }
    let(:chat_bot_option) { create(:chat_bot_option, chat_bot: chat_bot) }
    let(:platform) { 'whatsapp' }
    let(:reactivate_after) { 1 } # 1 hour

    before do
      allow(subject_instance).to receive(:platform).and_return(platform)
    end

    context 'when there is a preactivated bot' do
      before do
        allow(subject_instance).to receive(:preactivated_bot?).and_return(true)
      end

      it 'returns false' do
        expect(subject_instance.chat_bot_out_of_time?).to be false
      end
    end

    context 'when there is no preactivated bot' do
      before do
        allow(subject_instance).to receive(:preactivated_bot?).and_return(false)
      end

      context 'when there is no last message' do
        before do
          subject_instance.set_before_last_message(nil)
          allow(ChatBots::CurrentOptionFinder).to receive(:call)
            .with(customer: customer, platform: platform)
            .and_return(double(chat_bot: chat_bot))
        end

        it 'returns false' do
          expect(subject_instance.chat_bot_out_of_time?).to be_falsy
        end
      end

      context 'when there is no active chat bot' do
        before do
          allow(ChatBots::CurrentOptionFinder).to receive(:call)
            .with(customer: customer, platform: platform)
            .and_return(nil)
        end

        it 'returns false' do
          expect(subject_instance.chat_bot_out_of_time?).to be_falsy
        end
      end

      context 'when chat bot has no reactivation time set' do
        let(:reactivate_after) { nil }
        let(:last_message) { double(created_at: 2.hours.ago) }

        before do
          subject_instance.set_before_last_message(last_message)
          allow(ChatBots::CurrentOptionFinder).to receive(:call)
            .with(customer: customer, platform: platform)
            .and_return(double(chat_bot: chat_bot))
        end

        it 'returns false' do
          expect(subject_instance.chat_bot_out_of_time?).to be false
        end
      end

      context 'when chat bot has reactivation time set' do
        let(:last_message) { double(created_at: last_message_time) }

        before do
          subject_instance.set_before_last_message(last_message)
          allow(ChatBots::CurrentOptionFinder).to receive(:call)
            .with(customer: customer, platform: platform)
            .and_return(double(chat_bot: chat_bot))
        end

        context 'when time elapsed is less than reactivation time' do
          let(:last_message_time) { 30.minutes.ago }

          before do
            subject_instance.created_at = Time.current
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed equals reactivation time' do
          let(:last_message_time) { 1.hour.ago }

          before do
            subject_instance.created_at = Time.current
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end

        context 'when time elapsed is greater than reactivation time' do
          let(:last_message_time) { 2.hours.ago }

          before do
            subject_instance.created_at = Time.current
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end

        context 'with different reactivation times' do
          let(:last_message_time) { 3.hours.ago }

          before do
            subject_instance.created_at = Time.current
          end

          context 'when reactivate_after is 2 hours' do
            let(:reactivate_after) { 2 }

            it 'returns true' do
              expect(subject_instance.chat_bot_out_of_time?).to be true
            end
          end

          context 'when reactivate_after is 4 hours' do
            let(:reactivate_after) { 4 }

            it 'returns false' do
              expect(subject_instance.chat_bot_out_of_time?).to be false
            end
          end
        end

        context 'when ChatBots::CurrentOptionFinder returns object without chat_bot' do
          let(:last_message) { double(created_at: 2.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: nil))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be_falsy
          end
        end
      end
    end

    context 'for edge cases' do
      before do
        allow(subject_instance).to receive(:preactivated_bot?).and_return(false)
      end

      context 'when time calculation is exactly at the boundary' do
        let(:last_message_time) { 1.hour.ago }
        let(:last_message) { double(created_at: last_message_time) }

        before do
          subject_instance.set_before_last_message(last_message)
          subject_instance.created_at = Time.current
          allow(ChatBots::CurrentOptionFinder).to receive(:call)
            .with(customer: customer, platform: platform)
            .and_return(double(chat_bot: chat_bot))
        end

        it 'returns true when time difference equals reactivate_after' do
          expect(subject_instance.chat_bot_out_of_time?).to be true
        end
      end

      context 'when reactivate_after is 0' do
        let(:reactivate_after) { 0 }
        let(:last_message) { double(created_at: 1.minute.ago) }

        before do
          subject_instance.set_before_last_message(last_message)
          allow(ChatBots::CurrentOptionFinder).to receive(:call)
            .with(customer: customer, platform: platform)
            .and_return(double(chat_bot: chat_bot))
        end

        it 'returns true for any time elapsed' do
          expect(subject_instance.chat_bot_out_of_time?).to be true
        end
      end

      context 'when created_at is before last_message created_at' do
        let(:last_message_time) { 1.hour.from_now }
        let(:last_message) { double(created_at: last_message_time) }

        before do
          subject_instance.set_before_last_message(last_message)
          subject_instance.created_at = Time.current
          allow(ChatBots::CurrentOptionFinder).to receive(:call)
            .with(customer: customer, platform: platform)
            .and_return(double(chat_bot: chat_bot))
        end

        it 'handles negative time difference correctly' do
          expect(subject_instance.chat_bot_out_of_time?).to be false
        end
      end
    end

    # Test cases for all REACTIVATION_TIME_OPTIONS values
    context 'for all REACTIVATION_TIME_OPTIONS values' do
      before do
        allow(subject_instance).to receive(:preactivated_bot?).and_return(false)
      end

      # Test case for value 0 (immediate reactivation)
      # FIXME: This is the current behavior, it should never reactivate immediately
      #       but we are testing it to ensure it behaves as expected.
      context 'when reactivate_after is 0 (Seleccionar)' do
        let(:reactivate_after) { 0 }
        let(:last_message) { double(created_at: 1.second.ago) }

        before do
          subject_instance.set_before_last_message(last_message)
          subject_instance.created_at = Time.current
          allow(ChatBots::CurrentOptionFinder).to receive(:call)
            .with(customer: customer, platform: platform)
            .and_return(double(chat_bot: chat_bot))
        end

        it 'returns true immediately for any elapsed time' do
          expect(subject_instance.chat_bot_out_of_time?).to be true
        end
      end

      # Test case for value 0.0167 (1 minute)
      context 'when reactivate_after is 0.0167 (1 minuto)' do
        let(:reactivate_after) { 0.0167 }

        context 'when time elapsed is less than 1 minute' do
          let(:last_message) { double(created_at: 30.seconds.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 1 minute' do
          let(:last_message) { double(created_at: 2.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 0.0833 (5 minutes)
      context 'when reactivate_after is 0.0833 (5 minutos)' do
        let(:reactivate_after) { 0.0833 }

        context 'when time elapsed is less than 5 minutes' do
          let(:last_message) { double(created_at: 3.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 5 minutes' do
          let(:last_message) { double(created_at: 6.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 0.1667 (10 minutes)
      context 'when reactivate_after is 0.1667 (10 minutos)' do
        let(:reactivate_after) { 0.1667 }

        context 'when time elapsed is less than 10 minutes' do
          let(:last_message) { double(created_at: 8.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 10 minutes' do
          let(:last_message) { double(created_at: 12.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 0.25 (15 minutes)
      context 'when reactivate_after is 0.25 (15 minutos)' do
        let(:reactivate_after) { 0.25 }

        context 'when time elapsed is less than 15 minutes' do
          let(:last_message) { double(created_at: 10.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 15 minutes' do
          let(:last_message) { double(created_at: 20.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 0.5 (30 minutes)
      context 'when reactivate_after is 0.5 (30 minutos)' do
        let(:reactivate_after) { 0.5 }

        context 'when time elapsed is less than 30 minutes' do
          let(:last_message) { double(created_at: 25.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 30 minutes' do
          let(:last_message) { double(created_at: 35.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 1 (1 hour) - already covered in original tests but adding specific case
      context 'when reactivate_after is 1 (1 hora)' do
        let(:reactivate_after) { 1 }

        context 'when time elapsed is exactly 1 hour' do
          let(:last_message) { double(created_at: 1.hour.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 2 (2 hours)
      context 'when reactivate_after is 2 (2 horas)' do
        let(:reactivate_after) { 2 }

        context 'when time elapsed is less than 2 hours' do
          let(:last_message) { double(created_at: 90.minutes.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 2 hours' do
          let(:last_message) { double(created_at: 3.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 4 (4 hours)
      context 'when reactivate_after is 4 (4 horas)' do
        let(:reactivate_after) { 4 }

        context 'when time elapsed is less than 4 hours' do
          let(:last_message) { double(created_at: 3.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 4 hours' do
          let(:last_message) { double(created_at: 5.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 8 (8 hours)
      context 'when reactivate_after is 8 (8 horas)' do
        let(:reactivate_after) { 8 }

        context 'when time elapsed is less than 8 hours' do
          let(:last_message) { double(created_at: 7.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 8 hours' do
          let(:last_message) { double(created_at: 9.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 10 (10 hours)
      context 'when reactivate_after is 10 (10 horas)' do
        let(:reactivate_after) { 10 }

        context 'when time elapsed is less than 10 hours' do
          let(:last_message) { double(created_at: 9.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 10 hours' do
          let(:last_message) { double(created_at: 11.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 12 (12 hours)
      context 'when reactivate_after is 12 (12 horas)' do
        let(:reactivate_after) { 12 }

        context 'when time elapsed is less than 12 hours' do
          let(:last_message) { double(created_at: 11.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 12 hours' do
          let(:last_message) { double(created_at: 13.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 24 (24 hours)
      context 'when reactivate_after is 24 (24 horas)' do
        let(:reactivate_after) { 24 }

        context 'when time elapsed is less than 24 hours' do
          let(:last_message) { double(created_at: 23.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 24 hours' do
          let(:last_message) { double(created_at: 25.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 48 (48 hours)
      context 'when reactivate_after is 48 (48 horas)' do
        let(:reactivate_after) { 48 }

        context 'when time elapsed is less than 48 hours' do
          let(:last_message) { double(created_at: 47.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 48 hours' do
          let(:last_message) { double(created_at: 49.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end

      # Test case for value 72 (72 hours)
      context 'when reactivate_after is 72 (72 horas)' do
        let(:reactivate_after) { 72 }

        context 'when time elapsed is less than 72 hours' do
          let(:last_message) { double(created_at: 71.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns false' do
            expect(subject_instance.chat_bot_out_of_time?).to be false
          end
        end

        context 'when time elapsed is greater than 72 hours' do
          let(:last_message) { double(created_at: 73.hours.ago) }

          before do
            subject_instance.set_before_last_message(last_message)
            subject_instance.created_at = Time.current
            allow(ChatBots::CurrentOptionFinder).to receive(:call)
              .with(customer: customer, platform: platform)
              .and_return(double(chat_bot: chat_bot))
          end

          it 'returns true' do
            expect(subject_instance.chat_bot_out_of_time?).to be true
          end
        end
      end
    end
  end
end
