require 'rails_helper'

RSpec.describe FacebookMessages, type: :concern do
  let(:retailer) { create(:retailer) }
  let(:facebook_retailer) { create(:facebook_retailer, retailer: retailer) }
  let(:customer) { create(:customer, retailer: retailer) }

  before do
    # Mock external dependencies
    allow(Message::Platform).to receive(:call).and_return(:messenger)
    allow(customer).to receive(:reactivate_chat!)
    allow(customer).to receive(:platform_to_use=)
  end

  describe 'concern inclusion' do
    it 'can include the FacebookMessages module' do
      expect(described_class).to be_a(Module)
    end

    it 'extends ActiveSupport::Concern' do
      expect(described_class.singleton_class.ancestors).to include(ActiveSupport::Concern)
    end
  end

  describe '#reactivate_chat' do
    context 'when testing FacebookMessage behavior' do
      let(:facebook_message) do
        build(:facebook_message,
              customer: customer,
              facebook_retailer: facebook_retailer,
              sent_by_retailer: false)
      end

      before do
        # Mock callbacks and validations to avoid side effects
        allow(facebook_message).to receive(:set_sender_information)
        allow(facebook_message).to receive(:attach_agent_name)
        allow(facebook_message).to receive(:sent_by_retailer?)
        allow(facebook_message).to receive(:assign_agent)
        allow(facebook_message).to receive(:set_recent_inbound_message_date)
        allow(facebook_message).to receive(:upload_media_resources)
        allow(facebook_message).to receive(:send_welcome_message)
        allow(facebook_message).to receive(:send_inactive_message)
        allow(facebook_message).to receive(:broadcast_to_counter_channel)
        allow(facebook_message).to receive(:mark_unread_flag)
        allow(facebook_message).to receive(:set_last_interaction)
        allow(facebook_message).to receive(:send_facebook_message)
      end

      context 'when sent_by_retailer is true' do
        before do
          facebook_message.sent_by_retailer = true
        end

        it 'returns early without processing' do
          facebook_message.send(:reactivate_chat)

          expect(customer).not_to have_received(:reactivate_chat!)
          expect(customer).not_to have_received(:platform_to_use=)
          expect(Message::Platform).not_to have_received(:call)
        end
      end

      context 'when sent_by_retailer is false' do
        it 'sets customer platform_to_use with FacebookMessage class' do
          facebook_message.send(:reactivate_chat)

          expect(Message::Platform).to have_received(:call).with(message_klass: 'FacebookMessage')
          expect(customer).to have_received(:platform_to_use=).with(:messenger)
        end

        it 'calls reactivate_chat! on customer' do
          facebook_message.send(:reactivate_chat)

          expect(customer).to have_received(:reactivate_chat!)
        end
      end

      context 'when block_chat_reactivation is true' do
        before do
          facebook_message.block_chat_reactivation = true
        end

        it 'does not call reactivate_chat! on customer' do
          facebook_message.send(:reactivate_chat)

          expect(customer).not_to have_received(:reactivate_chat!)
        end

        it 'still sets platform_to_use' do
          facebook_message.send(:reactivate_chat)

          expect(Message::Platform).to have_received(:call).with(message_klass: 'FacebookMessage')
          expect(customer).to have_received(:platform_to_use=).with(:messenger)
        end
      end

      context 'when block_chat_reactivation is false' do
        before do
          facebook_message.block_chat_reactivation = false
        end

        it 'calls reactivate_chat! on customer' do
          facebook_message.send(:reactivate_chat)

          expect(customer).to have_received(:reactivate_chat!)
        end
      end
    end

    context 'when testing InstagramMessage behavior' do
      let(:instagram_message) do
        build(:instagram_message,
              customer: customer,
              facebook_retailer: facebook_retailer,
              sent_by_retailer: false)
      end

      before do
        allow(Message::Platform).to receive(:call).and_return(:instagram)

        # Mock callbacks and validations to avoid side effects
        allow(instagram_message).to receive(:set_sender_information)
        allow(instagram_message).to receive(:attach_agent_name)
        allow(instagram_message).to receive(:sent_by_retailer?)
        allow(instagram_message).to receive(:assign_agent)
        allow(instagram_message).to receive(:set_recent_inbound_message_date)
        allow(instagram_message).to receive(:upload_media_resources)
        allow(instagram_message).to receive(:send_welcome_message)
        allow(instagram_message).to receive(:send_inactive_message)
        allow(instagram_message).to receive(:broadcast_to_counter_channel)
        allow(instagram_message).to receive(:mark_unread_flag)
        allow(instagram_message).to receive(:set_last_interaction)
        allow(instagram_message).to receive(:send_facebook_message)
      end

      context 'when sent_by_retailer is true' do
        before do
          instagram_message.sent_by_retailer = true
        end

        it 'returns early without processing' do
          instagram_message.send(:reactivate_chat)

          expect(customer).not_to have_received(:reactivate_chat!)
          expect(customer).not_to have_received(:platform_to_use=)
          expect(Message::Platform).not_to have_received(:call)
        end
      end

      context 'when sent_by_retailer is false' do
        it 'sets customer platform_to_use with InstagramMessage class' do
          instagram_message.send(:reactivate_chat)

          expect(Message::Platform).to have_received(:call).with(message_klass: 'InstagramMessage')
          expect(customer).to have_received(:platform_to_use=).with(:instagram)
        end

        it 'calls reactivate_chat! on customer' do
          instagram_message.send(:reactivate_chat)

          expect(customer).to have_received(:reactivate_chat!)
        end
      end

      context 'when block_chat_reactivation is true' do
        before do
          instagram_message.block_chat_reactivation = true
        end

        it 'does not call reactivate_chat! on customer' do
          instagram_message.send(:reactivate_chat)

          expect(customer).not_to have_received(:reactivate_chat!)
        end

        it 'still sets platform_to_use' do
          instagram_message.send(:reactivate_chat)

          expect(Message::Platform).to have_received(:call).with(message_klass: 'InstagramMessage')
          expect(customer).to have_received(:platform_to_use=).with(:instagram)
        end
      end
    end

    context 'when testing edge cases' do
      let(:facebook_message) do
        build(:facebook_message,
              customer: customer,
              facebook_retailer: facebook_retailer,
              sent_by_retailer: false)
      end

      before do
        # Mock callbacks and validations to avoid side effects
        allow(facebook_message).to receive(:set_sender_information)
        allow(facebook_message).to receive(:attach_agent_name)
        allow(facebook_message).to receive(:sent_by_retailer?)
        allow(facebook_message).to receive(:assign_agent)
        allow(facebook_message).to receive(:set_recent_inbound_message_date)
        allow(facebook_message).to receive(:upload_media_resources)
        allow(facebook_message).to receive(:send_welcome_message)
        allow(facebook_message).to receive(:send_inactive_message)
        allow(facebook_message).to receive(:broadcast_to_counter_channel)
        allow(facebook_message).to receive(:mark_unread_flag)
        allow(facebook_message).to receive(:set_last_interaction)
        allow(facebook_message).to receive(:send_facebook_message)
      end

      context 'when customer is nil' do
        before do
          facebook_message.customer = nil
        end

        it 'raises an error when trying to set platform_to_use' do
          expect { facebook_message.send(:reactivate_chat) }.to raise_error(NoMethodError)
        end
      end

      context 'when Message::Platform service fails' do
        let(:error) { StandardError.new('Platform service error') }

        before do
          allow(Message::Platform).to receive(:call).and_raise(error)
        end

        it 'allows the exception to bubble up' do
          expect { facebook_message.send(:reactivate_chat) }.to raise_error(StandardError, 'Platform service error')
        end
      end

      context 'when customer.reactivate_chat! fails' do
        let(:error) { StandardError.new('Reactivation error') }

        before do
          allow(customer).to receive(:reactivate_chat!).and_raise(error)
        end

        it 'allows the exception to bubble up' do
          expect { facebook_message.send(:reactivate_chat) }.to raise_error(StandardError, 'Reactivation error')
        end
      end

      context 'when customer.platform_to_use= fails' do
        let(:error) { StandardError.new('Platform assignment error') }

        before do
          allow(customer).to receive(:platform_to_use=).and_raise(error)
        end

        it 'allows the exception to bubble up' do
          expect { facebook_message.send(:reactivate_chat) }.to raise_error(StandardError, 'Platform assignment error')
        end
      end

      context 'when sent_by_retailer is exactly true (not truthy)' do
        before do
          facebook_message.sent_by_retailer = true
        end

        it 'returns early without processing' do
          facebook_message.send(:reactivate_chat)

          expect(customer).not_to have_received(:reactivate_chat!)
          expect(customer).not_to have_received(:platform_to_use=)
        end
      end

      context 'when sent_by_retailer is false (not falsy)' do
        before do
          facebook_message.sent_by_retailer = false
        end

        it 'processes the reactivation' do
          facebook_message.send(:reactivate_chat)

          expect(customer).to have_received(:reactivate_chat!)
          expect(customer).to have_received(:platform_to_use=)
        end
      end
    end

    context 'when testing different message class responses' do
      let(:facebook_message) do
        build(:facebook_message,
              customer: customer,
              facebook_retailer: facebook_retailer,
              sent_by_retailer: false)
      end

      let(:instagram_message) do
        build(:instagram_message,
              customer: customer,
              facebook_retailer: facebook_retailer,
              sent_by_retailer: false)
      end

      before do
        # Mock callbacks for both messages
        [facebook_message, instagram_message].each do |message|
          allow(message).to receive(:set_sender_information)
          allow(message).to receive(:attach_agent_name)
          allow(message).to receive(:sent_by_retailer?)
          allow(message).to receive(:assign_agent)
          allow(message).to receive(:set_recent_inbound_message_date)
          allow(message).to receive(:upload_media_resources)
          allow(message).to receive(:send_welcome_message)
          allow(message).to receive(:send_inactive_message)
          allow(message).to receive(:broadcast_to_counter_channel)
          allow(message).to receive(:mark_unread_flag)
          allow(message).to receive(:set_last_interaction)
          allow(message).to receive(:send_facebook_message)
        end
      end

      it 'calls Message::Platform with FacebookMessage class name' do
        facebook_message.send(:reactivate_chat)

        expect(Message::Platform).to have_received(:call).with(message_klass: 'FacebookMessage')
      end

      it 'calls Message::Platform with InstagramMessage class name' do
        allow(Message::Platform).to receive(:call).and_return(:instagram)
        instagram_message.send(:reactivate_chat)

        expect(Message::Platform).to have_received(:call).with(message_klass: 'InstagramMessage')
      end
    end
  end

  # Test to verify the actual concern can be included
  describe 'actual concern inclusion' do
    it 'is included in FacebookMessage' do
      expect(FacebookMessage.included_modules).to include(described_class)
    end

    it 'is included in InstagramMessage' do
      expect(InstagramMessage.included_modules).to include(described_class)
    end
  end
end
