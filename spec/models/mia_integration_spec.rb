# frozen string literal: true

require 'rails_helper'

RSpec.describe MiaIntegration do
  subject(:mia_integration) { create(:mia_integration, mia_integration_type: mia_integration_type) }

  let(:mia_integration_type) { create(:mia_integration_type, :guides) }

  before do
    allow(ENV).to receive(:fetch).and_call_original
    allow(ENV).to receive(:fetch).with('SECRET_KEY_BASE', nil).and_return('test-secret-key')
    allow(ENV).to receive(:fetch).with('MIA_MULTI_TENANT_SECRET_KEY', nil).and_return('test-secret-key')
    allow(ENV).to receive(:fetch).with('MIA_PRODUCTS_SERVICE_URL', nil).and_return('https://products.mercately.ai')
    allow(ENV).to receive(:fetch).with('MIA_GUIDES_SERVICE_URL', nil).and_return('https://guides.mercately.ai')
    allow(ENV).to receive(:fetch).with('MIA_SUGGESTIONS_SERVICE_URL', nil).and_return('https://suggestions.mercately.ai')
    allow(ENV).to receive(:fetch).with('ENVIRONMENT', '').and_return('development')
  end

  it { expect(subject).to be_valid }

  describe 'ActiveModel validations' do
    it { is_expected.to validate_presence_of(:openai_key) }
    it { is_expected.to validate_uniqueness_of(:retailer_id).scoped_to(:mia_integration_type_id) }
  end

  describe 'ActiveRecord associations' do
    it { is_expected.to belong_to(:retailer) }
    it { is_expected.to belong_to(:mia_integration_type) }
  end

  describe 'callbacks' do
    describe 'after_commit' do
      describe '#generate_public_key' do
        context 'when integration type is chatbot and public_key is not present' do
          let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
          let(:integration) { build(:mia_integration, mia_integration_type: mia_integration_type, public_key: nil) }

          it 'enqueues mailer and job' do
            expect { integration.save }
              .to have_enqueued_mail(MiaNotificationMailer, :integration_confirm_email)
              .and have_enqueued_job(CreateAndSendMiaCredentialsJob)
          end

          it 'generates a random public key' do
            integration.save
            expect(integration.reload.public_key).to be_present
            expect(integration.public_key).not_to eq('test-secret-key')
          end
        end

        context 'when integration type is guides and public_key is not present' do
          let(:mia_integration_type) { create(:mia_integration_type, :guides) }
          let(:integration) { build(:mia_integration, mia_integration_type: mia_integration_type, public_key: nil) }

          it 'uses SECRET_KEY_BASE as public key' do
            integration.save
            expect(integration.reload.public_key).to eq('test-secret-key')
          end

          it 'does not enqueue mailer or job' do
            expect { integration.save }
              .not_to have_enqueued_mail(MiaNotificationMailer)

            expect { integration.save }
              .not_to have_enqueued_job(CreateAndSendMiaCredentialsJob)
          end
        end

        context 'when integration type is suggestions and public_key is not present' do
          let(:mia_integration_type) { create(:mia_integration_type, :suggestions) }
          let(:integration) { build(:mia_integration, mia_integration_type: mia_integration_type, public_key: nil) }

          it 'generates a random 32-byte key' do
            integration.save
            public_key = integration.reload.public_key

            expect(public_key).to be_present
            expect(public_key).not_to eq('test-secret-key')

            # Decode and verify it's 32 bytes
            decoded_key = Base64.urlsafe_decode64(public_key)
            expect(decoded_key.length).to eq(32)
          end

          it 'does not enqueue mailer or job' do
            expect { integration.save }
              .not_to have_enqueued_mail(MiaNotificationMailer)

            expect { integration.save }
              .not_to have_enqueued_job(CreateAndSendMiaCredentialsJob)
          end
        end

        context 'when public_key is already present' do
          let(:mia_integration_type_with_key) { create(:mia_integration_type, :guides) }
          let(:integration) do
            build(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type_with_key)
          end

          it 'does not call generate_public_key' do
            expect(integration).not_to receive(:generate_public_key)
            integration.save
          end
        end
      end

      describe 'sync_retailer_with_mia' do
        let(:retailer) { create(:retailer) }
        let!(:integration) { build(:mia_integration, mia_integration_type:, retailer:) }

        context 'when mia_integration_type is chatbot' do
          let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }

          it { expect { integration.save }.to have_enqueued_job(Mia::Chatbot::SyncRetailerTimezoneJob) }
          it { expect { integration.save }.to change(retailer, :feature_flag).to(true) }
        end

        context 'when mia_integration_type is not chatbot' do
          let(:mia_integration_type) { create(:mia_integration_type, :guides) }

          it { expect { integration.save }.not_to have_enqueued_job(Mia::Chatbot::SyncRetailerTimezoneJob) }
          it { expect { integration.save }.not_to change(retailer, :feature_flag) }
        end
      end

      describe '#delete_remote_file' do
        let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
        let!(:integration) { create(:mia_integration, mia_integration_type:) }

        it { expect { integration.destroy }.to have_enqueued_job(Mia::DeleteRemoteMiaIntegrationFileJob) }
        it { expect { integration.destroy }.to have_enqueued_mail(MiaNotificationMailer) }
      end
    end

    describe 'before_create' do
      describe '#generate_service_url' do
        let!(:retailer) { create(:retailer, catalog_slug: 'testretailer') }

        context 'when integration type is chatbot' do
          let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
          let(:integration) { build(:mia_integration, retailer: retailer, mia_integration_type: mia_integration_type) }

          context 'when ENVIRONMENT env var is production' do
            before { allow(ENV).to receive(:fetch).with('ENVIRONMENT', '').and_return('production') }

            it 'does not include production in service_url' do
              integration.save
              expect(integration.reload.service_url).to eq('https://mia-chatbot-testretailer.mercately.ai')
              expect(integration.service_url).not_to include('production')
            end
          end

          context 'when ENVIRONMENT env var is not production' do
            before { allow(ENV).to receive(:fetch).with('ENVIRONMENT', '').and_return('staging') }

            it 'includes ENVIRONMENT env var in service_url' do
              integration.save
              expect(integration.reload.service_url).to eq('https://mia-chatbot-testretailer-staging.mercately.ai')
              expect(integration.service_url).to include('staging')
            end
          end
        end

        context 'when integration type is products' do
          let(:mia_integration_type) { create(:mia_integration_type, :products) }
          let(:integration) { build(:mia_integration, retailer: retailer, mia_integration_type: mia_integration_type) }

          it 'uses MIA_PRODUCTS_SERVICE_URL' do
            integration.save
            expect(integration.reload.service_url).to eq('https://products.mercately.ai')
          end
        end

        context 'when integration type is guides' do
          let(:mia_integration_type) { create(:mia_integration_type, :guides) }
          let(:integration) { build(:mia_integration, retailer: retailer, mia_integration_type: mia_integration_type) }

          it 'uses MIA_GUIDES_SERVICE_URL' do
            integration.save
            expect(integration.reload.service_url).to eq('https://guides.mercately.ai')
          end
        end

        context 'when integration type is suggestions' do
          let(:mia_integration_type) { create(:mia_integration_type, :suggestions) }
          let(:integration) { build(:mia_integration, retailer: retailer, mia_integration_type: mia_integration_type) }

          it 'uses MIA_SUGGESTIONS_SERVICE_URL' do
            integration.save
            expect(integration.reload.service_url).to eq('https://suggestions.mercately.ai')
          end
        end
      end
    end
  end

  describe 'instance methods' do
    let(:mia_integration_type_for_instance_methods) { create(:mia_integration_type, :guides) }
    let(:mia_integration) do
      create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type_for_instance_methods)
    end

    describe '#retry_create_and_send_credentials' do
      it {
        expect do
          mia_integration.retry_create_and_send_credentials
        end.to have_enqueued_job(CreateAndSendMiaCredentialsJob)
      }
    end
  end

  describe 'private methods' do
    let(:retailer) { create(:retailer, catalog_slug: 'testretailer') }

    describe '#generate_chatbot_public_key' do
      let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
      let(:integration) do
        build(:mia_integration, mia_integration_type: mia_integration_type, retailer: retailer, public_key: nil)
      end

      before { integration.save! }

      it 'generates a random 32-byte key encoded in base64' do
        # Clear the public key to test the generation
        integration.update_column(:encrypted_public_key, nil)
        integration.update_column(:encrypted_public_key_iv, nil)
        integration.update_column(:encrypted_public_key_salt, nil)

        old_public_key = integration.public_key
        integration.send(:generate_chatbot_public_key)
        new_public_key = integration.reload.public_key

        expect(new_public_key).to be_present
        expect(new_public_key).not_to eq(old_public_key)
        expect(new_public_key).not_to eq('test-secret-key') # Should be random, not SECRET_KEY_BASE
      end

      it 'enqueues mailer and job when update succeeds' do
        expect { integration.send(:generate_chatbot_public_key) }
          .to have_enqueued_mail(MiaNotificationMailer, :integration_confirm_email)
          .and have_enqueued_job(CreateAndSendMiaCredentialsJob)
      end

      it 'does not enqueue anything when update fails' do
        allow(integration).to receive(:update).and_return(false)

        expect { integration.send(:generate_chatbot_public_key) }
          .not_to have_enqueued_mail(MiaNotificationMailer)

        expect { integration.send(:generate_chatbot_public_key) }
          .not_to have_enqueued_job(CreateAndSendMiaCredentialsJob)
      end
    end

    describe '#generate_standard_public_key' do
      let(:mia_integration_type) { create(:mia_integration_type, :guides) }
      let(:integration) do
        build(:mia_integration, mia_integration_type: mia_integration_type, retailer: retailer, public_key: nil)
      end

      before { integration.save! }

      it 'uses SECRET_KEY_BASE as public key' do
        integration.send(:generate_standard_public_key)
        expect(integration.reload.public_key).to eq('test-secret-key')
      end
    end

    describe '#generate_suggestions_public_key' do
      let(:mia_integration_type) { create(:mia_integration_type, :suggestions) }
      let(:integration) do
        build(:mia_integration, mia_integration_type: mia_integration_type, retailer: retailer, public_key: nil)
      end

      before { integration.save! }

      it 'generates a random 32-byte key encoded in base64' do
        integration.send(:generate_suggestions_public_key)
        new_public_key = integration.reload.public_key

        expect(new_public_key).to be_present
        expect(new_public_key).not_to eq('test-secret-key') # Should be random, not SECRET_KEY_BASE

        # Decode and verify it's 32 bytes
        decoded_key = Base64.urlsafe_decode64(new_public_key)
        expect(decoded_key.length).to eq(32)
      end
    end

    describe '#generate_chatbot_service_url' do
      let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
      let(:integration) { build(:mia_integration, mia_integration_type: mia_integration_type, retailer: retailer) }

      context 'when environment is production' do
        before { allow(ENV).to receive(:fetch).with('ENVIRONMENT', '').and_return('production') }

        it 'generates URL without environment suffix' do
          integration.send(:generate_chatbot_service_url)
          expect(integration.service_url).to eq('https://mia-chatbot-testretailer.mercately.ai')
        end
      end

      context 'when environment is not production' do
        before { allow(ENV).to receive(:fetch).with('ENVIRONMENT', '').and_return('staging') }

        it 'generates URL with environment suffix' do
          integration.send(:generate_chatbot_service_url)
          expect(integration.service_url).to eq('https://mia-chatbot-testretailer-staging.mercately.ai')
        end
      end
    end

    describe '#generate_products_service_url' do
      let(:mia_integration_type) { create(:mia_integration_type, :products) }
      let(:integration) { build(:mia_integration, mia_integration_type: mia_integration_type, retailer: retailer) }

      it 'uses MIA_PRODUCTS_SERVICE_URL' do
        integration.send(:generate_products_service_url)
        expect(integration.service_url).to eq('https://products.mercately.ai')
      end
    end

    describe '#generate_guides_service_url' do
      let(:mia_integration_type) { create(:mia_integration_type, :guides) }
      let(:integration) { build(:mia_integration, mia_integration_type: mia_integration_type, retailer: retailer) }

      it 'uses MIA_GUIDES_SERVICE_URL' do
        integration.send(:generate_guides_service_url)
        expect(integration.service_url).to eq('https://guides.mercately.ai')
      end
    end

    describe '#generate_suggestions_service_url' do
      let(:mia_integration_type) { create(:mia_integration_type, :suggestions) }
      let(:integration) { build(:mia_integration, mia_integration_type: mia_integration_type, retailer: retailer) }

      it 'uses MIA_SUGGESTIONS_SERVICE_URL' do
        integration.send(:generate_suggestions_service_url)
        expect(integration.service_url).to eq('https://suggestions.mercately.ai')
      end
    end

    describe '#delete_remote_file' do
      let(:mia_integration_type) { create(:mia_integration_type, :chatbots) }
      let(:integration) { create(:mia_integration, mia_integration_type: mia_integration_type, retailer: retailer) }

      it 'enqueues DeleteRemoteMiaIntegrationFileJob and mailer' do
        expect { integration.send(:delete_remote_file) }
          .to have_enqueued_job(Mia::DeleteRemoteMiaIntegrationFileJob).with(mia_integration_type.name, retailer.slug)
          .and have_enqueued_mail(MiaNotificationMailer, :delete_integration_confirm_email)
      end
    end

    describe '#public_key_builders' do
      let(:mia_integration_type_for_builders) { create(:mia_integration_type, :guides) }
      let(:integration) { build(:mia_integration, mia_integration_type: mia_integration_type_for_builders) }

      it 'returns hash with correct method references' do
        builders = integration.send(:public_key_builders)
        expect(builders.keys).to contain_exactly(:chatbot, :products, :guides, :suggestions)
        expect(builders[:chatbot]).to be_a(Method)
        expect(builders[:products]).to be_a(Method)
        expect(builders[:guides]).to be_a(Method)
        expect(builders[:suggestions]).to be_a(Method)
      end
    end

    describe '#service_url_builders' do
      let(:mia_integration_type_for_service_builders) { create(:mia_integration_type, :guides) }
      let(:integration) { build(:mia_integration, mia_integration_type: mia_integration_type_for_service_builders) }

      it 'returns hash with correct method references' do
        builders = integration.send(:service_url_builders)
        expect(builders.keys).to contain_exactly(:chatbot, :products, :guides, :suggestions)
        expect(builders[:chatbot]).to be_a(Method)
        expect(builders[:products]).to be_a(Method)
        expect(builders[:guides]).to be_a(Method)
        expect(builders[:suggestions]).to be_a(Method)
      end
    end
  end

  describe 'class methods' do
    describe '#integration_with' do
      let(:mia_integration_type) { create(:mia_integration_type, :guides) }
      let!(:integration) { create(:mia_integration, :with_public_key, mia_integration_type: mia_integration_type) }

      it { expect(described_class.integration_with(mia_integration_type.name)).to eq integration }
    end
  end

  describe '.ransackable_attributes' do
    let(:expected_attributes) { %w[created_at id kind mia_integration_type_id retailer_id service_url updated_at] }

    it { expect(described_class.ransackable_attributes).to match_array(expected_attributes) }
  end

  describe '.ransackable_associations' do
    let(:expected_attributes) { %w[mia_integration_type retailer] }

    it { expect(described_class.ransackable_associations).to match_array(expected_attributes) }
  end
end
