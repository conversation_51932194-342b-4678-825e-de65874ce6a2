require 'rails_helper'

RSpec.describe UserAcademyProgress do
  describe 'associations' do
    it { is_expected.to belong_to(:retailer_user) }
    it { is_expected.to belong_to(:academy_video) }
  end

  describe 'validations' do
    let!(:retailer) { create(:retailer) }
    let!(:retailer_user) { create(:retailer_user, retailer: retailer) }

    it 'is valid with valid attributes' do
      academy_video = create(:academy_video, external_url: 'https://www.youtube.com/watch?v=abc12345678')
      user_academy_progress = described_class.new(retailer_user: retailer_user, academy_video: academy_video)
      expect(user_academy_progress).to be_valid
    end

    it 'is not valid without a retailer_user' do
      academy_video = create(:academy_video, external_url: 'https://www.youtube.com/watch?v=abc12345678')
      user_academy_progress = described_class.new(academy_video: academy_video)
      expect(user_academy_progress).not_to be_valid
    end

    it 'is not valid without an academy_video' do
      user_academy_progress = described_class.new(retailer_user: retailer_user)
      expect(user_academy_progress).not_to be_valid
    end
  end
end
