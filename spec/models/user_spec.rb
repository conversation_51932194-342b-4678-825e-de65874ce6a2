require 'rails_helper'

RSpec.describe User do
  describe 'validations' do
    it { is_expected.to validate_presence_of(:agree_terms) }
    it { is_expected.to validate_presence_of(:email) }
    it { is_expected.to validate_uniqueness_of(:email).case_insensitive }
  end

  describe 'associations' do
    it { is_expected.to have_many(:retailer_users).dependent(:restrict_with_error) }
    it { is_expected.to have_many(:retailers).through(:retailer_users) }
  end

  describe 'nested attributes' do
    it { is_expected.to accept_nested_attributes_for(:retailers) }
    it { is_expected.to accept_nested_attributes_for(:retailer_users) }
  end

  describe 'devise modules' do
    it 'includes the correct devise modules' do
      expect(described_class.devise_modules).to include(
        :invitable, :database_authenticatable, :registerable,
        :recoverable, :rememberable, :validatable, :omniauthable
      )
    end

    it 'has correct omniauth providers' do
      expect(described_class.omniauth_providers).to include(:facebook)
    end
  end

  describe 'class methods' do
    describe '.ransackable_attributes' do
      it 'returns the correct ransackable attributes' do
        expected_attributes = %w[
          agree_terms api_session_device api_session_expiration api_session_token created_at email
          encrypted_password first_name full_name id invitation_accepted_at invitation_created_at
          invitation_limit invitation_sent_at invitation_token invitations_count invited_by_id
          invited_by_type last_name remember_created_at reset_password_sent_at reset_password_token updated_at
        ]
        expect(described_class.ransackable_attributes).to eq(expected_attributes)
      end
    end

    describe '.ransackable_associations' do
      it 'returns the correct ransackable associations' do
        expected_associations = %w[invited_by retailer_users retailers]
        expect(described_class.ransackable_associations).to eq(expected_associations)
      end
    end

    describe 'search_by_text scope' do
      let!(:user) { create(:user, first_name: 'Jim', last_name: 'Carrey', email: '<EMAIL>') }
      let!(:user_anna) { create(:user, first_name: 'Anna', last_name: 'Doe', email: '<EMAIL>') }
      let!(:user_jhon) { create(:user, first_name: 'Jhon', last_name: 'Doe', email: '<EMAIL>') }
      let!(:user_sha) { create(:user, first_name: 'Sha', last_name: 'Te', email: '<EMAIL>') }

      it 'filter records that only match the text to search' do
        expect(described_class.search_by_text('Anna')).to include user_anna
        expect(described_class.search_by_text('doe')).to include user_anna
        expect(described_class.search_by_text('doe')).to include user_jhon
        expect(described_class.search_by_text('connor')).to include user_sha
      end
    end

    describe 'ransacker full_name' do
      let!(:user) { create(:user, first_name: 'Jim', last_name: 'Carrey', email: '<EMAIL>') }
      let!(:user_anna) { create(:user, first_name: 'Anna', last_name: 'Doe', email: '<EMAIL>') }
      let!(:user_jhon) { create(:user, first_name: 'Jhon', last_name: 'Doe', email: '<EMAIL>') }
      let!(:user_sha) { create(:user, first_name: 'Sha', last_name: 'Te', email: '<EMAIL>') }

      describe 'full_name_cont' do
        let(:search_criteria) { 'mail' }
        let(:result) { described_class.ransack(full_name_cont: search_criteria).result }

        it 'returns users that contains the search criteria' do
          expect(result).to include user
          expect(result).to include user_anna
          expect(result).to include user_jhon
          expect(result).not_to include user_sha
        end
      end

      describe 'full_name_eq' do
        let(:search_criteria) { "#{user.first_name} #{user.last_name} #{user.email}" }
        let(:result) { described_class.ransack(full_name_eq: search_criteria).result }

        it 'returns users that exactly contains the search criteria' do
          expect(result).to include user
          expect(result).not_to include user_anna
          expect(result).not_to include user_jhon
          expect(result).not_to include user_sha
        end
      end
    end
  end

  describe 'instance methods' do
    describe '#full_name' do
      it 'returns a full name' do
        retailer_user = described_class.new(first_name: 'John', last_name: 'Wick')

        expect(retailer_user.full_name).to eq('John Wick')
      end
    end

    describe 'renew_invitation_token' do
      context 'with invitation token' do
        let(:user) { create(:user, :with_invitation_token) }
        let(:original_token) { user.invitation_token }

        it { expect(user.renew_invitation_token).not_to be_nil }
        it { expect { user.renew_invitation_token }.to change(user, :invitation_token) }

        it 'returns a valid encrypted token' do
          result = user.renew_invitation_token
          expect(result).to be_a(String)
          expect(result).not_to eq(original_token)
        end

        it 'saves the user without validation' do
          expect(user).to receive(:save).with(validate: false)
          user.renew_invitation_token
        end
      end

      context 'without invitation token' do
        let(:user) { build(:user) }

        it { expect(user.renew_invitation_token).to be_nil }
        it { expect { user.renew_invitation_token }.not_to change(user, :invitation_token) }
      end
    end

    describe 'generate_api_token!' do
      let(:user) { create(:user) }

      it { expect { user.generate_api_token! }.to change(user, :api_session_expiration) }
      it { expect { user.generate_api_token! }.to change(user, :api_session_token) }

      it 'returns the new token' do
        token = user.generate_api_token!
        expect(token).to eq(user.api_session_token)
        expect(token).to be_a(String)
        expect(token.length).to eq(32) # SecureRandom.hex generates 32 character hex string
      end

      it 'sets expiration to 1 year from now' do
        freeze_time do
          user.generate_api_token!
          expect(user.api_session_expiration).to be_within(1.second).of(1.year.from_now)
        end
      end

      context 'without api_session_device' do
        it { expect { user.generate_api_token! }.to change(user, :api_session_device) }

        it 'generates a device identifier' do
          user.generate_api_token!
          expect(user.api_session_device).to be_a(String)
          expect(user.api_session_device.length).to eq(6) # SecureRandom.hex(3) generates 6 character hex string
        end
      end

      context 'with api_session_device' do
        let(:user) { create(:user, api_session_device: 'existing_device') }

        it { expect { user.generate_api_token! }.not_to change(user, :api_session_device) }
      end

      context 'when generate_device is true' do
        it { expect { user.generate_api_token!(true) }.to change(user, :api_session_device) }

        it 'generates new device even if one exists' do
          user.update(api_session_device: 'old_device')
          user.generate_api_token!(true)
          expect(user.api_session_device).not_to eq('old_device')
        end
      end
    end

    describe 'destroy_api_token!' do
      let(:user) { create(:user, api_session_token: 'token', api_session_expiration: 1.day.from_now) }

      it { expect { user.destroy_api_token! }.to change(user, :api_session_token).to(nil) }
      it { expect { user.destroy_api_token! }.to change(user, :api_session_expiration).to(nil) }
    end

    describe 'build_retailer' do
      let(:user) { build(:user) }

      it { expect(user.build_retailer).to be_instance_of Retailer }
    end

    describe 'current_retailer_user' do
      let(:retailer) { create(:retailer) }
      let(:another_retailer) { create(:retailer) }
      let!(:retailer_user) { create(:retailer_user, user: user, retailer: retailer) }
      let!(:another_retailer_user) { create(:retailer_user, user: user, retailer: another_retailer, current: false) }
      let(:user) { create(:user) }

      it { expect(user.current_retailer_user).to eq retailer_user }
      it { expect(user.current_retailer_user).not_to eq another_retailer_user }
    end

    describe 'current_retailer' do
      let(:retailer) { create(:retailer) }
      let(:another_retailer) { create(:retailer) }
      let(:user) { create(:user) }

      context 'when user has current retailer user' do
        let!(:retailer_user) { create(:retailer_user, user: user, retailer: retailer) }
        let!(:another_retailer_user) { create(:retailer_user, user: user, retailer: another_retailer, current: false) }

        it { expect(user.current_retailer).to eq retailer }
        it { expect(user.current_retailer).not_to eq another_retailer }
      end

      context 'when user does not have current retailer user' do
        it { expect(user.current_retailer).to be_nil }
      end
    end

    describe 'current_mobile_retailer_user' do
      let(:retailer) { create(:retailer) }
      let(:another_retailer) { create(:retailer) }
      let!(:retailer_user) { create(:retailer_user, user: user, retailer: retailer, current_mobile: true) }
      let!(:another_retailer_user) do
        create(:retailer_user, user: user, retailer: another_retailer, current_mobile: false)
      end
      let(:user) { create(:user) }

      it { expect(user.current_mobile_retailer_user).to eq retailer_user }
      it { expect(user.current_mobile_retailer_user).not_to eq another_retailer_user }
    end

    describe 'current_mobile_retailer' do
      let(:retailer) { create(:retailer) }
      let(:another_retailer) { create(:retailer) }
      let(:user) { create(:user) }

      context 'when user has current mobile retailer user' do
        let!(:retailer_user) { create(:retailer_user, user: user, retailer: retailer, current_mobile: true) }
        let!(:another_retailer_user) do
          create(:retailer_user, user: user, retailer: another_retailer, current_mobile: false)
        end

        it { expect(user.current_mobile_retailer).to eq retailer }
        it { expect(user.current_mobile_retailer).not_to eq another_retailer }
      end

      context 'when user does not have current mobile retailer user' do
        it { expect(user.current_mobile_retailer).to be_nil }
      end
    end
  end

  describe 'delegated methods from Delegations::User' do
    let(:retailer) { create(:retailer) }
    let(:user) { create(:user) }
    let!(:retailer_user) { create(:retailer_user, user: user, retailer: retailer) }

    describe '#retailer_admin' do
      it 'delegates to current_retailer_user' do
        expect(user.retailer_admin).to eq(retailer_user.retailer_admin)
      end
    end

    describe '#retailer_supervisor' do
      it 'delegates to current_retailer_user' do
        expect(user.retailer_supervisor).to eq(retailer_user.retailer_supervisor)
      end
    end

    describe '#super_admin' do
      it 'delegates to current_retailer_user' do
        expect(user.super_admin).to eq(retailer_user.super_admin)
      end
    end

    describe '#only_assigned' do
      it 'delegates to current_retailer_user' do
        expect(user.only_assigned).to eq(retailer_user.only_assigned)
      end
    end

    describe '#allow_import' do
      it 'delegates to current_retailer_user' do
        expect(user.allow_import).to eq(retailer_user.allow_import)
      end
    end

    describe '#allow_export' do
      it 'delegates to current_retailer_user' do
        expect(user.allow_export).to eq(retailer_user.allow_export)
      end
    end

    describe '#allow_bots_edit' do
      it 'delegates to current_retailer_user' do
        expect(user.allow_bots_edit).to eq(retailer_user.allow_bots_edit)
      end
    end

    describe '#allow_edit_orders' do
      it 'delegates to current_retailer_user' do
        expect(user.allow_edit_orders).to eq(retailer_user.allow_edit_orders)
      end
    end

    describe '#removed_from_team' do
      it 'delegates to current_retailer_user' do
        expect(user.removed_from_team).to eq(retailer_user.removed_from_team)
      end
    end

    describe '#managed_agents' do
      it 'delegates to current_retailer_user' do
        expect(user.managed_agents).to eq(retailer_user.managed_agents)
      end
    end

    describe '#admin?' do
      it 'delegates to current_retailer_user' do
        expect(user.admin?).to eq(retailer_user.admin?)
      end
    end

    describe '#supervisor?' do
      it 'delegates to current_retailer_user' do
        expect(user.supervisor?).to eq(retailer_user.supervisor?)
      end
    end

    describe '#mobile_tokens' do
      it 'delegates to current_retailer_user' do
        expect(user.mobile_tokens).to eq(retailer_user.mobile_tokens)
      end
    end
  end

  describe 'concerns' do
    describe 'TransliteratableEmailConcern' do
      it 'includes TransliteratableEmailConcern' do
        expect(described_class.included_modules).to include(TransliteratableEmailConcern)
      end
    end

    describe 'EmailableConcern' do
      it 'includes EmailableConcern' do
        expect(described_class.included_modules).to include(EmailableConcern)
      end
    end

    describe 'Delegations::User' do
      it 'includes Delegations::User' do
        expect(described_class.included_modules).to include(Delegations::User)
      end
    end
  end

  describe 'attr_accessor' do
    let(:user) { build(:user) }

    it 'has created_by_partner accessor' do
      user.created_by_partner = true
      expect(user.created_by_partner).to be true
    end

    it 'has not_ask_terms accessor' do
      user.not_ask_terms = 'true'
      expect(user.not_ask_terms).to eq 'true'
    end

    it 'has shop_domain accessor' do
      user.shop_domain = 'example.com'
      expect(user.shop_domain).to eq 'example.com'
    end

    it 'has shop_id accessor' do
      user.shop_id = 123
      expect(user.shop_id).to eq 123
    end
  end

  describe 'validations with not_ask_terms' do
    context 'when not_ask_terms is true' do
      let(:user) { build(:user, not_ask_terms: 'true', agree_terms: nil) }

      it 'does not require agree_terms' do
        expect(user).to be_valid
      end
    end

    context 'when not_ask_terms is false' do
      let(:user) { build(:user, not_ask_terms: 'false', agree_terms: nil) }

      it 'requires agree_terms' do
        I18n.with_locale(:es) do
          expect(user).not_to be_valid
          expect(user.errors[:agree_terms]).to include('no aceptados')
        end
      end
    end
  end

  describe 'password validations' do
    context 'when creating a new user' do
      let(:user) { build(:user, password: nil) }

      it 'is invalid without a password' do
        I18n.with_locale(:es) do
          expect(user).not_to be_valid
          expect(user.errors[:password]).to include('no puede estar vacío')
        end
      end

      it 'is valid with a password' do
        user.password = 'password123'
        user.password_confirmation = 'password123'
        expect(user).to be_valid
      end

      context 'when created_by_partner is true' do
        it 'is valid without a password' do
          I18n.with_locale(:es) do
            user.created_by_partner = true
            expect(user).to be_valid
          end
        end

        it 'is valid with a password' do
          user.created_by_partner = true
          user.password = 'password123'
          user.password_confirmation = 'password123'
          expect(user).to be_valid
        end
      end
    end

    context 'when updating an existing record' do
      let(:user) { create(:user, password: 'password123', password_confirmation: 'password123') }

      it 'does not require a password if password is not being changed' do
        user.first_name = 'NewName'
        expect(user).to be_valid
      end

      it 'requires a password if password is being changed' do
        I18n.with_locale(:es) do
          user.password = 'newpassword'
          expect(user).not_to be_valid
          expect(user.errors[:password_confirmation]).to include('no coincide')
        end
      end

      it 'requires a password confirmation if password is being changed' do
        user.password = 'newpassword'
        user.password_confirmation = 'newpassword'
        expect(user).to be_valid
      end

      context 'when created_by_partner is true' do
        it 'requires a password if password is being changed' do
          I18n.with_locale(:es) do
            user.created_by_partner = true
            user.password = 'newpassword'
            expect(user).not_to be_valid
            expect(user.errors[:password_confirmation]).to include('no coincide')
          end
        end
      end
    end
  end

  describe 'private methods' do
    describe '#password_required?' do
      context 'when created_by_partner is true and record is new' do
        let(:user) { build(:user, created_by_partner: true) }

        it 'does not require password' do
          expect(user.send(:password_required?)).to be false
        end
      end

      context 'when created_by_partner is false' do
        let(:user) { build(:user, created_by_partner: false) }

        it 'requires password' do
          expect(user.send(:password_required?)).to be true
        end
      end

      context 'when created_by_partner is nil' do
        let(:user) { build(:user, created_by_partner: nil) }

        it 'requires password' do
          expect(user.send(:password_required?)).to be true
        end
      end

      context 'when created_by_partner is true but record is persisted' do
        let(:user) { create(:user, created_by_partner: true) }

        it 'follows default devise behavior for updates' do
          # For existing records, if password is being changed, it's required
          user.password = 'newpassword'
          expect(user.send(:password_required?)).to be true
        end
      end
    end
  end
end
