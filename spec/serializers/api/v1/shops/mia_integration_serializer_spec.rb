require 'rails_helper'

RSpec.describe Api::V1::Shops::MiaIntegrationSerializer, type: :serializer do
  let(:mia_integration_type) { create(:mia_integration_type, :guides) }
  let(:mia_integration) do
    create(:mia_integration, mia_integration_type: mia_integration_type, openai_key: 'test_key',
                             public_key: 'test_public_key').tap do |integration|
      allow(integration).to receive(:service_url).and_return('http://test.url')
    end
  end
  let(:serializer) { described_class.new(mia_integration) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }
  let(:serialized_data) { JSON.parse(serialization.to_json) }

  it 'serializes the openai_key' do
    expect(serialized_data['openai_key']).to eq('test_key')
  end

  it 'serializes the public_key' do
    expect(serialized_data['public_key']).to eq('test_public_key')
  end

  it 'serializes the service_url' do
    expect(serialized_data['service_url']).to eq('http://test.url')
  end
end
