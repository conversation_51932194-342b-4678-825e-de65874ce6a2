require 'rails_helper'

RSpec.describe Whatsapp::Events::OutboundStrategy, type: :service do
  subject { described_class.new(retailer: retailer, params: params, from: from, qr_params: qr_params) }

  let(:retailer) { create(:retailer) }
  let(:params) { { payload: { type: 'text', payload: { id: '123' } } } }
  let(:qr_params) { nil }
  let(:from) { 'gupshup' }
  let(:customer) { create(:customer, retailer: retailer) }

  describe '#initialize' do
    it 'sets all attributes correctly' do
      strategy = described_class.new(retailer: retailer, params: params, from: from, qr_params: qr_params)

      expect(strategy.retailer).to eq(retailer)
      expect(strategy.params).to eq(params)
      expect(strategy.from).to eq(from)
      expect(strategy.qr_params).to eq(qr_params)
    end

    it 'sets default values for optional parameters' do
      strategy = described_class.new(retailer: retailer, params: params)

      expect(strategy.from).to eq('gupshup')
      expect(strategy.qr_params).to be_nil
    end
  end

  describe '#call' do
    context 'when inputs are invalid' do
      it 'returns nil if retailer is blank' do
        subject = described_class.new(retailer: nil, params: params)
        expect(subject.call).to be_nil
      end

      it 'returns nil if params are blank' do
        subject = described_class.new(retailer: retailer, params: nil)
        expect(subject.call).to be_nil
      end

      it 'returns nil if both retailer and params are blank' do
        subject = described_class.new(retailer: nil, params: nil)
        expect(subject.call).to be_nil
      end
    end

    context 'when inputs are valid' do
      it 'calls process' do
        expect_any_instance_of(described_class).to receive(:process).and_call_original
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        allow(Whatsapp::Utils::OutboundPayloadBuilder).to receive(:call).and_return({})
        allow(GupshupWhatsappMessage).to receive(:create!).and_return(create(:gupshup_whatsapp_message, id: 1))
        allow_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)

        subject.call
      end
    end
  end

  describe '#process' do
    before do
      allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
    end

    context 'when customer cannot receive messages' do
      context 'when customer is blank' do
        it 'returns early without processing' do
          allow_any_instance_of(described_class).to receive(:save_customer).and_return(nil)

          result = subject.send(:process)
          expect(result).to be_nil
        end
      end
    end

    context 'when message is unsupported' do
      let(:params) do
        {
          'payload' => {
            'type' => 'unsupported',
            'payload' => {
              'code' => 'UNSUPPORTED_MESSAGE_TYPE',
              'reason' => 'This message type is not supported'
            }
          }
        }
      end

      it 'logs unsupported message and returns early' do
        message = "Unsupported outbound message type 'unsupported' - " \
                  'Code: UNSUPPORTED_MESSAGE_TYPE, Reason: This message type is not supported'
        expect(Rails.logger).to receive(:info).with(message)
        expect(Notifications::Web::Messages).not_to receive(:new)

        result = subject.send(:process)
        expect(result).to be_nil
      end
    end

    context 'when processing regular outbound message' do
      before do
        allow(Whatsapp::Utils::OutboundPayloadBuilder).to receive(:call).and_return({})
        allow(GupshupWhatsappMessage).to receive(:create!).and_return(instance_double(GupshupWhatsappMessage, id: 123))
        allow_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)
      end

      it 'creates message, logs, and broadcasts' do
        gwm = create(:gupshup_whatsapp_message, id: 123)
        allow(GupshupWhatsappMessage).to receive(:create!).and_return(gwm)

        expect(Whatsapp::Utils::OutboundPayloadBuilder).to receive(:call).with(
          retailer: retailer,
          customer: customer,
          params: params,
          from: from
        )
        expect(GupshupWhatsappMessage).to receive(:create!).and_return(gwm)

        subject.send(:process)
      end

      it 'logs message creation and broadcasts' do
        gwm = create(:gupshup_whatsapp_message, id: 123)
        allow(GupshupWhatsappMessage).to receive(:create!).and_return(gwm)

        expect(Rails.logger).to receive(:info).with('New outbound message 123 from gupshup')
        expect_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)

        subject.send(:process)
      end
    end

    context 'when processing reaction message' do
      let(:params) { { payload: { type: 'reaction', payload: { id: '123', emoji: '👍' } } } }
      let!(:original_message) { create(:gupshup_whatsapp_message, whatsapp_message_id: '123') }

      before do
        allow(GupshupWhatsappMessage).to receive(:find_by)
          .with('whatsapp_message_id = ?', '123')
          .and_return(original_message)
      end

      it 'updates the reaction in the original message and broadcasts' do
        expect(original_message).to receive(:update).with(reaction: '👍')
        expect_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)

        subject.send(:process)
      end

      context 'when original message is not found' do
        before do
          allow(GupshupWhatsappMessage).to receive(:find_by).and_return(nil)
        end

        it 'returns early without broadcasting' do
          expect_any_instance_of(GupshupWhatsappMessage).not_to receive(:update)
          expect(Notifications::Web::Messages).not_to receive(:new)

          result = subject.send(:process)
          expect(result).to be_nil
        end
      end
    end

    context 'when an exception is raised' do
      it 'handles error gracefully and returns false' do
        test_error = StandardError.new('Test database error')
        allow(Whatsapp::Utils::OutboundPayloadBuilder).to receive(:call).and_raise(test_error)

        expect(Rails.logger).to receive(:error).with(test_error)
        expect(Rails.logger).to receive(:info).with(an_instance_of(Array))
        expect(SlackError).to receive(:send_error).with(test_error)

        result = subject.send(:process)
        expect(result).to be false
      end

      it 'logs the backtrace when an exception occurs' do
        test_error = StandardError.new('Test error')
        backtrace_lines = %w[line1 line2 line3 line4 line5 line6 line7 line8 line9 line10 line11]
        allow(test_error).to receive(:backtrace).and_return(backtrace_lines)
        allow(Whatsapp::Utils::OutboundPayloadBuilder).to receive(:call).and_raise(test_error)

        expect(Rails.logger).to receive(:error).with(test_error)
        expect(Rails.logger).to receive(:info).with(%w[line1 line2 line3 line4 line5 line6 line7 line8 line9 line10])
        expect(SlackError).to receive(:send_error).with(test_error)

        result = subject.send(:process)
        expect(result).to be false
      end
    end

    context 'when message creation fails but handle_message_type returns nil' do
      before do
        allow_any_instance_of(described_class).to receive(:handle_message_type).and_return(nil)
      end

      it 'returns early without broadcasting' do
        expect(Notifications::Web::Messages).not_to receive(:new)

        result = subject.send(:process)
        expect(result).to be_nil
      end
    end
  end

  describe 'private methods' do
    describe '#valid_inputs?' do
      it 'returns true when both retailer and params are present' do
        result = subject.send(:valid_inputs?)
        expect(result).to be true
      end

      it 'returns false when retailer is blank' do
        subject = described_class.new(retailer: nil, params: params)
        result = subject.send(:valid_inputs?)
        expect(result).to be false
      end

      it 'returns false when params are blank' do
        subject = described_class.new(retailer: retailer, params: nil)
        result = subject.send(:valid_inputs?)
        expect(result).to be false
      end
    end

    describe '#reaction_message?' do
      context 'when message type is reaction' do
        let(:params) { { payload: { type: 'reaction' } } }

        it 'returns true' do
          result = subject.send(:reaction_message?)
          expect(result).to be true
        end
      end

      context 'when message type is not reaction' do
        let(:params) { { payload: { type: 'text' } } }

        it 'returns false' do
          result = subject.send(:reaction_message?)
          expect(result).to be false
        end
      end

      context 'when payload is missing' do
        let(:params) { {} }

        it 'returns false' do
          result = subject.send(:reaction_message?)
          expect(result).to be false
        end
      end
    end

    describe '#find_original_message' do
      let(:params) { { payload: { type: 'reaction', payload: { id: '123' } } } }

      it 'calls GupshupWhatsappMessage.find_by with correct parameters' do
        expect(GupshupWhatsappMessage).to receive(:find_by)
          .with('whatsapp_message_id = ?', '123')

        subject.send(:find_original_message)
      end
    end

    describe '#update_message_reaction' do
      let(:params) { { payload: { type: 'reaction', payload: { emoji: '🎉' } } } }
      let(:message) { create(:gupshup_whatsapp_message) }

      it 'updates the message with the correct emoji' do
        expect(message).to receive(:update).with(reaction: '🎉')

        subject.send(:update_message_reaction, message)
      end
    end

    describe '#build_message_payload' do
      before do
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        subject.instance_variable_set(:@customer, customer)
      end

      it 'calls OutboundPayloadBuilder with correct parameters' do
        expect(Whatsapp::Utils::OutboundPayloadBuilder).to receive(:call)
          .with(retailer: retailer, customer: customer, params: params, from: from)

        subject.send(:build_message_payload)
      end
    end

    describe '#log_message_creation' do
      let(:gwm) { create(:gupshup_whatsapp_message, id: 456) }

      it 'logs the message creation' do
        expect(Rails.logger).to receive(:info).with('New outbound message 456 from gupshup')

        subject.send(:log_message_creation, gwm)
      end
    end

    describe '#broadcast_message' do
      let(:gwm) { create(:gupshup_whatsapp_message) }

      it 'creates a notification and broadcasts it' do
        expect_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)

        subject.send(:broadcast_message, gwm)
      end
    end

    describe '#save_customer' do
      it 'calls Whatsapp::Helpers::Customers.save_customer with correct parameters' do
        expect(Whatsapp::Helpers::Customers).to receive(:save_customer)
          .with(retailer, params.merge(direction: 'outbound'), from)

        subject.send(:save_customer)
      end
    end

    describe '#unsupported_message?' do
      context 'when message type is unsupported' do
        let(:params) { { 'payload' => { 'type' => 'unsupported' } } }

        it 'returns true' do
          result = subject.send(:unsupported_message?)
          expect(result).to be true
        end
      end

      context 'when message type is not unsupported' do
        let(:params) { { 'payload' => { 'type' => 'text' } } }

        it 'returns false' do
          result = subject.send(:unsupported_message?)
          expect(result).to be false
        end
      end

      context 'when payload is missing' do
        let(:params) { {} }

        it 'returns false' do
          result = subject.send(:unsupported_message?)
          expect(result).to be false
        end
      end
    end

    describe '#log_unsupported_message' do
      let(:params) do
        {
          'payload' => {
            'type' => 'unsupported',
            'payload' => {
              'code' => 'ERROR_CODE',
              'reason' => 'Error reason'
            }
          }
        }
      end

      before do
        allow(Rails.logger).to receive(:info)
      end

      it 'logs the unsupported message details' do
        subject.send(:log_unsupported_message)

        message = "Unsupported outbound message type 'unsupported' - Code: ERROR_CODE, Reason: Error reason"
        expect(Rails.logger).to have_received(:info).with(message)
      end

      it 'returns nil' do
        result = subject.send(:log_unsupported_message)
        expect(result).to be_nil
      end

      context 'when payload data is missing' do
        let(:params) { { 'payload' => { 'type' => 'unsupported' } } }

        it 'logs with nil values for missing data' do
          subject.send(:log_unsupported_message)

          message = "Unsupported outbound message type 'unsupported' - Code: , Reason: "
          expect(Rails.logger).to have_received(:info).with(message)
        end
      end
    end

    describe '#handle_error' do
      let(:test_error) { StandardError.new('Test error') }

      before do
        allow(test_error).to receive(:backtrace).and_return(%w[line1 line2])
        allow(Rails.logger).to receive(:error)
        allow(Rails.logger).to receive(:info)
        allow(SlackError).to receive(:send_error)
      end

      it 'logs the error' do
        subject.send(:handle_error, test_error)

        expect(Rails.logger).to have_received(:error).with(test_error)
      end

      it 'logs the backtrace' do
        subject.send(:handle_error, test_error)

        expect(Rails.logger).to have_received(:info).with(%w[line1 line2])
      end

      it 'sends error to Slack' do
        subject.send(:handle_error, test_error)

        expect(SlackError).to have_received(:send_error).with(test_error)
      end

      it 'returns false' do
        result = subject.send(:handle_error, test_error)
        expect(result).to be false
      end
    end

    describe '#handle_message_type' do
      before do
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        subject.instance_variable_set(:@customer, customer)
      end

      context 'when message is a reaction' do
        let(:params) { { payload: { type: 'reaction', payload: { id: '123' } } } }

        it 'calls handle_reaction_message' do
          expect_any_instance_of(described_class).to receive(:handle_reaction_message)

          subject.send(:handle_message_type)
        end
      end

      context 'when message is not a reaction' do
        let(:params) { { payload: { type: 'text' } } }

        it 'calls create_outbound_message' do
          expect_any_instance_of(described_class).to receive(:create_outbound_message)

          subject.send(:handle_message_type)
        end
      end
    end

    describe '#handle_reaction_message' do
      let(:params) { { payload: { type: 'reaction', payload: { id: '123', emoji: '👍' } } } }
      let(:original_message) { create(:gupshup_whatsapp_message) }

      context 'when original message is found' do
        before do
          allow_any_instance_of(described_class).to receive(:find_original_message).and_return(original_message)
          allow_any_instance_of(described_class).to receive(:update_message_reaction)
        end

        it 'updates the reaction and returns the message' do
          expect_any_instance_of(described_class).to receive(:update_message_reaction).with(original_message)

          result = subject.send(:handle_reaction_message)
          expect(result).to eq(original_message)
        end
      end

      context 'when original message is not found' do
        before do
          allow_any_instance_of(described_class).to receive(:find_original_message).and_return(nil)
        end

        it 'returns nil without updating' do
          expect_any_instance_of(described_class).not_to receive(:update_message_reaction)

          result = subject.send(:handle_reaction_message)
          expect(result).to be_nil
        end
      end
    end

    describe '#create_outbound_message' do
      let(:gwm) { create(:gupshup_whatsapp_message) }

      before do
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        allow_any_instance_of(described_class).to receive(:build_message_payload).and_return({})
        allow(GupshupWhatsappMessage).to receive(:create!).and_return(gwm)
        allow_any_instance_of(described_class).to receive(:log_message_creation)
        subject.instance_variable_set(:@customer, customer)
      end

      it 'builds payload, creates message, logs, and returns the message' do
        expect_any_instance_of(described_class).to receive(:build_message_payload)
        expect(GupshupWhatsappMessage).to receive(:create!).and_return(gwm)
        expect_any_instance_of(described_class).to receive(:log_message_creation).with(gwm)

        result = subject.send(:create_outbound_message)
        expect(result).to eq(gwm)
      end
    end
  end
end
