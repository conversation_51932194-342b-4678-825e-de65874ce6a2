require 'rails_helper'

RSpec.describe Whatsapp::Events::InboundStrategy, type: :service do
  subject { described_class.new(retailer: retailer, params: params, from: from, qr_params: qr_params) }

  let(:retailer) { create(:retailer) }
  let(:params) { { payload: { type: 'text', payload: { id: '123' } }, from_group: nil } }
  let(:qr_params) { nil }
  let(:from) { 'gupshup' }
  let(:customer) { create(:customer, retailer: retailer) }

  describe '#initialize' do
    it 'sets all attributes correctly' do
      strategy = described_class.new(retailer: retailer, params: params, from: from, qr_params: qr_params)

      expect(strategy.retailer).to eq(retailer)
      expect(strategy.params).to eq(params)
      expect(strategy.from).to eq(from)
      expect(strategy.qr_params).to eq(qr_params)
    end

    it 'sets default values for optional parameters' do
      strategy = described_class.new(retailer: retailer, params: params)

      expect(strategy.retailer).to eq(retailer)
      expect(strategy.params).to eq(params)
      expect(strategy.from).to eq('gupshup')
      expect(strategy.qr_params).to be_nil
    end
  end

  describe '#call' do
    context 'when retailer or params are blank' do
      it 'returns nil if retailer is blank' do
        subject = described_class.new(retailer: nil, params: params)
        expect(subject.call).to be_nil
      end

      it 'returns nil if params are blank' do
        subject = described_class.new(retailer: retailer, params: nil)
        expect(subject.call).to be_nil
      end
    end

    context 'when retailer and params are present' do
      it 'calls process' do
        expect_any_instance_of(described_class).to receive(:process).and_call_original
        subject.call
      end
    end
  end

  describe '#process' do
    context 'when params indicate a group message' do
      let(:params) { { 'from_group' => true, payload: { type: 'text' } } }

      before do
        allow_any_instance_of(Customer).to receive(:blocked?).and_return(true)
        allow_any_instance_of(described_class).to receive(:save_group).and_return(customer)
      end

      it 'calls save_group and does not process a blocked customer' do
        expect_any_instance_of(described_class).to receive(:save_group).and_call_original
        expect(Notifications::Web::Messages).not_to receive(:new)

        subject.send(:process)
      end
    end

    context 'when params indicate an individual message' do
      it 'calls save_customer and broadcasts the message if customer is not blocked' do
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        allow(customer).to receive(:blocked?).and_return(false)
        payload = { retailer: retailer, customer: customer, params: params, from: from }

        allow(Whatsapp::Utils::InboundPayloadBuilder).to receive(:call).with(payload).and_return({})
        expect(GupshupWhatsappMessage).to receive(:create!).and_return(instance_double(GupshupWhatsappMessage, id: 1))
        expect_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)

        subject.send(:process)
      end
    end

    context 'when from_group is false or empty' do
      let(:params) { { 'from_group' => false, payload: { type: 'text' } } }

      it 'calls save_customer instead of save_group' do
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        allow(customer).to receive(:blocked?).and_return(false)
        allow(Whatsapp::Utils::InboundPayloadBuilder).to receive(:call).and_return({})
        allow(GupshupWhatsappMessage).to receive(:create!).and_return(instance_double(GupshupWhatsappMessage, id: 1))
        allow_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)

        expect_any_instance_of(described_class).to receive(:save_customer).and_call_original
        expect_any_instance_of(described_class).not_to receive(:save_group)

        subject.send(:process)
      end
    end

    context 'when from_group is empty string' do
      let(:params) { { 'from_group' => '', payload: { type: 'text' } } }

      it 'calls save_group because empty string is truthy' do
        allow_any_instance_of(described_class).to receive(:save_group).and_return(customer)
        allow(customer).to receive(:blocked?).and_return(false)
        allow(Whatsapp::Utils::InboundPayloadBuilder).to receive(:call).and_return({})
        allow(GupshupWhatsappMessage).to receive(:create!).and_return(instance_double(GupshupWhatsappMessage, id: 1))
        allow_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)

        expect_any_instance_of(described_class).to receive(:save_group).and_call_original
        expect_any_instance_of(described_class).not_to receive(:save_customer)

        subject.send(:process)
      end
    end

    context 'when there is a reaction type in payload' do
      let(:params) { { payload: { type: 'reaction', payload: { id: '123', gsId: '567', emoji: '👍' } } } }
      let!(:inbound_message) { create(:gupshup_whatsapp_message, whatsapp_message_id: '123') }

      before do
        allow(GupshupWhatsappMessage).to receive(:find_by)
          .with('whatsapp_message_id = :id', { id: '123' })
          .and_return(inbound_message)
      end

      it 'updates the reaction in the message if it exists' do
        expect(inbound_message).to receive(:update).with(reaction: '👍')
        expect_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)

        subject.send(:process)
      end
    end

    context 'when reaction message is found but update fails' do
      let(:params) { { payload: { type: 'reaction', payload: { id: '123', gsId: '567', emoji: '👍' } } } }
      let!(:inbound_message) { create(:gupshup_whatsapp_message, whatsapp_message_id: '123') }

      before do
        allow(GupshupWhatsappMessage).to receive(:find_by)
          .with('whatsapp_message_id = :id', { id: '123' })
          .and_return(inbound_message)
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        allow(customer).to receive(:blocked?).and_return(false)
      end

      it 'handles update failure gracefully' do
        test_error = StandardError.new('Update failed')
        allow(inbound_message).to receive(:update).and_raise(test_error)

        expect(Rails.logger).to receive(:error).with(test_error)
        expect(Rails.logger).to receive(:info).with(an_instance_of(Array))
        expect(SlackError).to receive(:send_error).with(test_error)

        result = subject.send(:process)
        expect(result).to be false
      end
    end

    context 'when inbound is blank' do
      let(:params) { { payload: { type: 'reaction', payload: { id: '123', emoji: '👍' } } } }

      before do
        allow(GupshupWhatsappMessage).to receive(:find_by).and_return(nil)
      end

      it 'returns early and does not update any message' do
        expect_any_instance_of(GupshupWhatsappMessage).not_to receive(:update)
        expect(subject.send(:process)).to be_falsey
      end
    end

    context 'when an exception is raised' do
      it 'logs the error and sends it to Slack' do
        allow_any_instance_of(described_class).to receive(:save_customer).and_raise(StandardError)

        expect(Rails.logger).to receive(:error).at_least(:once)
        allow(SlackError).to receive(:send_error).and_wrap_original do |method, *args|
          method.call(*args)
        end

        subject.send(:process)
      end

      it 'logs the backtrace when an exception occurs' do
        # Mock save_customer to succeed initially, then cause an error in the message creation
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        allow(customer).to receive(:blocked?).and_return(false)
        allow(Whatsapp::Utils::InboundPayloadBuilder).to receive(:call).and_return({})

        # Cause an error in GupshupWhatsappMessage.create!
        test_error = StandardError.new('Test database error')
        allow(GupshupWhatsappMessage).to receive(:create!).and_raise(test_error)

        expect(Rails.logger).to receive(:error).with(test_error)
        expect(Rails.logger).to receive(:info).with(an_instance_of(Array))
        expect(SlackError).to receive(:send_error).with(test_error)

        result = subject.send(:process)
        expect(result).to be false
      end
    end

    context 'when message is processed successfully' do
      before do
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        allow(customer).to receive(:blocked?).and_return(false)
        allow(Whatsapp::Utils::InboundPayloadBuilder).to receive(:call).and_return({})
        allow(GupshupWhatsappMessage).to receive(:create!).and_return(instance_double(GupshupWhatsappMessage, id: 123))
        allow_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)
      end

      it 'logs the new inbound message' do
        expect(Rails.logger).to receive(:info).with('New inbound message 123 from gupshup')

        subject.send(:process)
      end
    end

    context 'when message is unsupported' do
      let(:params) do
        {
          'payload' => {
            'type' => 'unsupported',
            'payload' => {
              'code' => 'UNSUPPORTED_MESSAGE_TYPE',
              'reason' => 'This message type is not supported'
            }
          }
        }
      end

      before do
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        allow(customer).to receive(:blocked?).and_return(false)
      end

      it 'logs unsupported message and returns early' do
        expect(Rails.logger).to receive(:info).with(
          "Unsupported inbound message type 'unsupported' - Code: UNSUPPORTED_MESSAGE_TYPE, " \
          'Reason: This message type is not supported'
        )
        expect(Notifications::Web::Messages).not_to receive(:new)

        result = subject.send(:process)
        expect(result).to be_nil
      end
    end

    context 'when payload structure is invalid' do
      let(:params) { { payload: nil } }

      before do
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(customer)
        allow(customer).to receive(:blocked?).and_return(false)
      end

      it 'handles nil payload gracefully' do
        allow(Whatsapp::Utils::InboundPayloadBuilder).to receive(:call).and_return({})
        allow(GupshupWhatsappMessage).to receive(:create!).and_return(instance_double(GupshupWhatsappMessage, id: 1))
        allow_any_instance_of(Notifications::Web::Messages).to receive(:broadcast!)

        expect { subject.send(:process) }.not_to raise_error
      end
    end

    context 'when customer creation returns nil' do
      it 'returns early without further processing' do
        allow_any_instance_of(described_class).to receive(:save_customer).and_return(nil)

        expect(Whatsapp::Utils::InboundPayloadBuilder).not_to receive(:call)
        expect(GupshupWhatsappMessage).not_to receive(:create!)
        expect(Notifications::Web::Messages).not_to receive(:new)

        result = subject.send(:process)
        expect(result).to be_nil
      end
    end
  end

  describe 'private methods' do
    describe '#save_customer' do
      it 'calls Whatsapp::Helpers::Customers.save_customer with correct parameters' do
        expect(Whatsapp::Helpers::Customers).to receive(:save_customer)
          .with(retailer, params.merge(direction: 'inbound'), from)

        subject.send(:save_customer)
      end
    end

    describe '#save_group' do
      context 'when qr_params is present' do
        let(:qr_params) { { 'qr_data' => 'test' } }

        it 'calls Whatsapp::Helpers::Groups.save_group with qr_params' do
          expect(Whatsapp::Helpers::Groups).to receive(:save_group)
            .with(retailer, qr_params.merge(direction: 'inbound'), from)

          subject.send(:save_group)
        end
      end

      context 'when qr_params is nil' do
        let(:qr_params) { nil }

        it 'calls Whatsapp::Helpers::Groups.save_group with params' do
          expect(Whatsapp::Helpers::Groups).to receive(:save_group)
            .with(retailer, params.merge(direction: 'inbound'), from)

          subject.send(:save_group)
        end
      end
    end

    describe '#unsupported_message?' do
      context 'when message type is unsupported' do
        let(:params) { { 'payload' => { 'type' => 'unsupported' } } }

        it 'returns true' do
          result = subject.send(:unsupported_message?)
          expect(result).to be true
        end
      end

      context 'when message type is not unsupported' do
        let(:params) { { 'payload' => { 'type' => 'text' } } }

        it 'returns false' do
          result = subject.send(:unsupported_message?)
          expect(result).to be false
        end
      end

      context 'when payload is missing' do
        let(:params) { {} }

        it 'returns false' do
          result = subject.send(:unsupported_message?)
          expect(result).to be false
        end
      end
    end

    describe '#log_unsupported_message' do
      let(:params) do
        {
          'payload' => {
            'type' => 'unsupported',
            'payload' => {
              'code' => 'ERROR_CODE',
              'reason' => 'Error reason'
            }
          }
        }
      end

      before do
        allow(Rails.logger).to receive(:info)
      end

      it 'logs the unsupported message details' do
        subject.send(:log_unsupported_message)

        expect(Rails.logger).to have_received(:info)
          .with("Unsupported inbound message type 'unsupported' - Code: ERROR_CODE, Reason: Error reason")
      end

      it 'returns nil' do
        result = subject.send(:log_unsupported_message)
        expect(result).to be_nil
      end

      context 'when payload data is missing' do
        let(:params) { { 'payload' => { 'type' => 'unsupported' } } }

        it 'logs with nil values for missing data' do
          subject.send(:log_unsupported_message)

          expect(Rails.logger).to have_received(:info)
            .with("Unsupported inbound message type 'unsupported' - Code: , Reason: ")
        end
      end
    end
  end
end
