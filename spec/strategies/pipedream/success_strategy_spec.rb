require 'rails_helper'

RSpec.describe Pipedream::SuccessStrategy do
  let(:retailer) { create(:retailer) }
  let(:account_id) { 123 }
  let(:external_id) { "#{retailer.id}-abc" }
  let(:params) do
    {
      account: {
        external_id: external_id,
        id: account_id,
        app: { name: 'Shopify' }
      }
    }
  end
  let(:logger) { double('Logger', warn: nil, error: nil) }

  before do
    allow(Rails).to receive(:logger).and_return(logger)

    # Mock Pipedream environment variables
    allow(ENV).to receive(:fetch).and_call_original
    allow(ENV).to receive(:fetch).with('PIPEDREAM_CLIENT_ID', nil).and_return('test_client_id')
    allow(ENV).to receive(:fetch).with('PIPEDREAM_CLIENT_SECRET', nil).and_return('test_client_secret')
    allow(ENV).to receive(:fetch).with('PIPEDREAM_PROJECT_ID', nil).and_return('test_project_id')
  end

  describe '#call' do
    context 'when retailer exists' do
      before do
        allow(Retailer).to receive(:find_by).with(id: retailer.id).and_return(retailer)
      end

      it 'creates a pipedream_integration with correct attributes' do
        integration_double = double('Integration')
        expect(retailer.pipedream_integrations).to receive(:create).with(
          retailer_id: retailer.id,
          platform: 'shopify',
          account_id: account_id,
          project_id: ENV.fetch('PIPEDREAM_PROJECT_ID', nil),
          metadata: {}
        ).and_return(integration_double)

        described_class.new(params: params).call
      end
    end

    context 'when retailer does not exist' do
      before do
        allow(Retailer).to receive(:find_by).with(id: retailer.id).and_return(nil)
      end

      it 'logs a warning and raises error when trying to register webhooks' do
        expect(logger).to receive(:warn).with(/Retailer no encontrado/)
        expect do
          described_class.new(params: params).call
        end.to raise_error(ArgumentError, 'Integration, retailer must be provided')
      end
    end

    context 'when integration creation raises an error' do
      before do
        allow(Retailer).to receive(:find_by).with(id: retailer.id).and_return(retailer)
        allow(retailer.pipedream_integrations).to receive(:create).and_raise(StandardError, 'fail')
      end

      it 'logs error and re-raises' do
        expect(logger).to receive(:error).with(/update retailer error: fail/)
        expect do
          described_class.new(params: params).call
        end.to raise_error(StandardError, 'fail')
      end
    end
  end

  describe '#retailer_id' do
    it 'returns integer id when external_id is valid' do
      strategy = described_class.new(params: params)
      expect(strategy.send(:retailer_id)).to eq(retailer.id)
    end

    it 'returns nil and logs error when external_id is invalid' do
      bad_params = { account: { external_id: 'notanumber-abc' } }
      expect(logger).to receive(:error).with(/retailer_id inválido/)
      strategy = described_class.new(params: bad_params)
      expect(strategy.send(:retailer_id)).to be_nil
    end

    it 'returns nil when external_id is missing' do
      strategy = described_class.new(params: { account: {} })
      expect(strategy.send(:retailer_id)).to be_nil
    end

    it 'returns nil when external_id does not contain "-"' do
      strategy = described_class.new(params: { account: { external_id: '1234' } })
      expect(strategy.send(:retailer_id)).to be_nil
    end
  end

  describe '#account_id' do
    it 'returns account id from params' do
      strategy = described_class.new(params: params)
      expect(strategy.send(:account_id)).to eq(account_id)
    end

    it 'returns nil if account id is missing' do
      strategy = described_class.new(params: { account: {} })
      expect(strategy.send(:account_id)).to be_nil
    end
  end

  describe '#retailer' do
    it 'memoizes the retailer' do
      allow(Retailer).to receive(:find_by).with(id: retailer.id).once.and_return(retailer)
      strategy = described_class.new(params: params)
      2.times { strategy.send(:retailer) }
      expect(Retailer).to have_received(:find_by).once
    end

    it 'returns nil if retailer_id is nil' do
      strategy = described_class.new(params: {})
      expect(strategy.send(:retailer)).to be_nil
    end
  end
end
