# Configuración para desarrollo local con Docker Kafka
# Usar con: source .env.kafka.local

# === KAFKA CONFIGURATION ===
export KAFKA_BOOTSTRAP_SERVERS="localhost:9092"
export KAFKA_CLIENT_ID="mercately_rails6_local"
export KAFKA_SECURITY_PROTOCOL="PLAINTEXT"

# === KAFKA FEATURES ===
export KAFKA_ENABLED="true"
export KAFKA_PRODUCER_ENABLED="true"
export KAFKA_CONSUMER_ENABLED="true"
export USE_KAFKA_SENDER="true"

# === KAFKA PRODUCER SETTINGS ===
export KAFKA_PRODUCER_POOL_SIZE="3"
export KAFKA_PRODUCER_MAX_IDLE_TIME="300"

# === RAILS ENVIRONMENT ===
export RAILS_ENV="development"
export RAILS_LOG_LEVEL="info"

# === WHATSAPP CONFIGURATION ===
# Note: WhatsApp mock was removed. Use real WhatsApp service for all environments.
# For testing, use RSpec mocks/stubs instead.

# === DATABASE (usar la configuración existente) ===
# No modificar las variables de DB existentes

# === REDIS (usar la configuración existente) ===
# No modificar las variables de Redis existentes

echo "✅ Configuración Kafka local cargada:"
echo "   KAFKA_BOOTSTRAP_SERVERS: $KAFKA_BOOTSTRAP_SERVERS"
echo "   KAFKA_CLIENT_ID: $KAFKA_CLIENT_ID"
echo "   KAFKA_PRODUCER_ENABLED: $KAFKA_PRODUCER_ENABLED"
echo "   KAFKA_CONSUMER_ENABLED: $KAFKA_CONSUMER_ENABLED"
