# Docker Environment Configuration for Kafka Campaigns
# This file provides simplified configuration for running Kafka campaigns in Docker

# === KAFKA CONFIGURATION ===
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_CONSUMER_ENABLED=true
KAFKA_PRODUCER_ENABLED=true
USE_KAFKA_SENDER=true
KAFKA_ENABLED=true

# === KAFKA AUTHENTICATION (for Docker - no auth needed) ===
KAFKA_USERNAME=mercately
KAFKA_PASSWORD=mercately-dev
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_SASL_MECHANISM=PLAIN

# === REDIS CONFIGURATION ===
REDIS_URL=redis://localhost:6379

# === RAILS CONFIGURATION ===
RAILS_ENV=development

# === DATABASE CONFIGURATION ===
# Use your existing database configuration
# DATABASE_URL=postgresql://user:password@localhost:5432/mercately_development

# === OPTIONAL: Override default Kafka settings ===
# KAFKA_SESSION_TIMEOUT_MS=300000
# KAFKA_HEARTBEAT_INTERVAL_MS=60000
# KAFKA_MAX_POLL_INTERVAL_MS=600000

# === DEVELOPMENT FLAGS ===
# Enable detailed logging for debugging
KAFKA_DEBUG=true
KARAFKA_DEBUG=true
