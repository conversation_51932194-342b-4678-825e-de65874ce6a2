GEM
  remote: https://gems.contribsys.com/
  specs:
    sidekiq-pro (5.5.8)
      sidekiq (~> 6.0, >= 6.5.6)

GEM
  remote: https://rubygems.org/
  specs:
    ably-rest (1.2.7)
      addressable (>= 2.0.0)
      excon (~> 0.55)
      faraday (~> 2.2)
      faraday-typhoeus (~> 0.2.0)
      json
      msgpack (>= 1.5.2, < 2.0)
      typhoeus (~> 1.4)
    actioncable (********)
      actionpack (= ********)
      activesupport (= ********)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      mail (>= 2.7.1)
    actionmailer (********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      activesupport (= ********)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (********)
      actionview (= ********)
      activesupport (= ********)
      rack (~> 2.0, >= 2.0.9)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (********)
      actionpack (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      nokogiri (>= 1.8.5)
    actionview (********)
      activesupport (= ********)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_model_serializers (0.10.14)
      actionpack (>= 4.1)
      activemodel (>= 4.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    activeadmin (3.2.5)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activeadmin-searchable_select (1.8.0)
      activeadmin (>= 1.x, < 4)
      jquery-rails (>= 3.0, < 5)
      select2-rails (~> 4.0)
    activeadmin_froala_editor (1.1.0)
      activeadmin (>= 2.9.0)
    activejob (********)
      activesupport (= ********)
      globalid (>= 0.3.6)
    activemodel (********)
      activesupport (= ********)
    activemodel-serializers-xml (1.0.3)
      activemodel (>= 5.0.0.a)
      activesupport (>= 5.0.0.a)
      builder (~> 3.1)
    activerecord (********)
      activemodel (= ********)
      activesupport (= ********)
    activerecord-import (1.8.1)
      activerecord (>= 4.2)
    activerecord-precounter (0.4.0)
      activerecord (>= 5)
    activerecord-session_store (2.1.0)
      actionpack (>= 6.1)
      activerecord (>= 6.1)
      cgi (>= 0.3.6)
      multi_json (~> 1.11, >= 1.11.2)
      rack (>= 2.0.8, < 4)
      railties (>= 6.1)
    activeresource (6.1.4)
      activemodel (>= 6.0)
      activemodel-serializers-xml (~> 1.0)
      activesupport (>= 6.0)
    activestorage (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activesupport (= ********)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activestorage-cloudinary-service (0.2.3)
    activesupport (********)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    airbrussh (1.5.3)
      sshkit (>= 1.6.1, != 1.7.0)
    ancestry (4.3.3)
      activerecord (>= 5.2.6)
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    ast (2.4.2)
    async (2.21.0)
      console (~> 1.29)
      fiber-annotation
      io-event (~> 1.6, >= 1.6.5)
    attr_encrypted (4.1.1)
      encryptor (~> 3.0.0)
    autoprefixer-rails (*********)
      execjs (~> 2)
    aws-eventstream (1.3.0)
    aws-partitions (1.1013.0)
    aws-sdk-core (3.214.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.96.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.174.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.10.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bigdecimal (3.1.8)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    bootstrap (*******)
      autoprefixer-rails (>= 9.1.0)
      popper_js (>= 1.16.1, < 2)
    bootstrap-daterangepicker-rails (3.0.4)
      railties (>= 4.0)
    brakeman (6.2.2)
      racc
    bson (5.0.2)
    builder (3.3.0)
    bundler-audit (0.9.2)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    byebug (11.1.3)
    cancancan (3.6.1)
    capistrano (3.19.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.1)
      capistrano (~> 3.1)
    capistrano-rails (1.6.3)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rails-console (2.3.0)
      capistrano (>= 3.5.0, < 4.0.0)
      sshkit-interactive (~> 0.3.0)
    capistrano-rails-logs-tail (1.0.5)
      capistrano (>= 3.4.0, < 4.0.0)
      capistrano-rails
    capistrano-rvm (0.1.2)
      capistrano (~> 3.0)
      sshkit (~> 1.2)
    capistrano-systemd-multiservice (0.1.0.beta13)
      capistrano (~> 3.7)
    case_transform (0.2)
      activesupport
    caxlsx (4.1.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.4)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    cgi (0.4.1)
    chargebee (2.46.0)
      cgi (>= 0.1.0, < 1.0.0)
      json_pure (~> 2.1)
      rest-client (>= 1.8, <= 2.0.2)
    chartkick (5.1.2)
    childprocess (5.1.0)
      logger (~> 1.5)
    ckeditor_rails (4.17.0)
      railties (>= 3.0)
    cloudinary (2.2.0)
      faraday (>= 2.0.1, < 3.0.0)
      faraday-follow_redirects (~> 0.3.0)
      faraday-multipart (~> 1.0, >= 1.0.4)
      ostruct
    cocoon (1.2.15)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    console (1.29.0)
      fiber-annotation
      fiber-local (~> 1.1)
      json
    countries (7.0.0)
      unaccent (~> 0.3)
    country_select (10.0.0)
      countries (> 5.0, < 8.0)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.19.1)
      addressable
    csv (3.3.0)
    currency_select (7.0.0)
      actionview (>= 6.1.0, < 7.3)
      money (~> 6.0)
    date (3.4.0)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_invitable (2.0.9)
      actionmailer (>= 5.0)
      devise (>= 4.6)
    diff-lcs (1.5.1)
    docile (1.4.1)
    domain_name (0.6.20240107)
    dotenv (3.1.4)
    dotenv-rails (3.1.4)
      dotenv (= 3.1.4)
      railties (>= 6.1)
    dox (2.4.0)
      activesupport (>= 4.0)
      rspec-core
    encryptor (3.0.0)
    erubi (1.13.0)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    excon (0.112.0)
    execjs (2.10.0)
    exponent-server-sdk (0.1.0)
      typhoeus
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    faraday-typhoeus (0.2.1)
      faraday (~> 2.0)
      typhoeus (~> 1.4)
    fcm (2.0.1)
      faraday (>= 1.0.0, < 3.0)
      googleauth (~> 1)
    fernet (2.3)
      valcro (~> 0.1)
    ffi (1.17.0)
    ffi (1.17.0-aarch64-linux-gnu)
    ffi (1.17.0-aarch64-linux-musl)
    ffi (1.17.0-arm-linux-gnu)
    ffi (1.17.0-arm-linux-musl)
    ffi (1.17.0-arm64-darwin)
    ffi (1.17.0-x86-linux-gnu)
    ffi (1.17.0-x86-linux-musl)
    ffi (1.17.0-x86_64-darwin)
    ffi (1.17.0-x86_64-linux-gnu)
    ffi (1.17.0-x86_64-linux-musl)
    ffprobe (0.1.0)
      hashie
    fiber-annotation (0.2.0)
    fiber-local (1.1.0)
      fiber-storage
    fiber-storage (1.0.0)
    font_awesome5_rails (1.5.0)
      nokogiri (>= 1.11.3)
      railties (>= 4.2)
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    gibbon (3.5.0)
      faraday (>= 1.0)
      multi_json (>= 1.11.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-cloud-env (2.2.1)
      faraday (>= 1.0, < 3.a)
    googleauth (1.11.2)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    groupdate (6.4.0)
      activesupport (>= 6.1)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hash_diff (1.1.1)
    hashdiff (1.1.2)
    hashery (2.1.2)
    hashie (5.0.0)
    htmlentities (4.3.4)
    http-cookie (1.0.7)
      domain_name (~> 0.5)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    hubspot-ruby (0.9.0)
      activesupport (>= 3.0.0)
      httparty (>= 0.10.0)
    humanize (3.0.0)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    imagen (0.2.0)
      parser (>= 2.5, != 2.5.1.1)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    interactor (3.1.2)
    interactor-rails (2.2.1)
      interactor (~> 3.0)
      rails (>= 4.2)
    io-event (1.7.4)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.7.1)
    json_pure (2.6.3)
    jsonapi-renderer (0.2.2)
    jsonapi-serializer (2.2.0)
      activesupport (>= 4.2)
    jwt (2.9.3)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    karafka (2.5.0)
      base64 (~> 0.2)
      karafka-core (>= 2.5.2, < 2.6.0)
      karafka-rdkafka (>= 0.19.5)
      waterdrop (>= 2.8.3, < 3.0.0)
      zeitwerk (~> 2.3)
    karafka-core (2.5.2)
      karafka-rdkafka (>= 0.19.2, < 0.21.0)
      logger (>= 1.6.0)
    karafka-rdkafka (0.19.5)
      ffi (~> 1.15)
      mini_portile2 (~> 2.6)
      rake (> 12)
    language_server-protocol (********)
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.23.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    mailgun-ruby (1.2.15)
      rest-client (>= 2.0.2)
    marcel (1.0.4)
    memory_profiler (1.1.0)
    method_source (1.1.0)
    mime-types (3.6.0)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.1105)
    mini_magick (5.0.1)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.2)
    momentjs-rails (********)
      railties (>= 3.1)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    mongo (2.21.0)
      bson (>= 4.14.1, < 6.0.0)
    mongoid (9.0.3)
      activemodel (>= 5.1, < 8.1, != 7.0.0)
      concurrent-ruby (>= 1.0.5, < 2.0)
      mongo (>= 2.18.0, < 3.0.0)
    mongoid-compatibility (1.0.0)
      activesupport
      mongoid (>= 2.0)
    mongoid-rspec (4.2.0)
      mongoid (>= 3.0, < 10.0)
      mongoid-compatibility (>= 0.5.1)
    mongoid_enumerable (0.4.4)
      mongoid (>= 4.0)
    msgpack (1.7.5)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    net-http (0.6.0)
      uri
    net-imap (0.5.1)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.3.0)
    netrc (0.11.0)
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-musl)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oj (3.16.7)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    omniauth (2.1.2)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-facebook (10.0.0)
      bigdecimal
      omniauth-oauth2 (>= 1.2, < 3)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    openssl (3.2.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    pagy (9.3.1)
    parallel (1.26.3)
    parallel_tests (4.7.2)
      parallel
    parser (*******)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pg_partition_manager (0.1.0)
      pg (~> 1.0)
    phonelib (0.6.43)
    phony (2.20.15)
    popper_js (1.16.1)
    premailer (1.27.0)
      addressable
      css_parser (>= 1.19.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.12.0)
      actionmailer (>= 3)
      net-smtp
      premailer (~> 1.7, >= 1.7.9)
    prismic.io (1.8.2)
      hashery (~> 2.1.1)
    prometheus-client (4.2.3)
      base64
    public_suffix (6.0.1)
    puma (6.5.0)
      nio4r (~> 2.0)
    puma-metrics (1.4.0)
      prometheus-client (>= 0.10)
      puma (>= 6.0)
    racc (1.8.1)
    rack (2.2.10)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-mini-profiler (3.3.1)
      rack (>= 1.2.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-proxy (0.7.7)
      rack
    rack-reverse-proxy (0.12.0)
      rack (>= 1.0.0)
      rack-proxy (~> 0.6, >= 0.6.1)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (********)
      actioncable (= ********)
      actionmailbox (= ********)
      actionmailer (= ********)
      actionpack (= ********)
      actiontext (= ********)
      actionview (= ********)
      activejob (= ********)
      activemodel (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      bundler (>= 1.15.0)
      railties (= ********)
      sprockets-rails (>= 2.0.0)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (********)
      actionpack (= ********)
      activesupport (= ********)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (4.2.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rb-readline (0.5.5)
    rdkafka (0.19.0)
      ffi (~> 1.15)
      mini_portile2 (~> 2.6)
      rake (> 12)
    recaptcha (5.17.0)
    redirect_safely (1.0.0)
      activemodel
    redis (4.8.1)
    regexp_parser (2.9.2)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.0.2)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.3.9)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.2)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.5)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-sqlimit (0.0.6)
      activerecord (>= 4.2.0, < 8)
      rspec (~> 3.0)
    rspec-support (3.13.1)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rubocop (1.69.0)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.4, < 3.0)
      rubocop-ast (>= 1.36.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.36.1)
      parser (>= 3.3.1.0)
    rubocop-factory_bot (2.26.1)
      rubocop (~> 1.61)
    rubocop-performance (1.23.0)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.27.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.52.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rspec (3.2.0)
      rubocop (~> 1.61)
    rubocop-rspec_rails (2.30.0)
      rubocop (~> 1.61)
      rubocop-rspec (~> 3, >= 3.0.1)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    rugged (1.7.2)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    securerandom (0.4.0)
    select2-rails (4.0.13)
    semantic_range (3.1.0)
    shopify_api (14.7.0)
      activesupport
      concurrent-ruby
      hash_diff
      httparty
      jwt
      oj
      openssl
      securerandom
      sorbet-runtime
      zeitwerk (~> 2.5)
    shopify_app (22.5.1)
      activeresource
      addressable (~> 2.7)
      jwt (>= 2.2.3)
      rails (> 5.2.1)
      redirect_safely (~> 1.0)
      shopify_api (>= 14.7.0, < 15.0)
      sprockets-rails (>= 2.0.0)
    shoulda-matchers (6.4.0)
      activesupport (>= 5.2.0)
    sidekiq (6.5.12)
      connection_pool (>= 2.2.5, < 3)
      rack (~> 2.0)
      redis (>= 4.5.0, < 5)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov-lcov (0.8.0)
    simplecov_json_formatter (0.1.4)
    slack-notifier (2.4.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    socket.io-rails (2.3.0)
      railties (>= 3.1)
    sorbet-runtime (0.5.11690)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.23.2)
      base64
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    sshkit-interactive (0.3.0)
      sshkit (~> 1.12)
    stackprof (0.2.26)
    streamio-ffmpeg (3.0.2)
      multi_json (~> 1.8)
    stripe (13.2.0)
    tailwindcss-rails (2.5.0)
      railties (>= 6.0.0)
    tailwindcss-rails (2.5.0-aarch64-linux)
      railties (>= 6.0.0)
    tailwindcss-rails (2.5.0-arm-linux)
      railties (>= 6.0.0)
    tailwindcss-rails (2.5.0-arm64-darwin)
      railties (>= 6.0.0)
    tailwindcss-rails (2.5.0-x86_64-darwin)
      railties (>= 6.0.0)
    tailwindcss-rails (2.5.0-x86_64-linux)
      railties (>= 6.0.0)
    thor (1.3.2)
    tilt (2.4.0)
    timecop (0.9.10)
    timeout (0.4.2)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uglifier (4.2.1)
      execjs (>= 0.3.0, < 3)
    unaccent (0.4.0)
    undercover (0.5.0)
      bigdecimal
      imagen (>= 0.1.8)
      rainbow (>= 2.1, < 4.0)
      rugged (>= 0.27, < 1.8)
    unicode-display_width (3.1.2)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    valcro (0.2.1)
    vcr (6.3.1)
      base64
    version_gem (1.1.4)
    vpim (24.2.20)
    warden (1.2.9)
      rack (>= 2.0.9)
    waterdrop (2.8.5)
      karafka-core (>= 2.4.9, < 3.0.0)
      karafka-rdkafka (>= 0.19.2)
      zeitwerk (~> 2.3)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.24.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webpacker (6.0.0.rc.6)
      activesupport (>= 5.2)
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    webrick (1.9.0)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    with_advisory_lock (5.1.0)
      activerecord (>= 6.1)
      zeitwerk (>= 2.6)
    xlsxtream (2.4.0)
      zip_tricks (>= 4.5, < 6)
    zeitwerk (2.7.1)
    zip_tricks (5.6.0)

GEM
  remote: https://rubygems.pkg.github.com/ThoughtCode/
  specs:
    mercately-kafka (0.1.4)
      rdkafka (~> 0.19.0)
    mercately-karafka (0.1.12)
      activesupport (~> 6.0)
      karafka (>= 2.4.17, < 3.0)
    mercately_churnzero_api (3.4.0)
      faraday (~> 2.12)
      jwt (= 2.9.3)
    mercately_churnzero_extension (3.12.1)
      mercately_churnzero_api (= 3.4.0)
    mercately_mia_api (1.19.0)
      base64 (~> 0.2)
      faraday (~> 2.12)
      faraday-multipart (~> 1.1)
      fernet (~> 2.3)
      nokogiri (~> 1.18)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin
  ruby
  x86-linux
  x86-linux-gnu
  x86-linux-musl
  x86_64-darwin
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  ably-rest (= 1.2.7)
  active_model_serializers (= 0.10.14)
  activeadmin (= 3.2.5)
  activeadmin-searchable_select (= 1.8.0)
  activeadmin_froala_editor (= 1.1.0)
  activerecord-import (= 1.8.1)
  activerecord-precounter (= 0.4.0)
  activerecord-session_store (= 2.1.0)
  activestorage-cloudinary-service (= 0.2.3)
  ancestry (= 4.3.3)
  async
  attr_encrypted (= 4.1.1)
  aws-sdk-s3 (= 1.174.0)
  bootsnap
  bootstrap (~> *******)
  bootstrap-daterangepicker-rails (= 3.0.4)
  brakeman (= 6.2.2)
  bundler-audit (= 0.9.2)
  byebug
  cancancan (= 3.6.1)
  capistrano
  capistrano-bundler
  capistrano-rails
  capistrano-rails-console
  capistrano-rails-logs-tail
  capistrano-rvm
  capistrano-systemd-multiservice
  caxlsx (= 4.1.0)
  caxlsx_rails (= 0.6.4)
  chargebee (= 2.46.0)
  chartkick (= 5.1.2)
  ckeditor_rails (= 4.17.0)
  cloudinary (= 2.2.0)
  cocoon (= 1.2.15)
  coffee-rails (= 5.0.0)
  coffee-script (= 2.4.1)
  country_select (= 10.0.0)
  csv (~> 3.3.0)
  currency_select (= 7.0.0)
  devise (= 4.9.4)
  devise_invitable (= 2.0.9)
  dotenv-rails
  dox (= 2.4.0)
  exponent-server-sdk (= 0.1.0)
  factory_bot_rails (= 6.4.4)
  faker (= 3.5.1)
  faraday (= 2.12.2)
  faraday-multipart (= 1.1.0)
  fcm (= 2.0.1)
  fernet (= 2.3)
  ffprobe (= 0.1.0)
  font_awesome5_rails (= 1.5.0)
  geocoder (~> 1.8)
  gibbon (= 3.5.0)
  globalid (= 1.2.1)
  groupdate (= 6.4.0)
  hubspot-ruby (= 0.9.0)
  humanize (~> 3.0.0)
  interactor-rails (= 2.2.1)
  jquery-rails (= 4.6.0)
  json (= 2.7.1)
  json_pure (= 2.6.3)
  jsonapi-serializer (= 2.2.0)
  jwt (= 2.9.3)
  letter_opener (= 1.10.0)
  listen (= 3.9.0)
  mailgun-ruby (= 1.2.15)
  memory_profiler (= 1.1.0)
  mercately-kafka (= 0.1.4)!
  mercately-karafka (= 0.1.12)!
  mercately_churnzero_extension (= 3.12.1)!
  mercately_mia_api (= 1.19.0)!
  mime-types (= 3.6.0)
  mini_magick (= 5.0.1)
  momentjs-rails (= ********)
  mongoid (= 9.0.3)
  mongoid-rspec (= 4.2.0)
  mongoid_enumerable (~> 0.4.4)
  net-scp (= 4.0.0)
  net-ssh (= 7.3.0)
  oauth2 (= 2.0.9)
  omniauth (= 2.1.2)
  omniauth-facebook (= 10.0.0)
  omniauth-oauth2 (= 1.8.0)
  omniauth-rails_csrf_protection (= 1.0.2)
  pagy (= 9.3.1)
  parallel_tests (= 4.7.2)
  pg (= 1.5.9)
  pg_partition_manager (= 0.1.0)
  phonelib (= 0.6.43)
  phony (= 2.20.15)
  premailer-rails (= 1.12.0)
  prismic.io (= 1.8.2)
  prometheus-client (= 4.2.3)
  puma (= 6.5.0)
  puma-metrics (= 1.4.0)
  rack-attack (= 6.7.0)
  rack-cors (= 2.0.2)
  rack-mini-profiler (= 3.3.1)
  rack-reverse-proxy (= 0.12.0)
  rails (~> *******)
  rails-controller-testing (= 1.0.5)
  rb-readline
  recaptcha (= 5.17.0)
  roo (= 2.10.1)
  rspec-rails (= 6.1.5)
  rspec-sqlimit
  rspec_junit_formatter (= 0.6.0)
  rubocop (= 1.69.0)
  rubocop-factory_bot (= 2.26.1)
  rubocop-performance (= 1.23.0)
  rubocop-rails (= 2.27.0)
  rubocop-rspec (= 3.2.0)
  rubocop-rspec_rails (= 2.30.0)
  rubyzip (= 2.3.2)
  sass-rails
  select2-rails (= 4.0.13)
  shopify_api (~> 14.7)
  shopify_app (~> 22.5)
  shoulda-matchers (= 6.4.0)
  sidekiq (< 7)
  sidekiq-pro (< 7)!
  simplecov (= 0.22.0)
  simplecov-lcov (= 0.8.0)
  slack-notifier (= 2.4.0)
  socket.io-rails (= 2.3.0)
  stackprof (= 0.2.26)
  streamio-ffmpeg (= 3.0.2)
  stripe (= 13.2.0)
  tailwindcss-rails (~> 2.5)
  timecop (= 0.9.10)
  tzinfo-data
  uglifier (>= 1.3.0)
  unaccent (= 0.4.0)
  undercover (= 0.5.0)
  vcr (= 6.3.1)
  vpim (= 24.2.20)
  web-console (= 4.2.1)
  webmock (= 3.24.0)
  webpacker (= 6.0.0.rc.6)
  webrick (= 1.9.0)
  with_advisory_lock (= 5.1.0)
  xlsxtream (= 2.4.0)

RUBY VERSION
   ruby 3.2.5p208

BUNDLED WITH
   2.5.23
