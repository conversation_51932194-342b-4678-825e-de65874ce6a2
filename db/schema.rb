# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_07_29_125028) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "pgcrypto"
  enable_extension "plpgsql"
  enable_extension "unaccent"
  enable_extension "uuid-ossp"

  create_table "academy_categories", force: :cascade do |t|
    t.string "name"
    t.string "module_name"
    t.text "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["module_name"], name: "index_academy_categories_on_module_name"
  end

  create_table "academy_videos", force: :cascade do |t|
    t.bigint "academy_category_id", null: false
    t.string "title", null: false
    t.text "description"
    t.integer "position", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "external_url"
    t.index ["academy_category_id"], name: "index_academy_videos_on_academy_category_id"
  end

  create_table "acquired_addons", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "payment_plan_id", null: false
    t.uuid "addon_id", null: false
    t.integer "quantity"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["addon_id"], name: "index_acquired_addons_on_addon_id"
    t.index ["payment_plan_id"], name: "index_acquired_addons_on_payment_plan_id"
  end

  create_table "action_tags", force: :cascade do |t|
    t.bigint "chat_bot_action_id"
    t.bigint "tag_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chat_bot_action_id"], name: "index_action_tags_on_chat_bot_action_id"
    t.index ["tag_id"], name: "index_action_tags_on_tag_id"
  end

  create_table "active_admin_comments", force: :cascade do |t|
    t.string "namespace"
    t.text "body"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "author_type"
    t.bigint "author_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["author_type", "author_id"], name: "index_active_admin_comments_on_author"
    t.index ["namespace"], name: "index_active_admin_comments_on_namespace"
    t.index ["resource_type", "resource_id"], name: "index_active_admin_comments_on_resource"
  end

  create_table "active_storage_attachments", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('active_storage_attachments_id_seq'::regclass)" }
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "old_blob_id"
    t.datetime "created_at", null: false
    t.boolean "is_aws", default: true
    t.uuid "blob_id", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["old_blob_id"], name: "index_active_storage_attachments_on_old_blob_id"
    t.index ["record_id"], name: "index_active_storage_attachments_on_record_id"
    t.index ["record_type", "record_id", "name", "old_blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('active_storage_blobs_id_seq'::regclass)" }
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.bigint "byte_size", null: false
    t.string "checksum", null: false
    t.datetime "created_at", null: false
    t.string "service_name"
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "old_id", default: -> { "gen_random_uuid()" }
    t.bigint "old_blob_id"
    t.string "variation_digest", null: false
    t.uuid "blob_id", null: false
    t.index ["blob_id"], name: "index_active_storage_variant_records_on_blob_id"
    t.index ["old_blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "additional_bot_answers", force: :cascade do |t|
    t.bigint "chat_bot_option_id"
    t.string "text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chat_bot_option_id"], name: "index_additional_bot_answers_on_chat_bot_option_id"
  end

  create_table "additional_fast_answers", force: :cascade do |t|
    t.bigint "template_id"
    t.text "answer"
    t.string "file_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "kind"
    t.string "file_deleted"
    t.index ["template_id"], name: "index_additional_fast_answers_on_template_id"
  end

  create_table "addons", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "identifier"
    t.integer "status", default: 0
    t.index ["name"], name: "index_addons_on_name", unique: true
  end

  create_table "admin_users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_admin_users_on_reset_password_token", unique: true
  end

  create_table "agent_assignments", force: :cascade do |t|
    t.bigint "retailer_user_id"
    t.bigint "customer_id"
    t.bigint "team_assignment_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id"], name: "index_agent_assignments_on_customer_id"
    t.index ["retailer_user_id"], name: "index_agent_assignments_on_retailer_user_id"
    t.index ["team_assignment_id"], name: "index_agent_assignments_on_team_assignment_id"
  end

  create_table "agent_customers", force: :cascade do |t|
    t.bigint "retailer_user_id", null: false
    t.bigint "customer_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "team_assignment_id"
    t.index ["customer_id"], name: "index_agent_customers_on_customer_id"
    t.index ["retailer_user_id", "customer_id"], name: "index_agent_customers_on_retailer_user_id_and_customer_id", unique: true, where: "(team_assignment_id IS NOT NULL)"
    t.index ["retailer_user_id"], name: "index_agent_customers_on_retailer_user_id"
    t.index ["team_assignment_id"], name: "index_agent_customers_on_team_assignment_id"
  end

  create_table "agent_hubspot_owners", force: :cascade do |t|
    t.bigint "retailer_user_id"
    t.bigint "hubspot_owner_id"
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["hubspot_owner_id"], name: "index_agent_hubspot_owners_on_hubspot_owner_id"
    t.index ["retailer_id"], name: "index_agent_hubspot_owners_on_retailer_id"
    t.index ["retailer_user_id", "hubspot_owner_id", "retailer_id"], name: "aho_retailer_user_hubspot_owner_retailer", unique: true
    t.index ["retailer_user_id"], name: "index_agent_hubspot_owners_on_retailer_user_id"
  end

  create_table "agent_notifications", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.bigint "retailer_user_id", null: false
    t.string "notification_type"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "customer_fb_post_id"
    t.index ["customer_fb_post_id"], name: "index_agent_notifications_on_customer_fb_post_id"
    t.index ["customer_id"], name: "index_agent_notifications_on_customer_id"
    t.index ["retailer_user_id"], name: "index_agent_notifications_on_retailer_user_id"
  end

  create_table "agent_team_changelogs", force: :cascade do |t|
    t.bigint "agent_team_id"
    t.bigint "retailer_user_id"
    t.bigint "team_assignment_id"
    t.boolean "active"
    t.string "action"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["agent_team_id"], name: "index_agent_team_changelogs_on_agent_team_id"
    t.index ["retailer_user_id"], name: "index_agent_team_changelogs_on_retailer_user_id"
    t.index ["team_assignment_id"], name: "index_agent_team_changelogs_on_team_assignment_id"
  end

  create_table "agent_teams", force: :cascade do |t|
    t.bigint "team_assignment_id"
    t.bigint "retailer_user_id"
    t.integer "max_assignments", default: 0
    t.integer "assigned_amount", default: 0
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_user_id"], name: "index_agent_teams_on_retailer_user_id"
    t.index ["team_assignment_id"], name: "index_agent_teams_on_team_assignment_id"
  end

  create_table "agent_watched_videos", force: :cascade do |t|
    t.bigint "retailer_user_id"
    t.bigint "mercately_video_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["mercately_video_id"], name: "index_agent_watched_videos_on_mercately_video_id"
    t.index ["retailer_user_id"], name: "index_agent_watched_videos_on_retailer_user_id"
  end

  create_table "ahoy_events", force: :cascade do |t|
    t.bigint "visit_id"
    t.bigint "user_id"
    t.string "name"
    t.jsonb "properties"
    t.datetime "time"
    t.index ["name", "time"], name: "index_ahoy_events_on_name_and_time"
    t.index ["properties"], name: "index_ahoy_events_on_properties", opclass: :jsonb_path_ops, using: :gin
    t.index ["user_id"], name: "index_ahoy_events_on_user_id"
    t.index ["visit_id"], name: "index_ahoy_events_on_visit_id"
  end

  create_table "ahoy_visits", force: :cascade do |t|
    t.string "visit_token"
    t.string "visitor_token"
    t.bigint "user_id"
    t.string "ip"
    t.text "user_agent"
    t.text "referrer"
    t.string "referring_domain"
    t.text "landing_page"
    t.string "browser"
    t.string "os"
    t.string "device_type"
    t.string "country"
    t.string "region"
    t.string "city"
    t.float "latitude"
    t.float "longitude"
    t.string "utm_source"
    t.string "utm_medium"
    t.string "utm_term"
    t.string "utm_content"
    t.string "utm_campaign"
    t.string "app_version"
    t.string "os_version"
    t.string "platform"
    t.datetime "started_at"
    t.index ["user_id"], name: "index_ahoy_visits_on_user_id"
    t.index ["visit_token"], name: "index_ahoy_visits_on_visit_token", unique: true
  end

  create_table "automatic_answer_days", force: :cascade do |t|
    t.bigint "automatic_answer_id"
    t.integer "day", null: false
    t.boolean "all_day", default: false
    t.integer "start_time"
    t.integer "end_time"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automatic_answer_id"], name: "index_automatic_answer_days_on_automatic_answer_id"
  end

  create_table "automatic_answers", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "message"
    t.integer "message_type"
    t.integer "interval"
    t.integer "status", default: 1
    t.integer "platform"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "whatsapp", default: false
    t.boolean "messenger", default: false
    t.boolean "instagram", default: false
    t.boolean "always_active", default: true
    t.index ["retailer_id"], name: "index_automatic_answers_on_retailer_id"
  end

  create_table "automation_actions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('automation_actions_id_seq'::regclass)" }
    t.integer "action_type"
    t.jsonb "tags", default: []
    t.bigint "automation_id"
    t.bigint "retailer_user_id"
    t.bigint "team_assignment_id"
    t.bigint "old_funnel_step_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "automatizable_type"
    t.bigint "automatizable_id"
    t.uuid "automatizable_uuid"
    t.uuid "funnel_step_id"
    t.uuid "automation_uuid"
    t.index ["automation_id"], name: "index_automation_actions_on_automation_id"
    t.index ["automation_uuid"], name: "index_automation_actions_on_automation_uuid"
    t.index ["automatizable_type", "automatizable_id"], name: "index_automation_actions_on_automatizable_actions"
    t.index ["automatizable_uuid"], name: "index_automation_actions_on_automatizable_uuid"
    t.index ["funnel_step_id"], name: "index_automation_actions_on_funnel_step_id"
    t.index ["id"], name: "index_automation_actions_on_id", unique: true
    t.index ["old_funnel_step_id"], name: "index_automation_actions_on_old_funnel_step_id"
    t.index ["retailer_user_id"], name: "index_automation_actions_on_retailer_user_id"
    t.index ["team_assignment_id"], name: "index_automation_actions_on_team_assignment_id"
  end

  create_table "automations", force: :cascade do |t|
    t.string "web_id"
    t.string "name"
    t.text "message"
    t.integer "waiting_time_to_send"
    t.integer "kind"
    t.integer "time_unit"
    t.integer "time_value"
    t.boolean "active", default: true
    t.boolean "deleted", default: false
    t.integer "amount_sent", default: 0
    t.bigint "retailer_id"
    t.bigint "retailer_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "triggers", default: [], array: true
    t.boolean "executable", default: true
    t.integer "old_funnel_id"
    t.uuid "uuid", default: -> { "gen_random_uuid()" }, null: false
    t.uuid "funnel_id"
    t.index ["funnel_id"], name: "index_automations_on_funnel_id"
    t.index ["retailer_id"], name: "index_automations_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_automations_on_retailer_user_id"
    t.index ["uuid"], name: "index_automations_on_uuid", unique: true
    t.index ["web_id"], name: "index_automations_on_web_id"
  end

  create_table "business_rules", force: :cascade do |t|
    t.bigint "rule_category_id"
    t.string "name"
    t.text "description"
    t.string "identifier"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "has_additional_data", default: false
    t.integer "additional_data_type"
    t.jsonb "json_options", default: {}
    t.index ["rule_category_id"], name: "index_business_rules_on_rule_category_id"
  end

  create_table "calendar_events", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "title"
    t.datetime "starts_at"
    t.datetime "ends_at"
    t.datetime "remember_at"
    t.bigint "retailer_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "web_id"
    t.integer "remember"
    t.string "timezone"
    t.index ["retailer_id"], name: "index_calendar_events_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_calendar_events_on_retailer_user_id"
  end

  create_table "campaign_chunks", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.datetime "send_at", null: false
    t.integer "contacts", null: false
    t.integer "status", default: 0
    t.index ["campaign_id"], name: "index_campaign_chunks_on_campaign_id"
  end

  create_table "campaigns", force: :cascade do |t|
    t.string "name"
    t.text "template_text"
    t.integer "status", default: 0
    t.datetime "send_at"
    t.jsonb "content_params"
    t.bigint "whatsapp_template_id"
    t.bigint "contact_group_id"
    t.bigint "retailer_id"
    t.string "web_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "cost", default: 0.0
    t.string "reason"
    t.integer "chunks", default: 0
    t.integer "current_chunk", default: 0
    t.boolean "in_chunks"
    t.datetime "send_chunk_at"
    t.float "estimated_cost"
    t.integer "customers_scope"
    t.boolean "mark_chats_as_resolved", default: true, null: false
    t.string "sender_strategy", default: "synchronous", null: false, comment: "Estrategia utilizada para enviar la campaña: synchronous, kafka, batch, api_direct, scheduled"
    t.index ["contact_group_id"], name: "index_campaigns_on_contact_group_id"
    t.index ["retailer_id"], name: "index_campaigns_on_retailer_id"
    t.index ["sender_strategy"], name: "index_campaigns_on_sender_strategy"
    t.index ["web_id"], name: "index_campaigns_on_web_id"
    t.index ["whatsapp_template_id"], name: "index_campaigns_on_whatsapp_template_id"
  end

  create_table "categories", force: :cascade do |t|
    t.string "name"
    t.string "meli_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ancestry"
    t.jsonb "template", default: []
    t.integer "status", default: 0
    t.index ["ancestry"], name: "index_categories_on_ancestry"
    t.index ["meli_id"], name: "index_categories_on_meli_id", unique: true, where: "(meli_id IS NOT NULL)"
  end

  create_table "chat_bot_actions", force: :cascade do |t|
    t.bigint "chat_bot_option_id"
    t.bigint "retailer_user_id"
    t.integer "action_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "target_field"
    t.string "webhook"
    t.integer "action_event", default: 0
    t.string "username"
    t.string "password"
    t.integer "payload_type", default: 0
    t.jsonb "data", default: []
    t.jsonb "headers", default: []
    t.integer "classification", default: 0
    t.string "exit_message"
    t.bigint "customer_related_field_id"
    t.bigint "jump_option_id"
    t.bigint "team_assignment_id"
    t.integer "goal_index"
    t.string "tracker"
    t.string "tracker_value"
    t.bigint "old_funnel_step_id"
    t.uuid "funnel_step_id"
    t.uuid "funnel_id"
    t.index ["chat_bot_option_id"], name: "index_chat_bot_actions_on_chat_bot_option_id"
    t.index ["customer_related_field_id"], name: "index_chat_bot_actions_on_customer_related_field_id"
    t.index ["funnel_step_id"], name: "index_chat_bot_actions_on_funnel_step_id"
    t.index ["jump_option_id"], name: "index_chat_bot_actions_on_jump_option_id"
    t.index ["old_funnel_step_id"], name: "index_chat_bot_actions_on_old_funnel_step_id"
    t.index ["retailer_user_id"], name: "index_chat_bot_actions_on_retailer_user_id"
    t.index ["team_assignment_id"], name: "index_chat_bot_actions_on_team_assignment_id"
  end

  create_table "chat_bot_custom_messages", force: :cascade do |t|
    t.bigint "chat_bot_id", null: false
    t.text "body"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["chat_bot_id"], name: "index_chat_bot_custom_messages_on_chat_bot_id"
  end

  create_table "chat_bot_customers", force: :cascade do |t|
    t.bigint "customer_id"
    t.bigint "chat_bot_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "platform"
    t.index ["chat_bot_id"], name: "index_chat_bot_customers_on_chat_bot_id"
    t.index ["customer_id"], name: "index_chat_bot_customers_on_customer_id"
  end

  create_table "chat_bot_option_apis", force: :cascade do |t|
    t.bigint "chat_bot_option_id", null: false
    t.string "api_url", null: false
    t.string "method", null: false
    t.jsonb "headers", default: [], null: false
    t.jsonb "body", default: [], null: false
    t.string "variables", default: [], array: true
    t.integer "body_type", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["chat_bot_option_id"], name: "index_chat_bot_option_apis_on_chat_bot_option_id"
  end

  create_table "chat_bot_option_forms", force: :cascade do |t|
    t.bigint "chat_bot_option_id", null: false
    t.string "data_source", null: false
    t.string "value_source", null: false
    t.string "title_source", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["chat_bot_option_id"], name: "index_chat_bot_option_forms_on_chat_bot_option_id"
  end

  create_table "chat_bot_options", force: :cascade do |t|
    t.bigint "chat_bot_id"
    t.string "text"
    t.string "ancestry"
    t.integer "position"
    t.string "answer"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "option_deleted", default: false
    t.integer "option_type", default: 0
    t.boolean "skip_option", default: false
    t.boolean "go_past_option", default: false
    t.boolean "go_start_option", default: false
    t.integer "interactive", default: 0
    t.string "list_title"
    t.string "button_title"
    t.string "selection_title"
    t.boolean "allow_media", default: false
    t.string "node_id"
    t.string "source"
    t.string "target"
    t.boolean "fake_option", default: false
    t.integer "message_type"
    t.integer "answer_type", default: 0
    t.boolean "fail_path_active", default: false
    t.integer "max_attempts"
    t.string "failure_text"
    t.boolean "use_api", default: false
    t.index ["ancestry"], name: "index_chat_bot_options_on_ancestry"
    t.index ["chat_bot_id"], name: "index_chat_bot_options_on_chat_bot_id"
  end

  create_table "chat_bot_waiting_times", force: :cascade do |t|
    t.bigint "chat_bot_id"
    t.integer "waiting_time", null: false
    t.integer "waiting_time_type", null: false
    t.integer "position", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["chat_bot_id"], name: "index_chat_bot_waiting_times_on_chat_bot_id"
  end

  create_table "chat_bots", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "name"
    t.string "trigger"
    t.integer "failed_attempts"
    t.string "goodbye_message"
    t.boolean "any_interaction", default: false
    t.string "web_id"
    t.boolean "enabled", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "error_message"
    t.boolean "repeat_menu_on_failure", default: false
    t.float "reactivate_after"
    t.integer "on_failed_attempt"
    t.string "on_failed_attempt_message"
    t.integer "platform"
    t.boolean "instagram_enabled", default: false
    t.boolean "messenger_enabled", default: false
    t.boolean "whatsapp_enabled", default: false
    t.boolean "enabled_goodbye_message", default: false
    t.boolean "disabled_chatbot_failed_attempts", default: false
    t.boolean "only_new_customers", default: true
    t.boolean "archived", default: false
    t.boolean "all_days", default: false
    t.jsonb "triggers", default: []
    t.integer "version", default: 2
    t.integer "kind", default: 0
    t.boolean "outbound_enabled", default: false
    t.float "response_waiting_time_h", default: 0.0
    t.integer "reminder_message"
    t.boolean "auto_resolve", default: false
    t.index ["archived"], name: "index_chat_bots_on_archived"
    t.index ["retailer_id"], name: "index_chat_bots_on_retailer_id"
  end

  create_table "chat_histories", force: :cascade do |t|
    t.bigint "customer_id"
    t.bigint "retailer_user_id"
    t.integer "action"
    t.integer "chat_status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id", "action", "chat_status"], name: "index_chat_histories_on_customer_id_and_action_and_chat_status", unique: true, where: "((action = 0) AND (chat_status = ANY (ARRAY[0, 1])))"
    t.index ["customer_id"], name: "index_chat_histories_on_customer_id"
    t.index ["retailer_user_id"], name: "index_chat_histories_on_retailer_user_id"
  end

  create_table "chatbot_days", force: :cascade do |t|
    t.bigint "chat_bot_id"
    t.integer "start_day"
    t.integer "end_day"
    t.integer "scheduled_start_day"
    t.integer "scheduled_end_day"
    t.integer "start_time"
    t.integer "end_time"
    t.string "scheduled_start_time"
    t.string "scheduled_end_time"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chat_bot_id"], name: "index_chatbot_days_on_chat_bot_id"
  end

  create_table "commission_invoices", force: :cascade do |t|
    t.decimal "total_amount"
    t.integer "payment_status", default: 0
    t.integer "active_retailers"
    t.datetime "period_start"
    t.datetime "period_end"
    t.integer "partner_commissions_count", default: 0
    t.bigint "partner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["partner_id"], name: "index_commission_invoices_on_partner_id"
  end

  create_table "contact_group_customers", force: :cascade do |t|
    t.bigint "contact_group_id"
    t.bigint "customer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["contact_group_id"], name: "index_contact_group_customers_on_contact_group_id"
    t.index ["customer_id"], name: "index_contact_group_customers_on_customer_id"
  end

  create_table "contact_groups", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "name"
    t.string "web_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "archived", default: false
    t.boolean "imported", default: false
    t.bigint "retailer_user_id"
    t.integer "contact_group_customers_count", default: 0, null: false
    t.index ["retailer_id"], name: "index_contact_groups_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_contact_groups_on_retailer_user_id"
    t.index ["web_id"], name: "index_contact_groups_on_web_id"
  end

  create_table "conversation_subtopics", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "conversation_topic_id", null: false
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["conversation_topic_id"], name: "index_conversation_subtopics_on_conversation_topic_id"
  end

  create_table "conversation_topics", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name"
    t.bigint "retailer_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id"], name: "index_conversation_topics_on_retailer_id"
  end

  create_table "country_conversations", force: :cascade do |t|
    t.bigint "retailer_whatsapp_conversation_id"
    t.string "country_code"
    t.integer "total_uic", default: 0
    t.integer "total_bic", default: 0
    t.float "total_cost_uic", default: 0.0
    t.float "total_cost_bic", default: 0.0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "total_marketing", default: 0
    t.float "total_cost_marketing", default: 0.0
    t.integer "total_authentication", default: 0
    t.float "total_cost_authentication", default: 0.0
    t.integer "total_utility", default: 0
    t.float "total_cost_utility", default: 0.0
    t.integer "total_service", default: 0
    t.float "total_cost_service", default: 0.0
    t.index ["retailer_whatsapp_conversation_id", "country_code"], name: "index_country_conversations_by_country", unique: true
    t.index ["retailer_whatsapp_conversation_id"], name: "index_country_conversations_by_date"
  end

  create_table "country_prices", force: :cascade do |t|
    t.float "ws_notification_cost"
    t.float "ws_bic_cost"
    t.float "ws_uic_cost"
    t.float "ws_marketing_cost"
    t.float "ws_authentication_cost"
    t.float "ws_utility_cost"
    t.float "ws_service_cost"
    t.string "country_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "ws_authentication_international_cost", default: 0.0
    t.index ["country_id"], name: "index_country_prices_on_country_id", unique: true
  end

  create_table "ctx_services", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "service_id"
    t.string "service_api_key"
    t.string "main_goal"
    t.integer "convertion_goal_index"
    t.jsonb "goals"
    t.jsonb "retargeting"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_ctx_services_on_retailer_id"
  end

  create_table "customer_active_bots", force: :cascade do |t|
    t.bigint "customer_id"
    t.bigint "chat_bot_option_id"
    t.integer "failed_bot_attempts", default: 0
    t.integer "platform"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_preactivated", default: false
    t.datetime "last_outbound_date"
    t.integer "reminders_count", default: 0
    t.index ["chat_bot_option_id"], name: "index_customer_active_bots_on_chat_bot_option_id"
    t.index ["customer_id"], name: "index_customer_active_bots_on_customer_id"
  end

  create_table "customer_addresses", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.text "address"
    t.text "description"
    t.string "city"
    t.string "state"
    t.string "country_id"
    t.string "zip_code"
    t.string "latitude"
    t.string "longitude"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "shopify_id"
    t.boolean "main", default: false
    t.index ["customer_id"], name: "index_customer_addresses_on_customer_id"
  end

  create_table "customer_bot_options", force: :cascade do |t|
    t.bigint "customer_id"
    t.bigint "chat_bot_option_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "platform"
    t.index ["chat_bot_option_id"], name: "index_customer_bot_options_on_chat_bot_option_id"
    t.index ["customer_id"], name: "index_customer_bot_options_on_customer_id"
  end

  create_table "customer_bot_responses", force: :cascade do |t|
    t.bigint "customer_id"
    t.bigint "chat_bot_option_id"
    t.jsonb "response", default: {}
    t.integer "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "platform"
    t.index ["chat_bot_option_id"], name: "index_customer_bot_responses_on_chat_bot_option_id"
    t.index ["customer_id"], name: "index_customer_bot_responses_on_customer_id"
    t.index ["status"], name: "index_customer_bot_responses_on_status"
  end

  create_table "customer_conversation_topics", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.bigint "retailer_user_id", null: false
    t.string "topicable_type", null: false
    t.uuid "topicable_id", null: false
    t.string "channel", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["customer_id", "topicable_id", "topicable_type", "channel"], name: "idx_conv_topicable_channel"
    t.index ["customer_id"], name: "index_customer_conversation_topics_on_customer_id"
    t.index ["retailer_user_id"], name: "index_customer_conversation_topics_on_retailer_user_id"
    t.index ["topicable_type", "topicable_id"], name: "index_customer_conversation_topics_on_topicable"
  end

  create_table "customer_event_categories", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_customer_event_categories_on_retailer_id"
  end

  create_table "customer_event_subcategories", force: :cascade do |t|
    t.bigint "customer_event_category_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_event_category_id"], name: "index_customer_event_category_id"
  end

  create_table "customer_events", force: :cascade do |t|
    t.bigint "customer_id"
    t.bigint "customer_event_category_id"
    t.bigint "customer_event_subcategory_id"
    t.string "type_event"
    t.string "action"
    t.string "source"
    t.string "url_spec"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "agent_name"
    t.integer "direction", default: 0
    t.boolean "migrated"
    t.index ["customer_event_category_id"], name: "index_customer_events_on_customer_event_category_id"
    t.index ["customer_event_subcategory_id"], name: "index_customer_events_on_customer_event_subcategory_id"
    t.index ["customer_id"], name: "index_customer_events_on_customer_id"
  end

  create_table "customer_exports", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_customer_exports_on_retailer_id"
  end

  create_table "customer_fb_posts", force: :cascade do |t|
    t.bigint "customer_id"
    t.bigint "facebook_post_id"
    t.integer "platform"
    t.string "root_comment_id"
    t.datetime "last_comment_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "retailer_id"
    t.datetime "recent_inbound_comment_date"
    t.integer "status_interaction", default: 0
    t.integer "unread_comment_amount", default: 0
    t.boolean "unread", default: false
    t.bigint "retailer_user_id"
    t.index ["customer_id", "facebook_post_id", "root_comment_id"], name: "index_customer_post_comment", unique: true
    t.index ["customer_id"], name: "index_customer_fb_posts_on_customer_id"
    t.index ["facebook_post_id"], name: "index_customer_fb_posts_on_facebook_post_id"
    t.index ["last_comment_date"], name: "index_customer_fb_posts_on_last_comment_date"
    t.index ["platform"], name: "index_customer_fb_posts_on_platform"
    t.index ["retailer_id"], name: "index_customer_fb_posts_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_customer_fb_posts_on_retailer_user_id"
    t.index ["root_comment_id"], name: "index_customer_fb_posts_on_root_comment_id"
  end

  create_table "customer_hubspot_fields", force: :cascade do |t|
    t.string "customer_field"
    t.bigint "hubspot_field_id"
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "hs_tag", default: false
    t.index ["customer_field", "hubspot_field_id", "retailer_id"], name: "chf_customer_field_husbpot_field_retailer", unique: true
    t.index ["hubspot_field_id"], name: "index_customer_hubspot_fields_on_hubspot_field_id"
    t.index ["retailer_id"], name: "index_customer_hubspot_fields_on_retailer_id"
  end

  create_table "customer_mia_chatbots", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.integer "platform", null: false
    t.boolean "active", default: false, null: false
    t.boolean "human_help", default: false, null: false
    t.boolean "buy_intention", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["customer_id", "platform"], name: "index_customer_mia_chatbots_on_customer_id_and_platform", unique: true
    t.index ["customer_id"], name: "index_customer_mia_chatbots_on_customer_id"
  end

  create_table "customer_related_data", force: :cascade do |t|
    t.bigint "customer_related_field_id"
    t.bigint "customer_id"
    t.string "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id"], name: "index_customer_related_data_on_customer_id"
    t.index ["customer_related_field_id"], name: "index_customer_related_data_on_customer_related_field_id"
  end

  create_table "customer_related_fields", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "name"
    t.string "identifier"
    t.integer "field_type", default: 0
    t.string "web_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "list_options", default: []
    t.index ["retailer_id"], name: "index_customer_related_fields_on_retailer_id"
  end

  create_table "customer_tag_histories", force: :cascade do |t|
    t.bigint "tag_id"
    t.date "calculation_date"
    t.bigint "customer_ids", default: [], null: false, array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["calculation_date"], name: "index_customer_tag_histories_on_calculation_date"
    t.index ["tag_id", "calculation_date"], name: "index_unique_tag_date", unique: true
    t.index ["tag_id"], name: "index_customer_tag_histories_on_tag_id"
  end

  create_table "customer_tags", force: :cascade do |t|
    t.bigint "tag_id"
    t.bigint "customer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id"], name: "index_customer_tags_on_customer_id"
    t.index ["tag_id"], name: "index_customer_tags_on_tag_id"
  end

  create_table "customers", force: :cascade do |t|
    t.string "email"
    t.string "first_name"
    t.string "last_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "retailer_id"
    t.string "phone"
    t.integer "meli_customer_id"
    t.string "meli_nickname"
    t.integer "id_type"
    t.string "id_number"
    t.string "address"
    t.string "city"
    t.string "state"
    t.string "zip_code"
    t.string "country_id"
    t.boolean "valid_customer", default: false
    t.string "psid"
    t.string "web_id"
    t.text "notes"
    t.boolean "whatsapp_opt_in", default: true
    t.string "whatsapp_name"
    t.boolean "unread_whatsapp_chat", default: false
    t.boolean "unread_messenger_chat", default: false
    t.boolean "active_bot", default: false
    t.bigint "chat_bot_option_id"
    t.integer "failed_bot_attempts", default: 0
    t.boolean "allow_start_bots", default: false
    t.jsonb "endpoint_response", default: {}
    t.jsonb "endpoint_failed_response", default: {}
    t.float "ws_notification_cost", default: 0.0672
    t.boolean "hs_active"
    t.string "hs_id"
    t.string "number_to_use"
    t.boolean "api_created", default: false
    t.boolean "ws_active", default: false
    t.datetime "last_chat_interaction"
    t.integer "pstype"
    t.boolean "has_deals", default: false
    t.integer "status_chat", default: 0
    t.integer "count_unread_messages", default: 0
    t.boolean "number_to_use_opt_in", default: false, null: false
    t.string "current_conversation"
    t.float "ws_uic_cost"
    t.float "ws_bic_cost"
    t.boolean "blocked", default: false
    t.jsonb "tmp_messages", default: []
    t.string "id_in_shops"
    t.string "web_id_in_shops"
    t.datetime "recent_inbound_message_date"
    t.datetime "recent_inbound_msn_date"
    t.datetime "recent_inbound_ig_date"
    t.bigint "retailer_user_id"
    t.integer "ws_from"
    t.boolean "is_group", default: false
    t.text "participants", default: [], array: true
    t.boolean "campaignerized", default: false
    t.float "ws_marketing_cost"
    t.float "ws_authentication_cost"
    t.float "ws_utility_cost"
    t.float "ws_service_cost"
    t.string "created_in"
    t.string "qr_phone_id"
    t.datetime "last_msn_interaction"
    t.datetime "last_ig_interaction"
    t.integer "unread_messenger_messages", default: 0
    t.integer "unread_instagram_messages", default: 0
    t.boolean "unread_instagram_chat", default: false
    t.boolean "allow_start_messenger_bots", default: false
    t.boolean "allow_start_instagram_bots", default: false
    t.string "shopify_id"
    t.boolean "shopify_needs_update"
    t.bigint "template_id"
    t.datetime "template_sent_at"
    t.integer "status_chat_messenger", default: 0
    t.integer "status_chat_instagram", default: 0
    t.integer "outbound_status"
    t.datetime "sent_at"
    t.datetime "delivered_at"
    t.datetime "read_at"
    t.integer "campaign_id"
    t.string "error_message"
    t.boolean "from_registration", default: false
    t.string "company_name"
    t.bigint "registered_retailer_id"
    t.boolean "allow_mia_chatbot", default: false
    t.boolean "human_help", default: false
    t.boolean "buy_intention", default: false
    t.index ["campaignerized"], name: "index_customers_on_campaignerized"
    t.index ["chat_bot_option_id"], name: "index_customers_on_chat_bot_option_id"
    t.index ["email"], name: "index_customers_on_email"
    t.index ["last_chat_interaction"], name: "index_customers_on_last_chat_interaction"
    t.index ["number_to_use"], name: "index_customers_on_number_to_use"
    t.index ["phone", "qr_phone_id"], name: "index_customers_on_phone_and_qr_phone_id"
    t.index ["phone"], name: "index_customers_on_phone"
    t.index ["psid"], name: "index_customers_on_psid"
    t.index ["qr_phone_id", "retailer_id"], name: "index_customers_on_qr_phone_id_and_retailer_id"
    t.index ["qr_phone_id"], name: "index_customers_on_qr_phone_id"
    t.index ["registered_retailer_id"], name: "index_customers_on_registered_retailer_id"
    t.index ["retailer_id", "phone"], name: "index_customers_on_retailer_id_and_phone"
    t.index ["retailer_id"], name: "index_customers_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_customers_on_retailer_user_id"
    t.index ["status_chat"], name: "index_customers_on_status_chat"
    t.index ["web_id"], name: "index_customers_on_web_id"
    t.index ["whatsapp_opt_in"], name: "index_customers_on_whatsapp_opt_in"
    t.index ["ws_active"], name: "index_customers_on_ws_active"
  end

  create_table "deal_automation_historics", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('deal_automation_historics_id_seq'::regclass)" }
    t.integer "event_type", null: false
    t.integer "status", default: 0, null: false
    t.jsonb "event_data", default: {}, null: false
    t.string "web_id"
    t.bigint "old_deal_automation_id"
    t.bigint "old_deal_id"
    t.bigint "customer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "old_funnel_id"
    t.uuid "funnel_id"
    t.uuid "deal_id"
    t.uuid "deal_automation_id"
    t.index ["created_at"], name: "index_deal_automation_historics_on_created_at"
    t.index ["customer_id"], name: "index_deal_automation_historics_on_customer_id"
    t.index ["deal_automation_id"], name: "index_deal_automation_historics_on_deal_automation_id"
    t.index ["deal_id"], name: "index_deal_automation_historics_on_deal_id"
    t.index ["event_type"], name: "index_deal_automation_historics_on_event_type"
    t.index ["funnel_id"], name: "index_deal_automation_historics_on_funnel_id"
    t.index ["id"], name: "index_deal_automation_historics_on_id", unique: true
    t.index ["old_deal_automation_id"], name: "index_deal_automation_historics_on_old_deal_automation_id"
    t.index ["old_deal_id"], name: "index_deal_automation_historics_on_old_deal_id"
    t.index ["old_funnel_id"], name: "index_deal_automation_historics_on_old_funnel_id"
  end

  create_table "deal_automation_messages", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('deal_automation_messages_id_seq'::regclass)" }
    t.text "message"
    t.integer "status", default: 0
    t.bigint "retailer_id"
    t.bigint "customer_id"
    t.bigint "whatsapp_template_id"
    t.bigint "gupshup_whatsapp_message_id"
    t.bigint "old_deal_id"
    t.bigint "old_deal_automation_id"
    t.jsonb "template_params"
    t.text "template_text"
    t.datetime "send_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.uuid "deal_id"
    t.uuid "deal_automation_id"
    t.index ["customer_id"], name: "index_deal_automation_messages_on_customer_id"
    t.index ["deal_automation_id"], name: "index_deal_automation_messages_on_deal_automation_id"
    t.index ["deal_id"], name: "index_deal_automation_messages_on_deal_id"
    t.index ["gupshup_whatsapp_message_id"], name: "index_deal_automation_messages_on_gupshup_whatsapp_message_id"
    t.index ["id"], name: "index_deal_automation_messages_on_id", unique: true
    t.index ["old_deal_automation_id"], name: "index_deal_automation_messages_on_old_deal_automation_id"
    t.index ["old_deal_id"], name: "index_deal_automation_messages_on_old_deal_id"
    t.index ["retailer_id"], name: "index_deal_automation_messages_on_retailer_id"
    t.index ["whatsapp_template_id"], name: "index_deal_automation_messages_on_whatsapp_template_id"
  end

  create_table "deal_automations", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('deal_automations_id_seq'::regclass)" }
    t.bigint "retailer_id"
    t.bigint "retailer_user_id"
    t.bigint "old_funnel_step_id"
    t.string "name"
    t.string "web_id"
    t.datetime "timestamp_position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "kind", default: 0
    t.bigint "whatsapp_template_id"
    t.jsonb "content_params"
    t.text "template_text"
    t.jsonb "tags_filter"
    t.jsonb "deal_amount_filter"
    t.integer "lead_status_filter"
    t.integer "sending_frequency"
    t.integer "time_interval"
    t.integer "time_interval_programmed"
    t.integer "interval_day"
    t.string "interval_hour"
    t.boolean "pause_when_client_responded", default: false
    t.boolean "pause_after_number_of_messages", default: false
    t.integer "amount_messages_to_pause"
    t.boolean "deleted", default: false
    t.integer "old_to_funnel_id"
    t.integer "old_to_funnel_step_id"
    t.integer "deal_status_filter", default: [], array: true
    t.boolean "has_actions", default: false
    t.uuid "to_funnel_id"
    t.uuid "funnel_step_id"
    t.uuid "to_funnel_step_id"
    t.index ["funnel_step_id"], name: "index_deal_automations_on_funnel_step_id"
    t.index ["id"], name: "index_deal_automations_on_id", unique: true
    t.index ["old_funnel_step_id"], name: "index_deal_automations_on_old_funnel_step_id"
    t.index ["retailer_id"], name: "index_deal_automations_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_deal_automations_on_retailer_user_id"
    t.index ["to_funnel_id"], name: "index_deal_automations_on_to_funnel_id"
    t.index ["to_funnel_step_id"], name: "index_deal_automations_on_to_funnel_step_id"
    t.index ["whatsapp_template_id"], name: "index_deal_automations_on_whatsapp_template_id"
  end

  create_table "deals", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('deals_id_seq'::regclass)" }
    t.bigint "retailer_id"
    t.bigint "old_funnel_step_id"
    t.bigint "customer_id"
    t.string "name"
    t.string "web_id"
    t.decimal "amount", precision: 50, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "retailer_user_id"
    t.integer "position", default: 0, null: false
    t.datetime "expected_close_date"
    t.integer "lead_status"
    t.boolean "waiting_auto_assignment", default: false
    t.datetime "funnel_step_time_change"
    t.datetime "opening_date"
    t.datetime "timestamp_position"
    t.uuid "funnel_step_id"
    t.index ["customer_id"], name: "index_deals_on_customer_id"
    t.index ["funnel_step_id"], name: "index_deals_on_funnel_step_id"
    t.index ["id"], name: "index_deals_on_id", unique: true
    t.index ["old_funnel_step_id", "old_id", "position"], name: "index_deals_on_old_funnel_step_id_and_old_id_and_position"
    t.index ["old_funnel_step_id", "old_id"], name: "index_deals_on_old_funnel_step_id_and_old_id"
    t.index ["old_funnel_step_id", "position"], name: "index_deals_on_old_funnel_step_id_and_position"
    t.index ["old_funnel_step_id"], name: "index_deals_on_old_funnel_step_id"
    t.index ["retailer_id"], name: "index_deals_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_deals_on_retailer_user_id"
  end

  create_table "demo_request_leads", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.string "company"
    t.integer "employee_quantity"
    t.string "country"
    t.string "phone"
    t.string "message"
    t.string "problem_to_resolve"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "ecuador_invoices", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.bigint "paymentez_transaction_id"
    t.integer "sequential", default: -> { "nextval('invoice_sequential_seq'::regclass)" }
    t.integer "detail_type", null: false
    t.string "datil_invoice_id"
    t.decimal "subtotal"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["paymentez_transaction_id"], name: "index_ecuador_invoices_on_paymentez_transaction_id"
    t.index ["retailer_id"], name: "index_ecuador_invoices_on_retailer_id"
  end

  create_table "entitlements", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "pricing_tier_id", null: false
    t.string "name"
    t.string "identifier"
    t.integer "quantity"
    t.string "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["pricing_tier_id"], name: "index_entitlements_on_pricing_tier_id"
  end

  create_table "erasable_shopify_addresses", force: :cascade do |t|
    t.bigint "customer_id"
    t.string "shopify_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id"], name: "index_erasable_shopify_addresses_on_customer_id"
  end

  create_table "facebook_catalogs", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "uid"
    t.string "name"
    t.string "business_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_facebook_catalogs_on_retailer_id"
  end

  create_table "facebook_comments", force: :cascade do |t|
    t.bigint "customer_fb_post_id"
    t.string "message"
    t.string "url"
    t.string "comment_id"
    t.string "parent_id"
    t.boolean "sent_by_retailer", default: false
    t.string "attachment_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "retailer_user_id"
    t.string "sender_info"
    t.string "comment_identifier"
    t.string "file_data"
    t.boolean "note", default: false, null: false
    t.boolean "order_note", default: false
    t.json "order_summary"
    t.boolean "sent_from_mercately", default: false
    t.index ["customer_fb_post_id"], name: "index_facebook_comments_on_customer_fb_post_id"
    t.index ["retailer_user_id"], name: "index_facebook_comments_on_retailer_user_id"
  end

  create_table "facebook_messages", force: :cascade do |t|
    t.string "sender_uid"
    t.string "id_client"
    t.bigint "facebook_retailer_id"
    t.text "text"
    t.string "mid"
    t.string "reply_to"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "customer_id"
    t.date "date_read"
    t.boolean "sent_from_mercately", default: false
    t.boolean "sent_by_retailer", default: false
    t.string "file_type"
    t.string "url"
    t.string "file_data"
    t.string "filename"
    t.bigint "retailer_user_id"
    t.string "sender_first_name"
    t.string "sender_last_name"
    t.string "sender_email"
    t.string "message_identifier"
    t.boolean "note", default: false, null: false
    t.boolean "hs_sync", default: false
    t.boolean "order_note", default: false
    t.json "order_summary"
    t.text "error_message"
    t.string "internal_url"
    t.jsonb "payload", default: {}
    t.bigint "facebook_comment_id"
    t.boolean "sent_by_mia", default: false
    t.integer "mia_flag"
    t.index ["customer_id"], name: "index_facebook_messages_on_customer_id"
    t.index ["facebook_comment_id"], name: "index_facebook_messages_on_facebook_comment_id"
    t.index ["facebook_retailer_id", "mid"], name: "index_facebook_messages_on_facebook_retailer_id_and_mid"
    t.index ["facebook_retailer_id"], name: "index_facebook_messages_on_facebook_retailer_id"
    t.index ["retailer_user_id"], name: "index_facebook_messages_on_retailer_user_id"
  end

  create_table "facebook_posts", force: :cascade do |t|
    t.bigint "facebook_retailer_id"
    t.string "post_id"
    t.string "link"
    t.integer "platform"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["facebook_retailer_id"], name: "index_facebook_posts_on_facebook_retailer_id"
    t.index ["post_id", "platform"], name: "index_facebook_posts_on_post_id_and_platform", unique: true
  end

  create_table "facebook_retailers", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "uid"
    t.string "access_token"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "instagram_integrated", default: false
    t.string "instagram_uid"
    t.boolean "messenger_integrated", default: false
    t.boolean "facebook_comments", default: false
    t.boolean "instagram_comments", default: false
    t.jsonb "pages_response"
    t.string "ig_access_token"
    t.jsonb "ig_pages_response"
    t.string "ig_page_uid"
    t.string "ig_comment_uid"
    t.string "ig_comment_access_token"
    t.string "fb_comment_uid"
    t.string "fb_comment_access_token"
    t.string "ig_page_comment_uid"
    t.index ["retailer_id"], name: "index_facebook_retailers_on_retailer_id"
  end

  create_table "funnel_deal_histories", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('funnel_deal_histories_id_seq'::regclass)" }
    t.bigint "retailer_id", null: false
    t.bigint "old_funnel_id"
    t.bigint "retailer_user_id"
    t.bigint "old_funnel_step_id"
    t.integer "old_deal_ids", default: [], array: true
    t.jsonb "deal_with_amount", default: []
    t.date "calculation_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.uuid "funnel_id"
    t.uuid "deal_ids", default: [], array: true
    t.uuid "funnel_step_id"
    t.index ["deal_ids"], name: "index_funnel_deal_histories_on_deal_ids", using: :gin
    t.index ["funnel_id"], name: "index_funnel_deal_histories_on_funnel_id"
    t.index ["funnel_step_id"], name: "index_funnel_deal_histories_on_funnel_step_id"
    t.index ["id"], name: "index_funnel_deal_histories_on_id", unique: true
    t.index ["old_funnel_id"], name: "index_funnel_deal_histories_on_old_funnel_id"
    t.index ["old_funnel_step_id"], name: "index_funnel_deal_histories_on_old_funnel_step_id"
    t.index ["retailer_id"], name: "index_funnel_deal_histories_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_funnel_deal_histories_on_retailer_user_id"
  end

  create_table "funnel_exports", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('funnel_exports_id_seq'::regclass)" }
    t.bigint "retailer_id"
    t.bigint "old_funnel_id"
    t.string "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.uuid "funnel_id"
    t.index ["funnel_id"], name: "index_funnel_exports_on_funnel_id"
    t.index ["id"], name: "index_funnel_exports_on_id", unique: true
    t.index ["old_funnel_id"], name: "index_funnel_exports_on_old_funnel_id"
    t.index ["retailer_id"], name: "index_funnel_exports_on_retailer_id"
  end

  create_table "funnel_steps", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('funnel_steps_id_seq'::regclass)" }
    t.bigint "old_funnel_id"
    t.string "name"
    t.integer "position"
    t.decimal "step_total", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "web_id"
    t.integer "probability"
    t.integer "stagnation_time"
    t.uuid "funnel_id"
    t.index ["funnel_id"], name: "index_funnel_steps_on_funnel_id"
    t.index ["id"], name: "index_funnel_steps_on_id", unique: true
    t.index ["old_funnel_id"], name: "index_funnel_steps_on_old_funnel_id"
  end

  create_table "funnel_templates", force: :cascade do |t|
    t.string "type_of_template"
    t.string "web_id"
    t.string "name", null: false
    t.jsonb "steps", default: {}
    t.text "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "funnels", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "old_id", default: -> { "nextval('funnels_id_seq'::regclass)" }
    t.bigint "retailer_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "web_id"
    t.boolean "deleted"
    t.index ["id"], name: "index_funnels_on_id", unique: true
    t.index ["retailer_id"], name: "index_funnels_on_retailer_id"
  end

  create_table "global_settings", force: :cascade do |t|
    t.string "setting_key"
    t.string "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "custom_value"
    t.index ["setting_key"], name: "index_global_settings_on_setting_key", unique: true
  end

  create_table "gs_templates", force: :cascade do |t|
    t.integer "status", default: 0, null: false
    t.string "label"
    t.integer "key", default: 0, null: false
    t.string "category"
    t.text "text"
    t.text "example"
    t.string "language", default: "spanish"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "retailer_id"
    t.text "reason"
    t.boolean "submitted", default: false
    t.string "ws_template_id"
    t.integer "interactive_action", default: 0
    t.json "buttons"
    t.boolean "enable_sample", default: false
    t.boolean "add_security_recommendation"
    t.integer "code_expiry_minutes"
    t.boolean "add_expiration_text"
    t.index ["retailer_id"], name: "index_gs_templates_on_retailer_id"
  end

  create_table "gupshup_partners", force: :cascade do |t|
    t.integer "partner_id"
    t.string "name"
    t.string "token"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "gupshup_whatsapp_messages", force: :cascade do |t|
    t.bigint "retailer_id"
    t.bigint "customer_id"
    t.string "whatsapp_message_id"
    t.string "gupshup_message_id"
    t.integer "status", null: false
    t.string "direction", null: false
    t.json "message_payload"
    t.string "source", null: false
    t.string "destination", null: false
    t.string "channel", null: false
    t.datetime "sent_at"
    t.datetime "delivered_at"
    t.datetime "read_at"
    t.boolean "error"
    t.json "error_payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "message_type"
    t.bigint "retailer_user_id"
    t.float "cost"
    t.string "message_identifier"
    t.bigint "campaign_id"
    t.string "sender_first_name"
    t.string "sender_last_name"
    t.string "sender_email"
    t.boolean "note", default: false, null: false
    t.boolean "initiate_conversation", default: false
    t.integer "conversation_type"
    t.json "conversation_payload"
    t.boolean "hs_sync", default: false
    t.boolean "order_note", default: false
    t.json "order_summary"
    t.integer "integration", default: 0
    t.boolean "sent_from_mercately", default: true
    t.string "internal_url"
    t.bigint "integration_message_data_id"
    t.bigint "template_id"
    t.string "reaction"
    t.boolean "sent_by_mia", default: false
    t.integer "mia_flag"
    t.index ["campaign_id"], name: "index_gupshup_whatsapp_messages_on_campaign_id"
    t.index ["customer_id"], name: "index_gupshup_whatsapp_messages_on_customer_id"
    t.index ["gupshup_message_id"], name: "index_gupshup_whatsapp_messages_on_gupshup_message_id"
    t.index ["integration_message_data_id"], name: "index_gupshup_whatsapp_messages_on_integration_message_data_id"
    t.index ["retailer_id"], name: "index_gupshup_whatsapp_messages_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_gupshup_whatsapp_messages_on_retailer_user_id"
    t.index ["whatsapp_message_id"], name: "index_gupshup_whatsapp_messages_on_whatsapp_message_id"
  end

  create_table "hubspot_fields", force: :cascade do |t|
    t.string "hubspot_field"
    t.string "hubspot_label"
    t.string "hubspot_type"
    t.boolean "taken", default: false
    t.boolean "deleted", default: false
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id", "hubspot_field"], name: "index_hubspot_fields_on_retailer_id_and_hubspot_field", unique: true
    t.index ["retailer_id"], name: "index_hubspot_fields_on_retailer_id"
  end

  create_table "hubspot_owners", force: :cascade do |t|
    t.string "owner_id"
    t.string "first_name"
    t.string "last_name"
    t.boolean "taken", default: false
    t.boolean "deleted", default: false
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id", "owner_id"], name: "index_hubspot_owners_on_retailer_id_and_owner_id", unique: true
    t.index ["retailer_id"], name: "index_hubspot_owners_on_retailer_id"
  end

  create_table "import_contacts_loggers", force: :cascade do |t|
    t.integer "retailer_user_id"
    t.integer "retailer_id"
    t.string "original_file_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status", default: 0
  end

  create_table "instagram_comments", force: :cascade do |t|
    t.bigint "customer_fb_post_id"
    t.string "message"
    t.string "comment_id"
    t.string "parent_id"
    t.boolean "sent_by_retailer", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "retailer_user_id"
    t.string "sender_info"
    t.string "comment_identifier"
    t.boolean "note", default: false, null: false
    t.boolean "order_note", default: false
    t.json "order_summary"
    t.boolean "sent_from_mercately", default: false
    t.index ["customer_fb_post_id"], name: "index_instagram_comments_on_customer_fb_post_id"
    t.index ["retailer_user_id"], name: "index_instagram_comments_on_retailer_user_id"
  end

  create_table "instagram_messages", force: :cascade do |t|
    t.string "sender_uid"
    t.string "id_client"
    t.bigint "facebook_retailer_id"
    t.text "text"
    t.string "mid"
    t.string "reply_to"
    t.bigint "customer_id"
    t.date "date_read"
    t.boolean "sent_from_mercately", default: false
    t.boolean "sent_by_retailer", default: false
    t.string "file_type"
    t.string "url"
    t.string "file_data"
    t.string "filename"
    t.bigint "retailer_user_id"
    t.string "sender_first_name"
    t.string "sender_last_name"
    t.string "sender_email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "message_identifier"
    t.boolean "note", default: false, null: false
    t.boolean "hs_sync", default: false
    t.boolean "order_note", default: false
    t.json "order_summary"
    t.text "error_message"
    t.string "internal_url"
    t.jsonb "payload", default: {}
    t.bigint "instagram_comment_id"
    t.boolean "sent_by_mia", default: false
    t.integer "mia_flag"
    t.index ["customer_id"], name: "index_instagram_messages_on_customer_id"
    t.index ["facebook_retailer_id", "mid"], name: "index_instagram_messages_on_facebook_retailer_id_and_mid"
    t.index ["facebook_retailer_id"], name: "index_instagram_messages_on_facebook_retailer_id"
    t.index ["instagram_comment_id"], name: "index_instagram_messages_on_instagram_comment_id"
    t.index ["retailer_user_id"], name: "index_instagram_messages_on_retailer_user_id"
  end

  create_table "integration_message_data", force: :cascade do |t|
    t.string "title"
    t.bigint "retailer_id"
    t.bigint "whatsapp_template_id"
    t.jsonb "content_params"
    t.text "template_text"
    t.integer "platform"
    t.string "message_type"
    t.string "web_id"
    t.boolean "status", default: false
    t.jsonb "properties"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "dispatch_time"
    t.integer "time_to_ad"
    t.integer "time_unit_to_ad", default: 0
    t.index ["retailer_id"], name: "index_integration_message_data_on_retailer_id"
    t.index ["whatsapp_template_id"], name: "index_integration_message_data_on_whatsapp_template_id"
  end

  create_table "integration_messages", force: :cascade do |t|
    t.bigint "retailer_id"
    t.bigint "customer_id"
    t.bigint "whatsapp_template_id"
    t.bigint "gupshup_whatsapp_message_id"
    t.bigint "integration_message_data_id"
    t.jsonb "template_params"
    t.text "template_text"
    t.datetime "send_at"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "shopify_identifier"
    t.string "resource_id"
    t.index ["customer_id"], name: "index_integration_messages_on_customer_id"
    t.index ["gupshup_whatsapp_message_id"], name: "index_integration_messages_on_gupshup_whatsapp_message_id"
    t.index ["integration_message_data_id"], name: "index_integration_messages_on_integration_message_data_id"
    t.index ["retailer_id"], name: "index_integration_messages_on_retailer_id"
    t.index ["whatsapp_template_id"], name: "index_integration_messages_on_whatsapp_template_id"
  end

  create_table "integration_settings", force: :cascade do |t|
    t.string "country_code"
    t.string "mp_domain"
    t.boolean "mercado_pago", default: false
    t.boolean "stripe", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "payphone", default: false
  end

  create_table "karix_whatsapp_messages", force: :cascade do |t|
    t.string "uid"
    t.string "account_uid"
    t.string "source"
    t.string "destination"
    t.string "country"
    t.string "content_type"
    t.string "content_text"
    t.string "content_media_url"
    t.string "content_media_caption"
    t.string "content_media_type"
    t.string "content_location_longitude"
    t.string "content_location_latitude"
    t.string "content_location_label"
    t.string "content_location_address"
    t.datetime "created_time"
    t.datetime "sent_time"
    t.datetime "delivered_time"
    t.datetime "updated_time"
    t.string "status"
    t.string "channel"
    t.string "direction"
    t.string "error_code"
    t.string "error_message"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "retailer_id"
    t.bigint "customer_id"
    t.string "message_type"
    t.bigint "retailer_user_id"
    t.float "cost"
    t.string "message_identifier"
    t.bigint "campaign_id"
    t.string "sender_first_name"
    t.string "sender_last_name"
    t.string "sender_email"
    t.index ["campaign_id"], name: "index_karix_whatsapp_messages_on_campaign_id"
    t.index ["customer_id"], name: "index_karix_whatsapp_messages_on_customer_id"
    t.index ["retailer_id"], name: "index_karix_whatsapp_messages_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_karix_whatsapp_messages_on_retailer_user_id"
    t.index ["uid"], name: "index_karix_whatsapp_messages_on_uid", unique: true
  end

  create_table "meli_customers", force: :cascade do |t|
    t.string "access_token"
    t.string "meli_user_id"
    t.string "refresh_token"
    t.string "nickname"
    t.string "email"
    t.integer "points"
    t.string "link"
    t.string "seller_experience"
    t.string "seller_reputation_level_id"
    t.integer "transactions_canceled"
    t.integer "transactions_completed"
    t.integer "ratings_negative"
    t.integer "ratings_neutral"
    t.integer "ratings_positive"
    t.integer "ratings_total"
    t.string "phone"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "buyer_canceled_transactions"
    t.integer "buyer_completed_transactions"
    t.integer "buyer_canceled_paid_transactions"
    t.integer "buyer_unrated_paid_transactions"
    t.integer "buyer_unrated_total_transactions"
    t.integer "buyer_not_yet_rated_paid_transactions"
    t.integer "buyer_not_yet_rated_total_transactions"
    t.datetime "meli_registration_date"
    t.string "phone_area"
    t.boolean "phone_verified"
    t.string "first_name"
    t.string "last_name"
  end

  create_table "meli_retailers", force: :cascade do |t|
    t.string "access_token"
    t.string "meli_user_id"
    t.string "refresh_token"
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "nickname"
    t.string "email"
    t.integer "points"
    t.string "link"
    t.string "seller_experience"
    t.string "seller_reputation_level_id"
    t.integer "transactions_canceled"
    t.integer "transactions_completed"
    t.integer "ratings_negative"
    t.integer "ratings_neutral"
    t.integer "ratings_positive"
    t.integer "ratings_total"
    t.bigint "customer_id"
    t.string "phone"
    t.boolean "has_meli_info", default: false
    t.datetime "meli_token_updated_at"
    t.datetime "meli_info_updated_at"
    t.boolean "meli_user_active", default: true
    t.index ["customer_id"], name: "index_meli_retailers_on_customer_id"
    t.index ["retailer_id"], name: "index_meli_retailers_on_retailer_id"
  end

  create_table "mercately_videos", force: :cascade do |t|
    t.string "title"
    t.integer "view"
    t.string "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "message_blocks", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "phone"
    t.datetime "sent_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id", "phone"], name: "index_message_blocks_on_retailer_id_and_phone", unique: true
    t.index ["retailer_id"], name: "index_message_blocks_on_retailer_id"
  end

  create_table "mia_integration_types", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_mia_integration_types_on_name", unique: true
  end

  create_table "mia_integrations", force: :cascade do |t|
    t.bigint "retailer_id"
    t.text "old_public_key"
    t.string "old_openai_key"
    t.string "service_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "kind"
    t.bigint "mia_integration_type_id"
    t.string "encrypted_public_key"
    t.string "encrypted_openai_key"
    t.string "encrypted_public_key_iv"
    t.string "encrypted_openai_key_iv"
    t.string "encrypted_public_key_salt"
    t.string "encrypted_openai_key_salt"
    t.index ["mia_integration_type_id", "retailer_id"], name: "index_mia_integrations_on_mia_integration_type_id_&_retailer_id", unique: true
    t.index ["mia_integration_type_id"], name: "index_mia_integrations_on_mia_integration_type_id"
    t.index ["retailer_id", "kind"], name: "index_mia_integrations_on_retailer_id_and_kind", unique: true
    t.index ["retailer_id"], name: "index_mia_integrations_on_retailer_id"
  end

  create_table "mia_platforms", force: :cascade do |t|
    t.string "name", null: false
    t.boolean "active", default: false, null: false
    t.text "svg_content", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "ml_countries", force: :cascade do |t|
    t.string "name"
    t.string "site"
    t.string "domain"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "code"
  end

  create_table "mobile_tokens", force: :cascade do |t|
    t.bigint "retailer_user_id"
    t.string "device"
    t.string "encrypted_token"
    t.string "encrypted_token_iv"
    t.string "encrypted_token_salt"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "mobile_push_token"
    t.index ["device", "retailer_user_id"], name: "index_mobile_tokens_on_device_and_retailer_user_id", unique: true
    t.index ["retailer_user_id"], name: "index_mobile_tokens_on_retailer_user_id"
  end

  create_table "mp_retailers", force: :cascade do |t|
    t.string "access_token"
    t.string "mp_user_id"
    t.string "refresh_token"
    t.datetime "mp_token_updated_at"
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "public_key"
    t.index ["retailer_id"], name: "index_mp_retailers_on_retailer_id"
  end

  create_table "notifications", force: :cascade do |t|
    t.string "title"
    t.text "body"
    t.integer "visible_for"
    t.datetime "visible_until"
    t.boolean "published", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "onboarding_notifications", force: :cascade do |t|
    t.datetime "send_at"
    t.boolean "sent", default: false
    t.integer "template_type", default: 0
    t.integer "step", default: 0
    t.bigint "retailer_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "notification_type", default: 0
    t.index ["retailer_user_id"], name: "index_onboarding_notifications_on_retailer_user_id"
  end

  create_table "onboarding_schedules", force: :cascade do |t|
    t.bigint "retailer_user_id"
    t.boolean "skip_demo", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_user_id"], name: "index_onboarding_schedules_on_retailer_user_id"
  end

  create_table "option_sub_lists", force: :cascade do |t|
    t.bigint "chat_bot_option_id"
    t.string "value_to_save"
    t.string "value_to_show"
    t.integer "position", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chat_bot_option_id"], name: "index_option_sub_lists_on_chat_bot_option_id"
  end

  create_table "order_items", force: :cascade do |t|
    t.bigint "order_id"
    t.bigint "product_id"
    t.integer "quantity"
    t.decimal "unit_price"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "product_variation_id"
    t.boolean "from_ml", default: false
    t.index ["order_id"], name: "index_order_items_on_order_id"
    t.index ["product_id"], name: "index_order_items_on_product_id"
    t.index ["product_variation_id"], name: "index_order_items_on_product_variation_id"
  end

  create_table "orders", force: :cascade do |t|
    t.bigint "customer_id"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "meli_order_id"
    t.string "currency_id"
    t.float "total_amount"
    t.datetime "date_closed"
    t.integer "merc_status", default: 0
    t.integer "feedback_reason"
    t.string "feedback_message"
    t.integer "feedback_rating"
    t.string "web_id"
    t.string "pack_id"
    t.text "notes"
    t.bigint "retailer_user_id"
    t.bigint "sales_channel_id"
    t.integer "count_unread_messages", default: 0
    t.string "id_in_shops"
    t.index ["customer_id"], name: "index_orders_on_customer_id"
    t.index ["meli_order_id"], name: "index_orders_on_meli_order_id", unique: true
    t.index ["retailer_user_id"], name: "index_orders_on_retailer_user_id"
    t.index ["sales_channel_id"], name: "index_orders_on_sales_channel_id"
  end

  create_table "partner_commissions", force: :cascade do |t|
    t.bigint "commission_invoice_id"
    t.bigint "retailer_id"
    t.date "commission_date"
    t.integer "active_accounts"
    t.decimal "payment_plan_price"
    t.decimal "commission_rate"
    t.decimal "commission_amount"
    t.string "transactionable_type"
    t.string "transactionable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["commission_invoice_id"], name: "index_partner_commissions_on_commission_invoice_id"
    t.index ["retailer_id"], name: "index_partner_commissions_on_retailer_id"
    t.index ["transactionable_type", "transactionable_id", "retailer_id"], name: "index_unique_transactionable_on_partner_commissions", unique: true
  end

  create_table "partner_configs", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.bigint "retailer_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.float "marketing_cost"
    t.float "utility_cost"
    t.float "authentication_cost"
    t.float "service_cost"
    t.boolean "hide_credit_cards", default: false
    t.boolean "hide_live_chat", default: false
    t.boolean "hide_support_chat", default: false
    t.boolean "hide_pricing_info", default: false
    t.float "authentication_international_cost", default: 0.0
    t.index ["partner_id", "retailer_id"], name: "index_partner_configs_on_partner_id_and_retailer_id", unique: true
    t.index ["partner_id"], name: "index_partner_configs_on_partner_id"
    t.index ["retailer_id"], name: "index_partner_configs_on_retailer_id"
  end

  create_table "partners", force: :cascade do |t|
    t.string "email", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "full_name"
    t.string "company_name"
    t.boolean "accepted", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "web_id"
    t.string "paypal_email"
    t.uuid "unique_key", default: -> { "uuid_generate_v4()" }, null: false
    t.string "language", default: "es"
    t.decimal "custom_commission", comment: "Campo para el valor de la comisión para partners antiguos o con comisión establecida"
    t.string "whatsapp_number", comment: "Numero de whatsapp del partner"
    t.string "country_code", comment: "Codigo del pais para el numero de whatsapp del partner"
    t.decimal "mrr", precision: 10, scale: 2, default: "0.0"
    t.index ["email"], name: "index_partners_on_email", unique: true
    t.index ["reset_password_token"], name: "index_partners_on_reset_password_token", unique: true
    t.index ["unique_key"], name: "index_partners_on_unique_key", unique: true
    t.index ["web_id"], name: "index_partners_on_web_id", unique: true
  end

  create_table "payment_methods", force: :cascade do |t|
    t.string "stripe_pm_id", null: false
    t.bigint "retailer_id"
    t.string "payment_type", null: false
    t.json "payment_payload", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "main", default: false
    t.boolean "deleted", default: false
    t.string "chargebee_id"
    t.index ["retailer_id"], name: "index_payment_methods_on_retailer_id"
  end

  create_table "payment_plans", force: :cascade do |t|
    t.bigint "retailer_id"
    t.decimal "price", default: "0.0"
    t.date "start_date", default: -> { "CURRENT_TIMESTAMP" }
    t.date "next_pay_date"
    t.integer "status", default: 0
    t.integer "plan", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "karix_available_messages", default: 0
    t.integer "karix_available_notifications", default: 0
    t.integer "month_interval", default: 1
    t.integer "charge_attempt", default: 0, null: false
    t.text "notes"
    t.boolean "contact_based", default: false
    t.integer "contacts_included", default: 0
    t.integer "sales_development_id"
    t.integer "sales_agent_id"
    t.integer "customer_success_id"
    t.integer "support_agent_id"
    t.datetime "payment_start_date"
    t.datetime "end_date"
    t.string "chargebee_subscription_id"
    t.integer "max_agents", default: 2
    t.boolean "accept_terms_and_conditions", default: false
    t.boolean "growth_tools_access", default: false
    t.integer "onboarding_specialist_id"
    t.boolean "make_my_chatbot", default: false
    t.boolean "mercately_setup", default: false
    t.boolean "notification_sent", default: false
    t.boolean "chatbot_ai", default: false
    t.integer "whatsapp_integration_type", comment: "Campo para identificar si tiene integracion por QR o API"
    t.float "mia_contact_cost", default: 0.2
    t.integer "mia_free_contacts", default: 0
    t.integer "custom_storage_months"
    t.uuid "pricing_tier_id"
    t.index ["pricing_tier_id"], name: "index_payment_plans_on_pricing_tier_id"
    t.index ["retailer_id"], name: "index_payment_plans_on_retailer_id"
  end

  create_table "paymentez_credit_cards", force: :cascade do |t|
    t.string "card_type"
    t.string "number"
    t.string "name"
    t.bigint "retailer_id"
    t.string "token"
    t.string "status"
    t.string "expiry_month"
    t.string "expiry_year"
    t.boolean "deleted", default: false
    t.boolean "main", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_paymentez_credit_cards_on_retailer_id"
  end

  create_table "paymentez_transactions", force: :cascade do |t|
    t.string "status"
    t.string "payment_date"
    t.decimal "amount"
    t.string "authorization_code"
    t.integer "installments"
    t.string "dev_reference"
    t.string "message"
    t.string "carrier_code"
    t.string "pt_id"
    t.integer "status_detail"
    t.string "transaction_reference"
    t.bigint "retailer_id"
    t.bigint "payment_plan_id"
    t.bigint "paymentez_credit_card_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "month_interval", default: 0, null: false
    t.string "web_id"
    t.text "description", default: ""
    t.index ["payment_plan_id"], name: "index_paymentez_transactions_on_payment_plan_id"
    t.index ["paymentez_credit_card_id"], name: "index_paymentez_transactions_on_paymentez_credit_card_id"
    t.index ["retailer_id"], name: "index_paymentez_transactions_on_retailer_id"
  end

  create_table "permissions", force: :cascade do |t|
    t.string "name", limit: 80, null: false
    t.string "identifier", null: false
    t.bigint "parent_permission_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["identifier"], name: "index_permissions_on_identifier", unique: true
    t.index ["parent_permission_id"], name: "index_permissions_on_parent_permission_id"
  end

  create_table "pipedream_integrations", force: :cascade do |t|
    t.integer "retailer_id", null: false
    t.string "platform", null: false
    t.string "account_id", null: false
    t.string "project_id"
    t.jsonb "metadata"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id", "platform", "account_id"], name: "index_pipedream_integrations_on_retailer_platform_account", unique: true
    t.index ["retailer_id"], name: "index_pipedream_integrations_on_retailer_id"
  end

  create_table "plan_cancellations", force: :cascade do |t|
    t.bigint "retailer_id"
    t.integer "reason"
    t.string "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "desist_cancellation", default: false
    t.date "cancellation_date"
    t.index ["retailer_id"], name: "index_plan_cancellations_on_retailer_id"
  end

  create_table "plans", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.jsonb "metadata", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "identifier"
    t.integer "status", default: 0
    t.index ["name"], name: "index_plans_on_name", unique: true
  end

  create_table "pricing_tiers", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.integer "billing_cycle", default: 0, null: false
    t.decimal "price", precision: 10, scale: 2, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "pricing_model"
    t.string "currency_code"
    t.string "identifier"
    t.integer "status", default: 0
    t.string "priceable_type", null: false
    t.uuid "priceable_id", null: false
    t.index ["priceable_type", "priceable_id"], name: "index_pricing_tiers_on_priceable"
  end

  create_table "product_variations", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "variation_meli_id"
    t.jsonb "data", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status", default: 0
    t.index ["product_id"], name: "index_product_variations_on_product_id"
    t.index ["variation_meli_id"], name: "index_product_variations_on_variation_meli_id", unique: true, where: "(variation_meli_id IS NOT NULL)"
  end

  create_table "products", force: :cascade do |t|
    t.string "title"
    t.decimal "price"
    t.integer "available_quantity", default: 0
    t.text "description"
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "meli_product_id"
    t.string "meli_site_id"
    t.string "subtitle"
    t.decimal "base_price"
    t.decimal "original_price"
    t.integer "initial_quantity"
    t.integer "sold_quantity", default: 0
    t.datetime "meli_start_time"
    t.string "meli_listing_type_id"
    t.datetime "meli_stop_time"
    t.datetime "meli_end_time"
    t.string "meli_permalink"
    t.integer "category_id"
    t.integer "buying_mode"
    t.integer "condition", default: 0
    t.jsonb "ml_attributes", default: []
    t.bigint "main_picture_id"
    t.integer "status", default: 0
    t.integer "meli_status"
    t.integer "from", default: 0
    t.string "code"
    t.string "web_id"
    t.jsonb "meli_parent", default: []
    t.string "facebook_product_id"
    t.string "manufacturer_part_number"
    t.string "gtin"
    t.string "brand"
    t.string "url"
    t.boolean "connected_to_facebook", default: false
    t.string "id_in_shops"
    t.index ["category_id"], name: "index_products_on_category_id"
    t.index ["facebook_product_id"], name: "index_products_on_facebook_product_id", unique: true, where: "(facebook_product_id IS NOT NULL)"
    t.index ["meli_product_id"], name: "index_products_on_meli_product_id", unique: true, where: "(meli_product_id IS NOT NULL)"
    t.index ["retailer_id", "code"], name: "index_products_on_retailer_id_and_code", unique: true
    t.index ["retailer_id"], name: "index_products_on_retailer_id"
  end

  create_table "questions", force: :cascade do |t|
    t.bigint "product_id"
    t.text "answer"
    t.text "question"
    t.string "meli_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "customer_id"
    t.boolean "deleted_from_listing", default: false
    t.boolean "hold", default: false
    t.integer "status"
    t.datetime "date_read"
    t.string "site_id"
    t.integer "sender_id"
    t.bigint "order_id"
    t.integer "answer_status"
    t.datetime "date_created_question"
    t.datetime "date_created_answer"
    t.integer "meli_question_type"
    t.boolean "answered", default: false
    t.string "web_id"
    t.jsonb "attachments", default: []
    t.index ["customer_id"], name: "index_questions_on_customer_id"
    t.index ["meli_id"], name: "index_questions_on_meli_id", unique: true
    t.index ["order_id"], name: "index_questions_on_order_id"
    t.index ["product_id"], name: "index_questions_on_product_id"
  end

  create_table "quote_items", force: :cascade do |t|
    t.bigint "quote_id"
    t.string "name"
    t.integer "quantity", default: 0
    t.decimal "unit_price", precision: 10, scale: 2, default: "0.0"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["quote_id"], name: "index_quote_items_on_quote_id"
  end

  create_table "quotes", force: :cascade do |t|
    t.string "web_id"
    t.string "customer_name"
    t.string "title"
    t.integer "quote_status", default: 0
    t.date "due_date"
    t.boolean "include_tax", default: true
    t.text "comment"
    t.decimal "subtotal", precision: 10, scale: 2, default: "0.0"
    t.decimal "tax_amount", precision: 10, scale: 2, default: "0.0"
    t.decimal "total", precision: 10, scale: 2, default: "0.0"
    t.bigint "customer_id"
    t.bigint "retailer_id"
    t.bigint "retailer_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false
    t.index ["customer_id"], name: "index_quotes_on_customer_id"
    t.index ["retailer_id"], name: "index_quotes_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_quotes_on_retailer_user_id"
  end

  create_table "reminders", force: :cascade do |t|
    t.bigint "retailer_id"
    t.bigint "customer_id"
    t.bigint "retailer_user_id"
    t.bigint "whatsapp_template_id"
    t.bigint "gupshup_whatsapp_message_id"
    t.bigint "karix_whatsapp_message_id"
    t.jsonb "content_params"
    t.datetime "send_at"
    t.datetime "send_at_timezone"
    t.string "timezone"
    t.string "web_id"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "template_text"
    t.index ["customer_id"], name: "index_reminders_on_customer_id"
    t.index ["gupshup_whatsapp_message_id"], name: "index_reminders_on_gupshup_whatsapp_message_id"
    t.index ["karix_whatsapp_message_id"], name: "index_reminders_on_karix_whatsapp_message_id"
    t.index ["retailer_id"], name: "index_reminders_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_reminders_on_retailer_user_id"
    t.index ["whatsapp_template_id"], name: "index_reminders_on_whatsapp_template_id"
  end

  create_table "responsibles", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.integer "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "email"
    t.string "calendly"
    t.float "assignations_percentage"
    t.integer "agent_id"
  end

  create_table "retailer_addresses", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "name"
    t.string "address"
    t.string "city"
    t.string "state"
    t.string "country_id"
    t.string "web_id"
    t.boolean "shop_updated", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_retailer_addresses_on_retailer_id"
    t.index ["web_id"], name: "index_retailer_addresses_on_web_id"
  end

  create_table "retailer_amount_messages", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.date "calculation_date"
    t.integer "ws_inbound", default: 0
    t.integer "ws_outbound", default: 0
    t.integer "total_ws_messages", default: 0
    t.integer "msn_inbound", default: 0
    t.integer "msn_outbound", default: 0
    t.integer "total_msn_messages", default: 0
    t.integer "ig_inbound", default: 0
    t.integer "ig_outbound", default: 0
    t.integer "total_ig_messages", default: 0
    t.integer "ml_inbound", default: 0
    t.integer "ml_outbound", default: 0
    t.integer "total_ml_messages", default: 0
    t.bigint "retailer_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_retailer_amount_messages_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_retailer_amount_messages_on_retailer_user_id"
  end

  create_table "retailer_amount_messages_by_hours", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.integer "platform"
    t.date "calculation_date"
    t.jsonb "message_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_retailer_amount_messages_by_hours_on_retailer_id"
  end

  create_table "retailer_average_response_times", force: :cascade do |t|
    t.bigint "retailer_id"
    t.bigint "retailer_user_id"
    t.decimal "first_time_average"
    t.decimal "conversation_time_average"
    t.date "calculation_date"
    t.integer "platform"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "calculation_hour"
    t.integer "week_day"
    t.index ["retailer_id"], name: "index_retailer_average_response_times_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_retailer_average_response_times_on_retailer_user_id"
  end

  create_table "retailer_bill_details", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "business_name"
    t.string "identification_type"
    t.string "identification_number"
    t.string "business_phone"
    t.string "business_email"
    t.string "iva_description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "business_address"
    t.index ["retailer_id"], name: "index_retailer_bill_details_on_retailer_id"
  end

  create_table "retailer_business_rule_data", force: :cascade do |t|
    t.bigint "retailer_id"
    t.bigint "business_rule_id"
    t.jsonb "data", default: {}
    t.index ["business_rule_id"], name: "index_retailer_business_rule_data_on_business_rule_id"
    t.index ["retailer_id"], name: "index_retailer_business_rule_data_on_retailer_id"
  end

  create_table "retailer_business_rules", force: :cascade do |t|
    t.bigint "retailer_id"
    t.bigint "business_rule_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_rule_id"], name: "index_retailer_business_rules_on_business_rule_id"
    t.index ["retailer_id"], name: "index_retailer_business_rules_on_retailer_id"
  end

  create_table "retailer_conversations", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.bigint "retailer_user_id"
    t.integer "new_conversations", default: 0
    t.integer "recurring_conversations", default: 0
    t.integer "platform"
    t.date "calculation_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_retailer_conversations_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_retailer_conversations_on_retailer_user_id"
  end

  create_table "retailer_counters", force: :cascade do |t|
    t.bigint "retailer_id"
    t.integer "fb_comments", default: 0
    t.integer "ig_comments", default: 0
    t.integer "fb_comments_na", default: 0
    t.integer "ig_comments_na", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "ws_messages", default: 0
    t.integer "ws_messages_na", default: 0
    t.integer "fb_messages", default: 0
    t.integer "fb_messages_na", default: 0
    t.integer "ig_messages", default: 0
    t.integer "ig_messages_na", default: 0
    t.index ["retailer_id"], name: "index_retailer_counters_on_retailer_id"
  end

  create_table "retailer_customers", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.bigint "retailer_user_id"
    t.integer "new_customers", default: [], null: false, array: true
    t.integer "recurring_customers", default: [], null: false, array: true
    t.date "calculation_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_retailer_customers_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_retailer_customers_on_retailer_user_id"
  end

  create_table "retailer_mia_platforms", force: :cascade do |t|
    t.bigint "retailer_id"
    t.bigint "mia_platform_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["mia_platform_id"], name: "index_retailer_mia_platforms_on_mia_platform_id"
    t.index ["retailer_id"], name: "index_retailer_mia_platforms_on_retailer_id"
  end

  create_table "retailer_most_used_tags", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.bigint "tag_id", null: false
    t.integer "amount_used", default: 0
    t.date "calculation_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_retailer_most_used_tags_on_retailer_id"
    t.index ["tag_id"], name: "index_retailer_most_used_tags_on_tag_id"
  end

  create_table "retailer_onboardings", force: :cascade do |t|
    t.bigint "retailer_id"
    t.jsonb "business"
    t.string "user_role"
    t.string "market"
    t.string "workers"
    t.string "user_workers"
    t.jsonb "expectations"
    t.boolean "ecommerce"
    t.string "current_ecommerce"
    t.string "crm_experience"
    t.string "bot_experience"
    t.integer "step", default: 0
    t.boolean "finish", default: false
    t.boolean "mobile", default: false
    t.integer "ws_setup_status", default: 0
    t.string "utm_source"
    t.string "utm_medium"
    t.string "utm_campaign"
    t.string "utm_content"
    t.string "utm_term"
    t.string "utm_campaign_id"
    t.string "attributer_channel"
    t.string "attributer_channeldrilldown1"
    t.string "attributer_channeldrilldown2"
    t.string "attributer_channeldrilldown3"
    t.string "attributer_channeldrilldown4"
    t.string "attributer_landingpage"
    t.string "attributer_landingpagegroup"
    t.integer "ab_type"
    t.string "ad_investment"
    t.string "other_ecommerce"
    t.string "id_attended"
    t.string "work_area"
    t.index ["retailer_id"], name: "index_retailer_onboardings_on_retailer_id"
  end

  create_table "retailer_schedules", force: :cascade do |t|
    t.bigint "retailer_id"
    t.integer "weekday", null: false
    t.string "web_id"
    t.time "opening_time"
    t.time "closing_time"
    t.boolean "active", default: false, null: false
    t.boolean "shop_updated", default: false, null: false
    t.index ["retailer_id"], name: "index_retailer_schedules_on_retailer_id"
    t.index ["web_id"], name: "index_retailer_schedules_on_web_id"
  end

  create_table "retailer_unfinished_message_blocks", force: :cascade do |t|
    t.bigint "retailer_id"
    t.bigint "customer_id"
    t.datetime "message_created_date"
    t.string "direction"
    t.boolean "sent_by_retailer"
    t.integer "platform"
    t.date "message_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "statistics"
    t.integer "message_hour"
    t.index ["customer_id"], name: "index_retailer_unfinished_message_blocks_on_customer_id"
    t.index ["retailer_id"], name: "index_retailer_unfinished_message_blocks_on_retailer_id"
  end

  create_table "retailer_user_notifications", force: :cascade do |t|
    t.bigint "retailer_user_id"
    t.bigint "notification_id"
    t.boolean "seen", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notification_id"], name: "index_retailer_user_notifications_on_notification_id"
    t.index ["retailer_user_id"], name: "index_retailer_user_notifications_on_retailer_user_id"
  end

  create_table "retailer_users", force: :cascade do |t|
    t.string "email", default: ""
    t.string "encrypted_password", default: ""
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "agree_terms"
    t.jsonb "onboarding_status", default: {"step"=>0, "skipped"=>false, "completed"=>false}
    t.string "provider"
    t.string "uid"
    t.string "facebook_access_token"
    t.date "facebook_access_token_expiration"
    t.boolean "retailer_admin", default: true
    t.string "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.boolean "removed_from_team", default: false
    t.string "first_name"
    t.string "last_name"
    t.boolean "retailer_supervisor", default: false
    t.boolean "only_assigned", default: false
    t.integer "locale", default: 0
    t.string "api_session_token"
    t.string "api_session_device"
    t.datetime "api_session_expiration"
    t.boolean "whatsapp_unread", default: false
    t.boolean "ml_unread", default: false
    t.boolean "messenger_unread", default: false
    t.boolean "instagram_unread", default: false
    t.integer "unread_whatsapp_chats_count", default: 0, null: false
    t.integer "unread_messenger_chats_count", default: 0, null: false
    t.integer "unread_instagram_chats_count", default: 0, null: false
    t.integer "unread_ml_chats_count", default: 0, null: false
    t.integer "unread_ml_questions_count", default: 0, null: false
    t.integer "total_unread_ml_count", default: 0, null: false
    t.boolean "allow_import", default: false
    t.integer "mobile_type"
    t.string "app_version"
    t.boolean "active", default: true
    t.string "utm_campaign"
    t.string "utm_source"
    t.string "utm_medium"
    t.string "utm_term"
    t.string "utm_content"
    t.boolean "allow_export", default: true
    t.boolean "doppler_subscribed", default: false
    t.integer "unread_fb_comments_count", default: 0
    t.integer "unread_ig_comments_count", default: 0
    t.boolean "allow_bots_edit", default: false
    t.boolean "super_admin", default: false
    t.boolean "allow_edit_orders", default: false
    t.jsonb "managed_agents", default: []
    t.bigint "user_id"
    t.boolean "current", default: true
    t.string "favorite_funnel_id"
    t.boolean "see_phone_numbers", default: true
    t.string "phone"
    t.jsonb "notifications_payload", default: {}
    t.bigint "role_id"
    t.datetime "last_sync_with_churnzero_at"
    t.boolean "current_mobile", default: true
    t.index ["invitation_token"], name: "index_retailer_users_on_invitation_token", unique: true
    t.index ["invitations_count"], name: "index_retailer_users_on_invitations_count"
    t.index ["invited_by_id"], name: "index_retailer_users_on_invited_by_id"
    t.index ["invited_by_type", "invited_by_id"], name: "index_retailer_users_on_invited_by_type_and_invited_by_id"
    t.index ["retailer_id"], name: "index_retailer_users_on_retailer_id"
    t.index ["role_id"], name: "index_retailer_users_on_role_id"
    t.index ["user_id", "current"], name: "index_retailer_users_on_user_id_and_current", unique: true, where: "(current = true)"
    t.index ["user_id", "retailer_id"], name: "index_retailer_users_on_user_id_and_retailer_id", unique: true, where: "(user_id IS NOT NULL)"
    t.index ["user_id"], name: "index_retailer_users_on_user_id"
  end

  create_table "retailer_whatsapp_conversations", force: :cascade do |t|
    t.bigint "retailer_id"
    t.integer "year"
    t.integer "month"
    t.integer "free_uic_total", default: 0
    t.integer "free_bic_total", default: 0
    t.integer "free_point_total", default: 0
    t.integer "user_initiated_total", default: 0
    t.integer "business_initiated_total", default: 0
    t.float "user_initiated_cost", default: 0.0
    t.float "business_initiated_cost", default: 0.0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "free_tier_total", default: 0
    t.integer "day"
    t.integer "diff_ftc", default: 0
    t.integer "diff_uic", default: 0
    t.integer "diff_bic", default: 0
    t.integer "marketing_total", default: 0
    t.float "marketing_cost", default: 0.0
    t.integer "authentication_total", default: 0
    t.float "authentication_cost", default: 0.0
    t.integer "utility_total", default: 0
    t.float "utility_cost", default: 0.0
    t.integer "service_total", default: 0
    t.float "service_cost", default: 0.0
    t.integer "diff_marketing", default: 0
    t.integer "diff_authentication", default: 0
    t.integer "diff_utility", default: 0
    t.integer "diff_service", default: 0
    t.index ["month", "retailer_id"], name: "index_retailer_whatsapp_conversations_on_month_and_retailer_id"
    t.index ["retailer_id", "year", "month", "day"], name: "retailer_whatsapp_conversations_by_day", unique: true
    t.index ["retailer_id"], name: "index_retailer_whatsapp_conversations_on_retailer_id"
  end

  create_table "retailers", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "slug"
    t.string "id_number"
    t.integer "id_type"
    t.string "address"
    t.string "city"
    t.string "state"
    t.string "zip_code"
    t.string "phone_number"
    t.boolean "phone_verified"
    t.string "retailer_number"
    t.boolean "whats_app_enabled", default: false
    t.string "karix_whatsapp_phone"
    t.string "encrypted_api_key"
    t.datetime "last_api_key_modified_date"
    t.string "encrypted_api_key_iv"
    t.string "encrypted_api_key_salt"
    t.float "ws_balance", default: 0.0
    t.float "ws_next_notification_balance", default: 1.5
    t.float "ws_notification_cost", default: 0.0672
    t.float "ws_conversation_cost", default: 0.0
    t.string "gupshup_phone_number"
    t.string "gupshup_src_name"
    t.string "karix_account_uid"
    t.string "karix_account_token"
    t.boolean "unlimited_account", default: false
    t.boolean "ecu_charges", default: false
    t.boolean "allow_bots", default: false
    t.boolean "int_charges", default: true
    t.string "gupshup_api_key"
    t.boolean "manage_team_assignment", default: false
    t.boolean "show_stats", default: false
    t.boolean "allow_voice_notes", default: true
    t.integer "hubspot_match", default: 0
    t.datetime "hs_expires_in"
    t.string "hs_access_token"
    t.string "hs_refresh_token"
    t.boolean "all_customers_hs_integrated", default: true
    t.boolean "allow_send_videos", default: false
    t.boolean "hs_tags", default: false
    t.string "hs_id"
    t.integer "max_agents", default: 2
    t.string "ml_domain", default: "com.ec"
    t.string "ml_site", default: "MEC"
    t.string "gupshup_app_id"
    t.string "gupshup_app_token"
    t.string "timezone"
    t.boolean "send_max_size_files", default: false, null: false
    t.string "currency", default: "USD", null: false
    t.boolean "multiple_fast_answers", default: false
    t.text "description"
    t.string "country_code"
    t.string "unique_key", default: -> { "uuid_generate_v4()" }, null: false
    t.string "catalog_slug"
    t.boolean "delete_assets", default: true
    t.decimal "tax_amount", precision: 10, scale: 2, default: "0.0"
    t.boolean "shop_updated", default: false, null: false
    t.boolean "hs_sync_conversation", default: false
    t.integer "hs_conversacion_sync_time", default: 4
    t.datetime "hs_next_sync", default: "2025-07-31 00:23:42"
    t.string "facebook_url"
    t.string "instagram_url"
    t.string "twitter_url"
    t.string "whatsapp_url"
    t.string "tiktok_url"
    t.string "shop_main_color", default: "#21974E"
    t.string "font_color", default: "#FFFFFF"
    t.boolean "active_shop", default: true
    t.boolean "show_basic_stats", default: true
    t.string "gupshup_timezone"
    t.boolean "allow_chat_videos", default: false
    t.datetime "first_payment_date"
    t.datetime "gupshup_connection_date"
    t.boolean "shop_active_send_order", default: false
    t.boolean "shop_active_store_pickup", default: false
    t.boolean "registration_mail_sent", default: false
    t.boolean "bm_verified", default: false
    t.boolean "rotate_images_in_catalog", default: false
    t.string "mp_site"
    t.string "mp_domain"
    t.string "qr_phone_id"
    t.string "qr_phone_number"
    t.boolean "allow_qr", default: false
    t.integer "campaign_chunk_size", default: 250
    t.boolean "enabled_customer_events", default: false
    t.boolean "connect_qr_mercately", default: false
    t.boolean "is_capi", default: true
    t.boolean "shopify_integrated", default: false
    t.boolean "welcome_message_sent"
    t.boolean "is_workspace", default: false
    t.boolean "payphone_connected", default: false
    t.boolean "shopify_paid_version", default: false
    t.string "chargebee_customer_id"
    t.string "payphone_id"
    t.boolean "enabled_new_bots", default: false
    t.string "domain"
    t.text "terms_conditions_content"
    t.bigint "partner_id"
    t.integer "shipping_cost_method"
    t.datetime "last_sync_with_churnzero_date"
    t.boolean "mia_chatbot_active", default: false
    t.boolean "connect_bsp_mercately", default: true
    t.boolean "mia_products_sync", default: false
    t.integer "mia_products_synced"
    t.boolean "allow_parallel_import", default: false
    t.string "prev_catalog_slug"
    t.boolean "keep_chat_assignment", default: false
    t.boolean "feature_flag", default: false
    t.boolean "kafka_enabled", default: false
    t.boolean "is_on_biz_app", default: false
    t.boolean "connect_feature_bsp", default: false
    t.boolean "hide_product_prices", default: false
    t.index ["catalog_slug"], name: "index_retailers_on_catalog_slug", unique: true
    t.index ["domain"], name: "index_retailers_on_domain", unique: true, where: "((domain IS NOT NULL) AND ((domain)::text <> ''::text))"
    t.index ["encrypted_api_key"], name: "index_retailers_on_encrypted_api_key"
    t.index ["gupshup_src_name"], name: "index_retailers_on_gupshup_src_name", unique: true
    t.index ["partner_id"], name: "index_retailers_on_partner_id"
    t.index ["qr_phone_id"], name: "index_retailers_on_qr_phone_id"
    t.index ["slug"], name: "index_retailers_on_slug", unique: true
  end

  create_table "role_permissions", force: :cascade do |t|
    t.bigint "role_id"
    t.bigint "permission_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["permission_id"], name: "index_role_permissions_on_permission_id"
    t.index ["role_id"], name: "index_role_permissions_on_role_id"
  end

  create_table "roles", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name", "retailer_id"], name: "index_roles_on_name_and_retailer_id", unique: true
    t.index ["retailer_id"], name: "index_roles_on_retailer_id"
  end

  create_table "rule_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "sales_channels", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "title"
    t.string "web_id"
    t.integer "channel_type", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "id_in_shops"
    t.index ["retailer_id"], name: "index_sales_channels_on_retailer_id"
  end

  create_table "scheduled_automations", force: :cascade do |t|
    t.text "message"
    t.integer "status", default: 0
    t.bigint "automation_id"
    t.bigint "retailer_id"
    t.bigint "customer_id"
    t.bigint "facebook_comment_id"
    t.bigint "instagram_comment_id"
    t.datetime "send_at"
    t.string "origin_instance"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_id"], name: "index_scheduled_automations_on_automation_id"
    t.index ["customer_id"], name: "index_scheduled_automations_on_customer_id"
    t.index ["facebook_comment_id"], name: "index_scheduled_automations_on_facebook_comment_id"
    t.index ["instagram_comment_id"], name: "index_scheduled_automations_on_instagram_comment_id"
    t.index ["retailer_id"], name: "index_scheduled_automations_on_retailer_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.string "session_id", null: false
    t.text "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["session_id"], name: "index_sessions_on_session_id", unique: true
    t.index ["updated_at"], name: "index_sessions_on_updated_at"
  end

  create_table "shop_orders", force: :cascade do |t|
    t.string "web_id"
    t.decimal "total"
    t.decimal "shipping_value"
    t.integer "channel"
    t.decimal "subtotal"
    t.decimal "tax_amount"
    t.integer "total_items"
    t.integer "agent_id"
    t.string "agent_name"
    t.integer "origin"
    t.string "currency"
    t.bigint "campaign_id"
    t.bigint "customer_id"
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_shop_orders_on_campaign_id"
    t.index ["customer_id"], name: "index_shop_orders_on_customer_id"
    t.index ["retailer_id"], name: "index_shop_orders_on_retailer_id"
  end

  create_table "shopify_shops", force: :cascade do |t|
    t.string "shopify_domain", null: false
    t.string "shopify_token", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "retailer_id"
    t.string "access_scopes"
    t.string "charge_id"
    t.boolean "shopify_paid_version", default: false
    t.boolean "contacts_sync", default: false
    t.boolean "addresses_sync", default: false
    t.boolean "sync_products", default: false
    t.boolean "synchronizing_products", default: false
    t.boolean "synchronized_products", default: false
    t.index ["retailer_id"], name: "index_shopify_shops_on_retailer_id"
    t.index ["shopify_domain"], name: "index_shopify_shops_on_shopify_domain", unique: true
  end

  create_table "stripe_retailers", force: :cascade do |t|
    t.string "stripe_id"
    t.boolean "confirmed", default: false
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_stripe_retailers_on_retailer_id"
  end

  create_table "stripe_transactions", force: :cascade do |t|
    t.integer "retailer_id"
    t.string "stripe_id"
    t.decimal "amount"
    t.bigint "payment_method_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "month_interval", default: 0, null: false
    t.string "web_id"
    t.integer "status", default: 0
    t.string "confirm_link"
    t.text "description", default: ""
    t.jsonb "lines", default: []
    t.decimal "credits", default: "0.0"
    t.index ["payment_method_id"], name: "index_stripe_transactions_on_payment_method_id"
  end

  create_table "support_links", force: :cascade do |t|
    t.bigint "support_id"
    t.string "link"
    t.string "title"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["support_id"], name: "index_support_links_on_support_id"
  end

  create_table "supports", force: :cascade do |t|
    t.string "active_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tags", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "tag"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "web_id"
    t.string "tag_color", default: "#ffffff00"
    t.string "font_color", default: "#B2B3BD"
    t.boolean "deleted", default: false
    t.index ["retailer_id"], name: "index_tags_on_retailer_id"
  end

  create_table "team_assignments", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "name"
    t.boolean "active_assignment", default: false
    t.boolean "default_assignment", default: false
    t.string "web_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "whatsapp", default: false, null: false
    t.boolean "messenger", default: false, null: false
    t.boolean "instagram", default: false, null: false
    t.bigint "last_assigned"
    t.boolean "assign_offline_agents", default: false, null: false
    t.index ["retailer_id"], name: "index_team_assignments_on_retailer_id"
  end

  create_table "team_retailer_users", force: :cascade do |t|
    t.bigint "team_id"
    t.bigint "retailer_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_user_id"], name: "index_team_retailer_users_on_retailer_user_id"
    t.index ["team_id"], name: "index_team_retailer_users_on_team_id"
  end

  create_table "teams", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "name"
    t.string "color"
    t.string "web_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_teams_on_retailer_id"
    t.index ["web_id"], name: "index_teams_on_web_id"
  end

  create_table "temp_shopify_retailers", force: :cascade do |t|
    t.string "shopify_domain"
    t.integer "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "template_stats", force: :cascade do |t|
    t.bigint "whatsapp_template_id", null: false
    t.datetime "calculation_date"
    t.integer "sent_count", default: 0
    t.integer "responded_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["whatsapp_template_id"], name: "index_template_stats_on_whatsapp_template_id"
  end

  create_table "templates", force: :cascade do |t|
    t.string "title"
    t.text "answer"
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "enable_for_questions", default: false
    t.boolean "enable_for_chats", default: false
    t.string "web_id"
    t.boolean "enable_for_messenger", default: false
    t.boolean "enable_for_whatsapp", default: false
    t.bigint "retailer_user_id"
    t.boolean "global", default: false
    t.boolean "enable_for_instagram", default: false
    t.string "file_type"
    t.integer "kind"
    t.boolean "enable_for_fb_comments", default: false
    t.boolean "enable_for_ig_comments", default: false
    t.bigint "team_id"
    t.string "file_deleted"
    t.index ["retailer_id"], name: "index_templates_on_retailer_id"
    t.index ["retailer_user_id"], name: "index_templates_on_retailer_user_id"
    t.index ["team_id"], name: "index_templates_on_team_id"
  end

  create_table "top_ups", force: :cascade do |t|
    t.bigint "retailer_id"
    t.float "amount", default: 0.0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_top_ups_on_retailer_id"
  end

  create_table "user_academy_progresses", force: :cascade do |t|
    t.bigint "retailer_user_id", null: false
    t.bigint "academy_video_id", null: false
    t.boolean "watched", default: false, null: false
    t.datetime "completed_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["academy_video_id"], name: "index_user_academy_progresses_on_academy_video_id"
    t.index ["retailer_user_id"], name: "index_user_academy_progresses_on_retailer_user_id"
  end

  create_table "user_academy_statuses", force: :cascade do |t|
    t.bigint "retailer_user_id", null: false
    t.bigint "academy_category_id", null: false
    t.boolean "academy_closed", default: false, null: false
    t.boolean "minimized", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["academy_category_id"], name: "index_user_academy_statuses_on_academy_category_id"
    t.index ["retailer_user_id"], name: "index_user_academy_statuses_on_retailer_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "first_name"
    t.string "last_name"
    t.string "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.string "api_session_token"
    t.string "api_session_device"
    t.datetime "api_session_expiration"
    t.boolean "agree_terms", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true
    t.index ["invited_by_id", "invited_by_type"], name: "index_users_on_invited_by_id_and_invited_by_type"
    t.index ["invited_by_type", "invited_by_id"], name: "index_users_on_invited_by_type_and_invited_by_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "whatsapp_logs", force: :cascade do |t|
    t.jsonb "payload_sent"
    t.jsonb "response"
    t.string "error_message"
    t.string "gupshup_message_id"
    t.bigint "gupshup_whatsapp_message_id"
    t.bigint "retailer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["gupshup_message_id"], name: "index_whatsapp_logs_on_gupshup_message_id"
    t.index ["gupshup_whatsapp_message_id"], name: "index_whatsapp_logs_on_gupshup_whatsapp_message_id"
    t.index ["retailer_id"], name: "index_whatsapp_logs_on_retailer_id"
  end

  create_table "whatsapp_templates", force: :cascade do |t|
    t.bigint "retailer_id"
    t.text "text", default: ""
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "template_type", default: 0, null: false
    t.string "gupshup_template_id"
    t.string "name"
    t.string "category"
    t.text "example"
    t.string "language", default: "spanish"
    t.datetime "receive_at"
    t.text "reason"
    t.boolean "submitted", default: false
    t.integer "interactive_action", default: 0
    t.json "buttons"
    t.boolean "enable_sample", default: false
    t.boolean "add_security_recommendation"
    t.integer "code_expiry_minutes"
    t.boolean "add_expiration_text"
    t.bigint "team_id"
    t.boolean "formatted_text", default: false
    t.string "header_text"
    t.string "header_example"
    t.boolean "expiration_offer", default: false
    t.string "header_offer_title"
    t.string "offer_type"
    t.string "offer_button_text"
    t.string "offer_button_value"
    t.index ["retailer_id"], name: "index_whatsapp_templates_on_retailer_id"
    t.index ["team_id"], name: "index_whatsapp_templates_on_team_id"
  end

  create_table "widget_configs", force: :cascade do |t|
    t.bigint "retailer_id"
    t.integer "platform", default: 0
    t.boolean "active", default: true
    t.string "theme_color"
    t.integer "button_position", default: 0
    t.integer "chat_icon", default: 0
    t.text "greeting_message"
    t.string "country_id"
    t.string "phone"
    t.integer "installation_method", default: 0
    t.string "web_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["retailer_id"], name: "index_widget_configs_on_retailer_id"
  end

  add_foreign_key "academy_videos", "academy_categories"
  add_foreign_key "acquired_addons", "addons"
  add_foreign_key "acquired_addons", "payment_plans"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "agent_assignments", "customers"
  add_foreign_key "agent_assignments", "retailer_users"
  add_foreign_key "agent_assignments", "team_assignments"
  add_foreign_key "agent_customers", "customers"
  add_foreign_key "agent_customers", "retailer_users"
  add_foreign_key "agent_hubspot_owners", "hubspot_owners"
  add_foreign_key "agent_hubspot_owners", "retailer_users"
  add_foreign_key "agent_hubspot_owners", "retailers"
  add_foreign_key "agent_notifications", "customers"
  add_foreign_key "agent_notifications", "retailer_users"
  add_foreign_key "agent_team_changelogs", "agent_teams"
  add_foreign_key "agent_team_changelogs", "retailer_users"
  add_foreign_key "agent_team_changelogs", "team_assignments"
  add_foreign_key "automation_actions", "automations"
  add_foreign_key "automations", "retailers"
  add_foreign_key "calendar_events", "retailer_users"
  add_foreign_key "calendar_events", "retailers"
  add_foreign_key "campaigns", "contact_groups"
  add_foreign_key "campaigns", "retailers"
  add_foreign_key "campaigns", "whatsapp_templates"
  add_foreign_key "chat_bot_actions", "chat_bot_options", column: "jump_option_id"
  add_foreign_key "chat_bot_custom_messages", "chat_bots"
  add_foreign_key "chat_bot_option_apis", "chat_bot_options"
  add_foreign_key "chat_bot_option_forms", "chat_bot_options"
  add_foreign_key "chat_bot_waiting_times", "chat_bots"
  add_foreign_key "commission_invoices", "partners"
  add_foreign_key "contact_group_customers", "contact_groups"
  add_foreign_key "contact_group_customers", "customers"
  add_foreign_key "contact_groups", "retailers"
  add_foreign_key "conversation_subtopics", "conversation_topics"
  add_foreign_key "conversation_topics", "retailers"
  add_foreign_key "customer_active_bots", "chat_bot_options"
  add_foreign_key "customer_active_bots", "customers"
  add_foreign_key "customer_addresses", "customers"
  add_foreign_key "customer_conversation_topics", "retailer_users"
  add_foreign_key "customer_event_categories", "retailers"
  add_foreign_key "customer_event_subcategories", "customer_event_categories"
  add_foreign_key "customer_events", "customer_event_categories"
  add_foreign_key "customer_events", "customer_event_subcategories"
  add_foreign_key "customer_events", "customers"
  add_foreign_key "customer_exports", "retailers"
  add_foreign_key "customer_hubspot_fields", "hubspot_fields"
  add_foreign_key "customer_hubspot_fields", "retailers"
  add_foreign_key "customers", "retailers", column: "registered_retailer_id"
  add_foreign_key "deal_automation_historics", "deal_automations"
  add_foreign_key "deal_automation_historics", "deals"
  add_foreign_key "deal_automation_historics", "funnels"
  add_foreign_key "deal_automation_messages", "deal_automations"
  add_foreign_key "deal_automation_messages", "deals"
  add_foreign_key "deal_automations", "funnel_steps"
  add_foreign_key "deals", "funnel_steps"
  add_foreign_key "entitlements", "pricing_tiers"
  add_foreign_key "erasable_shopify_addresses", "customers"
  add_foreign_key "facebook_catalogs", "retailers"
  add_foreign_key "facebook_messages", "customers"
  add_foreign_key "facebook_messages", "facebook_retailers"
  add_foreign_key "facebook_retailers", "retailers"
  add_foreign_key "funnel_deal_histories", "funnel_steps"
  add_foreign_key "funnel_deal_histories", "funnels"
  add_foreign_key "funnel_exports", "funnels"
  add_foreign_key "funnel_steps", "funnels"
  add_foreign_key "gs_templates", "retailers"
  add_foreign_key "gupshup_whatsapp_messages", "customers"
  add_foreign_key "gupshup_whatsapp_messages", "retailers"
  add_foreign_key "hubspot_fields", "retailers"
  add_foreign_key "hubspot_owners", "retailers"
  add_foreign_key "instagram_messages", "customers"
  add_foreign_key "instagram_messages", "facebook_retailers"
  add_foreign_key "instagram_messages", "retailer_users"
  add_foreign_key "integration_message_data", "retailers"
  add_foreign_key "integration_messages", "retailers"
  add_foreign_key "karix_whatsapp_messages", "customers"
  add_foreign_key "karix_whatsapp_messages", "retailers"
  add_foreign_key "meli_retailers", "retailers"
  add_foreign_key "mia_integrations", "mia_integration_types"
  add_foreign_key "mia_integrations", "retailers"
  add_foreign_key "mobile_tokens", "retailer_users"
  add_foreign_key "mp_retailers", "retailers"
  add_foreign_key "onboarding_notifications", "retailer_users"
  add_foreign_key "onboarding_schedules", "retailer_users"
  add_foreign_key "partner_commissions", "commission_invoices"
  add_foreign_key "partner_commissions", "retailers"
  add_foreign_key "partner_configs", "partners"
  add_foreign_key "partner_configs", "retailers"
  add_foreign_key "payment_methods", "retailers"
  add_foreign_key "payment_plans", "pricing_tiers"
  add_foreign_key "payment_plans", "retailers"
  add_foreign_key "paymentez_transactions", "payment_plans"
  add_foreign_key "paymentez_transactions", "paymentez_credit_cards"
  add_foreign_key "paymentez_transactions", "retailers"
  add_foreign_key "permissions", "permissions", column: "parent_permission_id"
  add_foreign_key "pipedream_integrations", "retailers"
  add_foreign_key "questions", "products"
  add_foreign_key "retailer_amount_messages", "retailer_users"
  add_foreign_key "retailer_amount_messages", "retailers"
  add_foreign_key "retailer_amount_messages_by_hours", "retailers"
  add_foreign_key "retailer_average_response_times", "retailers"
  add_foreign_key "retailer_business_rule_data", "business_rules"
  add_foreign_key "retailer_business_rule_data", "retailers"
  add_foreign_key "retailer_conversations", "retailers"
  add_foreign_key "retailer_customers", "retailers"
  add_foreign_key "retailer_mia_platforms", "mia_platforms"
  add_foreign_key "retailer_mia_platforms", "retailers"
  add_foreign_key "retailer_most_used_tags", "retailers"
  add_foreign_key "retailer_most_used_tags", "tags"
  add_foreign_key "retailer_unfinished_message_blocks", "customers"
  add_foreign_key "retailer_unfinished_message_blocks", "retailers"
  add_foreign_key "retailer_user_notifications", "notifications"
  add_foreign_key "retailer_user_notifications", "retailer_users"
  add_foreign_key "retailer_users", "roles"
  add_foreign_key "retailer_users", "users"
  add_foreign_key "retailers", "partners"
  add_foreign_key "role_permissions", "permissions"
  add_foreign_key "role_permissions", "roles"
  add_foreign_key "roles", "retailers"
  add_foreign_key "scheduled_automations", "automations"
  add_foreign_key "scheduled_automations", "customers"
  add_foreign_key "scheduled_automations", "retailers"
  add_foreign_key "shop_orders", "campaigns"
  add_foreign_key "shop_orders", "customers"
  add_foreign_key "shop_orders", "retailers"
  add_foreign_key "shopify_shops", "retailers"
  add_foreign_key "stripe_retailers", "retailers"
  add_foreign_key "support_links", "supports"
  add_foreign_key "template_stats", "whatsapp_templates"
  add_foreign_key "user_academy_progresses", "academy_videos"
  add_foreign_key "user_academy_progresses", "retailer_users"
  add_foreign_key "user_academy_statuses", "academy_categories"
  add_foreign_key "user_academy_statuses", "retailer_users"
  add_foreign_key "whatsapp_logs", "gupshup_whatsapp_messages"
  add_foreign_key "whatsapp_logs", "retailers"
  add_foreign_key "widget_configs", "retailers"
end
