# Configuración de Kafka
# =====================

# Servidores de <PERSON>fka (requerido)
KAFKA_BOOTSTRAP_SERVERS=your-kafka-server:port

# Identificador del cliente (requerido)
KAFKA_CLIENT_ID=mercately_rails6_production

# Configuración de seguridad (requerido)
KAFKA_SECURITY_PROTOCOL=SASL_SSL
KAFKA_SASL_MECHANISM=SCRAM-SHA-256
KAFKA_USERNAME=your-kafka-username
KAFKA_PASSWORD=your-kafka-password

# Certificado SSL (requerido)
KAFKA_CA_CERT=/path/to/your/ca-certificate.crt

# Configuración de Karafka (opcional)
KAFKA_CONCURRENCY=1
KAFKA_MAX_WAIT_TIME=10
KAFKA_SOCKET_MAX_FAILS=3
KAFKA_CONNECTIONS_MAX_IDLE_MS=60000
KAFKA_SOCKET_CONNECTION_SETUP_TIMEOUT_MS=10000
KAFKA_ERROR_HANDLER=Mercately::Kafka::CustomExceptionHandler

# Configuración de nodos
# ======================

# Habilitar Kafka en este nodo (true/false)
KAFKA_ENABLED=true

# Habilitar consumidor de Kafka en este nodo (true/false)
KAFKA_CONSUMER_ENABLED=true

# Habilitar productor de Kafka en este nodo (true/false)
KAFKA_PRODUCER_ENABLED=true

# Habilitar envío por Kafka (true/false)
USE_KAFKA_SENDER=true

# Auto-iniciar Karafka con la aplicación (true/false)
AUTO_START_KARAFKA=false

# Configuración del pool de productores
# ====================================

# Tamaño máximo del pool de productores
KAFKA_PRODUCER_POOL_SIZE=5

# Tiempo máximo de inactividad para un productor (segundos)
KAFKA_PRODUCER_MAX_IDLE_TIME=300

# Configuración adicional de Mercately::Kafka
# ===========================================

# Configuración del consumidor
KAFKA_ENABLE_AUTO_COMMIT=true
KAFKA_AUTO_OFFSET_RESET=earliest
KAFKA_SESSION_TIMEOUT_MS=30000

# Configuración del productor (timeouts y reintentos)
KAFKA_RETRIES=3
KAFKA_RETRY_BACKOFF_MS=2000
KAFKA_MESSAGE_TIMEOUT_MS=30000
KAFKA_REQUEST_TIMEOUT_MS=15000
KAFKA_DELIVERY_TIMEOUT_MS=60000

# Configuración de batching
KAFKA_BATCH_SIZE=16384
KAFKA_LINGER_MS=10
KAFKA_COMPRESSION_TYPE=snappy

# Procesamiento de Kafka (legacy)
USE_KAFKA_PROCESSOR=true

# Configuración para Docker (desarrollo)
# ======================================

# Modo Docker para Kafka (true/false)
KAFKA_DOCKER_MODE=false

# Configuración específica para Docker (solo desarrollo)
KAFKA_DOCKER_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_DOCKER_USE_SENDER=true
KAFKA_DOCKER_ENABLED=true
KAFKA_DOCKER_CONSUMER_ENABLED=true
KAFKA_DOCKER_PRODUCER_ENABLED=true
KAFKA_DOCKER_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_DOCKER_USERNAME=mercately
KAFKA_DOCKER_PASSWORD=mercately-dev
