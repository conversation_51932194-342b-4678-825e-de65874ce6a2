# frozen_string_literal: true

# IMPORTANTE: Este archivo requiere que todas las variables de entorno de Kafka
# estén configuradas externamente antes de iniciar la aplicación.
#
# Variables requeridas:
# - KAFKA_BOOTSTRAP_SERVERS
# - KAFKA_CLIENT_ID
# - KAFKA_SECURITY_PROTOCOL
# - KAFKA_SASL_MECHANISM (si usa SASL_SSL)
# - KAFKA_USERNAME (si usa SASL_SSL)
# - KAFKA_PASSWORD (si usa SASL_SSL)
# - KAFKA_CA_CERT (si usa SASL_SSL)
#
# Para validar la configuración: bin/validate-kafka-config

# Configurar Karafka
Mercately::Karafka.configure do |config|
  config.config_path = Rails.root.join('config').to_s

  # Validar que las variables críticas estén configuradas
  required_vars = %w[KAFKA_BOOTSTRAP_SERVERS KAFKA_CLIENT_ID KAFKA_SECURITY_PROTOCOL]
  missing_vars = required_vars.select { |var| ENV[var].blank? }

  if missing_vars.any?
    Rails.logger.error("❌ Variables de entorno faltantes para Karafka: #{missing_vars.join(', ')}")
    Rails.logger.error("❌ Ejecuta 'bin/validate-kafka-config' para más detalles")
    raise "Configuración de Kafka incompleta. Variables faltantes: #{missing_vars.join(', ')}"
  end

  # Validar configuración SASL_SSL si es necesaria
  if ENV['KAFKA_SECURITY_PROTOCOL'] == 'SASL_SSL'
    sasl_vars = %w[KAFKA_SASL_MECHANISM KAFKA_USERNAME KAFKA_PASSWORD KAFKA_CA_CERT]
    missing_sasl_vars = sasl_vars.select { |var| ENV[var].blank? }

    if missing_sasl_vars.any?
      Rails.logger.error("❌ Variables SASL_SSL faltantes: #{missing_sasl_vars.join(', ')}")
      raise "Configuración SASL_SSL incompleta. Variables faltantes: #{missing_sasl_vars.join(', ')}"
    end
  end

  # Registrar la configuración para depuración (sin exponer credenciales)
  Rails.logger.info("✅ Karafka configurado para entorno: #{Rails.env}")
  Rails.logger.info("✅ KAFKA_BOOTSTRAP_SERVERS: #{ENV['KAFKA_BOOTSTRAP_SERVERS']}")
  Rails.logger.info("✅ KAFKA_CLIENT_ID: #{ENV['KAFKA_CLIENT_ID']}")
  Rails.logger.info("✅ KAFKA_SECURITY_PROTOCOL: #{ENV['KAFKA_SECURITY_PROTOCOL']}")

  if ENV['KAFKA_PASSWORD']
    Rails.logger.info("✅ KAFKA_PASSWORD: [CONFIGURADA]")
  end
end

# Configurar la aplicación Karafka
Karafka::App.setup do |config|
  config.client_id = ENV['KAFKA_CLIENT_ID']
  config.kafka = {
    'bootstrap.servers': ENV['KAFKA_BOOTSTRAP_SERVERS'],
    'security.protocol': ENV['KAFKA_SECURITY_PROTOCOL']
  }

  # Agregar configuración SASL si es necesario
  if ENV['KAFKA_SECURITY_PROTOCOL'] == 'SASL_SSL'
    config.kafka[:'sasl.mechanisms'] = ENV['KAFKA_SASL_MECHANISM']
    config.kafka[:'sasl.username'] = ENV['KAFKA_USERNAME']
    config.kafka[:'sasl.password'] = ENV['KAFKA_PASSWORD']

    # Agregar configuración SSL
    ca_cert_path = ENV['KAFKA_CA_CERT']
    if File.exist?(ca_cert_path)
      config.kafka[:'ssl.ca.location'] = ca_cert_path
      Rails.logger.info("✅ Certificado SSL configurado: #{ca_cert_path}")
    else
      Rails.logger.error("❌ Certificado SSL no encontrado: #{ca_cert_path}")
      raise "Certificado SSL requerido pero no encontrado en: #{ca_cert_path}"
    end
  end

  # La configuración de rutas se hace en config/karafka.yml
end

# Cargar todos los consumidores
Rails.root.glob('app/consumers/**/*.rb').each { |f| require f }

# Suscribir listeners para logging y monitoreo
Karafka.monitor.subscribe(Karafka::Instrumentation::LoggerListener.new)
Karafka.monitor.subscribe(Karafka::Instrumentation::ProctitleListener.new)

# Monitoreo de eventos del ciclo de vida de la aplicación
Karafka.monitor.subscribe('app.stopping') do
  Rails.logger.info('Karafka is stopping - performing cleanup...')
end

Karafka.monitor.subscribe('app.stopped') do
  Rails.logger.info('Karafka has stopped')
end
